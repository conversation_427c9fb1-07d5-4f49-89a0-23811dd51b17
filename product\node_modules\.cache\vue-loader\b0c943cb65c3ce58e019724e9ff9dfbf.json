{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\preview-code\\preview-code.vue?vue&type=style&index=0&id=14cf3f8f&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\preview-code\\preview-code.vue", "mtime": 1752541693464}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoucXItbWFyayB7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCiAgdG9wOiAwOw0KICBsZWZ0OiAwOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuNyk7DQogIHotaW5kZXg6IDEwMDA7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAuaWNvbi1ib3ggew0KICAgIHdpZHRoOiAyNHB4Ow0KICAgIGhlaWdodDogMjRweDsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgYm9yZGVyLXJhZGl1czogMjRweDsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgbWFyZ2luLWxlZnQ6IDI3NXB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDI1cHg7DQogICAgZm9udC1zaXplOiAxOHB4Ow0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIH0NCiAgLnRpcHMgew0KICAgIGZvbnQtc2l6ZTogMjBweDsNCiAgICBsaW5lLWhlaWdodDogMzZweDsNCiAgICBjb2xvcjogI2ZmZjsNCiAgICBtYXJnaW4tdG9wOiAyNXB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["preview-code.vue"], "names": [], "mappings": ";AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "preview-code.vue", "sourceRoot": "src/components/preview-code", "sourcesContent": ["<template>\r\n  <div class=\"qr-mark\">\r\n    <div class=\"icon-box\" @click=\"handleclose\">\r\n      <i class=\"el-icon-close\"></i>\r\n    </div>\r\n    <div class=\"qr-box\">\r\n      <vueQr :text=\"url\" :size=\"200\"></vueQr>\r\n    </div>\r\n    <p class=\"tips\">打开APP扫描二维码签到{{tips}}</p>\r\n  </div>\r\n</template>\r\n<script>\r\nimport vueQr from 'vue-qr'\r\nexport default {\r\n  components: { vueQr },\r\n  name: 'preview-code',\r\n  props: {\r\n    url: String,\r\n    tips: String\r\n  },\r\n  methods: {\r\n    handleclose () {\r\n      this.$emit('cancel')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.qr-mark {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  top: 0;\r\n  left: 0;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  z-index: 1000;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  .icon-box {\r\n    width: 24px;\r\n    height: 24px;\r\n    text-align: center;\r\n    line-height: 24px;\r\n    border-radius: 24px;\r\n    cursor: pointer;\r\n    margin-left: 275px;\r\n    margin-bottom: 25px;\r\n    font-size: 18px;\r\n    background-color: #fff;\r\n  }\r\n  .tips {\r\n    font-size: 20px;\r\n    line-height: 36px;\r\n    color: #fff;\r\n    margin-top: 25px;\r\n  }\r\n}\r\n</style>\r\n"]}]}