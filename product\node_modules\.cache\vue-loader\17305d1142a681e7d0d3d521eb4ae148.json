{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\signature\\signature.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\signature\\signature.vue", "mtime": 1752541693490}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdzaWduYXR1cmUnLA0KICBkYXRhICgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcGFzc3dvcmQ6ICcnDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5Y676LCD55So562+5ZCNDQogICAgaGFuZGxlU3VyZSAoKSB7DQogICAgICBpZiAodGhpcy5wYXNzd29yZCA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36L6T5YWl562+5ZCN5a+G56CBJykNCiAgICAgIH0NCiAgICAgIHRoaXMuJGFwaS5vZmZpY2VBdXRvbWF0aW9uLnVzZXJTaWduYXR1cmVUb2tlbih7IHBhc3NXb3JkOiB0aGlzLnBhc3N3b3JkIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5lcnJjb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRlbWl0KCdjbG9zZScsIHJlcy5kYXRhKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5Y+W5raIDQogICAgaGFuZGxlQ2FuY2VsICgpIHsNCiAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["signature.vue"], "names": [], "mappings": ";AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "signature.vue", "sourceRoot": "src/components/signature", "sourcesContent": ["<template>\r\n  <div class=\"signature\">\r\n    <p class=\"title\">\r\n      <i class=\"el-icon-lock\"></i>\r\n      签名密码\r\n    </p>\r\n    <div class=\"input-box\">\r\n      <el-input v-model=\"password\"\r\n                placeholder=\"请输入签名密码\"\r\n                clearable\r\n                type=\"password\"></el-input>\r\n    </div>\r\n    <div class=\"btn_box\">\r\n      <el-button type=\"primary\"\r\n                 size=\"mini\"\r\n                 @click=\"handleSure\">确定</el-button>\r\n      <el-button type=\"primary\"\r\n                 size=\"mini\"\r\n                 @click=\"handleCancel\">取消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'signature',\r\n  data () {\r\n    return {\r\n      password: ''\r\n    }\r\n  },\r\n  methods: {\r\n    // 去调用签名\r\n    handleSure () {\r\n      if (this.password === '') {\r\n        return this.$message.warning('请输入签名密码')\r\n      }\r\n      this.$api.officeAutomation.userSignatureToken({ passWord: this.password }).then(res => {\r\n        if (res.errcode === 200) {\r\n          this.$emit('close', res.data)\r\n        }\r\n      })\r\n    },\r\n    // 取消\r\n    handleCancel () {\r\n      this.$emit('close')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.signature {\r\n  width: 600px;\r\n  padding: 15px 20px;\r\n  .title {\r\n    color: #3364a8;\r\n    font-size: $textSize16;\r\n    line-height: 30px;\r\n  }\r\n  .btn_box {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-top: 20px;\r\n    align-items: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}