{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\InnovationExcellence.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\InnovationExcellence.vue", "mtime": 1752541693826}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAgOA;AACA;AACA;AACA;AAEA;EACAA,4BADA;EAEAC;IACAC,aADA;IAEAC,YAFA;IAGAC;EAHA,CAFA;;EAQAC;IACA;MACAC,cADA;MACA;MACAC,mBAFA;MAEA;MACAC;QACAC,YADA;QAEAC;MAFA,CAHA;MAOAC,QAPA;MAQAC,WARA;MASAC,oBATA;MAUA;MACAC,SAXA;MAYAC,YAZA;MAaAC,SAbA;MAcAC,gBAdA;MAeAC,WAfA;MAgBAC,iBAhBA;MAiBAC,sBAjBA;MAmBAC,OAnBA;MAoBAC,MApBA;MAqBAC,UArBA;MAsBAC,aAtBA;MAuBAC,cAvBA;MAwBAC;IAxBA;EA0BA,CAnCA;;EAoCAC,qBApCA;EAqCAC,mBArCA;;EAsCAC;IACA;IACA;IACA;EACA,CA1CA;;EA2CAC;IACA;AACA;AACA;IACA;MACA;QACAC;MADA;MAGA;QAAA1B;MAAA;MACA;IACA,CAVA;;IAYA;AACA;AACA;IACA;MACA;MACA;QAAAA;MAAA;MACA;IACA,CAnBA;;IAoBA2B;MACA;MACA;IACA,CAvBA;;IAwBAC;MACA;QACAC,uBADA;QAEAC,sBAFA;QAGAC;MAHA,GAIAC,IAJA,CAIA;QACA;UAAAhB;QAAA;UACA;YACA,mCADA,CACA;;YACA;UACA;QACA,CALA;MAMA,CAXA,EAWAiB,KAXA,CAWA;QACA,2BADA,CAEA;;QACA;MACA,CAfA;IAgBA,CAzCA;;IA0CAC;MACA;MACA;MACA;MACA;IACA,CA/CA;;IAgDA;IACA;MACA;QACAzB,mBADA;QAEAC,uBAFA;QAGAH,qBAHA;QAIAC,8BAJA;QAKA2B,4BALA;QAMA;QACA;QACAC,2BARA;QASAhC,oCATA;QASA;QACAiC,gDAVA,CAUA;;MAVA;MAYA;QAAArC;QAAAW;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA,CAFA;IAGA,CAvEA;;IAwEA;IACA2B;MACA;IACA,CA3EA;;IA4EAC;MACA;IACA,CA9EA;;IA+EAC;MACA;MACA;IACA,CAlFA;;IAmFAC;MACA;MACA;MACA;MACA;MACA,yCALA,CAMA;;MACA;IACA,CA3FA;;IA4FA;IACAC;MACA;MACA;IACA,CAhGA;;IAiGA;IACAC;MACA;MACA;IACA,CArGA;;IAsGA;IACAC;MACA;MACA;IACA,CA1GA;;IA2GA;IACAC;MACA;MACA;IACA,CA/GA;;IAgHAC;MACA;QACA;UACAjB,uBADA;UAEAC,sBAFA;UAGAC;QAHA,GAIAC,IAJA,CAIA;UACA;QACA,CANA,EAMAC,KANA,CAMA;UACA;YACAF,YADA;YAEAgB;UAFA;QAIA,CAXA;MAYA,CAbA,MAaA;QACA;UACAA,oBADA;UAEAhB;QAFA;MAIA;IACA,CApIA;;IAqIA;IACA;MACA;QAAAf;QAAAqB;MAAA;MACA;QAAAW;QAAAC;MAAA;;MACA;QACA;QACA;UACAF,eADA;UAEAhB;QAFA;MAIA;IACA;;EAhJA;AA3CA", "names": ["name", "components", "InnovationNew", "finishDetail", "titleDetail", "data", "officeData", "auditStatusData", "searchParams", "officeId", "auditStatusParams", "time", "keyword", "publishStartTime", "pageNo", "pageSize", "total", "currentRow", "show", "showFinish", "showTitleDetail", "ids", "id", "choose", "selectObj", "selectData", "tableData", "props", "mixins", "mounted", "methods", "types", "updateList", "handleDelete", "confirmButtonText", "cancelButtonText", "type", "then", "catch", "newCallback", "publishEndTime", "memberType", "auditStatus", "handleSizeChange", "handleCurrentChange", "search", "reset", "newData", "modify", "finishStatus", "handleClick", "passClick", "message", "<PERSON><PERSON><PERSON>", "errmsg"], "sourceRoot": "src/views/AssessmentOrgan/InnovationExcellence", "sources": ["InnovationExcellence.vue"], "sourcesContent": ["<template>\r\n  <!-- 创新创优目标 -->\r\n  <div class=\"InnovationExcellence\">\r\n    <search-box @search-click=\"search\"\r\n                @reset-click=\"reset\"\r\n                title=\"创新创优目标筛选\">\r\n      <!--关键字查找框  -->\r\n      <zy-widget label=\"关键字\">\r\n        <el-input placeholder=\"请输入关键词\"\r\n                  v-model=\"keyword\"\r\n                  clearable\r\n                  @keyup.enter.native=\"search\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\"></div>\r\n        </el-input>\r\n      </zy-widget>\r\n\r\n      <!-- 时间查询 -->\r\n      <zy-widget label=\"起止时间\">\r\n        <el-date-picker v-model=\"time\"\r\n                        type=\"daterange\"\r\n                        range-separator=\"至\"\r\n                        start-placeholder=\"开始时间\"\r\n                        value-format=\"timestamp\"\r\n                        end-placeholder=\"结束时间\">\r\n        </el-date-picker>\r\n      </zy-widget>\r\n\r\n      <!-- 部门查询(用到机构树)  -->\r\n      <zy-widget label=\"部门查询\"\r\n                 v-permissions=\"'auth:innovation:department'\">\r\n        <zy-select v-model=\"searchParams.officeId\"\r\n                   clearable\r\n                   @keyup.enter.native=\"search\"\r\n                   placeholder=\"请选择部门\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\">\r\n          </div>\r\n        </zy-select>\r\n      </zy-widget>\r\n\r\n      <!-- 审核状态查询 -->\r\n      <zy-widget label=\"审核状态查询\">\r\n        <el-select v-model=\"searchParams.auditStatusParams\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择审核状态\">\r\n          <el-option v-for=\"item in auditStatusData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n\r\n    </search-box>\r\n\r\n    <div class=\"qd-list-wrap\">\r\n      <!-- 新增按钮 -->\r\n      <div class=\"qd-btn-box\">\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-plus\"\r\n                   @click=\"newData\">新增\r\n        </el-button>\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   v-permissions=\"'auth:innovation:checkPass'\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n        <el-button type=\"danger\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   v-permissions=\"'auth:innovation:checkNotPass'\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n      </div>\r\n\r\n      <div class=\"tableData\">\r\n        <zy-table>\r\n          <el-table :data=\"tableData\"\r\n                    slot=\"zytable\"\r\n                    row-key=\"id\"\r\n                    ref=\"multipleTable\"\r\n                    @select=\"selected\"\r\n                    @select-all=\"selectedAll\"\r\n                    :tree-props=\"{children: 'children'}\">\r\n            <el-table-column type=\"selection\"\r\n                             fixed=\"left\"\r\n                             width=\"50\">\r\n            </el-table-column>\r\n            <el-table-column label=\"标题\"\r\n                             show-overflow-tooltip\r\n                             prop=\"title\"\r\n                             min-width=\"330px\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"modify(scope.row)\"\r\n                           size=\"small\"> {{scope.row.title}}</el-button>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"部门\"\r\n                             width=\"130px\"\r\n                             prop=\"officeName\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{scope.row.officeName }} </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"发布时间\"\r\n                             width=\"170\"\r\n                             prop=\"publishTime\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{$format(scope.row.publishTime).substr(0,16)}} </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"时限要求\"\r\n                             width=\"170\"\r\n                             prop=\"endTime\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{$format(scope.row.endTime).substr(0,16)}} </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"分值\"\r\n                             align=\"center\"\r\n                             width=\"70\"\r\n                             prop=\"score\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"审核状态\"\r\n                             width=\"120\"\r\n                             prop=\"auditStatus\">\r\n\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"重点工作\"\r\n                             align=\"center\"\r\n                             width=\"100\"\r\n                             prop=\"isMainwork\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"类型\"\r\n                             width=\"120\"\r\n                             show-overflow-tooltip\r\n                             prop=\"classify\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"完成情况\"\r\n                             width=\"100\"\r\n                             fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"finishStatus(scope.row)\"\r\n                           size=\"small\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 完成情况\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"操作\"\r\n                             width=\"120\"\r\n                             fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"handleClick(scope.row)\"\r\n                           size=\"small\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 编辑\r\n                </el-button>\r\n\r\n                <el-button type=\"text\"\r\n                           size=\"small\"\r\n                           @click=\"handleDelete(scope.row.id)\"\r\n                           :class=\"scope.row.auditStatus == '审核通过' ? '' : 'delBtn'\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 删除\r\n                </el-button>\r\n\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </zy-table>\r\n      </div>\r\n\r\n      <div class=\"paging_box\">\r\n        <el-pagination @size-change=\"handleSizeChange\"\r\n                       @current-change=\"handleCurrentChange\"\r\n                       :current-page.sync=\"pageNo\"\r\n                       :page-sizes=\"[10, 20, 30, 40]\"\r\n                       :page-size.sync=\"pageSize\"\r\n                       background\r\n                       layout=\"total, prev, pager, next, sizes, jumper\"\r\n                       :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <zy-pop-up v-model=\"show\"\r\n               :title=\"id?'编辑':'新增'\">\r\n      <InnovationNew :id=\"id\"\r\n                     :memberType=\"memberType\"\r\n                     @newCallback=\"newCallback\">\r\n      </InnovationNew>\r\n    </zy-pop-up>\r\n    <zy-pop-up v-model=\"showFinish\"\r\n               class=\"titleStyle\"\r\n               title=\"完成情况\">\r\n      <finishDetail :id=\"id\"\r\n                    :memberType=\"memberType\"\r\n                    @newCallback=\"newCallback\">\r\n      </finishDetail>\r\n    </zy-pop-up>\r\n    <zy-pop-up v-model=\"showTitleDetail\"\r\n               class=\"titleStyle\"\r\n               title=\"详情\"\r\n               :beforeClose=\"updateList\">\r\n      <titleDetail :id=\"id\"\r\n                   :memberType=\"memberType\"\r\n                   @newCallback=\"newCallback\">\r\n      </titleDetail>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport tableData from '@mixins/tableData'\r\nimport InnovationNew from './InnovationNew.vue'\r\nimport finishDetail from './finishDetail'\r\nimport titleDetail from './titleDetail'\r\n\r\nexport default {\r\n  name: 'InnovationExcellence',\r\n  components: {\r\n    InnovationNew,\r\n    finishDetail,\r\n    titleDetail\r\n\r\n  },\r\n  data () {\r\n    return {\r\n      officeData: [], // 机构树,\r\n      auditStatusData: [], // 审核状态\r\n      searchParams: {\r\n        officeId: '',\r\n        auditStatusParams: ''\r\n      },\r\n      time: [],\r\n      keyword: '',\r\n      publishStartTime: '',\r\n      // ***\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      total: 10,\r\n      currentRow: null,\r\n      show: false,\r\n      showFinish: false,\r\n      showTitleDetail: false,\r\n\r\n      ids: '',\r\n      id: '',\r\n      choose: [],\r\n      selectObj: [],\r\n      selectData: [],\r\n      tableData: []\r\n    }\r\n  },\r\n  props: ['memberType'],\r\n  mixins: [tableData],\r\n  mounted () {\r\n    this.getInnovationExcellenceList()\r\n    this.treeList()\r\n    this.dictionaryPubkvs()\r\n  },\r\n  methods: {\r\n    /**\r\n      *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluate_audit_status'\r\n      })\r\n      var { data } = res\r\n      this.auditStatusData = data.evaluate_audit_status\r\n    },\r\n\r\n    /**\r\n      *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    updateList () {\r\n      this.showTitleDetail = false\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    handleDelete (ids) {\r\n      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$api.AssessmentOrgan.reqDelInnovationExcellence({ ids }).then((res) => {\r\n          if (res.errcode === 200) {\r\n            this.getInnovationExcellenceList()// 删除后更新页面\r\n            this.$message.success('删除成功')\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('取消删除')\r\n        // this.getMonthlyWorkRecordlist()\r\n        return false\r\n      })\r\n    },\r\n    newCallback () {\r\n      this.getInnovationExcellenceList()\r\n      this.show = false\r\n      this.showFinish = false\r\n      this.showTitleDetail = false\r\n    },\r\n    // 请求后台数据 获取列表信息\r\n    async getInnovationExcellenceList () {\r\n      const res = await this.$api.AssessmentOrgan.reqInnovationExcellenceList({\r\n        pageNo: this.pageNo,\r\n        pageSize: this.pageSize,\r\n        keyword: this.keyword,\r\n        publishStartTime: this.time[0],\r\n        publishEndTime: this.time[1],\r\n        // // sedateId: this.sedateId\r\n        // sedateId: this.selectedYear\r\n        memberType: this.memberType,\r\n        officeId: this.searchParams.officeId, // 部门查询\r\n        auditStatus: this.searchParams.auditStatusParams // 审核状态\r\n      })\r\n      var { data, total } = res\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n      this.selectObj = []\r\n      this.selectData = []\r\n      this.$nextTick(function () {\r\n        this.memoryChecked()\r\n      })\r\n    },\r\n    // paging_box 里面用到的方法\r\n    handleSizeChange () {\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    search () {\r\n      this.pageNo = 1\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    reset () {\r\n      this.keyword = ''\r\n      this.publishStartTime = ''\r\n      this.time.splice(0, this.time.length)\r\n      this.searchParams.officeId = ''\r\n      this.searchParams.auditStatusParams = ''\r\n      // 重置后重新调用一次列表信息\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    // 新增\r\n    newData () {\r\n      this.id = ''\r\n      this.show = true\r\n    },\r\n    // 标题详情 modify\r\n    modify (row) {\r\n      this.id = row.id\r\n      this.showTitleDetail = true\r\n    },\r\n    // 完成情况\r\n    finishStatus (row) {\r\n      this.id = row.id\r\n      this.showFinish = true\r\n    },\r\n    // 编辑\r\n    handleClick (row) {\r\n      this.id = row.id\r\n      this.show = true\r\n    },\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckInnovation(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 审核 (创新创优)\r\n    async getCheckInnovation (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckInnovation({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getInnovationExcellenceList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.InnovationExcellence {\r\n  height: 100%;\r\n  width: 100%;\r\n  overflow: hidden;\r\n\r\n  .qd-list-wrap {\r\n    height: calc(100% - 83px);\r\n  }\r\n  .tableData {\r\n    .el-scrollbar__wrap .el-scrollbar__view th {\r\n      background: #f5f7fb;\r\n    }\r\n    height: calc(100% - 132px);\r\n  }\r\n  .button-box {\r\n    background: #fff;\r\n    margin-top: 20px;\r\n  }\r\n  .button-box .el-button {\r\n    background: #fff;\r\n  }\r\n  .el-button--danger.is-plain:hover,\r\n  .el-button--danger.is-plain:focus {\r\n    // color: #f56c6c;\r\n    background: #f56c6c;\r\n    color: #fff;\r\n  }\r\n  .el-button--success.is-plain:hover,\r\n  .el-button--success.is-plain:focus {\r\n    background: #67c23a;\r\n    color: #fff;\r\n  }\r\n  .delBtn {\r\n    color: #f56c6c;\r\n  }\r\n  .paging_box {\r\n    background: #fff;\r\n  }\r\n  .scoreEdit {\r\n    width: 700px;\r\n    height: 100%;\r\n    padding: 24px;\r\n    .form-button {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n  .search-box {\r\n    //title文字样式\r\n    .search-title {\r\n      width: 133px;\r\n      height: 16px;\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-left: 32px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}