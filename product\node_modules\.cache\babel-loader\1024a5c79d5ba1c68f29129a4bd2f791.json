{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue", "mtime": 1756287526224}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,gBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAJ;MACAG,YADA;MAEAC;IAFA,CALA;IASAC;MACAF,WADA;MAEAC,cAFA;MAGAE;IAHA;EATA,CAFA;;EAiBAC;IACA;MACAC,WADA;MAEAC,wBAFA;MAEA;MACAC,yBAHA;MAGA;MACA;MACAC,SACA,SADA,EACA,SADA,EACA,SADA,EACA,SADA,EAEA,SAFA,EAEA,SAFA,EAEA,SAFA,EAEA,SAFA,EAGA,SAHA,EAGA,SAHA,EAGA,SAHA,EAGA,SAHA,EAIA,SAJA,EAIA,SAJA,EAIA,SAJA,EAIA,SAJA,EAKA,SALA,EAKA,SALA,EAKA,SALA,EAKA,SALA;IALA;EAaA,CA/BA;;EAgCAC;IACA;EACA,CAlCA;;EAmCAC;IACA;IACA;MACAC;IACA;;IACA;MACA;IACA;EACA,CA3CA;;EA4CAC;IACAC;MACA;MACA;QACA;MACA,CAJA,CAKA;;;MACA;IACA,CARA;;IASAC;MACA;MACA;MACA;MACA;QACAC;UACAC,eADA;UAEAC,qCAFA;UAGAC,qCAHA;UAIAC,sBAJA;UAKAC,cALA;UAMAC;YACAC;UADA;QANA,CADA;QAWAC;UACAC,uEADA;UAEAC,yIAFA;UAGAC,2DAHA;UAIAC,iGAJA;UAKAC,uDALA;UAMAC,gDANA;UAOAC,gDAPA;UAQAC,oDARA;UASAC,mIATA;UAUAX;YACAC,aADA;YAEAW,YAFA;YAGAC;UAHA,CAVA;UAeAjB;YACA;;YACA;cACA;YACA,CAFA,MAEA;cACA;YACA;UACA;QAtBA,CAXA;QAmCAkB,SACA;UACAtC,eADA;UAEAG,WAFA;UAGAoC,kLAHA;UAIAC,kLAJA;UAKAC,wBALA;UAMAC;YACAC,WADA;YAEAC;UAFA,CANA;UAUAC;YACAC,UADA;YAEAC,kBAFA;YAGAX,YAHA;YAIAX,aAJA;YAKAL;UALA,CAVA;UAiBA4B;YACAF;UADA,CAjBA;UAoBAG;YACA1B,cADA;YAEAD,sBAFA,CAEA;;UAFA,CApBA;UAwBAf;YACA2C,iBADA;YAEAlD,eAFA;YAGAiD;cAAAxB;YAAA;UAHA;QAxBA,CADA,EA+BA;UACAtB,WADA;UAEAoC,gLAFA;UAGAC,kLAHA;UAIAjC,OACA;YACA2C,UADA;YAEAD;cACAxB;YADA;UAFA,CADA,CAJA;UAYAoB;YACAC;UADA;QAZA,CA/BA,CAnCA;QAmFAK,UACA;UACAhD,cADA;UAEA0B,8IAFA;UAGAC,2IAHA;UAIAsB;YACAC,KADA;YAEAC,KAFA;YAGAC;UAHA,CAJA;UASAC;YACAC,YADA;YAEAC,sDAFA;YAGAC;cACAxD,cADA;cAEAyD,IAFA;cAGAC,IAHA;cAIAC,KAJA;cAKAC,KALA;cAMAC,aACA;gBAAAC;gBAAAxC;cAAA,CADA,EAEA;gBAAAwC;gBAAAxC;cAAA,CAFA;YANA;UAHA,CATA;UAwBAyC,KAxBA;UAyBAC,YAzBA;UA0BApB;QA1BA,CADA;MAnFA;MAkHA,6BAtHA,CAuHA;;MACAqB;QACA;UACA;QACA;MACA,CAJA,EAxHA,CA6HA;;MACA,0BA9HA,CA+HA;;MACA;QACA;MACA,CAFA;MAGA;QACA;MACA,CAFA;IAGA,CA/IA;;IAiJA;IACAC;MACA;MAEA;QACA;QACA;UACA;YACAlE,gBADA;YAEAmE,cAFA;YAGAC;UAHA,GADA,CAMA;;UACA;YACApE;UADA;QAGA,CAZA,CAcA;;;QACA;QACA;UACAA,iBADA;UAEAmE,cAFA;UAGAC;QAHA,GAhBA,CAsBA;;QACA;UACApE,eADA;UAEAmE,cAFA;UAGAC;QAHA;MAKA,CA5BA,EA4BA,IA5BA,EAHA,CA+BA;IACA,CAlLA;;IAoLA;IACAC;MACA;QACA1D;QACA;MACA,CAJA,CAKA;;;MACA;QACA;UACAX,gBADA;UAEAmE,cAFA;UAGAC;QAHA;QAKA;MACA;IACA;;EAnMA;AA5CA", "names": ["name", "props", "id", "type", "required", "chartData", "default", "data", "chart", "autoHighlightTimer", "currentHighlightIndex", "colors", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "getColor", "initChart", "tooltip", "trigger", "formatter", "backgroundColor", "borderColor", "borderWidth", "textStyle", "color", "legend", "orient", "right", "left", "top", "bottom", "itemWidth", "itemHeight", "icon", "itemGap", "fontSize", "fontFamily", "series", "radius", "center", "avoidLabelOverlap", "emphasis", "scale", "scaleSize", "label", "show", "position", "labelLine", "itemStyle", "value", "graphic", "shape", "cx", "cy", "r", "style", "fill", "lineWidth", "stroke", "x", "y", "x2", "y2", "colorStops", "offset", "z", "silent", "window", "startAutoHighlight", "seriesIndex", "dataIndex", "stopAutoHighlight"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["PieChart.vue"], "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"pie-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'PieChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    name: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      autoHighlightTimer: null, // 自动高亮定时器\n      currentHighlightIndex: -1, // 当前高亮的数据项索引\n      // 预定义的颜色数组，按顺序分配给数据项\n      colors: [\n        '#4FC3F7', '#FFF67C', '#66BB6A', '#FFA726',\n        '#FF7043', '#AB47BC', '#5C6BC0', '#42A5F5',\n        '#FFCA28', '#4CAF50', '#EF5350', '#8D6E63',\n        '#9C27B0', '#3F51B5', '#2196F3', '#00BCD4',\n        '#FF9800', '#795548', '#607D8B', '#E91E63'\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    // 清除自动高亮定时器\n    if (this.autoHighlightTimer) {\n      clearInterval(this.autoHighlightTimer)\n    }\n    if (this.chart) {\n      this.chart.dispose()\n    }\n  },\n  methods: {\n    getColor (index, item) {\n      // 如果数据项中有颜色信息，优先使用数据中的颜色\n      if (item && item.color) {\n        return item.color\n      }\n      // 否则使用预定义的颜色数组\n      return this.colors[index % this.colors.length]\n    },\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00d4ff',\n          borderWidth: 1,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: this.id === 'category_distribution' ? 'horizontal' : 'vertical',\n          right: this.id === 'reply-type-pie' ? '0%' : this.id === 'category_distribution' ? null : this.id === 'proposal-statistics' ? '0%' : '5%',\n          left: this.id === 'category_distribution' ? 'center' : null,\n          top: this.id === 'reply-type-pie' ? '38%' : this.id === 'category_distribution' ? null : 'center',\n          bottom: this.id === 'category_distribution' ? 20 : null,\n          itemWidth: this.id === 'reply-type-pie' ? 12 : 5,\n          itemHeight: this.id === 'reply-type-pie' ? 6 : 5,\n          icon: this.id === 'reply-type-pie' ? null : 'circle',\n          itemGap: this.id === 'reply-type-pie' ? 40 : this.id === 'category_distribution' ? 30 : this.id === 'proposal-statistics' ? 12 : 25,\n          textStyle: {\n            color: '#fff',\n            fontSize: 12,\n            fontFamily: 'Microsoft YaHei'\n          },\n          formatter: (name) => {\n            const item = this.chartData.find(d => d.name === name)\n            if (this.id === 'reply-type-pie') {\n              return `${name}`\n            } else {\n              return `${name}  ${item ? item.value : ''}%`\n            }\n          }\n        },\n        series: [\n          {\n            name: this.name,\n            type: 'pie',\n            radius: this.id === 'category_distribution' ? ['30%', '45%'] : this.id === 'proposal-statistics' ? ['60%', '85%'] : this.id === 'reply-type-pie' ? ['40%', '70%'] : ['55%', '80%'],\n            center: this.id === 'category_distribution' ? ['50%', '25%'] : this.id === 'proposal-statistics' ? ['22%', '50%'] : this.id === 'reply-type-pie' ? ['50%', '50%'] : ['30%', '50%'],\n            avoidLabelOverlap: false,\n            emphasis: {\n              scale: true,\n              scaleSize: 10\n            },\n            label: {\n              show: true,\n              position: 'center',\n              fontSize: 16,\n              color: '#fff',\n              formatter: this.name\n            },\n            labelLine: {\n              show: false\n            },\n            itemStyle: {\n              borderWidth: 3,\n              borderColor: '#07345F' // 用大屏背景色\n            },\n            data: this.chartData.map((item, index) => ({\n              value: item.value,\n              name: item.name,\n              itemStyle: { color: this.getColor(index, item) }\n            }))\n          },\n          {\n            type: 'pie',\n            radius: this.id === 'category_distribution' ? ['50%', '51%'] : this.id === 'proposal-statistics' ? ['94%', '95%'] : this.id === 'reply-type-pie' ? ['1%', '1%'] : ['88%', '89%'],\n            center: this.id === 'category_distribution' ? ['50%', '25%'] : this.id === 'proposal-statistics' ? ['22%', '50%'] : this.id === 'reply-type-pie' ? ['50%', '50%'] : ['30%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: '#2f689a'\n                }\n              }\n            ],\n            label: {\n              show: false\n            }\n          }\n        ],\n        graphic: [\n          {\n            type: 'circle',\n            left: this.id === 'category_distribution' ? '36.5%' : this.id === 'proposal-statistics' ? '12%' : this.id === 'reply-type-pie' ? '40%' : '17%',\n            top: this.id === 'category_distribution' ? '13%' : this.id === 'proposal-statistics' ? '23%' : this.id === 'reply-type-pie' ? '35%' : '26%',\n            shape: {\n              cx: 0,\n              cy: 0,\n              r: this.id === 'category_distribution' ? 60 : this.id === 'proposal-statistics' ? 40 : this.id === 'reply-type-pie' ? 0 : 50\n            },\n            style: {\n              fill: 'none',\n              lineWidth: this.id === 'category_distribution' ? 4 : 3,\n              stroke: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: '#23E1FF' },\n                  { offset: 1, color: 'rgba(35,225,255,0)' }\n                ]\n              }\n            },\n            z: 10,\n            silent: true,\n            position: [0, 0]\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 监听窗口大小变化\n      window.addEventListener('resize', () => {\n        if (this.chart) {\n          this.chart.resize()\n        }\n      })\n      // 启动自动高亮效果\n      this.startAutoHighlight()\n      // 添加鼠标事件监听\n      this.chart.on('mouseover', () => {\n        this.stopAutoHighlight()\n      })\n      this.chart.on('mouseout', () => {\n        this.startAutoHighlight()\n      })\n    },\n\n    // 开始自动高亮效果\n    startAutoHighlight () {\n      if (this.chartData.length === 0) return\n\n      this.autoHighlightTimer = setInterval(() => {\n        // 取消当前高亮和tooltip\n        if (this.currentHighlightIndex >= 0) {\n          this.chart.dispatchAction({\n            type: 'downplay',\n            seriesIndex: 0,\n            dataIndex: this.currentHighlightIndex\n          })\n          // 隐藏tooltip\n          this.chart.dispatchAction({\n            type: 'hideTip'\n          })\n        }\n\n        // 高亮下一个数据项\n        this.currentHighlightIndex = (this.currentHighlightIndex + 1) % this.chartData.length\n        this.chart.dispatchAction({\n          type: 'highlight',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n\n        // 显示tooltip\n        this.chart.dispatchAction({\n          type: 'showTip',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n      }, 2000) // 每2秒切换一次\n    },\n\n    // 停止自动高亮效果\n    stopAutoHighlight () {\n      if (this.autoHighlightTimer) {\n        clearInterval(this.autoHighlightTimer)\n        this.autoHighlightTimer = null\n      }\n      // 取消所有高亮\n      if (this.chart && this.currentHighlightIndex >= 0) {\n        this.chart.dispatchAction({\n          type: 'downplay',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n        this.currentHighlightIndex = -1\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pie-chart {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n</style>\n"]}]}