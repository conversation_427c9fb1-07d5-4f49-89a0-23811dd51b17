{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\InnovationNew.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\InnovationNew.vue", "mtime": 1752541693828}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAyFA;EACAA,qBADA;;EAEAC;IACA;MACAC,+DADA;MACA;MAEAC,cAHA;MAIAC,gBAJA;MAKAC;QACAC,MADA;QAEAC,SAFA;QAGAC,YAHA;QAIAC,cAJA;QAKAC,WALA;QAMAC,SANA;QAOAC,YAPA;QAQAC,cARA;QASAC,eATA;QAUAC;MAVA,CALA;MAkBAC;QAEAT,QACA;UAAAU;UAAAC;UAAAC;QAAA,CADA,CAFA;QAKAX,WACA;UAAAS;UAAAC;UAAAC;QAAA,CADA,CALA;QAQAL,cACA;UAAAG;UAAAC;UAAAC;QAAA,CADA,CARA;QAWAT,UACA;UAAAO;UAAAC;UAAAC;QAAA,CADA,CAXA;QAcAN,aACA;UAAAI;UAAAC;UAAAC;QAAA,CADA;MAdA,CAlBA;MAoCAC;IApCA;EAsCA,CAzCA;;EA0CAC,2BA1CA;;EA2CAC;IACA;IACA,6CAFA,CAEA;IACA;;IACA;IACA;IAEA;;IACA;MACA;IACA;EACA,CAtDA;;EAuDAC;IACA;AACA;AACA;IACA;MACA;MACA;QAAAtB;MAAA;MACA;IACA,CARA;;IASAuB;MACA;IACA,CAXA;;IAYA;AACA;AACA;IACA;MACA;QACAC;MADA;MAGA;QAAAxB;MAAA;MACA;IACA,CArBA;;IAsBA;IACA;MACA;MACA;QAAAA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CApCA;;IAqCA;MACA;MACA;QAAAA;MAAA;MACA;MACA;MACA;IACA,CA3CA;;IA4CAyB;MACAC;IACA,CA9CA;;IA+CAC;MACA;QACA;QACA;QACA;UACA;;UACA;YACAC;UACA;;UACA;YACAvB,WADA;YAEAC,sBAFA;YAGAC,4BAHA;YAIAC,gCAJA;YAKAC,0BALA;YAMAC,sBANA;YAOAC,4BAPA;YAQAC,gCARA;YASAC,kCATA;YAUAC;UAVA,GAYAe,IAZA,CAYAC;YACA;cAAAC;cAAAC;YAAA;;YACA;cACA;gBACAf,eADA;gBAEAgB;cAFA;cAIA;YACA;UACA,CArBA;QAsBA,CA3BA,MA2BA;UACA;YACAhB,iBADA;YAEAgB;UAFA;UAIA;QACA;MACA,CArCA;IAsCA,CAtFA;;IAuFAC;MACA;IACA;;EAzFA;AAvDA", "names": ["name", "data", "user", "officeData", "classifyData", "form", "id", "title", "officeId", "officeName", "endTime", "score", "classify", "isMainwork", "publishTime", "auditStatus", "rules", "required", "message", "trigger", "circlesStatus", "props", "mounted", "methods", "select", "types", "handleChange", "console", "submitForm", "url", "then", "res", "<PERSON><PERSON><PERSON>", "errmsg", "type", "resetForm"], "sourceRoot": "src/views/AssessmentOrgan/InnovationExcellence", "sources": ["InnovationNew.vue"], "sourcesContent": ["<template>\r\n  <div class=\"InnovationNew\">\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"demo-form\">\r\n      <el-form-item label=\"标题\"\r\n                    prop=\"title\">\r\n        <el-input v-model=\"form.title\"\r\n                  clearable>\r\n\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"发布时间\"\r\n                    prop=\"publishTime\">\r\n        <el-date-picker type=\"date\"\r\n                        placeholder=\"选择日期\"\r\n                        value-format=\"timestamp\"\r\n                        v-model=\"form.publishTime\"\r\n                        style=\"width: 100%;\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"部门\"\r\n                    prop=\"officeId\">\r\n        <zy-select v-model=\"form.officeId\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\"\r\n                   @select=\"select\"\r\n                   placeholder=\"请选择部门\">\r\n        </zy-select>\r\n        <!-- <button @click=\"demo\">11</button> -->\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"时效选择\"\r\n                    prop=\"endTime\">\r\n        <el-form-item prop=\"endTime\">\r\n          <el-date-picker type=\"date\"\r\n                          placeholder=\"选择日期\"\r\n                          value-format=\"timestamp\"\r\n                          v-model=\"form.endTime\"\r\n                          style=\"width: 100%;\"></el-date-picker>\r\n        </el-form-item>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"分值\"\r\n                    class=\"form-title\">\r\n        <el-input-number v-model=\"form.score\"\r\n                         controls-position=\"right\"\r\n                         @change=\"handleChange\"\r\n                         :min=\"0\"\r\n                         :max=\"100\"\r\n                         class=\"form-content\">\r\n        </el-input-number>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"是否重点工作\"\r\n                    prop=\"isMainwork\">\r\n        <el-radio-group v-model=\"form.isMainwork\">\r\n          <el-radio label=\"1\">是</el-radio>\r\n          <el-radio label=\"0\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"类型\"\r\n                    prop=\"classify\">\r\n        <el-select v-model=\"form.classify\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择类型\">\r\n          <el-option v-for=\"item in classifyData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'InnovationNew',\r\n  data () {\r\n    return {\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())), // 获取当前登录人信息\r\n\r\n      officeData: [],\r\n      classifyData: [],\r\n      form: {\r\n        id: '',\r\n        title: '',\r\n        officeId: '',\r\n        officeName: '',\r\n        endTime: '',\r\n        score: '',\r\n        classify: '',\r\n        isMainwork: '',\r\n        publishTime: '',\r\n        auditStatus: ''\r\n\r\n      },\r\n      rules: {\r\n\r\n        title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n        officeId: [\r\n          { required: true, message: '请选择部门', trigger: 'blur' }\r\n        ],\r\n        publishTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ],\r\n        endTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ],\r\n        isMainwork: [\r\n          { required: true, message: '请选择', trigger: 'blur' }\r\n        ]\r\n      },\r\n      circlesStatus: []\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  mounted () {\r\n    this.treeList()\r\n    this.form.publishTime = new Date().getTime() // 获取当前时间 需要默认登陆时显示当前时间时 解开\r\n    // this.form.endTime = this.$utils.tmp(false) //获取当前时间 需要默认登陆时显示当前时间时 解开\r\n    this.form.officeName = this.user.officeName\r\n    this.form.officeId = this.user.officeId\r\n\r\n    this.dictionaryPubkvs()\r\n    if (this.id) {\r\n      this.getInnovationDetails()\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n    *机构树\r\n   */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    select (row) {\r\n      this.form.officeName = row ? row.label : ''\r\n    },\r\n    /**\r\n *字典\r\n*/\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_functional_innovate'\r\n      })\r\n      var { data } = res\r\n      this.classifyData = data.evaluation_functional_innovate\r\n    },\r\n    // 获取目标详情 (作用:编辑界面内容填充)\r\n    async getInnovationDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqInnovationDetails(this.id)\r\n      var { data } = res\r\n      this.form.id = data.id\r\n      this.form.title = data.title\r\n      this.form.officeName = data.officeName\r\n      this.form.officeId = data.officeId\r\n      this.form.publishTime = data.publishTime\r\n      this.form.endTime = data.endTime\r\n      this.form.score = data.score\r\n      this.form.auditStatus = data.auditStatus\r\n      this.form.isMainwork = data.isMainwork\r\n      this.form.classify = data.classify\r\n    },\r\n    async historycirclesInfo () {\r\n      const res = await this.$api.memberInformation.historycirclesInfo(this.id)\r\n      var { data } = res\r\n      this.form.title = data.title\r\n      this.form.boutYear = data.boutYear\r\n      this.form.circlesStatus = data.circlesStatus\r\n    },\r\n    handleChange (value) {\r\n      console.log(value)\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        // this.form.publishTime = this.$format()\r\n        // console.log(this.form.publishTime)\r\n        if (valid) {\r\n          var url = '/functional/innovate/add?'\r\n          if (this.id) {\r\n            url = '/functional/innovate/edit?'\r\n          }\r\n          this.$api.AssessmentOrgan.reqAddInnovationExcellence(url, {\r\n            id: this.id,\r\n            title: this.form.title,\r\n            officeId: this.form.officeId,\r\n            officeName: this.form.officeName,\r\n            endTime: this.form.endTime,\r\n            score: this.form.score,\r\n            classify: this.form.classify,\r\n            isMainwork: this.form.isMainwork,\r\n            publishTime: this.form.publishTime,\r\n            auditStatus: this.form.auditStatus\r\n\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('newCallback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('newCallback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.InnovationNew {\r\n  width: 692px;\r\n  height: 100%;\r\n  padding: 24px 40px;\r\n  // overflow-y: scroll;\r\n  overflow-x: hidden;\r\n  overflow-y: auto;\r\n\r\n  .el-form-item__label {\r\n    text-align: right;\r\n    vertical-align: middle;\r\n    float: left;\r\n    font-size: 13px;\r\n    color: #606266;\r\n    line-height: 40px;\r\n    padding: 0 12px 0 0;\r\n    box-sizing: border-box;\r\n  }\r\n}\r\n</style>\r\n"]}]}