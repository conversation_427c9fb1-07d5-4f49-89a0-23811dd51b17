{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\DoubleQuote\\DoubleQuoteAddOrEdit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\DoubleQuote\\DoubleQuoteAddOrEdit.vue", "mtime": 1752541693820}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DoubleQuoteAddOrEdit.vue"], "names": [], "mappings": ";AAuGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "DoubleQuoteAddOrEdit.vue", "sourceRoot": "src/views/AssessmentOrgan/DoubleQuote", "sourcesContent": ["<template>\r\n  <!-- 新建/编辑 双招双引 -->\r\n  <div class=\"DoubleQuoteAddOrEdit\">\r\n    <div class=\"add-form-title\">{{ id? '编辑' : '新增'}}</div>\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"qd-form\">\r\n\r\n      <el-form-item label=\"类型\"\r\n                    class=\"form-item-wd100 leixin\"\r\n                    prop=\"doubleType\">\r\n        <el-select width=\"900\"\r\n                   v-model=\"form.doubleType\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择类型\">\r\n          <el-option v-for=\"item in doubleTypeData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <!--  -->\r\n\r\n      <!--  -->\r\n      <el-form-item label=\"标题\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"title\">\r\n        <el-input placeholder=\"请输入标题\"\r\n                  v-model=\"form.title\">\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"所属个人\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"publishUserName\">\r\n        <el-input placeholder=\"请选择所属个人\"\r\n                  :disabled=\"disabled\"\r\n                  readonly\r\n                  @focus=\"focus\"\r\n                  v-model=\"form.publishUserName\">\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"部门\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"officeId\">\r\n        <zy-select width=\"222\"\r\n                   node-key=\"id\"\r\n                   v-model=\"form.officeId\"\r\n                   :data=\"officeData\"\r\n                   @select=\"select\"\r\n                   placeholder=\"请选择部门\">\r\n        </zy-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"发布时间\"\r\n                    class=\"form-item-wd50\"\r\n                    prop=\"publishTime\">\r\n        <el-date-picker v-model=\"form.publishTime\"\r\n                        type=\"datetime\"\r\n                        value-format=\"timestamp\"\r\n                        placeholder=\"选择日期时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <br>\r\n      <el-form-item label=\"上传附件\"\r\n                    class=\"form-item-wd100 form-upload\">\r\n        <zy-upload-file ref=\"upload\"\r\n                        module=\"notice\"\r\n                        :data=\"file\"\r\n                        :max=\"10\"\r\n                        placeholder=\"仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式\"></zy-upload-file>\r\n      </el-form-item>\r\n      <el-form-item label=\"内容\"\r\n                    class=\"form-item-wd100\">\r\n        <wang-editor v-model='form.content'></wang-editor>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"form-footer-btn\">\r\n      <el-button type=\"primary\"\r\n                 size=\"small\"\r\n                 @click=\"submitForm('form')\">提交</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm1('form')\">重置</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm('form')\">取消</el-button>\r\n    </div>\r\n    <zy-pop-up v-model=\"userShow\"\r\n               title=\"选择所属个人\">\r\n      <candidates-user point=\"point_21\"\r\n                       :max=\"1\"\r\n                       :data=\"userData\"\r\n                       @userCallback=\"userCallback\"></candidates-user>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'DoubleQuoteAddOrEdit',\r\n  data () {\r\n    return {\r\n      id: this.$route.query.id,\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())),\r\n      doubleTypeData: [],\r\n      form: {\r\n        id: '',\r\n        doubleType: '',\r\n        title: '',\r\n        publishTime: '',\r\n        publishUserId: '',\r\n        publishUserName: '',\r\n        officeId: '',\r\n        officeName: '',\r\n        content: '',\r\n        // ******\r\n        attachmentId: '',\r\n        auditStatus: ''\r\n\r\n      },\r\n      rules: {\r\n        doubleType: [\r\n          { required: true, message: '请选择类型', trigger: 'blur' }\r\n        ],\r\n        title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n        publishUserName: [\r\n          { required: true, message: '请输入所属个人', trigger: 'blur' }\r\n        ],\r\n        officeId: [\r\n          { required: true, message: '请输入部门', trigger: 'blur' }\r\n        ],\r\n        publishTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ]\r\n      },\r\n      file: [],\r\n      disabled: false,\r\n      officeData: [],\r\n      userData: [],\r\n      // returnTypelist: [\r\n      //   { value: '文本', id: '文本' },\r\n      //   { value: '单选', id: '单选' },\r\n      //   { value: '多选', id: '多选' }\r\n      // ],\r\n      // time: [],\r\n      // userData: []\r\n      userShow: false\r\n    }\r\n  },\r\n  mounted () {\r\n    this.dictionaryPubkvs()\r\n    this.treeList()\r\n    this.form.publishTime = new Date().getTime()\r\n\r\n    if (this.id) {\r\n      this.getDoubleQuoteDetails()\r\n    }\r\n    this.form.officeName = this.user.officeName\r\n    this.form.officeId = this.user.officeId\r\n    this.form.publishUserId = this.user.id\r\n    this.form.publishUserName = this.user.userName\r\n    this.userData = [{ mobile: this.user.mobile, officeName: this.user.officeName, officeId: this.user.officeId, name: this.user.userName, position: this.user.position, userId: this.user.id, userName: this.user.userName }]\r\n\r\n    // TODO:测试环境暂时注释,提交时再解开*************************************************************************************\r\n    // 登录的user.otherInfo.IsEvaluationAdmin（是否考核管理员）如果等于true,则下面信息回显当前登录人的机构和用户信息，且为可编辑状态，可编辑状态下，调用选人点point_21选择人员并回显机构和人员信息，否则只回显当前登录人的机构和用户信息，不可编辑\r\n    // if (this.user.otherInfo.userOtherInfo.isEvaluationAdmin) {\r\n    //   this.disabled = false\r\n    // } else {\r\n    //   this.disabled = true\r\n    // }\r\n    // *************************************************************************************************************************\r\n  },\r\n  inject: ['tabDelJump'],\r\n  methods: {\r\n    /**\r\n     *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    select (row) {\r\n      this.form.officeName = row ? row.label : ''\r\n    },\r\n    /**\r\n     *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_same_double'\r\n      })\r\n      var { data } = res\r\n      this.doubleTypeData = data.evaluation_same_double\r\n    },\r\n    // 获取详情\r\n    async getDoubleQuoteDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqDoubleQuoteDetails(this.id)\r\n      const data = res.data\r\n      if (data.attachmentInfo) {\r\n        data.attachmentInfo.forEach(item => {\r\n          item.uid = item.id\r\n          item.fileName = item.oldName // 附件名称\r\n          this.file.push(item)\r\n        })\r\n      }\r\n      // TODO:待修改(参照新建工作目标)\r\n      const { id, title, publishTime, publishUserId, publishUserName, officeId, officeName, content, auditStatus, doubleType } = res.data\r\n\r\n      // this.file = attachment //报错原因:传入的附件attachment为对象 不能直接赋值给file数组\r\n      // for (const i in attachmentInfo) { // 方法:将对象转为数组(因为传入的附件数据为对象)\r\n      //   this.file.push(attachmentInfo[i])\r\n      // }\r\n\r\n      this.form = { id, title, publishTime, publishUserId, publishUserName, officeId, officeName, content, auditStatus, doubleType }\r\n    },\r\n    // 提交\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var data = {\r\n            id: this.form.id,\r\n            doubleType: this.form.doubleType,\r\n            title: this.form.title,\r\n            publishTime: this.form.publishTime,\r\n            publishUserId: this.form.publishUserId,\r\n            publishUserName: this.form.publishUserName,\r\n            content: this.form.content,\r\n            officeId: this.form.officeId,\r\n            officeName: this.form.officeName,\r\n\r\n            auditStatus: this.form.auditStatus,\r\n            type: '通知',\r\n\r\n            attachmentId: this.$refs.upload.obtainId().join(',') // 获取附件id\r\n          }\r\n          // data.org = JSON.parse(sessionStorage.getItem('user' + this.$logo())).officeId\r\n\r\n          const url = this.id ? '/samedouble/edit' : '/samedouble/add'\r\n          this.$api.AssessmentOrgan.reqAddDoubleQuote(url, data).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.tabDelJump()\r\n            }\r\n          })\r\n        } else {\r\n          this.$message.warning('请输入必填项')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    resetForm () {\r\n      this.$confirm('是否离开当前页面', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tabDelJump()\r\n      }).catch(() => {\r\n\r\n      })\r\n    },\r\n    // 重置按钮\r\n    resetForm1 () {\r\n      this.$confirm('是否重置当前输入的内容', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.form.content = ''\r\n        this.$refs.form.resetFields()\r\n      }).catch(() => {\r\n      })\r\n    },\r\n\r\n    focus () {\r\n      this.userShow = !this.userShow\r\n    },\r\n    // 选择用户的回调\r\n    userCallback (data, type) {\r\n      if (type) {\r\n        this.userData = data\r\n        this.form.publishUserName = data[0].name\r\n        this.form.officeName = data[0].officeName\r\n        this.form.publishUserId = data[0].userId\r\n      }\r\n      this.userShow = !this.userShow\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.DoubleQuoteAddOrEdit {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n  background-color: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);\r\n  padding: 20px 30px;\r\n\r\n  .el-select {\r\n    display: block;\r\n    width: 500px;\r\n  }\r\n\r\n  .leixin {\r\n    // width: 500px;\r\n  }\r\n\r\n  .wang-editor {\r\n    //解决内容输入过长时能自动换行\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n  }\r\n}\r\n</style>\r\n"]}]}