{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\ProgressBar.vue?vue&type=style&index=0&id=2527a83c&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\ProgressBar.vue", "mtime": 1756286988585}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5wcm9ncmVzcy1iYXItY29udGFpbmVyIHsKICB3aWR0aDogMTAwJTsKICBwYWRkaW5nOiAyMHB4OwoKICAucHJvZ3Jlc3MtaXRlbSB7CiAgICBtYXJnaW4tYm90dG9tOiAyNXB4OwoKICAgICY6bGFzdC1jaGlsZCB7CiAgICAgIG1hcmdpbi1ib3R0b206IDA7CiAgICB9CgogICAgLnByb2dyZXNzLWxhYmVsIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBtYXJnaW4tYm90dG9tOiA4cHg7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgY29sb3I6ICNmZmY7CgogICAgICAubGFiZWwtdGV4dCB7CiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsKICAgICAgfQoKICAgICAgLmxhYmVsLXZhbHVlIHsKICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgY29sb3I6ICNGRkZGRkY7CiAgICAgIH0KCiAgICAgIC5sYWJlbC1wZXJjZW50IHsKICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgY29sb3I6ICNGRkZGRkY7CiAgICAgIH0KICAgIH0KCiAgICAucHJvZ3Jlc3MtdHJhY2sgewogICAgICB3aWR0aDogMTAwJTsKICAgICAgaGVpZ2h0OiA4cHg7CiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTsKICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CgogICAgICAucHJvZ3Jlc3MtZmlsbCB7CiAgICAgICAgaGVpZ2h0OiAxMDAlOwogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgICB0cmFuc2l0aW9uOiB3aWR0aCAwLjhzIGVhc2UtaW4tb3V0OwogICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["ProgressBar.vue"], "names": [], "mappings": ";AAgCA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ProgressBar.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"progress-bar-container\">\n    <div class=\"progress-item\" v-for=\"(item, index) in progressData\" :key=\"index\">\n      <div class=\"progress-label\">\n        <!-- <span class=\"label-text\">{{ item.label }}</span> -->\n        <span class=\"label-value\">{{ item.value }}件</span>\n        <span class=\"label-percent\">占答复总件数{{ item.percent }}%</span>\n      </div>\n      <div class=\"progress-track\">\n        <div class=\"progress-fill\" :style=\"{\n          width: item.percent + '%',\n          background: getProgressGradient(item.color)\n        }\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ProgressBar',\n  props: {\n    progressData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.progress-bar-container {\n  width: 100%;\n  padding: 20px;\n\n  .progress-item {\n    margin-bottom: 25px;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .progress-label {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 8px;\n      font-size: 14px;\n      color: #fff;\n\n      .label-text {\n        font-weight: 500;\n      }\n\n      .label-value {\n        font-size: 12px;\n        color: #FFFFFF;\n      }\n\n      .label-percent {\n        font-size: 12px;\n        color: #FFFFFF;\n      }\n    }\n\n    .progress-track {\n      width: 100%;\n      height: 8px;\n      background: rgba(255, 255, 255, 0.1);\n      border-radius: 4px;\n      overflow: hidden;\n      position: relative;\n\n      .progress-fill {\n        height: 100%;\n        border-radius: 4px;\n        transition: width 0.8s ease-in-out;\n        position: relative;\n      }\n    }\n  }\n}\n</style>\n"]}]}