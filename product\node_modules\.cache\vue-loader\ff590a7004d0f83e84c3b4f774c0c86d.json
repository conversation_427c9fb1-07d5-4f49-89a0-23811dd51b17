{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\titleDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\titleDetail.vue", "mtime": 1752541693795}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["titleDetail.vue"], "names": [], "mappings": ";AA8EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "titleDetail.vue", "sourceRoot": "src/views/AssessmentOrgan/BusinessObjectives", "sourcesContent": ["<template>\r\n  <div class=\"titleDetail details\">\r\n\r\n    <div class=\"checkClass\">\r\n\r\n      <el-button type=\"primary\"\r\n                 icon=\"el-icon-circle-check\"\r\n                 v-permissions=\"'auth:business:checkPass'\"\r\n                 @click=\"passClick(2)\">审核通过\r\n      </el-button>\r\n\r\n      <el-button type=\"danger\"\r\n                 v-permissions=\"'auth:business:checkNotPass'\"\r\n                 icon=\"el-icon-remove-outline\"\r\n                 @click=\"passClick(3)\">审核不通过\r\n      </el-button>\r\n    </div>\r\n\r\n    <div class=\"details-title\">详情</div>\r\n    <div class=\"details-item-box\">\r\n      <div class=\"details-item-title\">\r\n        <div class=\"details-item-label\">标题</div>\r\n        <div class=\"details-item-value\">{{form.title}}</div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">发布时间</div>\r\n          <div class=\"details-item-value\">{{$format(form.publishTime).substr(0,16)}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">完成时间</div>\r\n          <div class=\"details-item-value\">{{$format(form.endTime).substr(0,16)}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">机构名</div>\r\n          <div class=\"details-item-value\">{{form.officeName}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">分值</div>\r\n          <div class=\"details-item-value\">{{form.score}}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">审核状态</div>\r\n          <div class=\"details-item-value\">{{auditStatusName}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">类型</div>\r\n          <div class=\"details-item-value\">{{classDetail}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">是否重点工作</div>\r\n          <div class=\"details-item-value\">{{form.isMainwork==1?'是':'否'}}</div>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"details-item\">\r\n        <div class=\"details-item-label\">附件</div>\r\n        <div class=\"details-item-value\">\r\n          <div class=\"details-item-files\"\r\n               v-for=\"(item, index) in details.attachmentList\"\r\n               :key=\"index\">\r\n            <p>{{item.fileName}}</p>\r\n            <div>\r\n              <el-button size=\"medium\"\r\n                         type=\"text\"\r\n                         @click=\"priew(item)\"> 预览 </el-button>\r\n              <el-button size=\"medium\"\r\n                         type=\"text\"\r\n                         @click=\"fileClick(item)\"> 下载 </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  props: ['id', 'uid'],\r\n  data () {\r\n    return {\r\n      form: {\r\n        id: '',\r\n        title: '',\r\n        officeName: '',\r\n        publishTime: '',\r\n        endTime: '',\r\n        score: '',\r\n        auditStatus: '',\r\n        isMainwork: '',\r\n        classify: ''\r\n      },\r\n      details: {},\r\n      classifyData: []\r\n    }\r\n  },\r\n  inject: ['openoffice', 'openPdf'],\r\n  mounted () {\r\n    // this.form.overTime = this.$format()\r\n    this.dictionaryPubkvs()\r\n    this.getBusinessObjectiveDetails()\r\n  },\r\n  computed: {\r\n    auditStatusName () {\r\n      if (this.form.auditStatus === '1') {\r\n        return '待审核'\r\n      } else if (this.form.auditStatus === '2') {\r\n        return '审核通过'\r\n      } else {\r\n        return '审核不通过'\r\n      }\r\n    },\r\n    classDetail () {\r\n      if (typeof this.classifyData[this.form.classify - 1] === 'object') { return this.classifyData[this.form.classify - 1].value }\r\n      return ''\r\n    }\r\n  },\r\n  methods: {\r\n    passClick (auditStatus) {\r\n      this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.getCheckWork(this.id, auditStatus)\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消操作'\r\n        })\r\n      })\r\n    },\r\n    // 审核 (加分项)\r\n    async getCheckWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckWork({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n        this.getBusinessObjectiveDetails()\r\n      }\r\n    },\r\n    /**\r\n      *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_functional_work'\r\n      })\r\n      var { data } = res\r\n      this.classifyData = data.evaluation_functional_work\r\n    },\r\n    // 获取 标题详情\r\n    async getBusinessObjectiveDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqBusinessObjectiveDetails(this.id)\r\n      const { id, title, officeName, publishTime, endTime, score, auditStatus, isMainwork, classify } = res.data\r\n\r\n      this.form = { id, title, officeName, publishTime, endTime, score, auditStatus, isMainwork, classify }\r\n      // console.log('@@@查看是否能收到审核状态auditStatus', this.form)\r\n    }\r\n    // 附件\r\n    // fileClick (data) {\r\n    //   this.$api.proposal.downloadFile({ id: data.id }, data.fileName)\r\n    // },\r\n    // priew (data) {\r\n    //   console.log(data)\r\n    //   if (data.fileType === 'pdf') {\r\n    //     this.openPdf(data.filePath)\r\n    //     return\r\n    //   }\r\n    //   const arr = ['doc', 'docx', 'xlsx']\r\n    //   if (arr.includes(data.fileType)) {\r\n    //     this.openoffice(data.filePath)\r\n    //   }\r\n    // }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.titleDetail {\r\n  width: 1000px;\r\n  height: 100%;\r\n  padding: 0 24px;\r\n  padding-bottom: 24px;\r\n\r\n  .checkClass {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding-right: 10px;\r\n  }\r\n\r\n  .details-item-content {\r\n    width: 100%;\r\n    padding: 24px;\r\n\r\n    p {\r\n      line-height: 36px;\r\n    }\r\n  }\r\n\r\n  .details-item-img {\r\n    img {\r\n      width: calc(100% - 24px);\r\n    }\r\n  }\r\n\r\n  .details-item-files {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-right: 20px;\r\n  }\r\n}\r\n</style>\r\n"]}]}