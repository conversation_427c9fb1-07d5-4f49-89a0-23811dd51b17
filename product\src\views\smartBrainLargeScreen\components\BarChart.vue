<template>
  <div :id="id" class="discussion-chart"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'DiscussionChart',
  props: {
    id: {
      type: String,
      required: true
    },
    chartData: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  data () {
    return {
      chart: null
    }
  },
  mounted () {
    this.initChart()
  },
  beforeDestroy () {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.resizeChart)
  },
  watch: {
    chartData: {
      handler () {
        this.renderChart()
      },
      deep: true
    }
  },
  methods: {
    initChart () {
      const chartContainer = document.getElementById(this.id)
      if (!chartContainer) return
      this.chart = echarts.init(chartContainer)
      this.renderChart()
      window.addEventListener('resize', this.resizeChart)
    },
    getOption () {
      const xAxisData = this.chartData.map(item => item.name)
      const seriesData = this.chartData.map(item => item.value)
      return {
        legend: {
          show: this.id === 'committee_proposal' ? true : null,
          data: ['提交件数'],
          top: '2%',
          left: 'center',
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          itemWidth: 12,
          itemHeight: 8
        },
        grid: {
          left: this.id === 'committee_proposal' ? '0%' : '3%',
          right: this.id === 'committee_proposal' ? '0%' : '3%',
          bottom: this.id === 'committee_proposal' ? '20%' : '8%',
          top: this.id === 'committee_proposal' ? '10%' : '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            color: '#A2B0B8',
            fontSize: this.id === 'committee_proposal' ? 12 : 14,
            interval: 0,
            rotate: 0,
            margin: 8,
            formatter: this.id === 'committee_proposal' ? this.formatAxisLabel : null
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 4,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            color: '#A2B0B8',
            fontSize: 14
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              type: 'dashed'
            }
          }
        },
        series: this.id === 'committee_proposal' ? [
          {
            type: 'bar',
            name: '提交件数',
            data: seriesData,
            barWidth: 25,
            showBackground: true, // 显示背景
            backgroundStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              borderRadius: [2, 2, 0, 0]
            },
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(31, 198, 255, 1)' },
                  { offset: 1, color: 'rgba(31, 198, 255, 0)' }
                ]
              },
              borderRadius: [2, 2, 0, 0]
            }
          }
        ] : [
          {
            type: 'bar',
            data: seriesData,
            barWidth: 20,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(31, 198, 255, 1)' },
                  { offset: 1, color: 'rgba(31, 198, 255, 0)' }
                ]
              },
              borderRadius: [2, 2, 0, 0]
            },
            emphasis: {
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: '#00E4FF' },
                    { offset: 0.5, color: '#0090FF' },
                    { offset: 1, color: '#005090' }
                  ]
                }
              }
            },
            label: {
              show: false,
              position: 'top',
              color: '#00D4FF',
              fontSize: 12,
              fontWeight: 'bold'
            }
          }
        ],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00D4FF',
          borderWidth: 1,
          textStyle: {
            color: '#FFFFFF'
          },
          formatter: function (params) {
            const data = params[0]
            return `${data.name}<br/>人数: ${data.value}人`
          }
        }
      }
    },
    renderChart () {
      if (!this.chart || !this.chartData.length) return
      this.chart.setOption(this.getOption(), true)
    },
    resizeChart () {
      if (this.chart) {
        this.chart.resize()
      }
    },
    formatAxisLabel (value) {
      // 为committee_proposal处理文本换行
      if (this.id === 'committee_proposal') {
        // 根据文本长度进行换行处理
        if (value.length > 4) {
          // 如果包含"委"字，在"委"字后换行
          if (value.includes('委') && value.indexOf('委') < value.length - 1) {
            return value.replace('委', '委\n')
          }
          // 否则在中间位置换行
          const mid = Math.ceil(value.length / 2)
          return value.substring(0, mid) + '\n' + value.substring(mid)
        }
      }
      return value
    }
  }
}
</script>

<style lang="scss" scoped>
.discussion-chart {
  width: 100%;
  height: 100%;
}
</style>
