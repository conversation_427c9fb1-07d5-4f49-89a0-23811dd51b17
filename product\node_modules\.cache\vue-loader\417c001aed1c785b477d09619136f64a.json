{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\proposalStatistics\\proposalStatisticsBox.vue?vue&type=template&id=778a9eeb&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\proposalStatistics\\proposalStatisticsBox.vue", "mtime": 1756344694009}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}