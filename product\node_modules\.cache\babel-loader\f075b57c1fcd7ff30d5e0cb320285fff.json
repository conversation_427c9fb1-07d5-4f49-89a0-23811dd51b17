{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\FinishDetailPop.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\FinishDetailPop.vue", "mtime": 1752541693823}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAwFA;EACAA,oBADA;;EAEAC;IACA;MACAC;QACAC,SADA;QAEAC,YAFA;QAGAC,cAHA;QAIAC,WAJA;QAKA;QACA;QACA;QACAC;MARA,CADA;MAYAC;QACAC;MADA;IAZA;EAgBA,CAnBA;;EAoBAC,iCApBA;;EAqBAC;IACA;IACA;EACA,CAxBA;;EAyBAC;IACAC;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA,CAFA,MAEA;QACA;MACA;IACA;;EATA,CAzBA;EAoCAC;IACAC;MACA;QACAC,uBADA;QAEAC,sBAFA;QAGAC;MAHA,GAIAC,IAJA,CAIA;QACA;MACA,CANA,EAMAC,KANA,CAMA;QACA;UACAF,YADA;UAEAG;QAFA;MAIA,CAXA;IAYA,CAdA;;IAeA;IACA;MACA;QAAAC;QAAAb;MAAA;MACA;QAAAc;QAAAC;MAAA;;MACA;QACA;UACAH,eADA;UAEAH;QAFA;QAIA;MACA;IACA,CA1BA;;IA2BA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;MACA;MACA;QAAAjB;MAAA;MACA;IACA,CAjDA;;IAkDAwB;MACA;QAAAC;MAAA,iBADA,CACA;IACA,CApDA;;IAqDAC;MACA;QACA;QACA;MACA;;MACA;;MACA;QACA;MACA;IACA;;EA9DA;AApCA", "names": ["props", "data", "form", "title", "overTime", "officeName", "content", "attachmentId", "details", "auditStatus", "inject", "mounted", "computed", "auditStatusName", "methods", "passClick", "confirmButtonText", "cancelButtonText", "type", "then", "catch", "message", "ids", "<PERSON><PERSON><PERSON>", "errmsg", "fileClick", "id", "priew"], "sourceRoot": "src/views/AssessmentOrgan/InnovationExcellence", "sources": ["FinishDetailPop.vue"], "sourcesContent": ["<template>\r\n  <div class=\"FinishDetailPop details\">\r\n\r\n    <div class=\"checkClass\">\r\n\r\n      <el-button type=\"primary\"\r\n                 icon=\"el-icon-circle-check\"\r\n                 v-permissions=\"'auth:innovation:checkPass'\"\r\n                 @click=\"passClick(2)\">审核通过\r\n      </el-button>\r\n\r\n      <el-button type=\"danger\"\r\n                 v-permissions=\"'auth:innovation:checkNotPass'\"\r\n                 icon=\"el-icon-remove-outline\"\r\n                 @click=\"passClick(3)\">审核不通过\r\n      </el-button>\r\n    </div>\r\n\r\n    <div class=\"details-title\">完成情况详情</div>\r\n    <div class=\"details-item-box\">\r\n      <div class=\"details-item-title\">\r\n        <div class=\"details-item-label\">标题</div>\r\n        <div class=\"details-item-value\">{{details.title}}</div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">完成时间</div>\r\n          <div class=\"details-item-value\">{{$format(details.overTime).substr(0,16)}}</div>\r\n        </div>\r\n        <!-- <div class=\"details-item\">\r\n          <div class=\"details-item-label\">发布部门</div>\r\n          <div class=\"details-item-value\">{{details.orgName}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">是否置顶</div>\r\n          <div class=\"details-item-value\">{{details.isTop?'是':'否'}}</div>\r\n        </div> -->\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">审核状态</div>\r\n          <div class=\"details-item-value\">{{auditStatusName}}</div>\r\n        </div>\r\n        <!-- <div class=\"details-item\">\r\n          <div class=\"details-item-label\">类型</div>\r\n          <div class=\"details-item-value\">{{details.type}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">是否App显示</div>\r\n          <div class=\"details-item-value\">{{details.isAppShow?'是':'否'}}</div>\r\n        </div> -->\r\n      </div>\r\n      <!-- 以下附件内容未修改 -->\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">附件</div>\r\n\r\n        <div class=\"details-item-value\"\r\n             v-if=\"details.attachmentInfo\">\r\n          <div class=\"details-item-files\"\r\n               v-for=\"(item,index) in details.attachmentInfo \"\r\n               :key=\"index\">\r\n            <p>{{item.oldName}}</p>\r\n            <div>\r\n\r\n              <!-- 预览修改 -->\r\n              <el-button size=\"medium\"\r\n                         type=\"text\">\r\n                <a :href=\"item.fullPath\"\r\n                   target=\"_blank\">预览</a>\r\n              </el-button>\r\n\r\n              <el-button size=\"medium\"\r\n                         type=\"text\"\r\n                         @click=\"fileClick(item)\"> 下载\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-content\"\r\n             v-html=\"details.content\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  props: ['id', 'uid'],\r\n  data () {\r\n    return {\r\n      form: {\r\n        title: '',\r\n        overTime: '',\r\n        officeName: '',\r\n        content: '',\r\n        // evaluationId: this.$route.query.mid,\r\n        // id: this.$route.query.id,\r\n        // ******\r\n        attachmentId: ''\r\n\r\n      },\r\n      details: {\r\n        auditStatus: ''\r\n      }\r\n    }\r\n  },\r\n  inject: ['openoffice', 'openPdf'],\r\n  mounted () {\r\n    this.form.overTime = this.$format()\r\n    this.getFinishDetail()\r\n  },\r\n  computed: {\r\n    auditStatusName () {\r\n      if (this.details.auditStatus === '1') {\r\n        return '待审核'\r\n      } else if (this.details.auditStatus === '2') {\r\n        return '审核通过'\r\n      } else {\r\n        return '审核不通过'\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    passClick (auditStatus) {\r\n      this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.getCheckWork(this.uid, auditStatus)\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消操作'\r\n        })\r\n      })\r\n    },\r\n    // 审核 (加分项)\r\n    async getCheckWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckFinishDetail({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n        this.getFinishDetail()\r\n      }\r\n    },\r\n    // 获取 完成情况详情\r\n    // async getFinishDetail () {\r\n    //   const res = await this.$api.AssessmentOrgan.reqfinishDetail(this.uid)\r\n    //   const { id, evaluationId, title, overTime, auditStatus, content, attchmentId } = res.data\r\n    //   // this.file = attchmentId //报错原因:传入的附件attachment为对象 不能直接赋值给file数组\r\n    //   for (const i in attchmentId) { // 方法:将对象转为数组(因为传入的附件数据为对象)\r\n    //     this.file.push(attchmentId[i])\r\n    //   }\r\n\r\n    //   this.form = { title, overTime, evaluationId, id, content, auditStatus }\r\n    //   // console.log('@@@查看是否能收到审核状态auditStatus', this.form)\r\n    // },\r\n\r\n    // async noticeinfo () {\r\n    //   const res = await this.$api.notificationAnnouncement.noticeinfo(this.rowId)\r\n    //   var { data } = res\r\n    //   this.details = data\r\n    // },\r\n    async getFinishDetail () {\r\n      const res = await this.$api.AssessmentOrgan.reqfinishDetail(this.uid)\r\n      var { data } = res\r\n      this.details = data\r\n    },\r\n    fileClick (data) {\r\n      this.$api.proposal.downloadFile({ id: data.id }, data.oldName) // 附件文件名显示\r\n    },\r\n    priew (data) {\r\n      if (data.fileType === 'pdf') {\r\n        this.openPdf(data.filePath)\r\n        return\r\n      }\r\n      const arr = ['doc', 'docx', 'xlsx']\r\n      if (arr.includes(data.fileType)) {\r\n        this.openoffice(data.filePath)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.FinishDetailPop {\r\n  width: 900px;\r\n  height: 100%;\r\n  padding: 0 24px;\r\n  padding-bottom: 24px;\r\n\r\n  .checkClass {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding-right: 45px;\r\n  }\r\n\r\n  .details-item-content {\r\n    word-break: break-all;\r\n    width: 100%;\r\n    padding: 24px;\r\n\r\n    p {\r\n      line-height: 36px;\r\n    }\r\n  }\r\n\r\n  .details-item-img {\r\n    img {\r\n      width: calc(100% - 24px);\r\n    }\r\n  }\r\n\r\n  .details-item-files {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-right: 20px;\r\n  }\r\n}\r\n</style>\r\n"]}]}