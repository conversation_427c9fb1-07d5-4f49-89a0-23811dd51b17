{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue?vue&type=style&index=0&id=d2faf212&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue", "mtime": 1756349639780}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5yYW5raW5nLWJhci1jaGFydCB7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwp9Cg=="}, {"version": 3, "sources": ["RankingBarChart.vue"], "names": [], "mappings": ";AAuOA;AACA;AACA;AACA", "file": "RankingBarChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"ranking-bar-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'RankingBar<PERSON><PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    showValues: {\n      type: Boolean,\n      default: true\n    },\n    maxValue: {\n      type: Number,\n      default: null\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.renderChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n    getOption () {\n      const xAxisData = this.chartData.map(item => item.name)\n      const seriesData = this.chartData.map(item => item.value)\n      // 计算柱子宽度\n      // const dom = 300\n      const barWidth = 20\n      // 生成渐变色数组\n      const colors = []\n      for (let i = 0; i < this.chartData.length; i++) {\n        colors.push({\n          type: 'linear',\n          x: 0,\n          x2: 1,\n          y: 0,\n          y2: 0,\n          colorStops: [\n            {\n              offset: 0,\n              color: '#006BBC' // 最左边\n            }, {\n              offset: 0.5,\n              color: 'rgba(31,198,255,0.2)' // 左边的右边 颜色\n            }, {\n              offset: 0.5,\n              color: '#006BBC ' // 右边的左边 颜色\n            }, {\n              offset: 1,\n              color: '#3dc8ca'\n            }\n          ]\n        })\n      }\n\n      return {\n        // 提示框\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00D4FF',\n          borderWidth: 1,\n          axisPointer: {\n            type: 'shadow'\n          },\n          textStyle: {\n            color: '#FFFFFF'\n          },\n          formatter: function (params) {\n            const data = params[0]\n            return `${data.name}<br/>人数: ${data.value}人`\n          }\n        },\n        // 区域位置\n        grid: {\n          left: '0%',\n          right: '0%',\n          bottom: '0%',\n          top: '5%',\n          containLabel: true\n        },\n        // X轴\n        xAxis: {\n          data: xAxisData,\n          type: 'category',\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.2)'\n            }\n          },\n          splitLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12,\n            interval: 0,\n            rotate: 35,\n            margin: 8,\n            formatter: (value) => {\n              return value\n            }\n          }\n        },\n        // y轴\n        yAxis: {\n          type: 'value',\n          show: true,\n          splitNumber: 5,\n          axisLine: {\n            show: false\n          },\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              type: 'dashed'\n            }\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12\n          }\n        },\n        series: [\n          {\n            type: 'bar',\n            barWidth: barWidth,\n            itemStyle: {\n              normal: {\n                color: function (params) {\n                  return colors[params.dataIndex % colors.length]\n                }\n              }\n            },\n            label: {\n              show: true,\n              position: 'top',\n              color: '#FFFFFF',\n              fontSize: 12,\n              align: 'center'\n            },\n            data: seriesData\n          },\n          {\n            z: 2,\n            type: 'pictorialBar',\n            data: seriesData,\n            symbol: 'diamond',\n            symbolOffset: [0, '50%'],\n            symbolSize: [barWidth, barWidth * 0.5],\n            itemStyle: {\n              normal: {\n                color: function (params) {\n                  return colors[params.dataIndex % colors.length]\n                }\n              }\n            }\n          },\n          {\n            z: 3,\n            type: 'pictorialBar',\n            symbolPosition: 'end',\n            data: seriesData,\n            symbol: 'diamond',\n            symbolOffset: [0, '-50%'],\n            symbolSize: [barWidth, barWidth * 0.5],\n            itemStyle: {\n              normal: {\n                borderWidth: 0,\n                color: function (params) {\n                  return colors[params.dataIndex % colors.length].colorStops[0].color\n                }\n              }\n            }\n          }\n        ]\n      }\n    },\n    renderChart () {\n      if (!this.chart || !this.chartData.length) return\n      this.chart.setOption(this.getOption(), true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.ranking-bar-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}