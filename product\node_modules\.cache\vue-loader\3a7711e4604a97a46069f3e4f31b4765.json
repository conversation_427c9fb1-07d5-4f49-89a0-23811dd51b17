{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\WordCloud.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\WordCloud.vue", "mtime": 1756282591803}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["WordCloud.vue"], "names": [], "mappings": ";AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "WordCloud.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div :id=\"chartId\" class=\"word-cloud-container\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport 'echarts-wordcloud'\nexport default {\n  name: 'WordCloud',\n  props: {\n    chartId: {\n      type: String,\n      default: 'wordCloud'\n    },\n    words: {\n      type: Array,\n      required: true,\n      default: () => []\n      // 期望格式: [{ text: '经济建设', weight: 10 }, { text: '人才培养', weight: 8 }, ...]\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      // 预定义颜色数组，模拟图片中的颜色效果\n      colors: [\n        '#4FC3F7', // 浅蓝色\n        '#26C6DA', // 青色\n        '#66BB6A', // 绿色\n        '#FFA726', // 橙色\n        '#FF7043', // 橙红色\n        '#AB47BC', // 紫色\n        '#5C6BC0', // 蓝紫色\n        '#42A5F5', // 蓝色\n        '#FFCA28', // 黄色\n        '#4CAF50', // 绿色\n        '#EF5350', // 红色\n        '#A1E2FF', // 浅蓝色\n        '#00BCD4', // 青蓝色\n        '#FF9800', // 深橙色\n        '#9C27B0' // 深紫色\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.chartId)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.$nextTick(() => {\n        this.updateChart()\n      })\n      // 监听窗口大小变化\n      window.addEventListener('resize', this.resizeChart)\n      // 添加点击事件\n      this.chart.on('click', (params) => {\n        this.$emit('word-click', {\n          text: params.data[3], // 散点图数据格式 [x, y, value, name]\n          weight: params.data[2]\n        })\n      })\n    },\n    updateChart () {\n      if (!this.chart) return\n      // 按照你提供的样式配置词云\n      const option = {\n        backgroundColor: 'transparent',\n        tooltip: {\n          show: true,\n          position: 'top',\n          textStyle: {\n            fontSize: 16\n          }\n        },\n        series: [{\n          type: 'wordCloud',\n          // 网格大小，各项之间间距\n          gridSize: 20,\n          sizeRange: [20, 40],\n          size: 0.6,\n          rotationRange: [0, 0],\n          drawOutOfBound: false,\n          // 位置相关设置\n          left: 'center',\n          top: 'center',\n          right: null,\n          bottom: null,\n          width: '100%',\n          height: '100%',\n          textStyle: {\n            normal: {\n              color: function () {\n                return 'rgb(' + [\n                  Math.round(Math.random() * 200 + 55),\n                  Math.round(Math.random() * 200 + 55),\n                  Math.round(Math.random() * 200 + 55)\n                ].join(',') + ')'\n              }\n            },\n            emphasis: {\n              shadowBlur: 10,\n              shadowColor: '#2ac'\n            }\n          },\n          data: this.words\n        }]\n      }\n      this.chart.setOption(option, true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.word-cloud-container {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}