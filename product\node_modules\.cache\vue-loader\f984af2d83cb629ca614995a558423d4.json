{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\titleDetail.vue?vue&type=style&index=0&id=7938f0ee&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\titleDetail.vue", "mtime": 1752541693795}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoudGl0bGVEZXRhaWwgew0KICB3aWR0aDogMTAwMHB4Ow0KICBoZWlnaHQ6IDEwMCU7DQogIHBhZGRpbmc6IDAgMjRweDsNCiAgcGFkZGluZy1ib3R0b206IDI0cHg7DQoNCiAgLmNoZWNrQ2xhc3Mgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCiAgICBwYWRkaW5nLXJpZ2h0OiAxMHB4Ow0KICB9DQoNCiAgLmRldGFpbHMtaXRlbS1jb250ZW50IHsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBwYWRkaW5nOiAyNHB4Ow0KDQogICAgcCB7DQogICAgICBsaW5lLWhlaWdodDogMzZweDsNCiAgICB9DQogIH0NCg0KICAuZGV0YWlscy1pdGVtLWltZyB7DQogICAgaW1nIHsNCiAgICAgIHdpZHRoOiBjYWxjKDEwMCUgLSAyNHB4KTsNCiAgICB9DQogIH0NCg0KICAuZGV0YWlscy1pdGVtLWZpbGVzIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIHBhZGRpbmctcmlnaHQ6IDIwcHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["titleDetail.vue"], "names": [], "mappings": ";AAsLA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "titleDetail.vue", "sourceRoot": "src/views/AssessmentOrgan/BusinessObjectives", "sourcesContent": ["<template>\r\n  <div class=\"titleDetail details\">\r\n\r\n    <div class=\"checkClass\">\r\n\r\n      <el-button type=\"primary\"\r\n                 icon=\"el-icon-circle-check\"\r\n                 v-permissions=\"'auth:business:checkPass'\"\r\n                 @click=\"passClick(2)\">审核通过\r\n      </el-button>\r\n\r\n      <el-button type=\"danger\"\r\n                 v-permissions=\"'auth:business:checkNotPass'\"\r\n                 icon=\"el-icon-remove-outline\"\r\n                 @click=\"passClick(3)\">审核不通过\r\n      </el-button>\r\n    </div>\r\n\r\n    <div class=\"details-title\">详情</div>\r\n    <div class=\"details-item-box\">\r\n      <div class=\"details-item-title\">\r\n        <div class=\"details-item-label\">标题</div>\r\n        <div class=\"details-item-value\">{{form.title}}</div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">发布时间</div>\r\n          <div class=\"details-item-value\">{{$format(form.publishTime).substr(0,16)}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">完成时间</div>\r\n          <div class=\"details-item-value\">{{$format(form.endTime).substr(0,16)}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">机构名</div>\r\n          <div class=\"details-item-value\">{{form.officeName}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">分值</div>\r\n          <div class=\"details-item-value\">{{form.score}}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">审核状态</div>\r\n          <div class=\"details-item-value\">{{auditStatusName}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">类型</div>\r\n          <div class=\"details-item-value\">{{classDetail}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">是否重点工作</div>\r\n          <div class=\"details-item-value\">{{form.isMainwork==1?'是':'否'}}</div>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"details-item\">\r\n        <div class=\"details-item-label\">附件</div>\r\n        <div class=\"details-item-value\">\r\n          <div class=\"details-item-files\"\r\n               v-for=\"(item, index) in details.attachmentList\"\r\n               :key=\"index\">\r\n            <p>{{item.fileName}}</p>\r\n            <div>\r\n              <el-button size=\"medium\"\r\n                         type=\"text\"\r\n                         @click=\"priew(item)\"> 预览 </el-button>\r\n              <el-button size=\"medium\"\r\n                         type=\"text\"\r\n                         @click=\"fileClick(item)\"> 下载 </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  props: ['id', 'uid'],\r\n  data () {\r\n    return {\r\n      form: {\r\n        id: '',\r\n        title: '',\r\n        officeName: '',\r\n        publishTime: '',\r\n        endTime: '',\r\n        score: '',\r\n        auditStatus: '',\r\n        isMainwork: '',\r\n        classify: ''\r\n      },\r\n      details: {},\r\n      classifyData: []\r\n    }\r\n  },\r\n  inject: ['openoffice', 'openPdf'],\r\n  mounted () {\r\n    // this.form.overTime = this.$format()\r\n    this.dictionaryPubkvs()\r\n    this.getBusinessObjectiveDetails()\r\n  },\r\n  computed: {\r\n    auditStatusName () {\r\n      if (this.form.auditStatus === '1') {\r\n        return '待审核'\r\n      } else if (this.form.auditStatus === '2') {\r\n        return '审核通过'\r\n      } else {\r\n        return '审核不通过'\r\n      }\r\n    },\r\n    classDetail () {\r\n      if (typeof this.classifyData[this.form.classify - 1] === 'object') { return this.classifyData[this.form.classify - 1].value }\r\n      return ''\r\n    }\r\n  },\r\n  methods: {\r\n    passClick (auditStatus) {\r\n      this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.getCheckWork(this.id, auditStatus)\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消操作'\r\n        })\r\n      })\r\n    },\r\n    // 审核 (加分项)\r\n    async getCheckWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckWork({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n        this.getBusinessObjectiveDetails()\r\n      }\r\n    },\r\n    /**\r\n      *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_functional_work'\r\n      })\r\n      var { data } = res\r\n      this.classifyData = data.evaluation_functional_work\r\n    },\r\n    // 获取 标题详情\r\n    async getBusinessObjectiveDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqBusinessObjectiveDetails(this.id)\r\n      const { id, title, officeName, publishTime, endTime, score, auditStatus, isMainwork, classify } = res.data\r\n\r\n      this.form = { id, title, officeName, publishTime, endTime, score, auditStatus, isMainwork, classify }\r\n      // console.log('@@@查看是否能收到审核状态auditStatus', this.form)\r\n    }\r\n    // 附件\r\n    // fileClick (data) {\r\n    //   this.$api.proposal.downloadFile({ id: data.id }, data.fileName)\r\n    // },\r\n    // priew (data) {\r\n    //   console.log(data)\r\n    //   if (data.fileType === 'pdf') {\r\n    //     this.openPdf(data.filePath)\r\n    //     return\r\n    //   }\r\n    //   const arr = ['doc', 'docx', 'xlsx']\r\n    //   if (arr.includes(data.fileType)) {\r\n    //     this.openoffice(data.filePath)\r\n    //   }\r\n    // }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.titleDetail {\r\n  width: 1000px;\r\n  height: 100%;\r\n  padding: 0 24px;\r\n  padding-bottom: 24px;\r\n\r\n  .checkClass {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding-right: 10px;\r\n  }\r\n\r\n  .details-item-content {\r\n    width: 100%;\r\n    padding: 24px;\r\n\r\n    p {\r\n      line-height: 36px;\r\n    }\r\n  }\r\n\r\n  .details-item-img {\r\n    img {\r\n      width: calc(100% - 24px);\r\n    }\r\n  }\r\n\r\n  .details-item-files {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-right: 20px;\r\n  }\r\n}\r\n</style>\r\n"]}]}