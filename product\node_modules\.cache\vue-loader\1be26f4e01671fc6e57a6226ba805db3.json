{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\CompilationColumn\\CompilationColumnNew\\CompilationColumnNew.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\CompilationColumn\\CompilationColumnNew\\CompilationColumnNew.vue", "mtime": 1752541697662}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CompilationColumnNew.vue"], "names": [], "mappings": ";AAuDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CompilationColumnNew.vue", "sourceRoot": "src/views/wisdomWarehouse/CompilationColumn/CompilationColumnNew", "sourcesContent": ["<template>\r\n  <div class=\"CompilationColumnNew\">\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-position=\"top\"\r\n             class=\"newForm\">\r\n      <el-form-item label=\"栏目名称\"\r\n                    class=\"form-input\"\r\n                    prop=\"name\">\r\n        <el-input placeholder=\"请输入栏目名称\"\r\n                  v-model=\"form.name\"\r\n                  clearable>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"所属年份\" props=\"year\">\r\n            <el-date-picker\r\n      v-model=\"form.year\"\r\n      type=\"year\"\r\n      placeholder=\"选择年\">\r\n    </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"排序\"\r\n                    class=\"form-input\"\r\n                    prop=\"sort\">\r\n        <el-input-number placeholder=\"请输入排序\"\r\n                         :min=\"1\"\r\n                         style=\"width:296px;\"\r\n                         v-model=\"form.sort\"\r\n                         clearable></el-input-number>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否置顶\"\r\n                    class=\"form-input\">\r\n        <el-radio-group v-model=\"form.isTop\">\r\n          <el-radio label=\"1\">置顶</el-radio>\r\n          <el-radio label=\"0\">不置顶</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否APP显示\"\r\n                    class=\"form-input\">\r\n        <el-radio-group v-model=\"form.isPushApp\">\r\n          <el-radio label=\"1\">是</el-radio>\r\n          <el-radio label=\"0\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <div class=\"form-button\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'CompilationColumnNew',\r\n  data () {\r\n    return {\r\n      menu: [],\r\n      form: {\r\n        name: '',\r\n        superior: '',\r\n        sort: 0,\r\n        isTop: '1',\r\n        isPushApp: '1',\r\n        year: ''\r\n      },\r\n      rules: {\r\n        name: [\r\n          { required: true, message: '请输入栏目名称', trigger: 'blur' }\r\n        ],\r\n        sort: [\r\n          { required: true, message: '请输入排序', trigger: 'blur' }\r\n        ],\r\n        year: [\r\n          { required: true, message: '请输入排序', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  props: ['id', 'modules'],\r\n  mounted () {\r\n    this.informationColumnTree()\r\n    if (this.id) {\r\n      this.informationColumnInfo()\r\n    }\r\n  },\r\n  methods: {\r\n    async informationColumnTree () {\r\n      // const res = await this.$api.wisdomWarehouse.informationColumnTree({ module: this.modules })\r\n      // var { data } = res\r\n      // this.menu = data\r\n    },\r\n    async informationColumnInfo () {\r\n      const res = await this.$api.wisdomWarehouse.assemblycolumnInfo(this.id)\r\n      var { data: { parentId, sort, name, isTop, isApp, year } } = res\r\n      this.form.superior = parentId\r\n      this.form.name = name\r\n      this.form.sort = sort\r\n      this.form.isTop = isTop\r\n      this.form.isPushApp = isApp\r\n      this.form.year = year\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.superior === '') {\r\n            this.form.superior = 1\r\n          }\r\n          var url = '/assemblycolumn/add'\r\n          if (this.id) {\r\n            url = '/assemblycolumn/edit'\r\n          }\r\n          this.$api.wisdomWarehouse.assemblycolumn(url, {\r\n            id: this.id,\r\n            name: this.form.name,\r\n            sort: this.form.sort,\r\n            isTop: this.form.isTop,\r\n            year: new Date(this.form.year).getFullYear(),\r\n            isApp: this.form.isPushApp\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('newCallback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('newCallback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./CompilationColumnNew.scss\";\r\n</style>\r\n"]}]}