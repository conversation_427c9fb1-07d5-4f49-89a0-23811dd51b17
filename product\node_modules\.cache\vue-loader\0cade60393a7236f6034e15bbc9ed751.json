{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\UEditors\\UEditor.vue?vue&type=style&index=0&id=32937a98&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\UEditors\\UEditor.vue", "mtime": 1752541693432}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouaGVsbG8gew0KICB3aWR0aDogMTAwJTsNCg0KICBoMSwNCiAgaDIgew0KICAgIGZvbnQtd2VpZ2h0OiBub3JtYWw7DQogIH0NCg0KICB1bCB7DQogICAgbGlzdC1zdHlsZS10eXBlOiBub25lOw0KICAgIHBhZGRpbmc6IDA7DQogIH0NCg0KICBsaSB7DQogICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgIG1hcmdpbjogMCAxMHB4Ow0KICB9DQoNCiAgYSB7DQogICAgY29sb3I6ICM0MmI5ODM7DQogIH0NCg0KICAuaGludCB7DQogICAgY29sb3I6IHJlZDsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgfQ0KDQogIC5oaW50PmltZyB7DQogICAgd2lkdGg6IDIwcHg7DQogICAgaGVpZ2h0OiAyMHB4Ow0KICAgIGJvcmRlcjogMXB4IHNvbGlkIHJlZDsNCiAgICBtYXJnaW46IDAgNXB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["UEditor.vue"], "names": [], "mappings": ";AAwLA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "UEditor.vue", "sourceRoot": "src/components/UEditors", "sourcesContent": ["<template>\r\n  <div class=\"hello\">\r\n    <vue-ueditor-wrap v-model=\"model\"\r\n                      @ready=\"ready\"\r\n                      :config=\"myConfig\"\r\n                      ref=\"editors\"></vue-ueditor-wrap>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport VueUeditorWrap from 'vue-ueditor-wrap'\r\nexport default {\r\n  name: 'UEditors',\r\n  components: {\r\n    VueUeditorWrap// eslint-disable-line\r\n  },\r\n  data () {\r\n    return {\r\n      myConfig: {\r\n        // 是否跟随内容撑开\r\n        autoHeightEnabled: false,\r\n        elementPathEnabled: false,\r\n        wordCount: true,\r\n        pasteplain: true, // 纯文本模式\r\n        // 高度\r\n        initialFrameHeight: 280,\r\n        // 宽度\r\n        initialFrameWidth: '100%',\r\n        // 图片上传的路径\r\n        serverUrl: `${this.$api.general.baseURL()}/ueditor/exec`,\r\n        // serverUrl: `http://*************/ueditor/exec`,\r\n        // 资源依赖的路径\r\n        UEDITOR_HOME_URL: './UEditor/',\r\n        toolbars: [\r\n          // ['undo', 'redo', 'bold', 'italic', 'underline'], // 第一行工具栏按钮\r\n          // ['justifyleft', 'justifycenter', 'justifyright', 'justifyjustify'], // 第二行工具栏按钮\r\n          // ['insertunorderedlist', 'insertorderedlist', 'blockquote'] // 第三行工具栏按钮\r\n          // ['link', 'unlink', 'insertimage'], // 第四行工具栏按钮\r\n          // ['inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts'],\r\n          // ['indent', 'autotypeset'] // /第五行工具栏按钮\r\n          ['autotypeset'] // 第五行工具栏按钮\r\n        ],\r\n        // 鼠标右键的功能，但是一般不用编辑器带的功能，因为编辑器自带的不能右键复制，所以这个变量设置为空数组\r\n        contextMenu: [],\r\n        lineheight: [1, 1.25, 1.5, 1.75, 2, 2.25, 2.5, 2.75, 3, 3.25, 3.5, 3.75, 4],\r\n        autotypeset: {\r\n          mergeEmptyline: true, // 合并空行\r\n          removeClass: true, // 去掉冗余的class\r\n          removeEmptyline: true, // 去掉空行\r\n          textAlign: 'left', // 段落的排版方式，可以是 left,right,center,justify 去掉这个属性表示不执行排版\r\n          imageBlockLine: 'center', // 图片的浮动方式，独占一行剧中,左右浮动，默认: center,left,right,none 去掉这个属性表示不执行排版\r\n          pasteFilter: true, // 根据规则过滤没事粘贴进来的内容\r\n          clearFontSize: true, // 去掉所有的内嵌字号，使用编辑器默认的字号\r\n          clearFontFamily: true, // 去掉所有的内嵌字体，使用编辑器默认的字体\r\n          removeEmptyNode: true, // 去掉空节点\r\n          // 可以去掉的标签\r\n          removeTagNames: { 标签名字: 1 },\r\n          indent: true, // 行首缩进\r\n          indentValue: '2em', // 行首缩进的大小\r\n          symbolConver: 'tobdc',\r\n          bdc2sb: false,\r\n          tobdc: true\r\n          // ignoreChars: /[\\uFF10-\\uFF19]/g\r\n        },\r\n        maximumWords: 10000,\r\n        zIndex: 999,\r\n        fontfamily: [\r\n          { label: '', name: '宋体, SimSun', val: '宋体, SimSun' },\r\n          { label: '', name: '微软雅黑, Microsoft YaHei', val: '微软雅黑, Microsoft YaHei' },\r\n          { label: '', name: '黑体, SimHei', val: '黑体, SimHei' }\r\n          // 添加其他字体选项\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    model (newValue) {\r\n      this.checkAndRemoveSpan(newValue)\r\n      this.handleContent()\r\n      // setTimeout(() => {\r\n      //   this.formatContent(this.$refs.editor.editor)\r\n      // }, 200)\r\n    }\r\n  },\r\n  computed: {\r\n    model: {\r\n      get () {\r\n        return this.value\r\n      },\r\n      set (value) {\r\n        this.$emit('input', value)\r\n      }\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String\r\n    },\r\n    maximumWords: {\r\n      type: Number\r\n    }\r\n    // toolbars: {}\r\n  },\r\n  created () {\r\n    if (this.maximumWords) {\r\n      this.myConfig.maximumWords = this.maximumWords\r\n    }\r\n  },\r\n  methods: {\r\n    formatContent (editor) {\r\n      console.log('触发')\r\n      // editor.execCommand('removeFormat')\r\n      editor.body.innerHTML = editor.body.innerHTML.replace(/\\s*style=\"[^\"]*\"/g, ' ')\r\n      // 调用UEditor的命令进行自动排版\r\n      editor.execCommand('autotypeset', {\r\n        mergeEmptyline: true, // 合并空行\r\n        removeClass: true, // 去掉冗余的class\r\n        removeEmptyline: true, // 去掉空行\r\n        textAlign: 'left', // 段落的排版方式，可以是 left,right,center,justify 去掉这个属性表示不执行排版\r\n        imageBlockLine: 'center', // 图片的浮动方式，独占一行剧中,左右浮动，默认: center,left,right,none 去掉这个属性表示不执行排版\r\n        pasteFilter: true, // 根据规则过滤没事粘贴进来的内容\r\n        clearFontSize: true, // 去掉所有的内嵌字号，使用编辑器默认的字号\r\n        clearFontFamily: true, // 去掉所有的内嵌字体，使用编辑器默认的字体\r\n        removeEmptyNode: true, // 去掉空节点\r\n        // 可以去掉的标签\r\n        removeTagNames: { 标签名字: 1 },\r\n        indent: true, // 行首缩进\r\n        indentValue: '2em', // 行首缩进的大小\r\n        symbolConver: 'tobdc',\r\n        bdc2sb: false,\r\n        tobdc: true\r\n      })\r\n    },\r\n    handleContent () {\r\n      // 处理编辑器内容\r\n      // 在自动排版后执行的操作\r\n      // console.log('文本已自动排版')\r\n      var _this = this\r\n      var processedContent = this.model.replace(/[\\uff21-\\uff3a\\uff41-\\uff5a\\uff10-\\uff19／％．]/g, function (char) {\r\n        // 忽略数字、冒号、逗号和句号的转换\r\n        return String.fromCharCode(char.charCodeAt(0) - 65248)\r\n      })\r\n      // 将处理后的内容设置回编辑器\r\n      // setTimeout(() => {\r\n      _this.model = processedContent\r\n      // }, 200)\r\n      // console.log(processedContent)\r\n      // console.log('变化')\r\n    },\r\n    checkAndRemoveSpan (content) {\r\n      const hasSpan = /<span\\b[^>]*>/i.test(content)\r\n      if (hasSpan) {\r\n        const newContent = content.replace(/<span\\b[^>]*>/gi, '<span>')\r\n        this.model = newContent\r\n      }\r\n    },\r\n    blur () {\r\n      this.$emit('blur')\r\n      // this.handleContent()\r\n    },\r\n    ready (editor) {\r\n      setTimeout(() => {\r\n        this.formatContent(editor)\r\n      }, 500)\r\n      console.log(editor)\r\n      // 监听粘贴事件\r\n      editor.addListener('afterpaste', () => {\r\n        this.formatContent(editor)\r\n      })\r\n      editor.addListener('blur', this.blur)\r\n      editor.addListener('handleContent', this.handleContent)\r\n      // console.log(editor.getContent())\r\n      editor.ready(() => {\r\n        editor.execCommand('fontfamily', '宋体') // 字体\r\n        editor.execCommand('lineheight', 2) // 行间距\r\n        editor.execCommand('fontsize', '26px') // 字号\r\n        editor.execCommand('forecolor', '#262626') // 字体颜色\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.hello {\r\n  width: 100%;\r\n\r\n  h1,\r\n  h2 {\r\n    font-weight: normal;\r\n  }\r\n\r\n  ul {\r\n    list-style-type: none;\r\n    padding: 0;\r\n  }\r\n\r\n  li {\r\n    display: inline-block;\r\n    margin: 0 10px;\r\n  }\r\n\r\n  a {\r\n    color: #42b983;\r\n  }\r\n\r\n  .hint {\r\n    color: red;\r\n    margin-bottom: 10px;\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .hint>img {\r\n    width: 20px;\r\n    height: 20px;\r\n    border: 1px solid red;\r\n    margin: 0 5px;\r\n  }\r\n}\r\n</style>\r\n"]}]}