{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\UEditors\\UEditor.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\UEditors\\UEditor.vue", "mtime": 1752541693432}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAUA;AACA;EACAA,gBADA;EAEAC;IACAC,cADA,CACA;;EADA,CAFA;;EAKAC;IACA;MACAC;QACA;QACAC,wBAFA;QAGAC,yBAHA;QAIAC,eAJA;QAKAC,gBALA;QAKA;QACA;QACAC,uBAPA;QAQA;QACAC,yBATA;QAUA;QACAC,wDAXA;QAYA;QACA;QACAC,8BAdA;QAeAC,WACA;QACA;QACA;QACA;QACA;QACA;QACA,eAPA,CAOA;QAPA,CAfA;QAwBA;QACAC,eAzBA;QA0BAC,2EA1BA;QA2BAC;UACAC,oBADA;UACA;UACAC,iBAFA;UAEA;UACAC,qBAHA;UAGA;UACAC,iBAJA;UAIA;UACAC,wBALA;UAKA;UACAC,iBANA;UAMA;UACAC,mBAPA;UAOA;UACAC,qBARA;UAQA;UACAC,qBATA;UASA;UACA;UACAC;YAAAC;UAAA,CAXA;UAYAC,YAZA;UAYA;UACAC,kBAbA;UAaA;UACAC,qBAdA;UAeAC,aAfA;UAgBAC,WAhBA,CAiBA;;QAjBA,CA3BA;QA8CAC,mBA9CA;QA+CAC,WA/CA;QAgDAC,aACA;UAAAC;UAAApC;UAAAqC;QAAA,CADA,EAEA;UAAAD;UAAApC;UAAAqC;QAAA,CAFA,EAGA;UAAAD;UAAApC;UAAAqC;QAAA,CAHA,CAIA;QAJA;MAhDA;IADA;EAyDA,CA/DA;;EAgEAC;IACAC;MACA;MACA,qBAFA,CAGA;MACA;MACA;IACA;;EAPA,CAhEA;EAyEAC;IACAD;MACAE;QACA;MACA,CAHA;;MAIAC;QACA;MACA;;IANA;EADA,CAzEA;EAmFAC;IACAC;MACAC;IADA,CADA;IAIAZ;MACAY;IADA,CAJA,CAOA;;EAPA,CAnFA;;EA4FAC;IACA;MACA;IACA;EACA,CAhGA;;EAiGAC;IACAC;MACAC,kBADA,CAEA;;MACAC,gFAHA,CAIA;;MACAA;QACAjC,oBADA;QACA;QACAC,iBAFA;QAEA;QACAC,qBAHA;QAGA;QACAC,iBAJA;QAIA;QACAC,wBALA;QAKA;QACAC,iBANA;QAMA;QACAC,mBAPA;QAOA;QACAC,qBARA;QAQA;QACAC,qBATA;QASA;QACA;QACAC;UAAAC;QAAA,CAXA;QAYAC,YAZA;QAYA;QACAC,kBAbA;QAaA;QACAC,qBAdA;QAeAC,aAfA;QAgBAC;MAhBA;IAkBA,CAxBA;;IAyBAmB;MACA;MACA;MACA;MACA;;MACA;QACA;QACA;MACA,CAHA,EALA,CASA;MACA;;MACAC,+BAXA,CAYA;MACA;MACA;IACA,CAxCA;;IAyCAC;MACA;;MACA;QACA;QACA;MACA;IACA,CA/CA;;IAgDAC;MACA,mBADA,CAEA;IACA,CAnDA;;IAoDAC;MACAC;QACA;MACA,CAFA,EAEA,GAFA;MAGAP,oBAJA,CAKA;;MACAC;QACA;MACA,CAFA;MAGAA;MACAA,wDAVA,CAWA;;MACAA;QACAA,uCADA,CACA;;QACAA,oCAFA,CAEA;;QACAA,uCAHA,CAGA;;QACAA,2CAJA,CAIA;MACA,CALA;IAMA;;EAtEA;AAjGA", "names": ["name", "components", "VueUeditorWrap", "data", "myConfig", "autoHeightEnabled", "elementPathEnabled", "wordCount", "pasteplain", "initialFrameHeight", "initialFrame<PERSON><PERSON>th", "serverUrl", "UEDITOR_HOME_URL", "toolbars", "contextMenu", "lineheight", "autotypeset", "mergeEmptyline", "removeClass", "removeEmptyline", "textAlign", "imageBlockLine", "pasteFilter", "clearFontSize", "clearFontFamily", "removeEmptyNode", "removeTagNames", "标签名字", "indent", "indentValue", "symbolConver", "bdc2sb", "tobdc", "maximumWords", "zIndex", "fontfamily", "label", "val", "watch", "model", "computed", "get", "set", "props", "value", "type", "created", "methods", "formatContent", "console", "editor", "handleContent", "_this", "checkAndRemoveSpan", "blur", "ready", "setTimeout"], "sourceRoot": "src/components/UEditors", "sources": ["UEditor.vue"], "sourcesContent": ["<template>\r\n  <div class=\"hello\">\r\n    <vue-ueditor-wrap v-model=\"model\"\r\n                      @ready=\"ready\"\r\n                      :config=\"myConfig\"\r\n                      ref=\"editors\"></vue-ueditor-wrap>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport VueUeditorWrap from 'vue-ueditor-wrap'\r\nexport default {\r\n  name: 'UEditors',\r\n  components: {\r\n    VueUeditorWrap// eslint-disable-line\r\n  },\r\n  data () {\r\n    return {\r\n      myConfig: {\r\n        // 是否跟随内容撑开\r\n        autoHeightEnabled: false,\r\n        elementPathEnabled: false,\r\n        wordCount: true,\r\n        pasteplain: true, // 纯文本模式\r\n        // 高度\r\n        initialFrameHeight: 280,\r\n        // 宽度\r\n        initialFrameWidth: '100%',\r\n        // 图片上传的路径\r\n        serverUrl: `${this.$api.general.baseURL()}/ueditor/exec`,\r\n        // serverUrl: `http://*************/ueditor/exec`,\r\n        // 资源依赖的路径\r\n        UEDITOR_HOME_URL: './UEditor/',\r\n        toolbars: [\r\n          // ['undo', 'redo', 'bold', 'italic', 'underline'], // 第一行工具栏按钮\r\n          // ['justifyleft', 'justifycenter', 'justifyright', 'justifyjustify'], // 第二行工具栏按钮\r\n          // ['insertunorderedlist', 'insertorderedlist', 'blockquote'] // 第三行工具栏按钮\r\n          // ['link', 'unlink', 'insertimage'], // 第四行工具栏按钮\r\n          // ['inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts'],\r\n          // ['indent', 'autotypeset'] // /第五行工具栏按钮\r\n          ['autotypeset'] // 第五行工具栏按钮\r\n        ],\r\n        // 鼠标右键的功能，但是一般不用编辑器带的功能，因为编辑器自带的不能右键复制，所以这个变量设置为空数组\r\n        contextMenu: [],\r\n        lineheight: [1, 1.25, 1.5, 1.75, 2, 2.25, 2.5, 2.75, 3, 3.25, 3.5, 3.75, 4],\r\n        autotypeset: {\r\n          mergeEmptyline: true, // 合并空行\r\n          removeClass: true, // 去掉冗余的class\r\n          removeEmptyline: true, // 去掉空行\r\n          textAlign: 'left', // 段落的排版方式，可以是 left,right,center,justify 去掉这个属性表示不执行排版\r\n          imageBlockLine: 'center', // 图片的浮动方式，独占一行剧中,左右浮动，默认: center,left,right,none 去掉这个属性表示不执行排版\r\n          pasteFilter: true, // 根据规则过滤没事粘贴进来的内容\r\n          clearFontSize: true, // 去掉所有的内嵌字号，使用编辑器默认的字号\r\n          clearFontFamily: true, // 去掉所有的内嵌字体，使用编辑器默认的字体\r\n          removeEmptyNode: true, // 去掉空节点\r\n          // 可以去掉的标签\r\n          removeTagNames: { 标签名字: 1 },\r\n          indent: true, // 行首缩进\r\n          indentValue: '2em', // 行首缩进的大小\r\n          symbolConver: 'tobdc',\r\n          bdc2sb: false,\r\n          tobdc: true\r\n          // ignoreChars: /[\\uFF10-\\uFF19]/g\r\n        },\r\n        maximumWords: 10000,\r\n        zIndex: 999,\r\n        fontfamily: [\r\n          { label: '', name: '宋体, SimSun', val: '宋体, SimSun' },\r\n          { label: '', name: '微软雅黑, Microsoft YaHei', val: '微软雅黑, Microsoft YaHei' },\r\n          { label: '', name: '黑体, SimHei', val: '黑体, SimHei' }\r\n          // 添加其他字体选项\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    model (newValue) {\r\n      this.checkAndRemoveSpan(newValue)\r\n      this.handleContent()\r\n      // setTimeout(() => {\r\n      //   this.formatContent(this.$refs.editor.editor)\r\n      // }, 200)\r\n    }\r\n  },\r\n  computed: {\r\n    model: {\r\n      get () {\r\n        return this.value\r\n      },\r\n      set (value) {\r\n        this.$emit('input', value)\r\n      }\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String\r\n    },\r\n    maximumWords: {\r\n      type: Number\r\n    }\r\n    // toolbars: {}\r\n  },\r\n  created () {\r\n    if (this.maximumWords) {\r\n      this.myConfig.maximumWords = this.maximumWords\r\n    }\r\n  },\r\n  methods: {\r\n    formatContent (editor) {\r\n      console.log('触发')\r\n      // editor.execCommand('removeFormat')\r\n      editor.body.innerHTML = editor.body.innerHTML.replace(/\\s*style=\"[^\"]*\"/g, ' ')\r\n      // 调用UEditor的命令进行自动排版\r\n      editor.execCommand('autotypeset', {\r\n        mergeEmptyline: true, // 合并空行\r\n        removeClass: true, // 去掉冗余的class\r\n        removeEmptyline: true, // 去掉空行\r\n        textAlign: 'left', // 段落的排版方式，可以是 left,right,center,justify 去掉这个属性表示不执行排版\r\n        imageBlockLine: 'center', // 图片的浮动方式，独占一行剧中,左右浮动，默认: center,left,right,none 去掉这个属性表示不执行排版\r\n        pasteFilter: true, // 根据规则过滤没事粘贴进来的内容\r\n        clearFontSize: true, // 去掉所有的内嵌字号，使用编辑器默认的字号\r\n        clearFontFamily: true, // 去掉所有的内嵌字体，使用编辑器默认的字体\r\n        removeEmptyNode: true, // 去掉空节点\r\n        // 可以去掉的标签\r\n        removeTagNames: { 标签名字: 1 },\r\n        indent: true, // 行首缩进\r\n        indentValue: '2em', // 行首缩进的大小\r\n        symbolConver: 'tobdc',\r\n        bdc2sb: false,\r\n        tobdc: true\r\n      })\r\n    },\r\n    handleContent () {\r\n      // 处理编辑器内容\r\n      // 在自动排版后执行的操作\r\n      // console.log('文本已自动排版')\r\n      var _this = this\r\n      var processedContent = this.model.replace(/[\\uff21-\\uff3a\\uff41-\\uff5a\\uff10-\\uff19／％．]/g, function (char) {\r\n        // 忽略数字、冒号、逗号和句号的转换\r\n        return String.fromCharCode(char.charCodeAt(0) - 65248)\r\n      })\r\n      // 将处理后的内容设置回编辑器\r\n      // setTimeout(() => {\r\n      _this.model = processedContent\r\n      // }, 200)\r\n      // console.log(processedContent)\r\n      // console.log('变化')\r\n    },\r\n    checkAndRemoveSpan (content) {\r\n      const hasSpan = /<span\\b[^>]*>/i.test(content)\r\n      if (hasSpan) {\r\n        const newContent = content.replace(/<span\\b[^>]*>/gi, '<span>')\r\n        this.model = newContent\r\n      }\r\n    },\r\n    blur () {\r\n      this.$emit('blur')\r\n      // this.handleContent()\r\n    },\r\n    ready (editor) {\r\n      setTimeout(() => {\r\n        this.formatContent(editor)\r\n      }, 500)\r\n      console.log(editor)\r\n      // 监听粘贴事件\r\n      editor.addListener('afterpaste', () => {\r\n        this.formatContent(editor)\r\n      })\r\n      editor.addListener('blur', this.blur)\r\n      editor.addListener('handleContent', this.handleContent)\r\n      // console.log(editor.getContent())\r\n      editor.ready(() => {\r\n        editor.execCommand('fontfamily', '宋体') // 字体\r\n        editor.execCommand('lineheight', 2) // 行间距\r\n        editor.execCommand('fontsize', '26px') // 字号\r\n        editor.execCommand('forecolor', '#262626') // 字体颜色\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.hello {\r\n  width: 100%;\r\n\r\n  h1,\r\n  h2 {\r\n    font-weight: normal;\r\n  }\r\n\r\n  ul {\r\n    list-style-type: none;\r\n    padding: 0;\r\n  }\r\n\r\n  li {\r\n    display: inline-block;\r\n    margin: 0 10px;\r\n  }\r\n\r\n  a {\r\n    color: #42b983;\r\n  }\r\n\r\n  .hint {\r\n    color: red;\r\n    margin-bottom: 10px;\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .hint>img {\r\n    width: 20px;\r\n    height: 20px;\r\n    border: 1px solid red;\r\n    margin: 0 5px;\r\n  }\r\n}\r\n</style>\r\n"]}]}