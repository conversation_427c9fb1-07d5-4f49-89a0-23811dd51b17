{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\SinceSituation\\SinceDetail.vue?vue&type=style&index=0&id=f58ad472&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\SinceSituation\\SinceDetail.vue", "mtime": 1752541697669}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SinceDetail.vue"], "names": [], "mappings": ";AAuHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SinceDetail.vue", "sourceRoot": "src/views/wisdomWarehouse/SinceSituation", "sourcesContent": ["<template>\r\n  <div class=\"sincedetail\">\r\n    <div class=\"sincedetail_head\"> 青岛政协委员履职表 </div>\r\n\r\n    <div class=\"sinceTable\">\r\n      <div class=\"userName\">\r\n        <div class=\"userName_item\">\r\n          <div class=\"userName_label\"> 用户名 </div>\r\n          <div class=\"userName_value\">{{destails.userName }}</div>\r\n        </div>\r\n        <div class=\"userName_item\">\r\n          <div class=\"userName_label\">年份</div>\r\n          <div class=\"userName_value\">\r\n            <el-date-picker\r\n              v-model=\"currYear\"\r\n              type=\"year\"\r\n              value-format=\"yyyy\"\r\n              placeholder=\"选择年份\"\r\n            >\r\n            </el-date-picker>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"sinceClounm \">\r\n        <div class=\"sinceItem\"> 履职项 </div>\r\n        <div class=\"sinceNum\"> 次数 </div>\r\n        <div class=\"sinceList\"> 详情 </div>\r\n      </div>\r\n\r\n      <div\r\n        class=\"sinTable\"\r\n        v-for=\"(item , index) in ListHead\"\r\n        :key=\"index\"\r\n        v-show=\"item.fieldName != 'userName'\"\r\n      >\r\n        <div\r\n          class=\"TableItem\"\r\n          v-if=\"item.children.length \"\r\n        >\r\n          <div class=\"itemLabelHas \"> {{item.label}} </div>\r\n          <div class=\"childItemBox\">\r\n            <div\r\n              class=\"childItem\"\r\n              v-for=\"(child , ind) in item.children\"\r\n              :key=\"ind\"\r\n            >\r\n              <div class=\"childItemLable\"> {{child.label}} </div>\r\n              <div class=\"childItemNum\">{{( destails[child.detailListName] || []).length }} </div>\r\n              <div class=\"childItemList\">\r\n                <div\r\n                  v-for=\" (news , i) in destails[child.detailListName] \"\r\n                  :key=\"i\"\r\n                > {{i +1 }} . {{news.name}} </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div\r\n          v-else\r\n          class=\"TableItem\"\r\n        >\r\n          <div class=\"itemLabel\"> {{item.label}} </div>\r\n          <div class=\"itemNum\"> {{ (destails[item.detailListName]|| []).length  }}</div>\r\n          <div class=\"itemList\">\r\n            <div\r\n              v-for=\" (news , i) in destails[item.detailListName] \"\r\n              :key=\"i\"\r\n            > {{i +1 }} . {{news.name}} </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport sinceData from './sinceDataT'\r\nexport default {\r\n  mixins: [sinceData],\r\n  name: 'SinceDetail',\r\n  data () {\r\n    return {\r\n      headerlist: [],\r\n      destails: {},\r\n      currYear: ''\r\n    }\r\n  },\r\n  props: {\r\n    rowId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    year: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  watch: {\r\n    currYear () {\r\n      this.getDutyDetail()\r\n    }\r\n  },\r\n  created () {\r\n    this.currYear = this.year || this.$format('', 'YYYY')\r\n    this.getDutyDetail()\r\n  },\r\n  computed: {\r\n  },\r\n  methods: {\r\n    async getDutyDetail () {\r\n      const res = await this.$api.sinceManagement.generateDutyDetail({ userId: this.rowId, year: this.currYear || this.$format('', 'YYYY') })\r\n      var { data } = res\r\n      this.destails = data\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" >\r\n.sincedetail {\r\n    width: 1000px;\r\n    height: 100%;\r\n    padding: 24px;\r\n    .sincedetail_head {\r\n        text-align: center;\r\n        font-weight: 700;\r\n        font-size: 24px;\r\n        margin-bottom: 50px;\r\n    }\r\n    .sinceTable {\r\n        border: 1px solid #e6e6e6;\r\n    }\r\n    .userName {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        line-height: 45px;\r\n        .userName_item {\r\n            display: flex;\r\n            flex: 1;\r\n            .userName_label {\r\n                text-align: center;\r\n                width: 120px;\r\n                background: #f8faff;\r\n                border-right: 1px solid #e6e6e6;\r\n            }\r\n            .userName_value {\r\n                flex: 1;\r\n                padding-left: 20px;\r\n                input {\r\n                    border: none;\r\n                }\r\n            }\r\n        }\r\n        .userName_item + .userName_item {\r\n            border-left: 1px solid #e6e6e6;\r\n        }\r\n    }\r\n    .sinceClounm {\r\n        display: flex;\r\n        min-height: 45px;\r\n        line-height: 40px;\r\n        text-align: center;\r\n        border-top: 1px solid #e6e6e6;\r\n        background: #f8faff;\r\n\r\n        .sinceItem {\r\n            width: 340px;\r\n        }\r\n        .sinceNum {\r\n            width: 100px;\r\n            border-left: 1px solid #e6e6e6;\r\n            border-right: 1px solid #e6e6e6;\r\n            box-sizing: border-box;\r\n        }\r\n        .sinceList {\r\n            flex: 1;\r\n        }\r\n    }\r\n\r\n    .sinTable {\r\n        background: #f8faff;\r\n        .TableItem {\r\n            display: flex;\r\n            line-height: 45px;\r\n            min-height: 45px;\r\n            text-align: center;\r\n            align-items: center;\r\n            border-top: 1px solid #e6e6e6;\r\n            width: 100%;\r\n            .itemLabel {\r\n                width: 340px;\r\n            }\r\n            .itemNum {\r\n                width: 100px;\r\n                border-left: 1px solid #e6e6e6;\r\n                border-right: 1px solid #e6e6e6;\r\n                box-sizing: border-box;\r\n                background: #fff;\r\n            }\r\n            .itemList {\r\n                flex: 1;\r\n                text-align: left;\r\n                padding-left: 20px;\r\n                background: #fff;\r\n                min-height: 45px;\r\n            }\r\n            .itemLabelHas {\r\n                width: 170px;\r\n            }\r\n            .childItemBox {\r\n                border-left: 1px solid #e6e6e6;\r\n                flex: 1;\r\n\r\n                .childItem {\r\n                    display: flex;\r\n\r\n                    .childItemLable {\r\n                        width: 170px;\r\n                        display: flex;\r\n                        align-items: center;\r\n                        justify-content: center;\r\n                        padding: 0 10px;\r\n                        box-sizing: border-box;\r\n                    }\r\n                    .childItemNum {\r\n                        width: 100px;\r\n                        border-left: 1px solid #e6e6e6;\r\n                        border-right: 1px solid #e6e6e6;\r\n                        box-sizing: border-box;\r\n                        display: flex;\r\n                        align-items: center;\r\n                        justify-content: center;\r\n                        background: #fff;\r\n                    }\r\n                    .childItemList {\r\n                        flex: 1;\r\n                        text-align: left;\r\n                        padding-left: 20px;\r\n                        background: #fff;\r\n                    }\r\n                }\r\n                .childItem + .childItem {\r\n                    border-top: 1px solid #e6e6e6;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}