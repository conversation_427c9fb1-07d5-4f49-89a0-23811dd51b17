{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\candidates-box\\candidates-box.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\candidates-box\\candidates-box.vue", "mtime": 1752541693438}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdDYW5kaWRhdGVzQm94JywNCiAgZGF0YSAoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHVzZXJTaG93OiBmYWxzZSwNCiAgICAgIHVzZXJEYXRhOiB0aGlzLmRhdGENCiAgICB9DQogIH0sDQogIHByb3BzOiB7DQogICAgcG9pbnQ6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfSwNCiAgICBwbGFjZWhvbGRlcjogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJ+ivt+mAieaLqeeUqOaItycNCiAgICB9LA0KICAgIG1heDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogMTAwMDANCiAgICB9LA0KICAgIGJlZm9yZUNsb3NlOiBGdW5jdGlvbiwNCiAgICBkYXRhOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfSwNCiAgICBkaXNhYmxlZDogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBkYXRhICgpIHsNCiAgICAgIHRoaXMudXNlckRhdGEgPSB0aGlzLmRhdGENCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICB1c2VyQ2xpY2sgKCkgew0KICAgICAgaWYgKHR5cGVvZiB0aGlzLmJlZm9yZUNsb3NlID09PSAnZnVuY3Rpb24nKSB7DQogICAgICAgIHRoaXMuYmVmb3JlQ2xvc2UodGhpcy5TaHV0KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5TaHV0KCkNCiAgICAgIH0NCiAgICB9LA0KICAgIFNodXQgKCkgew0KICAgICAgdGhpcy51c2VyU2hvdyA9ICF0aGlzLnVzZXJTaG93DQogICAgfSwNCiAgICAvLyDnp7vpmaR0YWcNCiAgICByZW1vdmUgKGRhdGEpIHsNCiAgICAgIHZhciB1c2VyRGF0YSA9IHRoaXMudXNlckRhdGENCiAgICAgIHRoaXMudXNlckRhdGEgPSB1c2VyRGF0YS5maWx0ZXIoaXRlbSA9PiBpdGVtLnVzZXJJZCAhPT0gZGF0YS51c2VySWQpDQogICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6ZGF0YScsIHRoaXMudXNlckRhdGEpDQogICAgfSwNCiAgICAvKioNCiAgICAgKiDpgInmi6nnlKjmiLfnmoTlm57osIMNCiAgICAqLw0KICAgIHVzZXJDYWxsYmFjayAoZGF0YSwgdHlwZSkgew0KICAgICAgaWYgKHR5cGUpIHsNCiAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOmRhdGEnLCBkYXRhKQ0KICAgICAgfQ0KICAgICAgdGhpcy51c2VyU2hvdyA9ICF0aGlzLnVzZXJTaG93DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["candidates-box.vue"], "names": [], "mappings": ";AAyBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "candidates-box.vue", "sourceRoot": "src/components/candidates-box", "sourcesContent": ["<template>\r\n  <div class=\"candidates-box\"\r\n       @click=\"userClick\">\r\n    <el-scrollbar class=\"candidates--user-box\">\r\n      <div v-if=\"!userData.length\"\r\n           class=\"form-user-box-text\">{{placeholder}}</div>\r\n      <el-tag v-for=\"tag in userData\"\r\n              :key=\"tag.userId\"\r\n              size=\"medium\"\r\n              closable\r\n              :disable-transitions=\"false\"\r\n              @close.stop=\"remove(tag)\">\r\n        {{tag.name}}\r\n      </el-tag>\r\n    </el-scrollbar>\r\n    <zy-pop-up v-model=\"userShow\"\r\n               :title=\"placeholder\">\r\n      <candidates-user :point=\"point\"\r\n                       :disabled=\"disabled\"\r\n                       :data=\"userData\"\r\n                       @userCallback=\"userCallback\"></candidates-user>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'CandidatesBox',\r\n  data () {\r\n    return {\r\n      userShow: false,\r\n      userData: this.data\r\n    }\r\n  },\r\n  props: {\r\n    point: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '请选择用户'\r\n    },\r\n    max: {\r\n      type: Number,\r\n      default: 10000\r\n    },\r\n    beforeClose: Function,\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    disabled: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  watch: {\r\n    data () {\r\n      this.userData = this.data\r\n    }\r\n  },\r\n  methods: {\r\n    userClick () {\r\n      if (typeof this.beforeClose === 'function') {\r\n        this.beforeClose(this.Shut)\r\n      } else {\r\n        this.Shut()\r\n      }\r\n    },\r\n    Shut () {\r\n      this.userShow = !this.userShow\r\n    },\r\n    // 移除tag\r\n    remove (data) {\r\n      var userData = this.userData\r\n      this.userData = userData.filter(item => item.userId !== data.userId)\r\n      this.$emit('update:data', this.userData)\r\n    },\r\n    /**\r\n     * 选择用户的回调\r\n    */\r\n    userCallback (data, type) {\r\n      if (type) {\r\n        this.$emit('update:data', data)\r\n      }\r\n      this.userShow = !this.userShow\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./candidates-box.scss\";\r\n</style>\r\n"]}]}