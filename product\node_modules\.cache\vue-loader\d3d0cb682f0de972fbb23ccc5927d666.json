{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\QuarterlyReview\\QuarterlyReview.vue?vue&type=style&index=0&id=f67781ae&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\QuarterlyReview\\QuarterlyReview.vue", "mtime": 1752541693862}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouUXVhcnRlcmx5UmV2aWV3IHsNCiAgaGVpZ2h0OiAxMDAlOw0KICB3aWR0aDogMTAwJTsNCg0KICAubXJpZ2h0IHsNCiAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQogIH0NCg0KICAudGFibGVEYXRhIHsNCiAgICAuZWwtc2Nyb2xsYmFyX193cmFwIC5lbC1zY3JvbGxiYXJfX3ZpZXcgdGggew0KICAgICAgYmFja2dyb3VuZDogI2Y1ZjdmYjsNCiAgICB9DQogICAgaGVpZ2h0OiBjYWxjKDEwMCUgLSAxMDZweCk7DQogIH0NCiAgLmJ1dHRvbi1ib3ggew0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgbWFyZ2luLXRvcDogMjBweDsNCiAgfQ0KICAuYnV0dG9uLWJveCAuZWwtYnV0dG9uIHsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICB9DQogIC5lbC1idXR0b24tLWRhbmdlci5pcy1wbGFpbjpob3ZlciwNCiAgLmVsLWJ1dHRvbi0tZGFuZ2VyLmlzLXBsYWluOmZvY3VzIHsNCiAgICAvLyBjb2xvcjogI2Y1NmM2YzsNCiAgICBiYWNrZ3JvdW5kOiAjZjU2YzZjOw0KICAgIGNvbG9yOiAjZmZmOw0KICB9DQogIC5lbC1idXR0b24tLXN1Y2Nlc3MuaXMtcGxhaW46aG92ZXIsDQogIC5lbC1idXR0b24tLXN1Y2Nlc3MuaXMtcGxhaW46Zm9jdXMgew0KICAgIGJhY2tncm91bmQ6ICM2N2MyM2E7DQogICAgY29sb3I6ICNmZmY7DQogIH0NCiAgLmRlbEJ0biB7DQogICAgY29sb3I6ICNmNTZjNmM7DQogIH0NCiAgLnBhZ2luZ19ib3ggew0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogIH0NCiAgLnNjb3JlRWRpdCB7DQogICAgd2lkdGg6IDcwMHB4Ow0KICAgIGhlaWdodDogMTAwJTsNCiAgICBwYWRkaW5nOiAyNHB4Ow0KICAgIC5mb3JtLWJ1dHRvbiB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgfQ0KICB9DQogIC5zZWFyY2gtYm94IHsNCiAgICAvL3RpdGxl5paH5a2X5qC35byPDQogICAgLnNlYXJjaC10aXRsZSB7DQogICAgICB3aWR0aDogMTE1cHg7DQogICAgICBoZWlnaHQ6IDE2cHg7DQogICAgICBmb250LXNpemU6ICR0ZXh0U2l6ZTE2Ow0KICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nIFNDOw0KICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgIGxpbmUtaGVpZ2h0OiAzNnB4Ow0KICAgICAgbWFyZ2luLWxlZnQ6IDMycHg7DQogICAgfQ0KICB9DQogIC8vIOWIqeeUqOaWsOeJueaAp+W8ueaAp+W4g+WxgGZsZXgs6K6+572uanVzdGlmeS1jb250ZW50OmZsZXgtZW5kOyDpobnnm67kvY3kuo7lrrnlmajnmoTnu5PlsL4NCiAgLy8g5LyY54K5OuaMiemSruS8muWbuuWumuS9jee9rumaj+edgOmhtemdoue8qeaUvizkuI3ot5HlgY8NCiAgLnJpZ2h0TW92ZSB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOw0KICAgIGhlaWdodDogNTBweDsNCiAgICBtYXJnaW4tdG9wOiAtOHB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDJweDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["QuarterlyReview.vue"], "names": [], "mappings": ";AAySA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "QuarterlyReview.vue", "sourceRoot": "src/views/AssessmentOrgan/QuarterlyReview", "sourcesContent": ["<template>\r\n  <!--  季度评议  -->\r\n  <div class=\"QuarterlyReview\">\r\n    <!-- 搜索栏search-box -->\r\n    <search-box @search-click=\"search\"\r\n                @reset-click=\"reset\"\r\n                title=\"季度评议筛选\">\r\n      <!--关键字查找框  -->\r\n      <zy-widget label=\"关键字\">\r\n        <el-input placeholder=\"请输入关键词\"\r\n                  v-model=\"searchParams.keyword\"\r\n                  clearable\r\n                  @keyup.enter.native=\"search\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\"></div>\r\n        </el-input>\r\n      </zy-widget>\r\n      <!--季度查询  -->\r\n      <zy-widget label=\"季度查询\">\r\n        <el-select v-model=\"searchParams.quarter\"\r\n                   clearable\r\n                   @keyup.enter.native=\"search\"\r\n                   placeholder=\"请选择季度\">\r\n          <el-option v-for=\"item in timeArr\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.label\"\r\n                     :value=\"item.value\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n\r\n      <!-- 部门查询(用到机构树)  -->\r\n      <zy-widget label=\"部门查询\"\r\n                 v-permissions=\"'auth:QuarterlyReview:department'\">\r\n        <zy-select v-model=\"searchParams.officeId\"\r\n                   clearable\r\n                   @keyup.enter.native=\"search\"\r\n                   placeholder=\"请选择部门\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\">\r\n          </div>\r\n        </zy-select>\r\n      </zy-widget>\r\n\r\n      <!-- 审核状态查询 -->\r\n      <!-- <zy-widget label=\"审核状态查询\">\r\n        <el-select v-model=\"searchParams.auditStatusParams\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择审核状态\">\r\n          <el-option v-for=\"item in auditStatusData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget> -->\r\n\r\n    </search-box>\r\n\r\n    <div class=\"qd-list-wrap \">\r\n      <div class=\"qd-btn-box rightMove\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"passClick()\">保存</el-button>\r\n      </div>\r\n      <!-- *** -->\r\n      <div class=\"tableData\">\r\n        <zy-table>\r\n          <el-table ref=\"singleTable\"\r\n                    slot=\"zytable\"\r\n                    @select=\"selected\"\r\n                    @select-all=\"selectedAll\"\r\n                    row-key=\"id\"\r\n                    :data=\"tableData\"\r\n                    highlight-current-row\r\n                    style=\"width: 100%\">\r\n            <el-table-column label=\"序号\"\r\n                             type=\"index\"\r\n                             fixed=\"left\"\r\n                             width=\"120\">\r\n            </el-table-column>\r\n            <el-table-column property=\"userName\"\r\n                             label=\"考核人\"\r\n                             width=\"250\">\r\n            </el-table-column>\r\n            <el-table-column property=\"officeName\"\r\n                             label=\"所属部门\"\r\n                             width=\"250\">\r\n            </el-table-column>\r\n            <el-table-column property=\"quarter\"\r\n                             label=\"季度\"\r\n                             min-width=\"250\">\r\n            </el-table-column>\r\n\r\n            <!-- TODO:evaluateLevel 评级值，匹配字典evaluation_audit_status -->\r\n            <el-table-column label=\"岗位履职状况\"\r\n                             width=\"350\"\r\n                             fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-radio-group v-model=\"scope.row.evaluateLevel\"\r\n                                v-for=\"(item, index) in status\"\r\n                                :key=\"index\">\r\n                  <el-radio :label=\"item.id\"\r\n                            class=\"mright\">{{item.value}}\r\n                  </el-radio>\r\n                </el-radio-group>\r\n              </template>\r\n\r\n            </el-table-column>\r\n\r\n          </el-table>\r\n        </zy-table>\r\n      </div>\r\n      <!-- 底部页签功能 -->\r\n      <div class=\"paging_box\">\r\n        <el-pagination @size-change=\"handleSizeChange\"\r\n                       @current-change=\"handleCurrentChange\"\r\n                       :current-page.sync=\"pageNo\"\r\n                       :page-sizes=\"[10, 20, 30, 40]\"\r\n                       :page-size.sync=\"pageSize\"\r\n                       background\r\n                       layout=\"total, prev, pager, next, sizes, jumper\"\r\n                       :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport tableData from '@mixins/tableData'\r\n\r\n// import func from 'vue-editor-bridge'\r\nexport default {\r\n  name: 'QuarterlyReview',\r\n  data () {\r\n    return {\r\n      id: '',\r\n      radio: 1,\r\n      timeArr: [{\r\n        value: '1',\r\n        label: '第一季度'\r\n      }, {\r\n        value: '2',\r\n        label: '第二季度'\r\n      }, {\r\n        value: '3',\r\n        label: '第三季度'\r\n      }, {\r\n        value: '4',\r\n        label: '第四季度'\r\n      }],\r\n      officeData: [], // 机构树,\r\n      searchParams: {\r\n        keyword: '',\r\n        quarter: '',\r\n        officeId: '',\r\n        auditStatusParams: ''\r\n\r\n      },\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      total: 10,\r\n\r\n      // *********\r\n      tableData: [],\r\n      currentRow: null,\r\n      evaluateLevel: '',\r\n      status: []\r\n      // choose: [],\r\n      // selectObj: [],\r\n      // selectData: []\r\n      // auditJson: []\r\n\r\n    }\r\n  },\r\n  mounted () {\r\n    this.dictionaryPubkvs()\r\n    this.getQuarterList()\r\n    this.treeList()\r\n    // console.log(document.getElementsByName('performanceStatus'))\r\n  },\r\n  inject: ['tabDelJump'],\r\n  mixins: [tableData],\r\n\r\n  methods: {\r\n\r\n    /**\r\n     *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_audit_status,evaluate_audit_status'\r\n      })\r\n      var { data } = res\r\n      this.status = data.evaluation_audit_status\r\n      this.auditStatusData = data.evaluate_audit_status\r\n    },\r\n\r\n    /**\r\n      *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n\r\n    passClick () {\r\n      // if (this.choose.length) {\r\n      //   this.$confirm('此操作将选择的资讯的状态改为??, 是否继续?', '提示', {\r\n      //     confirmButtonText: '确定',\r\n      //     cancelButtonText: '取消',\r\n      //     type: 'warning'\r\n      //   }).then(() => {\r\n      //     // console.log(this.tableData)\r\n      //   }).catch(() => {\r\n      //     this.$message({\r\n      //       type: 'info',\r\n      //       message: '已取消操作'\r\n      //     })\r\n      //   })\r\n      // } else {\r\n      //   this.$message({\r\n      //     message: '请至少选择一条数据',\r\n      //     type: 'warning'\r\n      //   })\r\n      // }\r\n      this.saveInfo()\r\n    },\r\n    // 保存\r\n    async saveInfo () {\r\n      var arr = []\r\n      this.tableData.forEach(item => {\r\n        arr.push({ id: item.id, evaluateLevel: item.evaluateLevel })\r\n      })\r\n      const res = await this.$api.AssessmentOrgan.reqSaveQuarter({\r\n        auditJson: JSON.stringify(arr),\r\n        quarter: this.searchParams.quarter\r\n      })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getQuarterList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 请求后台数据 获取列表信息\r\n    async getQuarterList () {\r\n      const res = await this.$api.AssessmentOrgan.reqQuarterList({\r\n        pageNo: this.pageNo,\r\n        pageSize: this.pageSize,\r\n        keyword: this.searchParams.keyword,\r\n        quarter: this.searchParams.quarter,\r\n        // // sedateId: this.sedateId\r\n        // sedateId: this.selectedYear\r\n        officeId: this.searchParams.officeId, // 部门查询\r\n        auditStatus: this.searchParams.auditStatusParams // 审核状态\r\n      })\r\n      var { data, total } = res\r\n      this.tableData = data\r\n      this.total = total\r\n    },\r\n    // paging_box 里面用到的方法\r\n    handleSizeChange () {\r\n      this.getQuarterList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getQuarterList()\r\n    },\r\n    // 一整个搜索框内 查询按钮 的逻辑search\r\n    search () {\r\n      this.pageNo = 1\r\n      this.getQuarterList()\r\n    },\r\n    // 一整个搜索框内 重置按钮 逻辑\r\n    reset () {\r\n      this.searchParams.keyword = ''\r\n      this.searchParams.quarter = ''\r\n      this.searchParams.officeId = ''\r\n      this.searchParams.auditStatusParams = ''\r\n      this.getQuarterList()\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.QuarterlyReview {\r\n  height: 100%;\r\n  width: 100%;\r\n\r\n  .mright {\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .tableData {\r\n    .el-scrollbar__wrap .el-scrollbar__view th {\r\n      background: #f5f7fb;\r\n    }\r\n    height: calc(100% - 106px);\r\n  }\r\n  .button-box {\r\n    background: #fff;\r\n    margin-top: 20px;\r\n  }\r\n  .button-box .el-button {\r\n    background: #fff;\r\n  }\r\n  .el-button--danger.is-plain:hover,\r\n  .el-button--danger.is-plain:focus {\r\n    // color: #f56c6c;\r\n    background: #f56c6c;\r\n    color: #fff;\r\n  }\r\n  .el-button--success.is-plain:hover,\r\n  .el-button--success.is-plain:focus {\r\n    background: #67c23a;\r\n    color: #fff;\r\n  }\r\n  .delBtn {\r\n    color: #f56c6c;\r\n  }\r\n  .paging_box {\r\n    background: #fff;\r\n  }\r\n  .scoreEdit {\r\n    width: 700px;\r\n    height: 100%;\r\n    padding: 24px;\r\n    .form-button {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n  .search-box {\r\n    //title文字样式\r\n    .search-title {\r\n      width: 115px;\r\n      height: 16px;\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-left: 32px;\r\n    }\r\n  }\r\n  // 利用新特性弹性布局flex,设置justify-content:flex-end; 项目位于容器的结尾\r\n  // 优点:按钮会固定位置随着页面缩放,不跑偏\r\n  .rightMove {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    height: 50px;\r\n    margin-top: -8px;\r\n    margin-bottom: 2px;\r\n  }\r\n}\r\n</style>\r\n"]}]}