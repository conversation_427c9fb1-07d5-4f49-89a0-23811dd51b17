{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\general-custom-topic\\custom-topic-details\\custom-topic-details.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\general-custom-topic\\custom-topic-details\\custom-topic-details.vue", "mtime": 1752541697693}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdjb21taXR0ZWVEYXRhQ3VzdG9tVG9waWMtd3cnLA0KICBkYXRhICgpIHsNCiAgICByZXR1cm4gew0KICAgICAgZGV0YWlsczoge30NCiAgICB9DQogIH0sDQogIHByb3BzOiBbJ2lkJywgJ25hbWUnXSwNCiAgbW91bnRlZCAoKSB7DQogICAgaWYgKHRoaXMuaWQpIHsNCiAgICAgIHRoaXMuY3VzdG9tVG9waWNMaXN0SW5mbygpDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgYXN5bmMgY3VzdG9tVG9waWNMaXN0SW5mbyAoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCB0aGlzLiRhcGkud2lzZG9tV2FyZWhvdXNlLmFzc2VtYmx5SW5mbyh0aGlzLmlkKQ0KICAgICAgdmFyIHsgZGF0YSB9ID0gcmVzDQogICAgICB0aGlzLmRldGFpbHMgPSBkYXRhDQogICAgICAvLyBpZiAoZGF0YS5leHRlcm5hbExpbmtzKSB7DQogICAgICAvLyAgIHdpbmRvdy5vcGVuKGRhdGEuZXh0ZXJuYWxMaW5rcywgJ19ibGFuaycpDQogICAgICAvLyB9DQogICAgfSwNCiAgICBkb3dubG9hZCAoZGF0YSkgew0KICAgICAgdGhpcy4kYXBpLnByb3Bvc2FsLmRvd25sb2FkRmlsZSh7IGlkOiBkYXRhLmlkIH0sIGRhdGEuZmlsZU5hbWUpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["custom-topic-details.vue"], "names": [], "mappings": ";AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "custom-topic-details.vue", "sourceRoot": "src/views/wisdomWarehouse/general-custom-topic/custom-topic-details", "sourcesContent": ["<template>\r\n  <div class=\"custom-project-details\">\r\n    <div class=\"custom-project-details-title\">{{details.name}}</div>\r\n    <div class=\"custom-project-details-xx\">\r\n      <div class=\"custom-project-details-tiem\">发布时间：{{details.pubDate}}</div>\r\n    </div>\r\n    <div class=\"custom-project-details-content\"\r\n         v-html=\"details.content\"></div>\r\n    <!-- *****附件***** -->\r\n    <div class=\"fileBox\"\r\n         v-if=\"details.attachmentList !==undefined && details.attachmentList.length > 0 \">\r\n      <div class=\"file_title\"> 资讯附件 </div>\r\n      <div class=\"fileListt\"\r\n           v-for=\"(item, index) in details.attachmentList\"\r\n           :key=\"index\">\r\n        <div class=\"file_item\">\r\n\r\n          <div class=\"file_name\"> {{item.fileName}} </div>\r\n\r\n          <div class=\"file_load\">\r\n            <div class=\"load_text\"><a :href=\"item.filePath\"\r\n                 target=\"_blank\">预览</a>\r\n            </div>\r\n            <div class=\"shu\"></div>\r\n            <div class=\"load_text\"\r\n                 @click=\"download(item)\">下载</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'committeeDataCustomTopic-ww',\r\n  data () {\r\n    return {\r\n      details: {}\r\n    }\r\n  },\r\n  props: ['id', 'name'],\r\n  mounted () {\r\n    if (this.id) {\r\n      this.customTopicListInfo()\r\n    }\r\n  },\r\n  methods: {\r\n    async customTopicListInfo () {\r\n      const res = await this.$api.wisdomWarehouse.assemblyInfo(this.id)\r\n      var { data } = res\r\n      this.details = data\r\n      // if (data.externalLinks) {\r\n      //   window.open(data.externalLinks, '_blank')\r\n      // }\r\n    },\r\n    download (data) {\r\n      this.$api.proposal.downloadFile({ id: data.id }, data.fileName)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './custom-topic-details.scss';\r\n</style>\r\n"]}]}