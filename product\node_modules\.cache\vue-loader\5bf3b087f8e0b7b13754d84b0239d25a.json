{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu-tree\\zy-menu-tree.vue?vue&type=style&index=0&id=73872f0f&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu-tree\\zy-menu-tree.vue", "mtime": 1752541693532}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3p5LW1lbnUtdHJlZS5zY3NzIjsNCg=="}, {"version": 3, "sources": ["zy-menu-tree.vue"], "names": [], "mappings": ";AA+PA", "file": "zy-menu-tree.vue", "sourceRoot": "src/components/zy-menu-tree", "sourcesContent": ["<template>\r\n  <div class=\"zy-menu-tree scrollBar\">\r\n    <div v-for=\"(menu, index) in menuItem\"\r\n         :key=\"index\">\r\n      <div :class=\"['menu-item', menu.active?'menu-item-active':'']\"\r\n           :style=\"{ paddingLeft: padding(hierarchy) + 'px' }\"\r\n           v-if=\"judge(menu,false)\"\r\n           @click=\"selected(menu)\">{{menu[props.label]}}</div>\r\n      <div v-if=\"judge(menu,true)\">\r\n        <div class=\"menu-item menu-item-title\"\r\n             :style=\"{ paddingLeft: padding(hierarchy) + 'px' }\"\r\n             @click=\"submenu(menu)\">{{menu[props.label]}} <div :class=\"['menu-icon',menu.hidden? 'menu-icon-active':'']\"></div>\r\n        </div>\r\n        <el-collapse-transition>\r\n          <zy-menu-tree v-if=\"menu.hidden\"\r\n                        :show=\"show\"\r\n                        :menu=\"menu[props.children]\"\r\n                        :props=\"props\"\r\n                        v-model=\"menuId\"\r\n                        :nodeKey=\"nodeKey\"\r\n                        :hierarchy=\"hierarchy+1\"></zy-menu-tree>\r\n        </el-collapse-transition>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyMenuTree',\r\n  data () {\r\n    return {\r\n      menuId: this.value,\r\n      menuItem: []\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    menu: {\r\n      type: Array,\r\n      default: function () {\r\n        return []\r\n      }\r\n    },\r\n    hierarchy: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    show: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label',\r\n          show: 'show'\r\n        }\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  created () {\r\n    this.menudata(this.deepCopy(this.menu))\r\n  },\r\n  watch: {\r\n    menu () {\r\n      this.menudata(this.deepCopy(this.menu))\r\n    },\r\n    value (val) {\r\n      this.menuId = val\r\n      this.selectedId()\r\n    },\r\n    menuId (val) {\r\n      this.$emit('id', val)\r\n    }\r\n  },\r\n  methods: {\r\n    judge (data, type) {\r\n      var show = false\r\n      if (this.show) {\r\n        if (type) {\r\n          if (data[this.props.children].length !== 0 && data[this.props.show]) {\r\n            show = true\r\n          } else {\r\n            show = false\r\n          }\r\n        } else {\r\n          if (data[this.props.children].length === 0 && data[this.props.show]) {\r\n            show = true\r\n          } else {\r\n            show = false\r\n          }\r\n        }\r\n      } else {\r\n        if (type) {\r\n          if (data[this.props.children].length) {\r\n            show = true\r\n          } else {\r\n            show = false\r\n          }\r\n        } else {\r\n          if (data[this.props.children].length) {\r\n            show = false\r\n          } else {\r\n            show = true\r\n          }\r\n        }\r\n      }\r\n      return show\r\n    },\r\n    padding (index) {\r\n      var hierarchy = 24 + (16 * index)\r\n      return hierarchy\r\n    },\r\n    menudata (data) {\r\n      data.forEach(item => {\r\n        if (item[this.props.children].length === 0) {\r\n          item.active = false\r\n          if (this.menuId === item[this.nodeKey]) {\r\n            item.active = true\r\n          }\r\n        } else {\r\n          item.hidden = false\r\n        }\r\n      })\r\n      this.menuItem = data\r\n      this.selectedId()\r\n    },\r\n    submenu (data) {\r\n      const arr = this.menuItem\r\n      arr.forEach(item => {\r\n        if (item[this.nodeKey] === data[this.nodeKey]) {\r\n          item.hidden = !item.hidden\r\n        }\r\n      })\r\n    },\r\n    // rowClick (result) {\r\n    //   this.$emit('on-row-click', result)\r\n    // },\r\n    selected (data) {\r\n      this.menuId = data[this.nodeKey]\r\n      // let result = this.makeData(data)\r\n      // this.$emit('on-row-click', result)\r\n    },\r\n    selectedId (type) {\r\n      if (this.hierarchy === 0) {\r\n        this.menuhierarchy(this.menuItem)\r\n      }\r\n      const arr = this.menuItem\r\n      arr.forEach(item => {\r\n        if (item[this.props.children].length === 0) {\r\n          item.active = false\r\n          if (item[this.nodeKey] === this.menuId) {\r\n            item.active = true\r\n            if (this.$route.path !== item.to) {\r\n              this.$router.push({ path: item.to })\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    menuhierarchy (data) {\r\n      data.forEach(item => {\r\n        if (item[this.nodeKey] === this.menuId) {\r\n          sessionStorage.setItem('curMenuItem', JSON.stringify(item))\r\n          if (this.$route.path !== item.to) {\r\n            this.$router.push({ path: item.to })\r\n          }\r\n          const result = this.makeData(item)\r\n          this.$emit('on-row-click', result)\r\n        }\r\n        if (item[this.props.children].length) {\r\n          this.menuhierarchy(item[this.props.children])\r\n        }\r\n      })\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    },\r\n    makeData (data) {\r\n      const t = this.type(data)\r\n      let o\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (let i = 0; i < data.length; i++) {\r\n          o.push(this.makeData(data[i]))\r\n        }\r\n      } else if (t === 'object') {\r\n        for (const i in data) {\r\n          if (i != 'active' && i != 'hidden') {// eslint-disable-line\r\n            o[i] = this.makeData(data[i])\r\n          }\r\n        }\r\n      }\r\n      return o\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-menu-tree.scss\";\r\n</style>\r\n"]}]}