{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-widget\\zy-widget.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-widget\\zy-widget.vue", "mtime": 1752541693628}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICd6eS13aWRnZXQnLAogIHByb3BzOiB7CiAgICBsYWJlbDogU3RyaW5nLAogICAgbGFiZWxXOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogKCkgPT4gJzEwMHB4JwogICAgfQogIH0KfTs="}, {"version": 3, "mappings": "AAWA;EACAA,iBADA;EAEAC;IACAC,aADA;IAEAC;MACAC,YADA;MAEAC;IAFA;EAFA;AAFA", "names": ["name", "props", "label", "labelW", "type", "default"], "sourceRoot": "src/components/zy-widget", "sources": ["zy-widget.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-widget\">\r\n    <span class=\"label\"\r\n          :style=\"`width: ${labelW};`\">{{ label }}</span>\r\n    <div class=\"zy-widget-slot\">\r\n      <slot></slot>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'zy-widget',\r\n  props: {\r\n    label: String,\r\n    labelW: {\r\n      type: String,\r\n      default: () => '100px'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.zy-widget {\r\n  margin-right: 24px;\r\n  margin-bottom: 12px;\r\n\r\n  .label {\r\n    line-height: 40px;\r\n    margin-right: 10px;\r\n    float: left;\r\n    width: 100px;\r\n    text-align: right;\r\n  }\r\n\r\n  .zy-widget-slot {\r\n    float: left;\r\n  }\r\n}\r\n</style>\r\n"]}]}