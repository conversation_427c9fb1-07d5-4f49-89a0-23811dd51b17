{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\wang-editor\\wang-editor.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\wang-editor\\wang-editor.vue", "mtime": 1752541693505}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["wang-editor.vue"], "names": [], "mappings": ";AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "wang-editor.vue", "sourceRoot": "src/components/wang-editor", "sourcesContent": ["<template>\r\n  <div>\r\n    <div :id=\"editorId\"\r\n         class=\"wang-editor\"></div>\r\n    <p v-if=\"contentLength > $props.max\"\r\n       style=\"color: red;\">\r\n      已超出最大{{ $props.max }}字数限制！\r\n    </p>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 引入富文本编辑器\r\nimport WangEditor from 'wangeditor'\r\nimport axios from 'axios'\r\nconst { BtnMenu } = WangEditor\r\nexport default {\r\n  name: 'wang-editor',\r\n  props: {\r\n    value: {\r\n      required: true\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: () => '请输入正文'\r\n    },\r\n    max: {\r\n      type: Number,\r\n      default: () => 9999999\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      editor: '',\r\n      editorId: '',\r\n      contentLength: 0\r\n    }\r\n  },\r\n  watch: {\r\n    value (newval) {\r\n      if (this.editor) {\r\n        if (newval !== this.editor.txt.html()) {\r\n          this.editor.txt.html(newval)\r\n        }\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value', // 这个字段，是指父组件设置 v-model 时，将变量值传给子组件的 msg\r\n    event: 'input'// 这个字段，是指父组件监听 parent-event 事件\r\n  },\r\n  methods: {\r\n    // // 生成一个随机不重复id,可以通过时间和随机数生成\r\n    randomId () {\r\n      const baseId = 'wang_editor'\r\n      const now = new Date().getTime()\r\n      return `${baseId}_${now}`\r\n    },\r\n    // 初始化编辑器\r\n    initEditor () {\r\n      const _this = this\r\n      _this.editorId = _this.randomId()// 生成一个id\r\n      this.$nextTick(() => {\r\n        // 获取实例,wangEditor是被注册在window的\r\n        const editor = new WangEditor('#' + _this.editorId)\r\n        _this.editor = editor// 将实例保存待调用其他api\r\n        _this.setConfig()\r\n        editor.create()// 开始创建编辑器；\r\n        _this.editor.txt.html(this.value)\r\n        // 设置是否可编辑\r\n        if (this.disabled !== 'undefined') {\r\n          this.editor.$textElem.attr('contenteditable', !this.disabled)\r\n        }\r\n      })\r\n    },\r\n    // 创建富文本编辑器\r\n    setConfig () {\r\n      var _this = this\r\n      // 开始创建\r\n      const setting = {\r\n        uploadImgShowBase64: false, // 是否允许上传base64位图片\r\n        pasteFilterStyle: true, // 是否过滤粘贴的样式\r\n        zIndex: 100, // 设置层叠位置\r\n        // 菜单列表\r\n        menus: [\r\n          'head', // 标题\r\n          'bold', // 粗体\r\n          'fontSize', // 字号\r\n          'fontName', // 字体\r\n          'italic', // 斜体\r\n          'indent', // 缩进\r\n          'lineHeight', // 行高\r\n          'underline', // 下划线\r\n          'strikeThrough', // 删除线\r\n          'foreColor', // 文字颜色\r\n          'backColor', // 背景颜色\r\n          'link', // 插入链接\r\n          'list', // 列表\r\n          'justify', // 对齐方式\r\n          'quote', // 引用\r\n          'emoticon', // 表情\r\n          'image', // 插入图片\r\n          'table', // 表格\r\n          'video', // 插入视频\r\n          // 'code', // 插入代码\r\n          'undo', // 撤销\r\n          'redo', // 恢复\r\n          'qgs' // 恢复\r\n        ],\r\n        showLinkImg: true, // 是否显示“网络图片”tab\r\n        onchange: function (html) {\r\n          // console.log('html===>', html)\r\n          html = html.replace(/<strong>(.*?)<\\/strong>/g, '$1')\r\n          html = html.replace(/<b>(.*?)<\\/b>/g, '$1')\r\n          _this.$emit('input', html)\r\n          const text = html\r\n          _this.contentLength = text.length\r\n          if (_this.contentLength > _this.$props.max) {\r\n            _this.$emit('restrictions', `已超出最大${_this.$props.max}字数限制！`)\r\n          } else {\r\n            _this.$emit('restrictions', '')\r\n          }\r\n        },\r\n        onlineVideoCallback: v => {\r\n          if (v.endsWith('.mp4')) {\r\n            _this.editor.cmd.do(\r\n              'insertHTML', `<video src=\"${v}\" controls=\"controls\" style=\"max-width:100%\"></video>`\r\n            )\r\n          }\r\n        },\r\n        customUploadImg: (resultFiles, insertImgFn) => {\r\n          const formData = new FormData()\r\n          formData.append('upfile', resultFiles[0])\r\n          axios.post(`${_this.$api.general.baseURL()}/ueditor/exec?action=uploadimage`, formData).then(res => {\r\n            insertImgFn(res.data.url)\r\n          })\r\n        }\r\n      }\r\n      // 配置给编辑器\r\n      _this.editor.config = Object.assign(_this.editor.config, setting)\r\n      _this.editor.config.placeholder = _this.$props.placeholder\r\n    }\r\n  },\r\n  created () {\r\n    // 创建editor实例\r\n    class AlertMenu extends BtnMenu {\r\n      constructor(editor) {\r\n        // data-title属性表示当鼠标悬停在该按钮上时提示该按钮的功能简述\r\n        const $elem = WangEditor.$(\r\n          `<div class=\"w-e-menu\" data-title=\"清除格式\">\r\n               <svg style=\"width:14px;heigth:14px;\" viewBox=\"0 0 1024 1024\"><path d=\"M969.382408 288.738615l-319.401123-270.852152a67.074236 67.074236 0 0 0-96.459139 5.74922l-505.931379 574.922021a68.35184 68.35184 0 0 0-17.886463 47.910169 74.101061 74.101061 0 0 0 24.274486 47.910168l156.50655 132.232065h373.060512L975.131628 383.281347a67.074236 67.074236 0 0 0-5.74922-96.459139z m-440.134747 433.746725H264.144729l-90.071117-78.572676c-5.74922-5.74922-12.137243-12.137243-12.137243-17.886463a36.411728 36.411728 0 0 1 5.749221-24.274485l210.804741-240.828447 265.102932 228.691204z m-439.495945 180.781036h843.218964a60.047411 60.047411 0 1 1 0 120.733624H89.751716a60.047411 60.047411 0 1 1 0-120.733624z m0 0\"></path></svg>\r\n            </div>`\r\n        )\r\n        super($elem, editor)\r\n      }\r\n\r\n      clickHandler () {\r\n        var editor = this.editor\r\n        console.log('editor===>>', editor)\r\n        var str = editor.txt.html()\r\n        str = str.replace(/<xml>[\\s\\S]*?<\\/xml>/ig, '')\r\n        str = str.replace(/<style>[\\s\\S]*?<\\/style>/ig, '')\r\n        str = str.replace(/<\\/?[^>]*>/g, '')\r\n        str = str.replace(/[ | ]*\\n/g, '\\n')\r\n        str = str.replace(/&nbsp;/ig, '')\r\n        editor.txt.html(str)\r\n      }\r\n\r\n      tryChangeActive () { }\r\n    }\r\n    WangEditor.registerMenu('qgs', AlertMenu)\r\n    this.initEditor()\r\n  }\r\n}\r\n\r\n</script>\r\n<style lang=\"scss\">\r\n.wang-editor {\r\n  width: 100%;\r\n\r\n  .w-e-text-container {\r\n    .w-e-text {\r\n      img {\r\n        width: 80%;\r\n        height: auto;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}