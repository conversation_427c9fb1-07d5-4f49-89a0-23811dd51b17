{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\search-box\\search-box.vue?vue&type=style&index=0&id=ad07ab62&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\search-box\\search-box.vue", "mtime": 1752541693483}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zZWFyY2gtYm94IHsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogIGJvcmRlci1yYWRpdXM6IDEwcHg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICBib3gtc2hhZG93OiAwcHggNHB4IDZweCAwcHggcmdiYSgyMzMsIDIzMywgMjMzLCAwLjQpOwogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICBwYWRkaW5nOiAxMnB4IDEwcHggMDsKCiAgLnNlYXJjaC10aXRsZSB7CiAgICBtaW4td2lkdGg6IDEyMHB4OwogICAgZmxleC1zaHJpbms6IDA7CiAgICBjb2xvcjogIzQ0NDsKICAgIGxpbmUtaGVpZ2h0OiA0MHB4OwogICAgaGVpZ2h0OiA0MHB4OwogIH0KCiAgLmVsLWlucHV0IHsKICAgIHdpZHRoOiAyMjJweDsKICAgIC8vIG1hcmdpbi1sZWZ0OiAyNHB4OwogICAgbWFyZ2luLWJvdHRvbTogMTJweDsKICB9CgogIC5lbC1zZWxlY3QgewogICAgLy8gbWFyZ2luLWxlZnQ6IDI0cHg7CiAgICBtYXJnaW4tYm90dG9tOiAxMnB4OwoKICAgIC5lbC1pbnB1dCB7CiAgICAgIG1hcmdpbi1sZWZ0OiAwOwogICAgICBtYXJnaW4tYm90dG9tOiAwOwogICAgfQogIH0KCiAgLnp5LXNlbGVjdCB7CiAgICAvLyBtYXJnaW4tbGVmdDogMjRweDsKICAgIG1hcmdpbi1ib3R0b206IDEycHg7CgogICAgLmVsLWlucHV0IHsKICAgICAgbWFyZ2luLWxlZnQ6IDA7CiAgICAgIG1hcmdpbi1ib3R0b206IDA7CiAgICB9CiAgfQoKICAuZWwtZGF0ZS1lZGl0b3IgewogICAgLy8gbWFyZ2luLWxlZnQ6IDI0cHg7CiAgICBtYXJnaW4tYm90dG9tOiAxMnB4OwogIH0KCiAgLnNlYXJjaC1ib3gtc2xvdCB7CiAgICB3aWR0aDogY2FsYygxMDAlIC0gMjQ2cHgpOwogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7CiAgICBmbGV4LXdyYXA6IHdyYXA7CiAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgaGVpZ2h0OiA1MnB4OwogIH0KCiAgLnNlYXJjaC1ib3gtYnRuIHsKICAgIGZsZXgtc2hyaW5rOiAwOwoKICAgIC5lbC1idXR0b24gewogICAgICBoZWlnaHQ6IDQwcHg7CiAgICAgIG1hcmdpbi1sZWZ0OiAyNHB4OwogICAgICBwYWRkaW5nOiAwIDE2cHg7CiAgICB9CgogICAgLmVsLWJ1dHRvbisuZWwtYnV0dG9uIHsKICAgICAgbWFyZ2luLWxlZnQ6IDE2cHg7CiAgICB9CgogICAgLmVsLWJ1dHRvbi0tdGV4dCB7CiAgICAgIG1hcmdpbi1sZWZ0OiA5cHg7CiAgICAgIGZvbnQtc2l6ZTogJHRleHRTaXplMTQ7CiAgICAgIHBhZGRpbmc6IDA7CgogICAgICAuZWwtaWNvbi1hcnJvdy1kb3duIHsKICAgICAgICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAwLjRzOwogICAgICAgIHRyYW5zZm9ybTogcm90YXRlKDApOwogICAgICB9CgogICAgICAuZWwtaWNvbi1hcnJvdy1kb3duLWEgewogICAgICAgIHRyYW5zaXRpb24tZHVyYXRpb246IDAuNHM7CiAgICAgICAgdHJhbnNmb3JtOiByb3RhdGUoLTE4MGRlZyk7CiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["search-box.vue"], "names": [], "mappings": ";AA0LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "search-box.vue", "sourceRoot": "src/components/search-box", "sourcesContent": ["<template>\r\n  <div class=\"search-box\"\r\n       ref=\"searchBox\">\r\n    <span class=\"search-title\"\r\n          v-if=\"title\">{{ title }}</span>\r\n    <div class=\"search-box-slot\"\r\n         ref=\"searchBoxSlot\">\r\n      <slot></slot>\r\n    </div>\r\n    <div class=\"search-box-btn\"\r\n         :style=\"{ width: (isMore ? 220 : 184) + 'px' }\">\r\n      <el-button type=\"text\"\r\n                 v-if=\"isMore\"\r\n                 @click=\"moreClick\">\r\n        <i :class=\"['el-icon-arrow-down', isShow ? '' : 'el-icon-arrow-down-a']\"></i>{{ isShow ? '展开' : '收起' }}\r\n      </el-button>\r\n      <el-button type=\"primary\"\r\n                 @click=\"search\"\r\n                 v-if=\"isSearch\">查询</el-button>\r\n      <el-button @click=\"reset\"\r\n                 v-if=\"isReset\">重置</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport _ from 'lodash'\r\nexport default {\r\n  name: 'search-box',\r\n  props: {\r\n    isSearch: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    isReset: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    title: String\r\n  },\r\n  mounted () {\r\n    this.$nextTick(() => {\r\n      this.collapse()\r\n    })\r\n    this.resize = window.onresize = _.debounce(() => {\r\n      this.collapse()\r\n    }, 500)\r\n  },\r\n  data () {\r\n    return {\r\n      isMore: false,\r\n      isShow: true,\r\n      resize: null\r\n    }\r\n  },\r\n  destroyed () {\r\n    this.resize = null\r\n  },\r\n  methods: {\r\n    search () {\r\n      this.$emit('search-click')\r\n    },\r\n    reset () {\r\n      this.$emit('reset-click')\r\n    },\r\n    collapse () {\r\n      var searchBoxSlot = this.$refs.searchBoxSlot\r\n      if (searchBoxSlot) {\r\n        var width = 0\r\n        for (let index = 0; index < searchBoxSlot.childNodes.length; index++) {\r\n          if (searchBoxSlot.childNodes[index].offsetWidth !== undefined) {\r\n            width += searchBoxSlot.childNodes[index].offsetWidth + 24\r\n          }\r\n        }\r\n        if (searchBoxSlot.offsetWidth < width) {\r\n          this.isMore = true\r\n        } else {\r\n          this.isMore = false\r\n        }\r\n      }\r\n    },\r\n    moreClick () {\r\n      var searchBoxSlot = this.$refs.searchBoxSlot\r\n      if (this.isShow) {\r\n        searchBoxSlot.style.height = 'auto'\r\n      } else {\r\n        searchBoxSlot.style.height = '52px'\r\n      }\r\n      this.isShow = !this.isShow\r\n      this.$emit('more-click', searchBoxSlot.offsetHeight, this.isShow)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\"\r\n       scoped>\r\n      .search-box {\r\n        box-sizing: border-box;\r\n        border-radius: 10px;\r\n        background-color: #fff;\r\n        box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        padding: 12px 10px 0;\r\n\r\n        .search-title {\r\n          min-width: 120px;\r\n          flex-shrink: 0;\r\n          color: #444;\r\n          line-height: 40px;\r\n          height: 40px;\r\n        }\r\n\r\n        .el-input {\r\n          width: 222px;\r\n          // margin-left: 24px;\r\n          margin-bottom: 12px;\r\n        }\r\n\r\n        .el-select {\r\n          // margin-left: 24px;\r\n          margin-bottom: 12px;\r\n\r\n          .el-input {\r\n            margin-left: 0;\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n\r\n        .zy-select {\r\n          // margin-left: 24px;\r\n          margin-bottom: 12px;\r\n\r\n          .el-input {\r\n            margin-left: 0;\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n\r\n        .el-date-editor {\r\n          // margin-left: 24px;\r\n          margin-bottom: 12px;\r\n        }\r\n\r\n        .search-box-slot {\r\n          width: calc(100% - 246px);\r\n          display: flex;\r\n          justify-content: flex-end;\r\n          flex-wrap: wrap;\r\n          overflow: hidden;\r\n          height: 52px;\r\n        }\r\n\r\n        .search-box-btn {\r\n          flex-shrink: 0;\r\n\r\n          .el-button {\r\n            height: 40px;\r\n            margin-left: 24px;\r\n            padding: 0 16px;\r\n          }\r\n\r\n          .el-button+.el-button {\r\n            margin-left: 16px;\r\n          }\r\n\r\n          .el-button--text {\r\n            margin-left: 9px;\r\n            font-size: $textSize14;\r\n            padding: 0;\r\n\r\n            .el-icon-arrow-down {\r\n              transition-duration: 0.4s;\r\n              transform: rotate(0);\r\n            }\r\n\r\n            .el-icon-arrow-down-a {\r\n              transition-duration: 0.4s;\r\n              transform: rotate(-180deg);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    </style>\r\n"]}]}