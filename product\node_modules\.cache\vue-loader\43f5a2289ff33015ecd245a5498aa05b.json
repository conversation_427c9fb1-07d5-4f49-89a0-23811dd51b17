{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue?vue&type=style&index=0&id=4a09f3e8&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue", "mtime": 1756287513677}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5waWUtY2hhcnQgewogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogMTAwJTsKICBwb3NpdGlvbjogcmVsYXRpdmU7Cn0K"}, {"version": 3, "sources": ["PieChart.vue"], "names": [], "mappings": ";AA4PA;AACA;AACA;AACA;AACA", "file": "PieChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"pie-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'PieChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    name: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      autoHighlightTimer: null, // 自动高亮定时器\n      currentHighlightIndex: -1, // 当前高亮的数据项索引\n      // 预定义的颜色数组，按顺序分配给数据项\n      colors: [\n        '#4FC3F7', '#FFF67C', '#66BB6A', '#FFA726',\n        '#FF7043', '#AB47BC', '#5C6BC0', '#42A5F5',\n        '#FFCA28', '#4CAF50', '#EF5350', '#8D6E63',\n        '#9C27B0', '#3F51B5', '#2196F3', '#00BCD4',\n        '#FF9800', '#795548', '#607D8B', '#E91E63'\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    // 清除自动高亮定时器\n    if (this.autoHighlightTimer) {\n      clearInterval(this.autoHighlightTimer)\n    }\n    if (this.chart) {\n      this.chart.dispose()\n    }\n  },\n  methods: {\n    getColor (index, item) {\n      // 如果数据项中有颜色信息，优先使用数据中的颜色\n      if (item && item.color) {\n        return item.color\n      }\n      // 否则使用预定义的颜色数组\n      return this.colors[index % this.colors.length]\n    },\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00d4ff',\n          borderWidth: 1,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: this.id === 'category_distribution' ? 'horizontal' : 'vertical',\n          right: this.id === 'reply-type-pie' ? '0%' : this.id === 'category_distribution' ? null : this.id === 'proposal-statistics' ? '0%' : '5%',\n          left: this.id === 'category_distribution' ? 'center' : null,\n          top: this.id === 'reply-type-pie' ? '38%' : this.id === 'category_distribution' ? null : 'center',\n          bottom: this.id === 'category_distribution' ? 20 : null,\n          itemWidth: this.id === 'reply-type-pie' ? 12 : 5,\n          itemHeight: this.id === 'reply-type-pie' ? 6 : 5,\n          icon: this.id === 'reply-type-pie' ? null : 'circle',\n          itemGap: this.id === 'reply-type-pie' ? 45 : this.id === 'category_distribution' ? 30 : this.id === 'proposal-statistics' ? 12 : 25,\n          textStyle: {\n            color: '#fff',\n            fontSize: 12,\n            fontFamily: 'Microsoft YaHei'\n          },\n          formatter: (name) => {\n            const item = this.chartData.find(d => d.name === name)\n            if (this.id === 'reply-type-pie') {\n              return `${name}`\n            } else {\n              return `${name}  ${item ? item.value : ''}%`\n            }\n          }\n        },\n        series: [\n          {\n            name: this.name,\n            type: 'pie',\n            radius: this.id === 'category_distribution' ? ['30%', '45%'] : this.id === 'proposal-statistics' ? ['60%', '85%'] : this.id === 'reply-type-pie' ? ['40%', '70%'] : ['55%', '80%'],\n            center: this.id === 'category_distribution' ? ['50%', '25%'] : this.id === 'proposal-statistics' ? ['22%', '50%'] : this.id === 'reply-type-pie' ? ['50%', '50%'] : ['30%', '50%'],\n            avoidLabelOverlap: false,\n            emphasis: {\n              scale: true,\n              scaleSize: 10\n            },\n            label: {\n              show: true,\n              position: 'center',\n              fontSize: 16,\n              color: '#fff',\n              formatter: this.name\n            },\n            labelLine: {\n              show: false\n            },\n            itemStyle: {\n              borderWidth: 3,\n              borderColor: '#07345F' // 用大屏背景色\n            },\n            data: this.chartData.map((item, index) => ({\n              value: item.value,\n              name: item.name,\n              itemStyle: { color: this.getColor(index, item) }\n            }))\n          },\n          {\n            type: 'pie',\n            radius: this.id === 'category_distribution' ? ['50%', '51%'] : this.id === 'proposal-statistics' ? ['94%', '95%'] : this.id === 'reply-type-pie' ? ['1%', '1%'] : ['88%', '89%'],\n            center: this.id === 'category_distribution' ? ['50%', '25%'] : this.id === 'proposal-statistics' ? ['22%', '50%'] : this.id === 'reply-type-pie' ? ['50%', '50%'] : ['30%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: '#2f689a'\n                }\n              }\n            ],\n            label: {\n              show: false\n            }\n          }\n        ],\n        graphic: [\n          {\n            type: 'circle',\n            left: this.id === 'category_distribution' ? '36.5%' : this.id === 'proposal-statistics' ? '12%' : this.id === 'reply-type-pie' ? '40%' : '17%',\n            top: this.id === 'category_distribution' ? '13%' : this.id === 'proposal-statistics' ? '23%' : this.id === 'reply-type-pie' ? '35%' : '26%',\n            shape: {\n              cx: 0,\n              cy: 0,\n              r: this.id === 'category_distribution' ? 60 : this.id === 'proposal-statistics' ? 40 : this.id === 'reply-type-pie' ? 0 : 50\n            },\n            style: {\n              fill: 'none',\n              lineWidth: this.id === 'category_distribution' ? 4 : 3,\n              stroke: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: '#23E1FF' },\n                  { offset: 1, color: 'rgba(35,225,255,0)' }\n                ]\n              }\n            },\n            z: 10,\n            silent: true,\n            position: [0, 0]\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 监听窗口大小变化\n      window.addEventListener('resize', () => {\n        if (this.chart) {\n          this.chart.resize()\n        }\n      })\n      // 启动自动高亮效果\n      this.startAutoHighlight()\n      // 添加鼠标事件监听\n      this.chart.on('mouseover', () => {\n        this.stopAutoHighlight()\n      })\n      this.chart.on('mouseout', () => {\n        this.startAutoHighlight()\n      })\n    },\n\n    // 开始自动高亮效果\n    startAutoHighlight () {\n      if (this.chartData.length === 0) return\n\n      this.autoHighlightTimer = setInterval(() => {\n        // 取消当前高亮和tooltip\n        if (this.currentHighlightIndex >= 0) {\n          this.chart.dispatchAction({\n            type: 'downplay',\n            seriesIndex: 0,\n            dataIndex: this.currentHighlightIndex\n          })\n          // 隐藏tooltip\n          this.chart.dispatchAction({\n            type: 'hideTip'\n          })\n        }\n\n        // 高亮下一个数据项\n        this.currentHighlightIndex = (this.currentHighlightIndex + 1) % this.chartData.length\n        this.chart.dispatchAction({\n          type: 'highlight',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n\n        // 显示tooltip\n        this.chart.dispatchAction({\n          type: 'showTip',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n      }, 2000) // 每2秒切换一次\n    },\n\n    // 停止自动高亮效果\n    stopAutoHighlight () {\n      if (this.autoHighlightTimer) {\n        clearInterval(this.autoHighlightTimer)\n        this.autoHighlightTimer = null\n      }\n      // 取消所有高亮\n      if (this.chart && this.currentHighlightIndex >= 0) {\n        this.chart.dispatchAction({\n          type: 'downplay',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n        this.currentHighlightIndex = -1\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pie-chart {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n</style>\n"]}]}