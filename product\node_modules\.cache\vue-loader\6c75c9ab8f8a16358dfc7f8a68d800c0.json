{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu\\zy-menu.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu\\zy-menu.vue", "mtime": 1752541693540}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["zy-menu.vue"], "names": [], "mappings": ";AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-menu.vue", "sourceRoot": "src/components/zy-menu", "sourcesContent": ["<template>\r\n  <el-scrollbar class=\"zy-menu\">\r\n    <el-menu\r\n      :default-active=\"key\"\r\n      :background-color=\"backgroundColor\"\r\n      :text-color=\"textColor\"\r\n      :active-text-color=\"activeTextColor\"\r\n      @select=\"select\"\r\n    >\r\n      <zy-menu-children\r\n        :menu=\"menu\"\r\n        :value=\"key\"\r\n        :props=\"props\"\r\n      ></zy-menu-children>\r\n    </el-menu>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nimport zyMenuChildren from './zy-menu-children'\r\nexport default {\r\n  name: 'zyMenu',\r\n  data () {\r\n    return {\r\n      key: ''\r\n    }\r\n  },\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    menu: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    textColor: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    backgroundColor: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    activeTextColor: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label',\r\n          id: 'id',\r\n          to: 'to',\r\n          icon: 'iconUrl',\r\n          isShow: 'isShow',\r\n          showValue: true\r\n        }\r\n      }\r\n    }\r\n  },\r\n  emits: ['select'],\r\n  components: {\r\n    zyMenuChildren\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.key = val\r\n        this.selectData(this.menu, val)\r\n      }\r\n    }\r\n  },\r\n  mounted () {\r\n    this.key = this.value\r\n    this.selectData(this.menu, this.value)\r\n  },\r\n  methods: {\r\n    select (key) {\r\n      this.key = key\r\n      this.$emit('input', key)\r\n      this.selectData(this.menu, key)\r\n    },\r\n    selectData (data, id) {\r\n      data.forEach(item => {\r\n        if (item[this.props.children].length === 0) {\r\n          if (item[this.props.id] === id) {\r\n            this.$emit('select', item)\r\n          }\r\n        } else {\r\n          this.selectData(item[this.props.children], id)\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './zy-menu.scss';\r\n</style>\r\n"]}]}