{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\UEditor\\UEditor.vue?vue&type=style&index=0&id=6eb76256&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\UEditor\\UEditor.vue", "mtime": 1752509077000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouaGVsbG8gew0KICBvdmVyZmxvdzogaGlkZGVuOw0KDQogIC5lZHVpLWVkaXRvciB7DQogICAgYm9yZGVyLXJhZGl1czogMCAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLmVkdWktZGVmYXVsdCB7DQogICAgLy8gei1pbmRleDogMTAwMDAgIWltcG9ydGFudDsgLyog5L2g5Y+v5Lul5qC55o2u6ZyA6KaB6K6+572u5LiN5ZCM55qE5bGC57qn5YC8ICovDQogIH0NCg0KICAuaGVsbG9UZXh0IHsNCiAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgIGJvcmRlci10b3A6IDA7DQogICAgbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIGZvbnQtZmFtaWx5OiBBcmlhbCwgSGVsdmV0aWNhLCBUYWhvbWEsIFZlcmRhbmEsIFNhbnMtU2VyaWY7DQogICAgdGV4dC1hbGlnbjogcmlnaHQ7DQogICAgY29sb3I6ICNhYWE7DQogICAgYm9yZGVyOiAxcHggc29saWQgI2NjYzsNCiAgICBib3JkZXItdG9wOiAwOw0KICAgIHBhZGRpbmctcmlnaHQ6IDZweDsNCiAgfQ0KDQogIGgxLA0KICBoMiB7DQogICAgZm9udC13ZWlnaHQ6IG5vcm1hbDsNCiAgfQ0KDQogIHVsIHsNCiAgICBsaXN0LXN0eWxlLXR5cGU6IG5vbmU7DQogICAgcGFkZGluZzogMDsNCiAgfQ0KDQogIGxpIHsNCiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgbWFyZ2luOiAwIDEwcHg7DQogIH0NCg0KICBhIHsNCiAgICBjb2xvcjogIzQyYjk4MzsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["UEditor.vue"], "names": [], "mappings": ";AAyJA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "UEditor.vue", "sourceRoot": "src/components/UEditor", "sourcesContent": ["<template>\r\n  <div class=\"hello\">\r\n    <vue-ueditor-wrap v-model=\"model\" @ready=\"ready\" :config=\"myConfig\"></vue-ueditor-wrap>\r\n    <div class=\"helloText\" v-if=\"maximumWords < length\">当前已输入{{ length }}个字符，您最多可以输入{{ maximumWords\r\n    }}个字符，当前已经超出{{ length - maximumWords }}个字符。\r\n    </div>\r\n    <div class=\"helloText\" v-if=\"maximumWords > length\">当前已输入{{ length }}个字符，您还可以输入{{ maximumWords - length\r\n    }}个字符。</div>\r\n    <div class=\"helloText\" v-if=\"!maximumWords\">当前已输入{{ length }}个字符</div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport VueUeditorWrap from './vue-ueditor-wrap.min.js'\r\nexport default {\r\n  name: 'UEditor',\r\n  components: {\r\n    VueUeditorWrap // eslint-disable-line\r\n  },\r\n  data () {\r\n    return {\r\n      length: 0,\r\n      myConfig: {\r\n        // 是否跟随内容撑开\r\n        autoHeightEnabled: false,\r\n        elementPathEnabled: false,\r\n        wordCount: false,\r\n        // 高度\r\n        initialFrameHeight: this.height,\r\n        // 宽度\r\n        initialFrameWidth: '100%',\r\n        // 图片上传的路径\r\n        serverUrl: `${this.$api.general.baseURL()}/ueditor/exec`,\r\n        // serverUrl: `http://*************/lzt/ueditor/exec`,\r\n        // 资源依赖的路径\r\n        UEDITOR_HOME_URL: './UEditor/',\r\n        // 鼠标右键的功能，但是一般不用编辑器带的功能，因为编辑器自带的不能右键复制，所以这个变量设置为空数组\r\n        contextMenu: [],\r\n        lineheight: [1, 1.25, 1.5, 1.75, 2, 2.25, 2.5, 2.75, 3, 3.25, 3.5, 3.75, 4],\r\n        autotypeset: {\r\n          mergeEmptyline: true, // 合并空行\r\n          removeClass: true, // 去掉冗余的class\r\n          removeEmptyline: false, // 去掉空行\r\n          textAlign: 'left', // 段落的排版方式，可以是 left,right,center,justify 去掉这个属性表示不执行排版\r\n          imageBlockLine: 'center', // 图片的浮动方式，独占一行剧中,左右浮动，默认: center,left,right,none 去掉这个属性表示不执行排版\r\n          pasteFilter: false, // 根据规则过滤没事粘贴进来的内容\r\n          clearFontSize: false, // 去掉所有的内嵌字号，使用编辑器默认的字号\r\n          clearFontFamily: false, // 去掉所有的内嵌字体，使用编辑器默认的字体\r\n          removeEmptyNode: false, // 去掉空节点\r\n          // 可以去掉的标签\r\n          removeTagNames: { 标签名字: 1 },\r\n          indent: true, // 行首缩进\r\n          indentValue: '2em', // 行首缩进的大小\r\n          bdc2sb: false,\r\n          tobdc: false\r\n        },\r\n        fontfamily: [\r\n          { label: '', name: 'songti', val: '宋体, SimSun' },\r\n          { label: '仿宋', name: 'fangsong', val: '仿宋, FangSong' },\r\n          { label: '仿宋_GB2312', name: 'fangsong', val: '仿宋_GB2312, 仿宋, FangSong' },\r\n          { label: '方正小标宋_GBK', name: '方正小标宋_GBK', val: '方正小标宋_GBK, 宋体, SimSun' },\r\n          { label: '方正仿宋_GBK', name: '方正仿宋_GBK', val: '方正仿宋_GBK, 仿宋, FangSong' },\r\n          { label: '方正楷体_GBK', name: '方正楷体_GBK', val: '方正楷体_GBK, 楷体, SimKai' },\r\n          { label: '方正黑体_GBK', name: '方正黑体_GBK', val: '方正黑体_GBK, 黑体, SimHei' },\r\n          { label: '', name: 'kaiti', val: '楷体, 楷体_GB2312, SimKai' },\r\n          { label: '', name: 'yahei', val: '微软雅黑, Microsoft YaHei' },\r\n          { label: '', name: 'heiti', val: '黑体, SimHei' },\r\n          { label: '', name: 'lishu', val: '隶书, SimLi' },\r\n          { label: '', name: 'andaleMono', val: 'andale mono' },\r\n          { label: '', name: 'arial', val: 'arial, helvetica,sans-serif' },\r\n          { label: '', name: 'arialBlack', val: 'arial black,avant garde' },\r\n          { label: '', name: 'comicSansMs', val: 'comic sans ms' },\r\n          { label: '', name: 'impact', val: 'impact,chicago' },\r\n          { label: '', name: 'timesNewRoman', val: 'times new roman' }\r\n        ],\r\n        toolbars: this.toolbars,\r\n        zIndex: 999\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    model: {\r\n      get () {\r\n        return this.value\r\n      },\r\n      set (value) {\r\n        this.length = value ? value.replace(/<.*?>/g, '').replace(/&nbsp;/ig, ' ').length : 0\r\n        this.$emit('input', value)\r\n      }\r\n    }\r\n  },\r\n  props: {\r\n    type: [String],\r\n    value: {\r\n      type: String\r\n    },\r\n    maximumWords: {\r\n      type: Number\r\n    },\r\n    height: {\r\n      type: Number,\r\n      default: 280\r\n    },\r\n    toolbars: {\r\n      type: Array,\r\n      default: () => [\r\n        [\r\n          'fullscreen',\r\n          'source', '|', 'undo', 'redo', '|',\r\n          'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',\r\n          'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',\r\n          'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',\r\n          'directionalityltr', 'directionalityrtl', 'indent', '|',\r\n          'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'touppercase', 'tolowercase', '|',\r\n          'link', 'unlink', 'anchor', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',\r\n          'simpleupload', 'insertimage', 'emotion', 'scrawl',\r\n          'insertvideo',\r\n          'music', 'attachment', 'map', 'gmap', 'insertframe', 'insertcode', 'webapp', 'pagebreak', 'template', 'background', '|',\r\n          'horizontal', 'date', 'time', 'spechars', 'snapscreen', 'wordimage', '|',\r\n          'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts', '|',\r\n          'print', 'preview', 'searchreplace', 'drafts', 'help'\r\n        ]\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    blur () {\r\n      this.$emit('blur')\r\n    },\r\n    ready (editor) {\r\n      editor.addListener('blur', this.blur)\r\n      if (this.type === 'ta') {\r\n        editor.ready(() => {\r\n          editor.execCommand('fontfamily', 'CESI仿宋-GB2312') // 字体\r\n          editor.execCommand('lineheight', 2) // 行间距\r\n          editor.execCommand('fontsize', '21px') // 字号\r\n        })\r\n      } else {\r\n        editor.ready(() => {\r\n          editor.execCommand('fontfamily', '宋体') // 字体\r\n          editor.execCommand('lineheight', 2) // 行间距\r\n        })\r\n      }\r\n      this.editor = editor\r\n    },\r\n    down () {\r\n      this.editor.ui.setFullScreen(false)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.hello {\r\n  overflow: hidden;\r\n\r\n  .edui-editor {\r\n    border-radius: 0 !important;\r\n  }\r\n\r\n  .edui-default {\r\n    // z-index: 10000 !important; /* 你可以根据需要设置不同的层级值 */\r\n  }\r\n\r\n  .helloText {\r\n    white-space: nowrap;\r\n    border-top: 0;\r\n    line-height: 24px;\r\n    font-size: 14px;\r\n    font-family: Arial, Helvetica, Tahoma, Verdana, Sans-Serif;\r\n    text-align: right;\r\n    color: #aaa;\r\n    border: 1px solid #ccc;\r\n    border-top: 0;\r\n    padding-right: 6px;\r\n  }\r\n\r\n  h1,\r\n  h2 {\r\n    font-weight: normal;\r\n  }\r\n\r\n  ul {\r\n    list-style-type: none;\r\n    padding: 0;\r\n  }\r\n\r\n  li {\r\n    display: inline-block;\r\n    margin: 0 10px;\r\n  }\r\n\r\n  a {\r\n    color: #42b983;\r\n  }\r\n}\r\n</style>\r\n"]}]}