{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecord\\MonthlyWorkRecord.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecord\\MonthlyWorkRecord.vue", "mtime": 1752541693843}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MonthlyWorkRecord.vue"], "names": [], "mappings": ";AAyLA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA", "file": "MonthlyWorkRecord.vue", "sourceRoot": "src/views/AssessmentOrgan/MonthlyWorkRecord", "sourcesContent": ["<template>\r\n  <!-- // 机关考核-平时考核-月工作纪实 -->\r\n  <div class=\"MonthlyWorkRecord\">\r\n    <!-- search-box 是一整个搜索框 内部放一些关键字、时间查询等组件 -->\r\n    <search-box @search-click=\"search\"\r\n                @reset-click=\"reset\"\r\n                title=\"月工作纪实筛选\">\r\n      <!--关键字查找框  -->\r\n      <zy-widget label=\"关键字\">\r\n        <el-input placeholder=\"请输入关键词\"\r\n                  v-model=\"searchParams.keyword\"\r\n                  clearable\r\n                  @keyup.enter.native=\"search\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\"></div>\r\n        </el-input>\r\n      </zy-widget>\r\n      <!-- 时间查询 -->\r\n      <zy-widget label=\"时间查询\">\r\n        <!--  下拉列表中 选中年份功能  -->\r\n        <el-select v-model=\"selectedYear\"\r\n                   placeholder=\"请选择年份\"\r\n                   @keyup.enter.native=\"search\">\r\n          <el-option v-for=\"item in timeArr\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n\r\n      <!-- 部门查询(用到机构树)  -->\r\n      <zy-widget label=\"部门查询\"\r\n                 v-permissions=\"'auth:MonthlyWorkRecord:department'\">\r\n        <zy-select v-model=\"searchParams.officeId\"\r\n                   clearable\r\n                   @keyup.enter.native=\"search\"\r\n                   placeholder=\"请选择部门\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\">\r\n          </div>\r\n        </zy-select>\r\n      </zy-widget>\r\n\r\n      <!-- 审核状态查询 -->\r\n      <zy-widget label=\"审核状态查询\">\r\n        <el-select v-model=\"searchParams.auditStatusParams\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择审核状态\">\r\n          <el-option v-for=\"item in auditStatusData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n\r\n      <!-- <el-select v-model=\"form.activityTypeName\"\r\n                 filterable\r\n                 clearable\r\n                 placeholder=\"请选择类型\">\r\n        <el-option v-for=\"item in classifyData\"\r\n                   :key=\"item.id\"\r\n                   :label=\"item.value\"\r\n                   :value=\"item.id\">\r\n        </el-option>\r\n      </el-select> -->\r\n\r\n    </search-box>\r\n\r\n    <div class=\"qd-list-wrap\">\r\n      <!-- 新增/删除 按钮 -->\r\n      <div class=\"qd-btn-box\">\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-plus\"\r\n                   @click=\"handleAdd\">新增\r\n        </el-button>\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   v-permissions=\"'auth:MonthlyWorkRecord:checkPass'\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n        <el-button type=\"danger\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   v-permissions=\"'auth:MonthlyWorkRecord:checkNotPass'\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n        <!-- <el-button type=\"danger\"\r\n                   icon=\"el-icon-delete\"\r\n                   plain\r\n                   @click=\"handleBatchDelete\">删除\r\n        </el-button> -->\r\n      </div>\r\n\r\n      <!--          tableData           -->\r\n      <div class=\"tableData\">\r\n        <zy-table>\r\n          <el-table :data=\"tableData\"\r\n                    slot=\"zytable\"\r\n                    row-key=\"id\"\r\n                    ref=\"multipleTable\"\r\n                    @select=\"selected\"\r\n                    @select-all=\"selectedAll\"\r\n                    :tree-props=\"{children: 'children'}\">\r\n\r\n            <!-- 多选框 -->\r\n            <el-table-column type=\"selection\"\r\n                             width=\"55\">\r\n            </el-table-column>\r\n            <!-- show-overflow-tooltip 实现表格列内容过长显示提示 -->\r\n            <el-table-column label=\"标题\"\r\n                             show-overflow-tooltip\r\n                             prop=\"title\"\r\n                             min-width=\"300px\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"modify(scope.row)\"\r\n                           size=\"small\"> {{scope.row.title}}\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"部门\"\r\n                             width=\"350px\"\r\n                             prop=\"officeName\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{scope.row.officeName }} </div>\r\n              </template>\r\n\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"发布时间\"\r\n                             width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{$format(scope.row.publishTime).substr(0,16)}} </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"审核状态\"\r\n                             width=\"140\"\r\n                             prop=\"auditStatusName\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"操作\"\r\n                             width=\"150\"\r\n                             fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"editClick(scope.row)\"\r\n                           size=\"small\"\r\n                           :disabled=\"scope.row.auditStatusName == '审核通过'\"> 编辑\r\n                </el-button>\r\n\r\n                <el-button type=\"text\"\r\n                           size=\"small\"\r\n                           @click=\"handleDelete(scope.row.id,scope.row.auditStatus)\"\r\n                           :class=\"scope.row.auditStatusName == '审核通过' ? '' : 'delBtn'\"\r\n                           :disabled=\"scope.row.auditStatusName == '审核通过'\"> 删除\r\n                </el-button>\r\n\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </zy-table>\r\n      </div>\r\n      <div class=\"paging_box\">\r\n        <el-pagination @size-change=\"handleSizeChange\"\r\n                       @current-change=\"handleCurrentChange\"\r\n                       :current-page.sync=\"pageNo\"\r\n                       :page-sizes=\"[10, 20, 30, 40]\"\r\n                       :page-size.sync=\"pageSize\"\r\n                       background\r\n                       layout=\"total, prev, pager, next, sizes, jumper\"\r\n                       :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport tableData from '@mixins/tableData'\r\n\r\nexport default {\r\n  name: 'MonthlyWorkRecord',\r\n  mixins: [tableData],\r\n\r\n  data () {\r\n    return {\r\n\r\n      officeData: [], // 机构树,\r\n      auditStatusData: [], // 审核状态\r\n      searchParams: {\r\n        keyword: '',\r\n        officeId: '',\r\n        auditStatusParams: ''\r\n      },\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      total: 10,\r\n      // id: null,\r\n      tableData: [],\r\n      selectData: [],\r\n      selectObj: [],\r\n      auditStatus: '',\r\n\r\n      publishTime: '',\r\n      selectedYear: '',\r\n\r\n      timeArr: [],\r\n      permissionsArr: []\r\n    }\r\n  },\r\n  mounted () {\r\n    // 出现页面时先调用一次 (展示内容页面)\r\n    this.getMonthlyWorkRecordlist()\r\n    this.initTime()\r\n\r\n    this.treeList()\r\n    this.dictionaryPubkvs()\r\n\r\n    const mune = JSON.parse(sessionStorage.getItem('menuChild'))\r\n    const path = this.$route.path\r\n    this.getPa(mune, path)\r\n  },\r\n  methods: {\r\n\r\n    getPa (data, path) {\r\n      data.forEach(item => {\r\n        if (item.children) {\r\n          this.getPa(item.children, path)\r\n        }\r\n        if (item.to === path) {\r\n          this.permissionsArr = item.permissions\r\n        }\r\n      })\r\n    },\r\n\r\n    /**\r\n      *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluate_audit_status'\r\n      })\r\n      var { data } = res\r\n      this.auditStatusData = data.evaluate_audit_status\r\n    },\r\n\r\n    /**\r\n      *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n\r\n    // 删除按钮方法  批量删除\r\n    handleDelete (ids) { // TODO: handleDelete 和 handleBatchDelete 1.组合使用为新增/删除栏的删除功能 2.单独使用handleDelete则为操作项的删除功能\r\n      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$api.AssessmentOrgan.reqDel({ ids }).then((res) => {\r\n          if (res.errcode === 200) {\r\n            this.getMonthlyWorkRecordlist()// 删除后更新页面\r\n            this.$message.success('删除成功')\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('取消删除')\r\n        // this.getMonthlyWorkRecordlist()\r\n        return false\r\n      })\r\n    },\r\n    handleBatchDelete () {\r\n      if (this.choose.length === 0) {\r\n        return this.$message.warning('请选择想要删除的项')\r\n      }\r\n      this.handleDelete(this.choose.join(','))\r\n    },\r\n\r\n    // 请求后台数据 获取列表信息\r\n    async getMonthlyWorkRecordlist () {\r\n      const res = await this.$api.AssessmentOrgan.getMonthlyWorkRecord({\r\n        pageNo: this.pageNo,\r\n        pageSize: this.pageSize,\r\n        keyword: this.searchParams.keyword,\r\n        // sedateId: this.sedateId\r\n        sedateId: this.selectedYear,\r\n\r\n        officeId: this.searchParams.officeId, // 部门查询\r\n        auditStatus: this.searchParams.auditStatusParams // 审核状态\r\n\r\n      })\r\n      var { data, total } = res\r\n\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n      this.selectObj = []\r\n      this.selectData = []\r\n    },\r\n    modify (row) {\r\n      this.newTab({\r\n        name: '月工作纪实详情',\r\n        menuId: '12300565232',\r\n        to: '/detailsContents',\r\n        params: {\r\n          rowId: row.id,\r\n\r\n          approve: this.permissionsArr.includes('auth:MonthlyWorkRecord:checkPass'),\r\n          noApprove: this.permissionsArr.includes('auth:MonthlyWorkRecord:checkNotPass')\r\n\r\n        }\r\n      })\r\n    },\r\n    editClick (row) {\r\n      this.newTab({ name: '月工作纪实编辑', menuId: '1', to: '/MonthlyWorkRecordAdd', params: { id: row.id } })\r\n    },\r\n    // 请求后台数据 初始化时间 在时间查询下拉框内\r\n    async initTime () {\r\n      const res = await this.$api.AssessmentOrgan.getMonthlyWorkTime()\r\n      var { data } = res\r\n      this.timeArr = data\r\n    },\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckMonthlyWork(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 审核 (加分项)\r\n    async getCheckMonthlyWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckMonthlyWork({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getMonthlyWorkRecordlist()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n\r\n    select (selection, row) {\r\n      var arr = this.selectData\r\n      if (this.selectObj[row.id]) {\r\n        arr.forEach((item, index) => {\r\n          if (item === row.id) {\r\n            arr.splice(index, 1)\r\n          }\r\n        })\r\n        delete this.selectObj[row.id]\r\n      } else {\r\n        this.selectObj[row.id] = row.id\r\n        arr.push(row.id)\r\n        this.selectData = arr\r\n      }\r\n    },\r\n    selectAll (selection) {\r\n      this.selectData = []\r\n      this.selectObj = []\r\n      if (selection.length) {\r\n        selection.forEach((item, index) => {\r\n          this.selectObj[item.id] = item.id\r\n          this.selectData.push(item.id)\r\n        })\r\n      }\r\n    },\r\n\r\n    // 新增按钮方法\r\n    handleAdd () {\r\n      const mid = new Date().getTime().toString()\r\n      this.newTab({\r\n        name: '新建月工作纪实',\r\n        menuId: mid,\r\n        to: '/MonthlyWorkRecordAdd',\r\n        params: { mid, titleId: 1 }\r\n      })\r\n    },\r\n\r\n    // paging_box 里面用到的方法\r\n    handleSizeChange () {\r\n      this.getMonthlyWorkRecordlist()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getMonthlyWorkRecordlist()\r\n    },\r\n    // 一整个搜索框内 查询按钮 的逻辑search\r\n    search () {\r\n      this.pageNo = 1\r\n      this.getMonthlyWorkRecordlist()\r\n    },\r\n    // 一整个搜索框内 重置按钮 逻辑\r\n    reset () {\r\n      this.adoptId = ''\r\n      this.approvalIds = ''\r\n      this.searchParams.keyword = ''\r\n      this.largeClass = ''\r\n      this.selectedYear = ''\r\n\r\n      this.searchParams.officeId = ''\r\n      this.searchParams.auditStatusParams = ''\r\n\r\n      this.getMonthlyWorkRecordlist()\r\n    }\r\n\r\n  },\r\n  inject: ['newTab'],\r\n  computed: {\r\n    // ...mapGetters(['conversion', 'permissions'])\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.MonthlyWorkRecord {\r\n  height: 100%;\r\n  width: 100%;\r\n  .tableData {\r\n    .el-scrollbar__wrap .el-scrollbar__view th {\r\n      background: #f5f7fb;\r\n    }\r\n    height: calc(100% - 150px);\r\n  }\r\n  .button-box {\r\n    background: #fff;\r\n    margin-top: 20px;\r\n  }\r\n  .button-box .el-button {\r\n    background: #fff;\r\n  }\r\n  .el-button--danger.is-plain:hover,\r\n  .el-button--danger.is-plain:focus {\r\n    // color: #f56c6c;\r\n    background: #f56c6c;\r\n    color: #fff;\r\n  }\r\n  .el-button--success.is-plain:hover,\r\n  .el-button--success.is-plain:focus {\r\n    background: #67c23a;\r\n    color: #fff;\r\n  }\r\n  .delBtn {\r\n    color: #f56c6c;\r\n  }\r\n  .paging_box {\r\n    background: #fff;\r\n  }\r\n  .scoreEdit {\r\n    width: 700px;\r\n    height: 100%;\r\n    padding: 24px;\r\n    .form-button {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n  .search-box {\r\n    //月工作实际title文字样式\r\n    .search-title {\r\n      width: 115px;\r\n      height: 16px;\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-left: 32px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}