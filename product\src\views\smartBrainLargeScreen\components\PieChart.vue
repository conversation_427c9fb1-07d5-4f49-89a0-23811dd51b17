<template>
  <div :id="id" class="pie-chart"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'PieChart',
  props: {
    id: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    chartData: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  data () {
    return {
      chart: null,
      autoHighlightTimer: null, // 自动高亮定时器
      currentHighlightIndex: -1, // 当前高亮的数据项索引
      // 预定义的颜色数组，按顺序分配给数据项
      colors: [
        '#4FC3F7', '#FFF67C', '#66BB6A', '#FFA726',
        '#FF7043', '#AB47BC', '#5C6BC0', '#42A5F5',
        '#FFCA28', '#4CAF50', '#EF5350', '#8D6E63',
        '#9C27B0', '#3F51B5', '#2196F3', '#00BCD4',
        '#FF9800', '#795548', '#607D8B', '#E91E63'
      ]
    }
  },
  mounted () {
    this.initChart()
  },
  beforeDestroy () {
    // 清除自动高亮定时器
    if (this.autoHighlightTimer) {
      clearInterval(this.autoHighlightTimer)
    }
    if (this.chart) {
      this.chart.dispose()
    }
  },
  methods: {
    getColor (index, item) {
      // 如果数据项中有颜色信息，优先使用数据中的颜色
      if (item && item.color) {
        return item.color
      }
      // 否则使用预定义的颜色数组
      return this.colors[index % this.colors.length]
    },
    initChart () {
      const chartContainer = document.getElementById(this.id)
      if (!chartContainer) return
      this.chart = echarts.init(chartContainer)
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00d4ff',
          borderWidth: 1,
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          orient: this.id === 'category_distribution' ? 'horizontal' : 'vertical',
          right: this.id === 'reply-type-pie' ? '0%' : this.id === 'category_distribution' ? null : this.id === 'proposal-statistics' ? '0%' : '5%',
          left: this.id === 'category_distribution' ? 'center' : null,
          top: this.id === 'reply-type-pie' ? '38%' : this.id === 'category_distribution' ? null : 'center',
          bottom: this.id === 'category_distribution' ? 20 : null,
          itemWidth: this.id === 'reply-type-pie' ? 12 : 5,
          itemHeight: this.id === 'reply-type-pie' ? 6 : 5,
          icon: this.id === 'reply-type-pie' ? null : 'circle',
          itemGap: this.id === 'reply-type-pie' ? 40 : this.id === 'category_distribution' ? 30 : this.id === 'proposal-statistics' ? 12 : 25,
          textStyle: {
            color: '#fff',
            fontSize: 12,
            fontFamily: 'Microsoft YaHei'
          },
          formatter: (name) => {
            const item = this.chartData.find(d => d.name === name)
            if (this.id === 'reply-type-pie') {
              return `${name}`
            } else {
              return `${name}  ${item ? item.value : ''}%`
            }
          }
        },
        series: [
          {
            name: this.name,
            type: 'pie',
            radius: this.id === 'category_distribution' ? ['30%', '45%'] : this.id === 'proposal-statistics' ? ['60%', '85%'] : this.id === 'reply-type-pie' ? ['40%', '70%'] : ['55%', '80%'],
            center: this.id === 'category_distribution' ? ['50%', '25%'] : this.id === 'proposal-statistics' ? ['22%', '50%'] : this.id === 'reply-type-pie' ? ['50%', '50%'] : ['30%', '50%'],
            avoidLabelOverlap: false,
            emphasis: {
              scale: true,
              scaleSize: 10
            },
            label: {
              show: true,
              position: 'center',
              fontSize: 16,
              color: '#fff',
              formatter: this.name
            },
            labelLine: {
              show: false
            },
            itemStyle: {
              borderWidth: 3,
              borderColor: '#07345F' // 用大屏背景色
            },
            data: this.chartData.map((item, index) => ({
              value: item.value,
              name: item.name,
              itemStyle: { color: this.getColor(index, item) }
            }))
          },
          {
            type: 'pie',
            radius: this.id === 'category_distribution' ? ['50%', '51%'] : this.id === 'proposal-statistics' ? ['94%', '95%'] : this.id === 'reply-type-pie' ? ['1%', '1%'] : ['88%', '89%'],
            center: this.id === 'category_distribution' ? ['50%', '25%'] : this.id === 'proposal-statistics' ? ['22%', '50%'] : this.id === 'reply-type-pie' ? ['50%', '50%'] : ['30%', '50%'],
            data: [
              {
                value: 100,
                itemStyle: {
                  color: '#2f689a'
                }
              }
            ],
            label: {
              show: false
            }
          }
        ],
        graphic: [
          {
            type: 'circle',
            left: this.id === 'category_distribution' ? '36.5%' : this.id === 'proposal-statistics' ? '12%' : this.id === 'reply-type-pie' ? '40%' : '17%',
            top: this.id === 'category_distribution' ? '13%' : this.id === 'proposal-statistics' ? '23%' : this.id === 'reply-type-pie' ? '35%' : '26%',
            shape: {
              cx: 0,
              cy: 0,
              r: this.id === 'category_distribution' ? 60 : this.id === 'proposal-statistics' ? 40 : this.id === 'reply-type-pie' ? 0 : 50
            },
            style: {
              fill: 'none',
              lineWidth: this.id === 'category_distribution' ? 4 : 3,
              stroke: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#23E1FF' },
                  { offset: 1, color: 'rgba(35,225,255,0)' }
                ]
              }
            },
            z: 10,
            silent: true,
            position: [0, 0]
          }
        ]
      }
      this.chart.setOption(option)
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        if (this.chart) {
          this.chart.resize()
        }
      })
      // 启动自动高亮效果
      this.startAutoHighlight()
      // 添加鼠标事件监听
      this.chart.on('mouseover', () => {
        this.stopAutoHighlight()
      })
      this.chart.on('mouseout', () => {
        this.startAutoHighlight()
      })
    },

    // 开始自动高亮效果
    startAutoHighlight () {
      if (this.chartData.length === 0) return

      this.autoHighlightTimer = setInterval(() => {
        // 取消当前高亮和tooltip
        if (this.currentHighlightIndex >= 0) {
          this.chart.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: this.currentHighlightIndex
          })
          // 隐藏tooltip
          this.chart.dispatchAction({
            type: 'hideTip'
          })
        }

        // 高亮下一个数据项
        this.currentHighlightIndex = (this.currentHighlightIndex + 1) % this.chartData.length
        this.chart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: this.currentHighlightIndex
        })

        // 显示tooltip
        this.chart.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: this.currentHighlightIndex
        })
      }, 2000) // 每2秒切换一次
    },

    // 停止自动高亮效果
    stopAutoHighlight () {
      if (this.autoHighlightTimer) {
        clearInterval(this.autoHighlightTimer)
        this.autoHighlightTimer = null
      }
      // 取消所有高亮
      if (this.chart && this.currentHighlightIndex >= 0) {
        this.chart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.currentHighlightIndex
        })
        this.currentHighlightIndex = -1
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pie-chart {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
