{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-export\\zy-export.vue?vue&type=style&index=0&id=609e39d5&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-export\\zy-export.vue", "mtime": 1752541693526}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3p5LWV4cG9ydC5zY3NzIjsNCg=="}, {"version": 3, "sources": ["zy-export.vue"], "names": [], "mappings": ";AAoKA", "file": "zy-export.vue", "sourceRoot": "src/components/zy-export", "sourcesContent": ["<template>\r\n  <div class=\"zy-export scrollBar\">\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-position=\"top\"\r\n             class=\"newForm\">\r\n      <el-form-item label=\"导出字段：\"\r\n                    prop=\"field\"\r\n                    class=\"form-title\">\r\n        <el-checkbox-group v-model=\"form.field\">\r\n          <el-checkbox v-for=\"item in field\"\r\n                       :label=\"item.id\"\r\n                       :key=\"item.id\">{{item.key}}</el-checkbox>\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <div class=\"form-button\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n\r\n    <div class=\"progress-box\"\r\n         v-if=\"show\"\r\n         @click.stop>\r\n      <div class=\"progress\">\r\n        <el-progress :percentage=\"percentage\"\r\n                     :color=\"customColor\"></el-progress>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport exportExcel from '@mixins/exportExcel'\r\nexport default {\r\n  name: 'zyExport',\r\n  data () {\r\n    return {\r\n      name: '',\r\n      percentage: 0,\r\n      customColor: '#94070A',\r\n      form: {\r\n        field: []\r\n      },\r\n      rules: {\r\n        field: [\r\n          { required: true, message: '请选择导出Excel的字段', trigger: 'blur' }\r\n        ]\r\n      },\r\n      field: [],\r\n      data: [],\r\n      show: false,\r\n      dataShow: false,\r\n      exporttypes: false\r\n    }\r\n  },\r\n  props: {\r\n    type: [String, Number, Array, Object],\r\n    params: {\r\n      type: Object,\r\n      default: () => {\r\n        return {}\r\n      }\r\n    },\r\n    excelId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  mixins: [exportExcel],\r\n  created () {\r\n    this.exportFields()\r\n    this.exportDatas()\r\n  },\r\n  watch: {\r\n    data (val) {\r\n      if (val.length && this.dataShow) {\r\n        this.exportExcelMethods()\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    async exportFields () {\r\n      const res = await this.$api.general.exportFields({ exportType: this.type })\r\n      res.data.fields.forEach((item, index) => {\r\n        item.id = index\r\n        if (item.checked) {\r\n          this.form.field.push(index)\r\n        }\r\n      })\r\n      this.name = res.data.name\r\n      this.field = res.data.fields\r\n    },\r\n    async exportDatas () {\r\n      var params = {}\r\n      if (!this.excelId) {\r\n        params = this.params\r\n      } else {\r\n        params.ids = this.excelId\r\n      }\r\n      params.exportType = this.type\r\n      const res = await this.$api.general.exportDatas(params)\r\n      this.exporttypes = true\r\n      this.data = res.data\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          this.show = true\r\n          this.setInterval = setInterval(() => {\r\n            if (this.percentage >= 90) {\r\n              clearInterval(this.setInterval)\r\n            } else {\r\n              this.percentage = this.percentage + 9\r\n            }\r\n          }, 200)\r\n          if (this.data.length || this.exporttypes) {\r\n            setTimeout(() => {\r\n              this.exportExcelMethods()\r\n            }, 400)\r\n          } else {\r\n            this.dataShow = true\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: '导出字段不能为空！',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    exportExcelMethods () {\r\n      this.percentage = 100\r\n      var field = []\r\n      this.form.field.forEach(item => {\r\n        field.push(this.fieldMethods(item))\r\n      })\r\n      this.exportExcel(this.name, field, this.data)\r\n      clearInterval(this.setInterval)\r\n      setTimeout(() => {\r\n        this.show = false\r\n        this.exporttypes = false\r\n        this.percentage = 0\r\n      }, 1000)\r\n    },\r\n    fieldMethods (id) {\r\n      var arr = {}\r\n      this.field.forEach(item => {\r\n        if (item.id === id) {\r\n          arr = item\r\n        }\r\n      })\r\n      return arr\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('callback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-export.scss\";\r\n</style>\r\n"]}]}