{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\CircularProgress.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\CircularProgress.vue", "mtime": 1756285769449}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,wBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,YADA;MAEAC,cAFA;MAGAE;IAHA,CALA;IAUAC;MACAJ,YADA;MAEAC,cAFA;MAGAE;IAHA,CAVA;IAeAE;MACAL,YADA;MAEAG;IAFA;EAfA,CAFA;;EAsBAG;IACA;MACAC;IADA;EAGA,CA1BA;;EA2BAC;IACA;EACA,CA7BA;;EA8BAC;IACA;MACA;IACA;;IACAC;EACA,CAnCA;;EAoCAC;IACAT;MACAU;QACA;MACA;;IAHA;EADA,CApCA;EA2CAC;IACAC;MACA;MACA;MACA;MACA;MACAJ;IACA,CAPA;;IASAK;MACA;MACA;QACAC,UACA;QACA;UACAhB,YADA;UAEAiB,cAFA;UAGAC,UAHA;UAIAC,OAJA;UAKAC;YACAC,2BADA;YAEAC,YAFA;YAGAC,kBAHA;YAIAC,YAJA;YAKAC,mCALA;YAMAC,kBANA;YAOAC,oBAPA;YAQAC;UARA;QALA,CAFA,EAkBA;QACA;UACA5B,YADA;UAEAiB,cAFA;UAGAC,UAHA;UAIAC,OAJA;UAKAC;YACAC,gBADA;YAEAC,YAFA;YAGAC,iBAHA;YAIAC,YAJA;YAKAC,mCALA;YAMAC,kBANA;YAOAC,oBAPA;YAQAC;UARA;QALA,CAnBA,CADA;QAqCAC,SACA;UACA7B,WADA;UAEA8B,qBAFA;UAGAC,sBAHA;UAIAC,cAJA;UAKAb,MALA;UAMAb,OACA;YACA2B,sBADA;YAEAC;cACA7B;gBACAL,cADA;gBAEAmC,IAFA;gBAGAC,IAHA;gBAIAC,KAJA;gBAKAC,KALA;gBAMAC,aACA;kBAAAC;kBAAAnC;gBAAA,CADA,EAEA;kBAAAmC;kBAAAnC;gBAAA,CAFA,EAGA;kBAAAmC;kBAAAnC;gBAAA,CAHA,EAIA;kBAAAmC;kBAAAnC;gBAAA,CAJA;cANA,CADA;cAcAoC,cAdA;cAeAC,8BAfA;cAgBAC,gBAhBA;cAiBAC;YAjBA;UAFA,CADA,EAuBA;YACAX,4BADA;YAEAC;cACA7B;gBACAL,cADA;gBAEAmC,MAFA;gBAGAC,MAHA;gBAIAS,MAJA;gBAKAN,aACA;kBAAAC;kBAAAnC;gBAAA,CADA,EAEA;kBAAAmC;kBAAAnC;gBAAA,CAFA,EAGA;kBAAAmC;kBAAAnC;gBAAA,CAHA;cALA;YADA;UAFA,CAvBA,CANA;UA8CAD;YAAA0C;UAAA,CA9CA;UA+CAC;YAAAD;UAAA,CA/CA;UAgDAE,eAhDA;UAiDAC,uBAjDA;UAkDAC;QAlDA,CADA;MArCA;MA4FA;IACA,CAxGA;;IA0GAC;MACA;QACA;MACA;IACA;;EA9GA;AA3CA", "names": ["name", "props", "id", "type", "required", "percentage", "default", "label", "color", "data", "chart", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "window", "watch", "handler", "methods", "initChart", "updateChart", "graphic", "left", "top", "z", "style", "text", "fontSize", "fontWeight", "fill", "textShadowColor", "textShadowBlur", "textShadowOffsetX", "textShadowOffsetY", "series", "radius", "center", "startAngle", "value", "itemStyle", "x", "y", "x2", "y2", "colorStops", "offset", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "shadowOffsetX", "shadowOffsetY", "r", "show", "labelLine", "animation", "animationDuration", "animationEasing", "resizeChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["CircularProgress.vue"], "sourcesContent": ["<template>\n  <div class=\"circular-progress\" :id=\"id\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'CircularProgress',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    percentage: {\n      type: Number,\n      required: true,\n      default: 0\n    },\n    label: {\n      type: String,\n      required: true,\n      default: ''\n    },\n    color: {\n      type: String,\n      default: '#00d4ff'\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  watch: {\n    percentage: {\n      handler () {\n        this.updateChart()\n      }\n    }\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.updateChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n\n    updateChart () {\n      if (!this.chart) return\n      const option = {\n        graphic: [\n          // 百分比数字 - 最高优先级\n          {\n            type: 'text',\n            left: 'center',\n            top: '30%',\n            z: 1000,\n            style: {\n              text: this.percentage + '%',\n              fontSize: 32,\n              fontWeight: 'bold',\n              fill: '#fff',\n              textShadowColor: 'rgba(0, 0, 0, 1)',\n              textShadowBlur: 15,\n              textShadowOffsetX: 3,\n              textShadowOffsetY: 3\n            }\n          },\n          // 标签文字 - 最高优先级\n          {\n            type: 'text',\n            left: 'center',\n            top: '58%',\n            z: 1000,\n            style: {\n              text: this.label,\n              fontSize: 16,\n              fontWeight: '500',\n              fill: '#fff',\n              textShadowColor: 'rgba(0, 0, 0, 1)',\n              textShadowBlur: 12,\n              textShadowOffsetX: 2,\n              textShadowOffsetY: 2\n            }\n          }\n        ],\n        series: [\n          {\n            type: 'pie',\n            radius: ['0%', '75%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            z: 100,\n            data: [\n              {\n                value: this.percentage,\n                itemStyle: {\n                  color: {\n                    type: 'linear',\n                    x: 0,\n                    y: 0,\n                    x2: 1,\n                    y2: 1,\n                    colorStops: [\n                      { offset: 0, color: this.color },\n                      { offset: 0.3, color: this.color + 'E6' },\n                      { offset: 0.7, color: this.color + 'B3' },\n                      { offset: 1, color: this.color + '80' }\n                    ]\n                  },\n                  shadowBlur: 25,\n                  shadowColor: this.color + '80',\n                  shadowOffsetX: 0,\n                  shadowOffsetY: 0\n                }\n              },\n              {\n                value: 100 - this.percentage,\n                itemStyle: {\n                  color: {\n                    type: 'radial',\n                    x: 0.5,\n                    y: 0.5,\n                    r: 0.8,\n                    colorStops: [\n                      { offset: 0, color: 'rgba(255, 255, 255, 0.0)' },\n                      { offset: 0.7, color: 'rgba(255, 255, 255, 0.0)' },\n                      { offset: 1, color: 'rgba(255, 255, 255, 0.0)' }\n                    ]\n                  }\n                }\n              }\n            ],\n            label: { show: false },\n            labelLine: { show: false },\n            animation: true,\n            animationDuration: 2000,\n            animationEasing: 'cubicOut'\n          }\n        ]\n      }\n      this.chart.setOption(option)\n    },\n\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.circular-progress {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 50; // 确保图表容器在背景图上方\n\n  // 添加整体的发光效果\n  &::before {\n    content: '';\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    width: 90%;\n    height: 90%;\n    border-radius: 50%;\n    background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, transparent 70%);\n    z-index: 2; // 发光效果在背景图上方，但在图表下方\n  }\n}\n</style>\n"]}]}