{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-select\\zy-select.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-select\\zy-select.vue", "mtime": 1752541693557}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAgDA;AACA;;AACA;EACA;EACA;IACAA;IACAC;EACA,CAHA;AAIA,CANA;;AAOA;EACAC,gBADA;;EAEAC;IACA;MACAC,SADA;MAEAC,IAFA;MAGAC,cAHA;MAIAC,IAJA;MAKAC,UALA;MAMAC,SANA;MAOAC;IAPA;EASA,CAZA;;EAaAC;IACAC,sCADA;IAEAT;MACAU,WADA;MAEAC;IAFA,CAFA;IAMA;IACAH;MACAE,YADA;MAEAC;QACA;UACAC,oBADA;UAEAC;QAFA;MAIA;IAPA,CAPA;IAgBAC;MACAJ,YADA;MAEAC;IAFA,CAhBA;IAoBA;IACAI;MACAL,aADA;MAEAC;IAFA,CArBA;IAyBA;IACAK;MACAN,aADA;MAEAC;IAFA,CA1BA;IA8BAM;MACAP,YADA;MAEAC;IAFA,CA9BA;IAkCA;IACAO;MACAR,aADA;MAEAC;IAFA;EAnCA,CAbA;EAqDAQ;IACAhB;MACA;QACA;QACA;UACA;UACAiB;YACAC;cACA;gBACAA;cACA,CAFA,MAEA;gBACAA,qCADA,CAEA;cACA;YACA,CAPA;UAQA,CATA;QAUA,CAZA;MAaA,CAfA,MAeA;QACAD;MACA;IACA,CApBA;;IAqBAX;MACA;QACA;MACA,CAFA,MAEA;QACA;QACA;QACA;UACA;QACA,CAFA;MAGA;IACA,CA/BA;;IAgCAT;MACA;IACA,CAlCA;;IAmCAM;MACA;IACA;;EArCA,CArDA;;EA4FAgB;IACA;IACA;EACA,CA/FA;;EAgGAC;IACAC;MACAC;QACA;;QACA;UACA;UACA;QACA;MACA,CANA,EAMA,GANA;IAOA,CATA;;IAUAC;MACAD;QACA;;QACA;UACA;YACA;UACA;;UACA;QACA;;QACA;MACA,CATA,EASA,GATA;IAUA,CArBA;;IAsBAE;MACAF;QACA;MACA,CAFA,EAEA,GAFA;;MAGA;QACA;MACA;;MACA;IACA,CA9BA;;IA+BAG;MACA;MACA;MACA;IACA,CAnCA;;IAoCAC;MACA;QACA;MACA;;MACA;IACA,CAzCA;;IA0CA;IACAC;MACA9B;QACA;UACA;UACA;UACA;YACA;UACA,CAFA;QAGA;;QACA;UACA;QACA;MACA,CAXA;IAYA,CAxDA;;IAyDA+B;MACA;MACA;IACA,CA5DA;;IA6DAC;MACA;QACA;MACA;IACA,CAjEA;;IAkEAC;MACAC;MACA;IACA;;EArEA;AAhGA", "names": ["clearTimeout", "timer", "name", "data", "h", "w", "visible", "i", "show", "input", "inputvalue", "props", "value", "type", "default", "children", "label", "nodeKey", "disabled", "filterable", "placeholder", "clearable", "watch", "erd", "that", "mounted", "methods", "focus", "delay", "blur", "switchClick", "filterNode", "handleNodeClick", "selectedMethods", "empty", "mouseover", "mouseleave", "console"], "sourceRoot": "src/components/zy-select", "sources": ["zy-select.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-tree-select\"\r\n       ref=\"zyTreeSelect\">\r\n    <el-popover :width=\"w\"\r\n                placement=\"bottom\"\r\n                trigger=\"manual\"\r\n                v-model=\"visible\"\r\n                popper-class=\"zy-tree-select-popover\">\r\n      <template slot=\"reference\">\r\n        <el-input :placeholder=\"inputvalue\"\r\n                  :readonly=\"!filterable\"\r\n                  @mouseover.native=\"mouseover\"\r\n                  @mouseleave.native=\"mouseleave\"\r\n                  :disabled=\"disabled\"\r\n                  v-model=\"input\"\r\n                  @focus=\"focus\"\r\n                  @blur=\"blur\"\r\n                  ref=\"input\">\r\n          <template slot=\"suffix\">\r\n            <i v-if=\"show\"\r\n               :class=\"['zy-tree-select-icon','el-icon-arrow-down',visible?'el-icon-arrow-down-a':'']\"></i>\r\n            <i v-if=\"!show\"\r\n               @click=\"empty\"\r\n               class=\"el-icon-circle-close\"></i>\r\n          </template>\r\n        </el-input>\r\n      </template>\r\n      <el-scrollbar class=\"zy-tree-select-body\"\r\n                    :style=\"{height: h}\">\r\n        <div class=\"select-body\"\r\n             ref=\"selectBody\">\r\n          <el-tree ref=\"tree\"\r\n                   :data=\"data\"\r\n                   :props=\"props\"\r\n                   highlight-current\r\n                   :node-key=\"nodeKey\"\r\n                   @node-expand=\"switchClick\"\r\n                   @node-collapse=\"switchClick\"\r\n                   @node-click=\"handleNodeClick\"\r\n                   :filter-node-method=\"filterNode\"\r\n                   :expand-on-click-node=\"false\"></el-tree>\r\n        </div>\r\n      </el-scrollbar>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nconst delay = (function () {\r\n  let timer = 0\r\n  return function (callback, ms) {\r\n    clearTimeout(timer)\r\n    timer = setTimeout(callback, ms)\r\n  }\r\n})()\r\nexport default {\r\n  name: 'zySelect',\r\n  data () {\r\n    return {\r\n      h: 'auto',\r\n      w: 0,\r\n      visible: false,\r\n      i: 0,\r\n      show: true,\r\n      input: '',\r\n      inputvalue: ''\r\n    }\r\n  },\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 是否开启关键字搜索\r\n    filterable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '请选择内容'\r\n    },\r\n    // 是否可以清空\r\n    clearable: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  watch: {\r\n    visible (val) {\r\n      if (val) {\r\n        this.w = this.$refs.zyTreeSelect.offsetWidth\r\n        this.$nextTick(() => {\r\n          const that = this\r\n          erd.listenTo(this.$refs.selectBody, (element) => {\r\n            that.$nextTick(() => {\r\n              if (element.offsetHeight > 260) {\r\n                that.h = '260px'\r\n              } else {\r\n                that.h = element.offsetHeight + 'px'\r\n                // that.h = 'auto'\r\n              }\r\n            })\r\n          })\r\n        })\r\n      } else {\r\n        erd.uninstall(this.$refs.selectBody)\r\n      }\r\n    },\r\n    value (val) {\r\n      if (val) {\r\n        this.selectedMethods(this.data)\r\n      } else {\r\n        this.input = ''\r\n        this.inputvalue = this.placeholder\r\n        this.$nextTick(function () {\r\n          this.$refs.tree.setCurrentKey()\r\n        })\r\n      }\r\n    },\r\n    data () {\r\n      this.selectedMethods(this.data)\r\n    },\r\n    input (val) {\r\n      this.$refs.tree.filter(val)\r\n    }\r\n  },\r\n  mounted () {\r\n    this.selectedMethods(this.data)\r\n    this.inputvalue = this.placeholder\r\n  },\r\n  methods: {\r\n    focus () {\r\n      delay(() => {\r\n        this.visible = true\r\n        if (this.filterable && this.value && this.i === 0) {\r\n          this.inputvalue = this.input\r\n          this.input = ''\r\n        }\r\n      }, 200)\r\n    },\r\n    blur () {\r\n      delay(() => {\r\n        this.visible = false\r\n        if (this.filterable && this.i !== 1) {\r\n          if (this.value && this.inputvalue !== this.placeholder) {\r\n            this.input = this.inputvalue\r\n          }\r\n          this.inputvalue = this.placeholder\r\n        }\r\n        this.i = 0\r\n      }, 200)\r\n    },\r\n    switchClick () {\r\n      delay(() => {\r\n        this.visible = true\r\n      }, 200)\r\n      if (this.filterable) {\r\n        this.i = 2\r\n      }\r\n      this.$refs.input.focus()\r\n    },\r\n    filterNode (value, data) {\r\n      if (!this.filterable) return true\r\n      if (!value) return true\r\n      return data[this.props.label].indexOf(value) !== -1\r\n    },\r\n    handleNodeClick (data) {\r\n      if (this.filterable) {\r\n        this.i = 1\r\n      }\r\n      this.$emit('input', data[this.nodeKey])\r\n    },\r\n    // 首次进来默认选中\r\n    selectedMethods (data) {\r\n      data.forEach(item => {\r\n        if (item[this.nodeKey] === this.value) {\r\n          this.input = item[this.props.label]\r\n          this.$emit('select', item)\r\n          this.$nextTick(function () {\r\n            this.$refs.tree.setCurrentKey(item[this.nodeKey])\r\n          })\r\n        }\r\n        if (item.children && item.children.length > 0) {\r\n          this.selectedMethods(item.children)\r\n        }\r\n      })\r\n    },\r\n    empty () {\r\n      this.i = 1\r\n      this.$emit('input', '')\r\n    },\r\n    mouseover () {\r\n      if (this.value && this.clearable) {\r\n        this.show = false\r\n      }\r\n    },\r\n    mouseleave () {\r\n      console.log(1)\r\n      this.show = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"./zy-tree-select.scss\";\r\n</style>\r\n"]}]}