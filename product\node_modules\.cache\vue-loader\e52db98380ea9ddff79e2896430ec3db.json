{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\candidates-user\\candidates-user.vue?vue&type=style&index=0&id=1e0cdde1&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\candidates-user\\candidates-user.vue", "mtime": 1752541693445}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL2NhbmRpZGF0ZXMtdXNlci5zY3NzIjsNCg=="}, {"version": 3, "sources": ["candidates-user.vue"], "names": [], "mappings": ";AA2bA", "file": "candidates-user.vue", "sourceRoot": "src/components/candidates-user", "sourcesContent": ["<template>\r\n  <div class=\"candidates-user\"\r\n       v-loading=\"loading\"\r\n       element-loading-text=\"拼命加载中\">\r\n    <div class=\"candidates-user-box\">\r\n      <div class=\"candidates-user-content\">\r\n        <div class=\"search-box\">\r\n          <el-input placeholder=\"搜索人员名字\"\r\n                    v-model=\"name\"\r\n                    clearable\r\n                    @keyup.enter.native=\"search\">\r\n            <div slot=\"prefix\"\r\n                 class=\"input-search\"></div>\r\n          </el-input>\r\n        </div>\r\n        <div class=\"user-box\">\r\n          <div class=\"user-tree-box\">\r\n            <div class=\"institutions-text\">选择机构</div>\r\n            <div class=\"user-tree\">\r\n              <!-- <zy-tree :tree=\"tree\"\r\n                       :choiceId.sync=\"choiceval\"\r\n                       @on-choice-click=\"choiceClick\"></zy-tree> -->\r\n              <zy-tree :tree=\"tree\"\r\n                       v-model=\"choiceval\"\r\n                       :props=\"{ children: 'children', label: 'name' }\"\r\n                       @on-tree-click=\"choiceClick\"\r\n                       :anykey=\"defaultUnitShowIds\"></zy-tree>\r\n            </div>\r\n          </div>\r\n          <div class=\"user-personnel-box\">\r\n            <div class=\"personnel-checkbox\">\r\n              <div class=\"personnel-checkbox-text\">人员列表</div>\r\n              <el-checkbox :indeterminate=\"isIndeterminate\"\r\n                           v-model=\"checkAll\"\r\n                           @change=\"handleCheckAllChange\"></el-checkbox>\r\n            </div>\r\n            <div class=\"user-content-box scrollBar\">\r\n              <el-checkbox-group v-model=\"checkedCities\"\r\n                                 @change=\"handleCheckedCitiesChange\">\r\n                <div class=\"user-content\"\r\n                     v-for=\"city in cities\"\r\n                     :key=\"city.userId\">\r\n                  <div class=\"user-content-icon-name\">\r\n                    <div class=\"user-content-icon\"></div>\r\n                    <div class=\"user-content-name el-checkbox__label ellipsis\">\r\n                      {{ city.userName }}\r\n                    </div>\r\n                  </div>\r\n                  <el-checkbox :value=\"city.userId\"\r\n                               :label=\"city.userId\"\r\n                               :disabled=\"maxUser(city.userId)\"></el-checkbox>\r\n                </div>\r\n              </el-checkbox-group>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"selected-user-box\">\r\n        <div class=\"selected-user-number\">\r\n          <div class=\"selected-user-number-text\">\r\n            {{ point }}已选择({{ storageData.length }}人)\r\n          </div>\r\n          <div class=\"selected-user-icon-delete\"\r\n               @click=\"deleteAll\"></div>\r\n        </div>\r\n        <div class=\"selected-user scrollBar\">\r\n          <div class=\"selected-user-content\"\r\n               v-for=\"(item, index) in storageData\"\r\n               :key=\"index\">\r\n            <div class=\"selected-user-icon\">\r\n              <div class=\"selected-user-icon-name\"></div>\r\n            </div>\r\n            <div class=\"selected-user-information\">\r\n              <div class=\"selected-user-name\">\r\n                {{ item.userName || item.name }}\r\n              </div>\r\n              <div class=\"selected-user-text ellipsis\">{{ item.position }}</div>\r\n            </div>\r\n            <div class=\"selected-user-delete\">\r\n              <div class=\"selected-user-icon-delete\"\r\n                   @click=\"deleteclick(item)\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"candidates-user-button\">\r\n      <el-button type=\"primary\"\r\n                 @click=\"submitForm\">确定</el-button>\r\n      <el-button @click=\"resetForm\">取消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport _ from 'lodash'\r\nexport default {\r\n  name: 'candidatesUser',\r\n  data () {\r\n    return {\r\n      choiceval: '',\r\n      tree: [],\r\n      name: '',\r\n      checkAll: false,\r\n      checkedCities: [],\r\n      cities: [],\r\n      isIndeterminate: true,\r\n      storage: {},\r\n      storageData: [],\r\n      selectObj: [],\r\n      loading: false,\r\n      defaultUnitIds: [],\r\n      defaultUnitShowIds: []\r\n    }\r\n  },\r\n  props: {\r\n    point: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    max: {\r\n      type: Number,\r\n      default: 10000\r\n    },\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    flag: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    groupId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    disabled: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    defualtUser: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    defaultUnit: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  created () {\r\n    if (this.data.length) {\r\n      this.default()\r\n    }\r\n    this.pointrees()\r\n  },\r\n  watch: {\r\n    defualtUser (val) {\r\n      if (val.length) {\r\n        this.cities = val\r\n        val.forEach(item => {\r\n          this.checkedCities = []\r\n          this.storageData = []\r\n          this.checkedCities.push(item.userId)\r\n          this.storage[this.choiceval] = item\r\n          this.storageData.push(item)\r\n        })\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    maxUser (id) {\r\n      var show = false\r\n      if (this.storageData.length >= this.max) {\r\n        show = true\r\n      }\r\n      this.storageData.forEach(item => {\r\n        if (item.userId === id) {\r\n          show = false\r\n        }\r\n      })\r\n      this.storageData.forEach(item => {\r\n        if (item.disabled && item.userId === id) {\r\n          show = true\r\n        }\r\n      })\r\n      // this.disabled.forEach(item => {\r\n      //   if (item.userId === id) {\r\n      //     show = true\r\n      //   }\r\n      // })\r\n      return show\r\n    },\r\n    search () {\r\n      this.loading = true\r\n      this.roleChooseusers()\r\n    },\r\n    default () {\r\n      // this.storageData = this.data\r\n      this.data.forEach(item => {\r\n        this.storageData.push(item)\r\n        this.selectObj[item.userId] = item.userId\r\n      })\r\n    },\r\n    async pointrees () {\r\n      const res = await this.$api.general.pointrees(this.point)\r\n      var { data } = res\r\n      this.tree = data\r\n      var that = this\r\n      const treeIdArr = []\r\n      function getArr (arr) {\r\n        that.defaultUnit.forEach(v => {\r\n          arr.forEach(v2 => {\r\n            if (v2.id.indexOf(v) !== -1) {\r\n              treeIdArr.push(v2.id)\r\n            } else {\r\n              getArr(v2.children)\r\n            }\r\n          })\r\n        })\r\n      }\r\n      if (this.defaultUnit.length) {\r\n        this.loading = true\r\n        getArr(this.tree)\r\n        this.defaultUnitIds = _.uniq(treeIdArr)\r\n        this.defaultUnitIds.forEach((v, index) => { that.forRoleChooseusers(v, index) })\r\n        this.getPidList()\r\n      }\r\n    },\r\n    getPidList () {\r\n      var arr = []\r\n      this.defaultUnitIds.forEach(v => {\r\n        arr = arr.concat(this.filterTree(this.tree, v))\r\n      })\r\n      this.getPid(arr)\r\n    },\r\n    getPid (item) {\r\n      item.forEach(v => {\r\n        this.defaultUnitShowIds.push(v.id)\r\n        if (v.children) {\r\n          this.getPid(v.children)\r\n        }\r\n      })\r\n    },\r\n    filterTree (nodes, id) {\r\n      if (!nodes || !nodes.length) return void 0 // eslint-disable-line\r\n      const children = []\r\n      for (let node of nodes) {\r\n        node = Object.assign({}, node)\r\n        const sub = this.filterTree(node.children, id)\r\n        if ((sub && sub.length) || node.id === id) {\r\n          sub && (node.children = sub)\r\n          children.push(node)\r\n        }\r\n      }\r\n      return children.length ? children : void 0 // eslint-disable-line\r\n    },\r\n    choiceClick (item) {\r\n      this.loading = true\r\n      this.roleChooseusers()\r\n    },\r\n    async forRoleChooseusers (val, index) {\r\n      var datas = {\r\n        pointCode: this.point,\r\n        treeId: val,\r\n        keyword: this.name\r\n      }\r\n      const res = await this.$api.general.pointreeUsers(datas)\r\n      var { data } = res\r\n      this.cities = this.cities.concat(data)\r\n      const labeluserUsersData = await this.$api.systemSettings.labeluserUsers({ labelCode: ' 202104', pageNo: 1, pageSize: 99999 })\r\n      data.forEach((item, index) => { // 匹配标签用户与 所展示的列表数据\r\n        labeluserUsersData.data.forEach((v) => {\r\n          if (v.userId === item.userId) {\r\n            this.checkedCities.push(item.userId)\r\n            this.storageData.push(item)\r\n          }\r\n        })\r\n      })\r\n      this.loading = false\r\n      if (index + 1 === this.defaultUnitIds.length) {\r\n        this.choiceval = this.defaultUnitIds[index]\r\n      }\r\n    },\r\n    async roleChooseusers () {\r\n      var datas = {\r\n        pointCode: this.point,\r\n        treeId: this.choiceval,\r\n        keyword: this.name\r\n      }\r\n      const res = await this.$api.general.pointreeUsers(datas)\r\n      var { data } = res\r\n      this.disabled.forEach(item => {\r\n        data = data.filter(tab => tab.userId !== item.userId)\r\n      })\r\n      this.cities = data\r\n      this.loading = false\r\n      if (!this.defaultUnit.length && !this.defualtUser.length) {\r\n        this.memoryChecked()\r\n      }\r\n    },\r\n    handleCheckAllChange (val) {\r\n      var arr = []\r\n      this.cities.forEach(item => {\r\n        arr.push(item.userId)\r\n        if (Object.prototype.hasOwnProperty.call(this.selectObj, item.userId)) {\r\n          if (!val) {\r\n            this.deleteData(item)\r\n          }\r\n          val ? null : delete this.selectObj[item.userId] // eslint-disable-line\r\n        } else {\r\n          this.selectObj[item.userId] = item.userId\r\n          this.pushData(item.userId)\r\n        }\r\n      })\r\n      this.checkedCities = val ? arr : []\r\n      this.storage[this.choiceval] = val ? arr : []\r\n      this.isIndeterminate = false\r\n    },\r\n    handleCheckedCitiesChange (value) {\r\n      if (!this.defaultUnit.length || !this.defualtUser.length) {\r\n        this.storage[this.choiceval] = value\r\n      }\r\n      const checkedCount = value.length\r\n      this.checkAll = checkedCount > this.cities.length || checkedCount === this.cities.length\r\n      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length\r\n      var values = []\r\n      value.forEach(item => {\r\n        values[item] = item\r\n      })\r\n      this.cities.forEach((item) => {\r\n        if (Object.prototype.hasOwnProperty.call(values, item.userId)) {\r\n        } else {\r\n          delete this.selectObj[item.userId]\r\n          this.deleteData(item)\r\n        }\r\n      })\r\n      value.forEach(item => {\r\n        if (Object.prototype.hasOwnProperty.call(this.selectObj, item)) {\r\n        } else {\r\n          this.selectObj[item] = item\r\n          if (!this.defaultUnit.length || !this.defualtUser.length) {\r\n            this.pushData(item)\r\n          }\r\n        }\r\n      })\r\n    },\r\n    deleteAll () {\r\n      this.$confirm('此操作将删除当前已选的所有用户, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        var arr = []\r\n        this.storageData.forEach(item => {\r\n          if (item.disabled) {\r\n            arr.push(item)\r\n          }\r\n        })\r\n        this.storageData = arr\r\n        this.selectObj = []\r\n        arr.forEach(item => {\r\n          this.selectObj[item.userId] = item.userId\r\n        })\r\n        if (arr.length) {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '当前选中用户有部分不能移除'\r\n          })\r\n        }\r\n        this.memoryChecked()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    deleteclick (data) {\r\n      if (data.disabled) {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '当前选中用户不能移除'\r\n        })\r\n        return\r\n      }\r\n      this.deleteData(data)\r\n      delete this.selectObj[data.userId]\r\n      this.memoryChecked()\r\n    },\r\n    deleteData (data) {\r\n      var found = this.data.some(function (item) {\r\n        return item.userId.includes(data.userId)\r\n      })\r\n      if (found) {\r\n        this.deleteApi(data)\r\n      }\r\n      const arr = this.storageData\r\n      arr.forEach((item, index) => {\r\n        if (item.userId === data.userId) {\r\n          arr.splice(index, 1)\r\n        }\r\n      })\r\n      this.storageData = arr\r\n    },\r\n    async deleteApi (a) {\r\n      var params = {\r\n        userId: a.userId,\r\n        groupId: this.groupId\r\n      }\r\n      const res = await this.$api.general.deleteApi(params)\r\n      this.$message({\r\n        message: res.errmsg,\r\n        type: 'success'\r\n      })\r\n    },\r\n    pushData (id) {\r\n      this.cities.forEach((item, index) => {\r\n        if (item.userId === id) {\r\n          this.storageData.push(item)\r\n        }\r\n      })\r\n    },\r\n    memoryChecked () {\r\n      var add = []\r\n      this.cities.forEach((row, index) => {\r\n        if (Object.prototype.hasOwnProperty.call(this.selectObj, row.userId)) {\r\n          add.push(row.userId)\r\n        }\r\n      })\r\n      this.checkedCities = add\r\n      const checkedCount = add.length\r\n      this.checkAll = checkedCount > this.cities.length || checkedCount === this.cities.length\r\n      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length\r\n    },\r\n    submitForm () {\r\n      this.$emit('userCallback', this.storageData, true)\r\n    },\r\n    resetForm () {\r\n      this.$emit('userCallback', this.data, false)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./candidates-user.scss\";\r\n</style>\r\n"]}]}