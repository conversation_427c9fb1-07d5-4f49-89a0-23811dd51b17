{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tabs\\zy-tabs.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tabs\\zy-tabs.vue", "mtime": 1752541693597}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["zy-tabs.vue"], "names": [], "mappings": ";AAsBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-tabs.vue", "sourceRoot": "src/components/zy-tabs", "sourcesContent": ["<template>\r\n  <div class=\"zy-tabs\">\r\n    <div class=\"zy-tabs-left\"\r\n         v-if=\"show&&offset>0||offset!=0\"\r\n         @click.stop=\"tabsLeft\"><i class=\"el-icon-d-arrow-left\"></i></div>\r\n    <div class=\"zy-tabs-right\"\r\n         v-if=\"show&&offset<biggest\"\r\n         @click.stop=\"tabsRight\"><i class=\"el-icon-d-arrow-right\"></i></div>\r\n    <div class=\"zy-tabs-box\">\r\n      <div class=\"zy-tabs-item-list\">\r\n        <div :class=\"['zy-tabs-item',item.class?'zy-tabs-item-active':'']\"\r\n             v-for=\"(item, index) in tabsData\"\r\n             :key=\"index\"\r\n             @click=\"selected(item)\">\r\n          <div class=\"zy-tabs-item-number\">{{item.number}}</div>\r\n          <div class=\"zy-tabs-item-text\">{{item.name}}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyTabs',\r\n  data () {\r\n    return {\r\n      tabsData: [],\r\n      show: false,\r\n      offset: 0,\r\n      biggest: 0,\r\n      screenWidth: document.body.clientWidth,\r\n      timer: false\r\n    }\r\n  },\r\n  props: {\r\n    value: {},\r\n    tabsList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    shift: {\r\n      type: Number,\r\n      default: 168\r\n    },\r\n    props: {\r\n      type: Object,\r\n      default: () => { }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  created () {\r\n    this.tabsCopyData(this.deepCopy(this.tabsList))\r\n  },\r\n  mounted () {\r\n    this.biggestClick()\r\n    // 绑定onresize事件 监听屏幕变化设置宽\r\n    this.$nextTick(() => {\r\n      this.screenWidth = document.body.clientWidth\r\n    })\r\n    window.onresize = () => {\r\n      return (() => {\r\n        window.screenWidth = document.body.clientWidth\r\n        this.screenWidth = window.screenWidth\r\n      })()\r\n    }\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      this.tabsData.forEach(item => {\r\n        if (item.id === val) {\r\n          this.selected(item)\r\n        }\r\n      })\r\n    },\r\n    tabsList (val) {\r\n      this.tabsCopyData(this.deepCopy(this.tabsList))\r\n    },\r\n    tabsData (val) {\r\n      if (val.length) {\r\n        this.tabsData.forEach(item => {\r\n          if (item.id === this.value) {\r\n            this.selected(item)\r\n          }\r\n        })\r\n      }\r\n    },\r\n    screenWidth (val) {\r\n      if (!this.timer) {\r\n        this.screenWidth = val\r\n        this.timer = true\r\n        this.biggestClick()\r\n        setTimeout(() => {\r\n          this.timer = false\r\n        }, 400)\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    selected (data) {\r\n      this.selectedClick(data)\r\n      var arr = this.tabsData\r\n      arr.forEach(item => {\r\n        item.class = false\r\n        if (item.id === data.id) {\r\n          this.$emit('id', item.id)\r\n          item.class = true\r\n        }\r\n      })\r\n      setTimeout(() => {\r\n        this.tabsBox()\r\n      }, 300)\r\n    },\r\n    selectedClick (data) {\r\n      var arr = []\r\n      this.tabsList.forEach((item, index) => {\r\n        if (!JSON.stringify(this.props) == '{}') {// eslint-disable-line\r\n          if (data.id === item[this.props.id]) {\r\n            arr = item\r\n          }\r\n        } else {\r\n          if (data.id === item.id) {\r\n            arr = item\r\n          }\r\n        }\r\n      })\r\n      this.$emit('tabs-click', arr)\r\n    },\r\n    biggestClick () {\r\n      var tabBox = document.querySelector('.zy-tabs-box')\r\n      var itemBox = document.querySelector('.zy-tabs-item-list')\r\n      if (itemBox.offsetWidth > tabBox.offsetWidth) {\r\n        this.show = true\r\n        this.biggest = itemBox.offsetWidth - tabBox.offsetWidth\r\n      } else {\r\n        this.show = false\r\n      }\r\n    },\r\n    tabsLeft () {\r\n      var itemBox = document.querySelector('.zy-tabs-item-list')\r\n      var offset = this.offset - this.shift\r\n      if (this.offset - this.shift <= 0) {\r\n        offset = 0\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      this.offset = offset\r\n    },\r\n    tabsRight () {\r\n      var itemBox = document.querySelector('.zy-tabs-item-list')\r\n      var offset = this.offset + this.shift\r\n      if (this.biggest < this.offset + this.shift) {\r\n        offset = this.biggest\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      this.offset = offset\r\n    },\r\n    tabsBox () {\r\n      var tabBox = document.querySelector('.zy-tabs-box')\r\n      var itemBox = document.querySelector('.zy-tabs-item-list')\r\n      var item = document.querySelector('.zy-tabs-item-active')\r\n      if (tabBox.offsetWidth < itemBox.offsetWidth) {\r\n        itemBox.style.transform = 'translateX(0px)'\r\n        itemBox.style.transitionDuration = '.4s'\r\n        if (itemBox.offsetWidth === item.offsetLeft + item.offsetWidth) {\r\n          this.offset = itemBox.offsetWidth - tabBox.offsetWidth\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`\r\n        } else if (tabBox.offsetWidth / 2 > itemBox.offsetWidth - item.offsetLeft) {\r\n          this.offset = itemBox.offsetWidth - tabBox.offsetWidth\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`\r\n        } else {\r\n          if (item.offsetLeft - tabBox.offsetWidth / 2 < 0) {\r\n            this.offset = 0\r\n          } else {\r\n            this.offset = item.offsetLeft - tabBox.offsetWidth / 2\r\n          }\r\n          itemBox.style.transform = `translateX(-${item.offsetLeft - tabBox.offsetWidth / 2}px)`\r\n        }\r\n      }\r\n    },\r\n    tabsCopyData (data) {\r\n      this.initData(data)\r\n      this.tabsData = data\r\n    },\r\n    initData (items) {\r\n      items.forEach((item, index) => {\r\n        if ((typeof item.id) === 'undefined') { // eslint-disable-line\r\n          item.id = item[this.props.id]\r\n        }\r\n        if ((typeof item.name) === 'undefined') { // eslint-disable-line\r\n          item.name = item[this.props.name]\r\n        }\r\n        if ((typeof item.number) === 'undefined') { // eslint-disable-line\r\n          item.number = item[this.props.number]\r\n        }\r\n        if ((typeof item.class) === 'undefined') { // eslint-disable-line\r\n          item.class = false\r\n        }\r\n        if (this.tabId === item.id) {\r\n          item.class = true\r\n        }\r\n      })\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    }\r\n  },\r\n  beforeDestroy () {\r\n    window.onresize = null\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-tabs.scss\";\r\n</style>\r\n"]}]}