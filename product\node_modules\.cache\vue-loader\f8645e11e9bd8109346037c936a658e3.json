{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-cascader\\zy-cascader.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-cascader\\zy-cascader.vue", "mtime": 1752541693518}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICd6eUNhc2NhZGVyJywNCiAgZGF0YSAoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGlkOiB0aGlzLnZhbHVlLA0KICAgICAgaW5wdXQ6ICcnLA0KICAgICAgc2VsZWN0RGF0YToge30sDQogICAgICBvcHRpb25zX3Nob3c6IGZhbHNlLA0KICAgICAgaW5wdXRUZXh0OiAnJywNCiAgICAgIGRldGVybWluZTogZmFsc2UNCiAgICB9DQogIH0sDQogIHByb3BzOiB7DQogICAgdmFsdWU6IFtTdHJpbmcsIE51bWJlciwgQXJyYXksIE9iamVjdF0sDQogICAgLy8g5pWw5o2uDQogICAgZGF0YTogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgLy8g5qCR57uT5p6E6YWN572uDQogICAgcHJvcHM6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBjaGlsZHJlbjogJ2NoaWxkcmVuJywNCiAgICAgICAgICBsYWJlbDogJ2xhYmVsJw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvLyDop6blj5HmlrnlvI8gY2xpY2svZm9jdXMvaG92ZXIvbWFudWFsDQogICAgdHJpZ2dlcjogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJ2NsaWNrJw0KICAgIH0sDQogICAgcGxhY2Vob2xkZXI6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICfor7fpgInmi6nlhoXlrrknDQogICAgfSwNCiAgICAvLyDmmK/lkKbnpoHnlKgNCiAgICBkaXNhYmxlZDogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICAvLyDlrr3luqYNCiAgICB3aWR0aDogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJzI5NicNCiAgICB9LA0KICAgIG5vZGVLZXk6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICdpZCcNCiAgICB9LA0KICAgIGNoaWxkOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZQ0KICAgIH0NCiAgfSwNCiAgbW9kZWw6IHsNCiAgICBwcm9wOiAndmFsdWUnLA0KICAgIGV2ZW50OiAnaWQnDQogIH0sDQogIGNyZWF0ZWQgKCkgew0KICAgIHRoaXMuaW5wdXRUZXh0ID0gdGhpcy5wbGFjZWhvbGRlcg0KICB9LA0KICB3YXRjaDogew0KICAgIHZhbHVlICh2YWwpIHsNCiAgICAgIGlmICh2YWwpIHsNCiAgICAgICAgdGhpcy5pZCA9IHZhbA0KICAgICAgICB0aGlzLmRldGVybWluZSA9IHRydWUNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuaWQgPSAnJw0KICAgICAgICB0aGlzLmlucHV0ID0gJycNCiAgICAgICAgdGhpcy5pbnB1dFRleHQgPSB0aGlzLnBsYWNlaG9sZGVyDQogICAgICB9DQogICAgfSwNCiAgICBpZCAodmFsKSB7DQogICAgICB0aGlzLiRlbWl0KCdpZCcsIHZhbCkNCiAgICB9LA0KICAgIHNlbGVjdERhdGEgKHZhbCkgew0KICAgICAgdGhpcy4kZW1pdCgnc2VsZWN0JywgdmFsKQ0KICAgIH0sDQogICAgb3B0aW9uc19zaG93ICgpIHsNCiAgICAgIGlmICh0aGlzLmlucHV0ID09PSAnJyAmJiB0aGlzLmlkICYmICF0aGlzLm9wdGlvbnNfc2hvdykgew0KICAgICAgICB0aGlzLmRldGVybWluZSA9IHRydWUNCiAgICAgICAgdGhpcy5pbnB1dCA9IHRoaXMuc2VsZWN0ZWRUZXh0DQogICAgICAgIHRoaXMuaW5wdXRUZXh0ID0gdGhpcy5zZWxlY3RlZFRleHQNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBibHVyICgpIHsNCiAgICAgIGlmICh0aGlzLmlucHV0ID09PSAnJyAmJiB0aGlzLmlkICYmICF0aGlzLm9wdGlvbnNfc2hvdykgew0KICAgICAgICB0aGlzLmRldGVybWluZSA9IHRydWUNCiAgICAgICAgdGhpcy5pbnB1dCA9IHRoaXMuc2VsZWN0ZWRUZXh0DQogICAgICAgIHRoaXMuaW5wdXRUZXh0ID0gdGhpcy5zZWxlY3RlZFRleHQNCiAgICAgIH0NCiAgICB9LA0KICAgIGZvY3VzICgpIHsNCiAgICAgIHRoaXMuZGV0ZXJtaW5lID0gZmFsc2UNCiAgICAgIGlmICh0aGlzLmlucHV0ICYmIHRoaXMuaWQpIHsNCiAgICAgICAgdGhpcy5pbnB1dCA9ICcnDQogICAgICAgIHRoaXMuaW5wdXRUZXh0ID0gdGhpcy5zZWxlY3RlZFRleHQNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOS4i+a<PERSON>ieahhumAieS4reS6i+S7tg0KICAgIHNlbGVjdGVkQ2xpY2sgKGRhdGEpIHsNCiAgICAgIC8vIGlmICh0aGlzLnNlbGVjdGVkVGV4dCA9PT0gZGF0YVt0aGlzLnByb3BzLmxhYmVsXSkgew0KICAgICAgLy8gICByZXR1cm4NCiAgICAgIC8vIH0NCiAgICAgIHRoaXMuaW5wdXQgPSBkYXRhW3RoaXMucHJvcHMubGFiZWxdDQogICAgICB0aGlzLnNlbGVjdERhdGEgPSBkYXRhDQogICAgICB0aGlzLm9wdGlvbnNfc2hvdyA9IGZhbHNlDQogICAgICB0aGlzLmlucHV0VGV4dCA9IGRhdGFbdGhpcy5wcm9wcy5sYWJlbF0NCiAgICAgIHRoaXMuc2VsZWN0ZWRUZXh0ID0gZGF0YVt0aGlzLnByb3BzLmxhYmVsXQ0KICAgICAgdGhpcy5kZXRlcm1pbmUgPSB0cnVlDQogICAgICB0aGlzLiRyZWZzWyd6eS1jYXNjYWRlciddLmZvY3VzKCkNCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLiRyZWZzWyd6eS1jYXNjYWRlciddLmJsdXIoKQ0KICAgICAgfSwgMjIpDQogICAgfSwNCiAgICByZW1vdmUgKCkgew0KICAgICAgdGhpcy5pZCA9ICcnDQogICAgICB0aGlzLmlucHV0ID0gJycNCiAgICAgIHRoaXMuaW5wdXRUZXh0ID0gdGhpcy5wbGFjZWhvbGRlcg0KICAgICAgdGhpcy5zZWxlY3REYXRhID0ge30NCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLm9wdGlvbnNfc2hvdyA9IGZhbHNlDQogICAgICB9LCAyMikNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["zy-cascader.vue"], "names": [], "mappings": ";AAmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-cascader.vue", "sourceRoot": "src/components/zy-cascader", "sourcesContent": ["<template>\r\n  <div class=\"zy-cascader\"\r\n       :style=\"{ width: width + 'px' }\">\r\n    <el-popover popper-class=\"zy-cascader-popover\"\r\n                :trigger=\"trigger\"\r\n                :disabled=\"disabled\"\r\n                :width=\"width\"\r\n                v-model=\"options_show\">\r\n      <el-input slot=\"reference\"\r\n                clearable\r\n                @blur=\"blur\"\r\n                @focus=\"focus\"\r\n                ref=\"zy-cascader\"\r\n                v-model=\"input\"\r\n                @clear=\"remove\"\r\n                :disabled=\"disabled\"\r\n                :placeholder=\"inputText\">\r\n        <i slot=\"suffix\"\r\n           v-if=\"input == ''\"\r\n           :class=\"['zy-cascader-icon', 'el-icon-arrow-down', options_show ? 'zy-cascader-icon-a' : '']\"></i>\r\n      </el-input>\r\n      <el-scrollbar class=\"zy-cascader-box\">\r\n        <zy-tree-components :tree=\"data\"\r\n                            v-model=\"id\"\r\n                            :child=\"child\"\r\n                            :props=\"props\"\r\n                            :keyword=\"input\"\r\n                            :node-key=\"nodeKey\"\r\n                            :determine=\"determine\"\r\n                            @on-tree-click=\"selectedClick\"></zy-tree-components>\r\n      </el-scrollbar>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyCascader',\r\n  data () {\r\n    return {\r\n      id: this.value,\r\n      input: '',\r\n      selectData: {},\r\n      options_show: false,\r\n      inputText: '',\r\n      determine: false\r\n    }\r\n  },\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    // 数据\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    // 触发方式 click/focus/hover/manual\r\n    trigger: {\r\n      type: String,\r\n      default: 'click'\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '请选择内容'\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 宽度\r\n    width: {\r\n      type: String,\r\n      default: '296'\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    child: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  created () {\r\n    this.inputText = this.placeholder\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.id = val\r\n        this.determine = true\r\n      } else {\r\n        this.id = ''\r\n        this.input = ''\r\n        this.inputText = this.placeholder\r\n      }\r\n    },\r\n    id (val) {\r\n      this.$emit('id', val)\r\n    },\r\n    selectData (val) {\r\n      this.$emit('select', val)\r\n    },\r\n    options_show () {\r\n      if (this.input === '' && this.id && !this.options_show) {\r\n        this.determine = true\r\n        this.input = this.selectedText\r\n        this.inputText = this.selectedText\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    blur () {\r\n      if (this.input === '' && this.id && !this.options_show) {\r\n        this.determine = true\r\n        this.input = this.selectedText\r\n        this.inputText = this.selectedText\r\n      }\r\n    },\r\n    focus () {\r\n      this.determine = false\r\n      if (this.input && this.id) {\r\n        this.input = ''\r\n        this.inputText = this.selectedText\r\n      }\r\n    },\r\n    // 下拉框选中事件\r\n    selectedClick (data) {\r\n      // if (this.selectedText === data[this.props.label]) {\r\n      //   return\r\n      // }\r\n      this.input = data[this.props.label]\r\n      this.selectData = data\r\n      this.options_show = false\r\n      this.inputText = data[this.props.label]\r\n      this.selectedText = data[this.props.label]\r\n      this.determine = true\r\n      this.$refs['zy-cascader'].focus()\r\n      setTimeout(() => {\r\n        this.$refs['zy-cascader'].blur()\r\n      }, 22)\r\n    },\r\n    remove () {\r\n      this.id = ''\r\n      this.input = ''\r\n      this.inputText = this.placeholder\r\n      this.selectData = {}\r\n      setTimeout(() => {\r\n        this.options_show = false\r\n      }, 22)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-cascader.scss\";\r\n</style>\r\n"]}]}