{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-select-checkbox\\zy-select-checkbox.vue?vue&type=style&index=0&id=a602ed62&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-select-checkbox\\zy-select-checkbox.vue", "mtime": 1752541693550}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3p5LXNlbGVjdC1jaGVja2JveC5zY3NzIjsNCg=="}, {"version": 3, "sources": ["zy-select-checkbox.vue"], "names": [], "mappings": ";AAiOA", "file": "zy-select-checkbox.vue", "sourceRoot": "src/components/zy-select-checkbox", "sourcesContent": ["<template>\r\n  <div class=\"zy-select-checkbox\"\r\n       :style=\"{ width: width + 'px' }\">\r\n    <el-popover popper-class=\"zy-select-checkbox-popover\"\r\n                :trigger=\"trigger\"\r\n                :disabled=\"disabled\"\r\n                :width=\"width\"\r\n                v-model=\"options_show\">\r\n      <div slot=\"reference\"\r\n           :class=\"['zy-select-checkbox-input',disabled?'zy-select-checkbox-input-disabled':'']\">\r\n        <div :class=\"['zy-select-checkbox-input-icon',options_show?'zy-select-checkbox-input-icon-a':'']\">\r\n          <i class=\"el-icon-arrow-down\"></i>\r\n        </div>\r\n        <div v-if=\"!selectData.length\"\r\n             class=\"zy-select-checkbox-input-text\">{{placeholder}}</div>\r\n        <el-tag v-for=\"tag in selectData\"\r\n                :key=\"tag[nodeKey]\"\r\n                size=\"medium\"\r\n                closable\r\n                :disable-transitions=\"false\"\r\n                @close=\"remove(tag)\">\r\n          {{tag[props.label]}}\r\n        </el-tag>\r\n      </div>\r\n\r\n      <el-scrollbar class=\"zy-select-checkbox-box-box\">\r\n        <div>\r\n          <el-input placeholder=\"请输入关键字查询\"\r\n                    v-model=\"keyword\"\r\n                    clearable>\r\n          </el-input>\r\n        </div>\r\n        <el-scrollbar class=\"zy-select-checkbox-box\">\r\n          <el-tree :class=\"['zy-select-checkbox-tree',child? 'zy-select-checkbox-tree-a':'']\"\r\n                   ref=\"tree\"\r\n                   :data=\"data\"\r\n                   show-checkbox\r\n                   :props=\"props\"\r\n                   check-strictly\r\n                   highlight-current\r\n                   :node-key=\"nodeKey\"\r\n                   :filter-node-method=\"filterNode\"\r\n                   @check-change=\"selectedClick\">\r\n          </el-tree>\r\n        </el-scrollbar>\r\n      </el-scrollbar>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zySelectCheckbox',\r\n  data () {\r\n    return {\r\n      keyword: '',\r\n      ids: [],\r\n      selectData: [],\r\n      options_show: false\r\n    }\r\n  },\r\n  props: {\r\n    value: [Array],\r\n    // 数据\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    // 触发方式 click/focus/hover/manual\r\n    trigger: {\r\n      type: String,\r\n      default: 'click'\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '请选择内容'\r\n    },\r\n    message: {\r\n      type: String,\r\n      default: '选择的内容'\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 是否禁用\r\n    child: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 宽度\r\n    width: {\r\n      type: String,\r\n      default: '296'\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    max: {\r\n      type: Number\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  mounted () {\r\n    this.dataMethods()\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      this.dataMethods()\r\n    },\r\n    data (val) {\r\n      this.dataMethods()\r\n    },\r\n    keyword (val) {\r\n      this.$refs.tree.filter(val)\r\n    }\r\n  },\r\n  methods: {\r\n    filterNode (value, data) {\r\n      if (!value) return true\r\n      return data[this.props.label].indexOf(value) !== -1\r\n    },\r\n    dataMethods () {\r\n      this.ids = this.getArray(this.value)\r\n      this.selected(this.ids)\r\n      this.$nextTick(function () {\r\n        this.$refs.tree.setCheckedKeys(this.ids)\r\n      })\r\n    },\r\n    // 数组去重\r\n    getArray (a) {\r\n      var hash = {}\r\n      var len = a.length\r\n      var result = []\r\n      for (var i = 0; i < len; i++) {\r\n        if (!hash[a[i]]) {\r\n          hash[a[i]] = true\r\n          result.push(a[i])\r\n        }\r\n      }\r\n      return result\r\n    },\r\n    // 首次进来传入的数据\r\n    selected (data) {\r\n      var arr = []\r\n      data.forEach(item => {\r\n        arr = arr.concat(this.selectedMethods(this.data, item))\r\n      })\r\n      this.selectData = arr\r\n      this.$emit('choose-click', this.selectData)\r\n    },\r\n    // 首次进来默认选中\r\n    selectedMethods (data, id) {\r\n      var obj = []\r\n      data.forEach(item => {\r\n        if (item[this.nodeKey] === id) {\r\n          obj.push(item)\r\n        }\r\n        if (item.children && item.children.length > 0) {\r\n          obj = obj.concat(this.selectedMethods(item.children, id))\r\n        }\r\n      })\r\n      return obj\r\n    },\r\n    // 下拉框选中事件\r\n    selectedClick (data, type) {\r\n      if (type) {\r\n        if (this.max) {\r\n          if (this.ids.length === this.max) {\r\n            this.$nextTick(function () {\r\n              this.$refs.tree.setCheckedKeys(this.ids)\r\n            })\r\n            this.$emit('id', this.ids)\r\n            this.$emit('choose-click', this.selectData)\r\n            this.$message({\r\n              message: `${this.message}不能超过${this.max}个`,\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n        }\r\n        this.ids.push(data[this.nodeKey])\r\n        this.selectData.push(data)\r\n      } else {\r\n        var ids = this.ids\r\n        var selectData = this.selectData\r\n        this.ids = ids.filter(item => item !== data[this.nodeKey])\r\n        this.selectData = selectData.filter(item => item[this.nodeKey] !== data[this.nodeKey])\r\n      }\r\n      this.$emit('id', this.ids)\r\n      this.$emit('choose-click', this.selectData)\r\n    },\r\n    // 移除tag\r\n    remove (data) {\r\n      if (this.disabled) {\r\n        return\r\n      }\r\n      var ids = this.ids\r\n      var selectData = this.selectData\r\n      this.ids = ids.filter(item => item !== data[this.nodeKey])\r\n      this.selectData = selectData.filter(item => item[this.nodeKey] !== data[this.nodeKey])\r\n      this.$nextTick(function () {\r\n        this.$refs.tree.setCheckedKeys(this.ids)\r\n      })\r\n      this.$emit('id', this.ids)\r\n      this.$emit('choose-click', this.selectData)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-select-checkbox.scss\";\r\n</style>\r\n"]}]}