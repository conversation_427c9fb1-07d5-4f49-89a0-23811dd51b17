{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecordAdd\\MonthlyWorkRecordAdd.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecordAdd\\MonthlyWorkRecordAdd.vue", "mtime": 1752541693848}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MonthlyWorkRecordAdd.vue"], "names": [], "mappings": ";AAoGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MonthlyWorkRecordAdd.vue", "sourceRoot": "src/views/AssessmentOrgan/MonthlyWorkRecordAdd", "sourcesContent": ["<template>\r\n  <!-- 新建/编辑 月工作纪实 -->\r\n  <div class=\"MonthlyWorkRecordAdd\">\r\n    <div class=\"add-form-title\">{{ id? '编辑' : '新增'}}</div>\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"qd-form\">\r\n      <el-form-item label=\"标题\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"title\">\r\n        <el-input placeholder=\"请输入标题\"\r\n                  v-model=\"form.title\">\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"所属个人\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"publishUserName\">\r\n        <el-input placeholder=\"请选择所属个人\"\r\n                  :disabled=\"disabled\"\r\n                  readonly\r\n                  @focus=\"focus\"\r\n                  v-model=\"form.publishUserName\">\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"部门\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"officeId\">\r\n        <zy-select width=\"222\"\r\n                   node-key=\"id\"\r\n                   v-model=\"form.officeId\"\r\n                   :data=\"officeData\"\r\n                   @select=\"select\"\r\n                   placeholder=\"请选择部门\">\r\n        </zy-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"发布时间\"\r\n                    class=\"form-item-wd50\"\r\n                    prop=\"publishTime\">\r\n        <el-date-picker v-model=\"form.publishTime\"\r\n                        type=\"datetime\"\r\n                        value-format=\"timestamp\"\r\n                        placeholder=\"选择日期时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <br>\r\n      <el-form-item label=\"上传附件\"\r\n                    class=\"form-item-wd100 form-upload\">\r\n        <zy-upload-file ref=\"upload\"\r\n                        module=\"notice\"\r\n                        :data=\"file\"\r\n                        :max=\"10\"\r\n                        placeholder=\"仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式\">\r\n        </zy-upload-file>\r\n      </el-form-item>\r\n\r\n      <!-- <el-form-item label=\"上传附件\"\r\n                    class=\"form-upload\">\r\n        <el-upload class=\"form-upload-demo\"\r\n                   drag\r\n                   action=\"/\"\r\n                   :before-remove=\"beforeRemove\"\r\n                   :before-upload=\"handleFile\"\r\n                   :http-request=\"fileUpload\"\r\n                   :file-list=\"file\"\r\n                   multiple>\r\n          <div class=\"el-upload__text\">将附件拖拽至此区域，或<em>点击上传</em></div>\r\n          <div class=\"el-upload__tip\">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>\r\n        </el-upload>\r\n      </el-form-item> -->\r\n\r\n      <el-form-item label=\"内容\"\r\n                    class=\"form-item-wd100\">\r\n        <wang-editor v-model='form.content'></wang-editor>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"form-footer-btn\">\r\n      <el-button type=\"primary\"\r\n                 size=\"small\"\r\n                 @click=\"submitForm('form')\">提交</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm1('form')\">重置</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm('form')\">取消</el-button>\r\n    </div>\r\n    <zy-pop-up v-model=\"userShow\"\r\n               title=\"选择所属个人\">\r\n      <candidates-user point=\"point_21\"\r\n                       :max=\"1\"\r\n                       :data=\"userData\"\r\n                       @userCallback=\"userCallback\"></candidates-user>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'MonthlyWorkRecordAdd',\r\n  data () {\r\n    return {\r\n      id: this.$route.query.id,\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())), // 获取当前登录人信息\r\n      form: {\r\n        title: '',\r\n        publishTime: '',\r\n        publishUserId: '',\r\n        publishUserName: '',\r\n        officeId: '',\r\n        officeName: '',\r\n        content: '',\r\n        // ******\r\n        attachmentId: '',\r\n        auditStatus: ''\r\n\r\n      },\r\n      rules: {\r\n        title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n        publishUserName: [\r\n          { required: true, message: '请输入所属个人', trigger: 'blur' }\r\n        ],\r\n        officeId: [\r\n          { required: true, message: '请输入部门', trigger: 'blur' }\r\n        ],\r\n        publishTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ]\r\n      },\r\n      file: [],\r\n      disabled: false,\r\n      officeData: [],\r\n      userData: [],\r\n      userShow: false\r\n    }\r\n  },\r\n  mounted () {\r\n    this.treeList()\r\n    this.form.publishTime = new Date().getTime()\r\n    if (this.id) {\r\n      this.templatePageInfo()\r\n    }\r\n    // 需根据登录信息做判断\r\n    this.form.officeName = this.user.officeName\r\n    this.form.officeId = this.user.officeId\r\n    this.form.publishUserId = this.user.id\r\n    this.form.publishUserName = this.user.userName\r\n    this.userData = [{ mobile: this.user.mobile, officeName: this.user.officeName, officeId: this.user.officeId, name: this.user.userName, position: this.user.position, userId: this.user.id, userName: this.user.userName }]\r\n\r\n    // TODO:测试环境暂时注释,提交时再解开*************************************************************************************\r\n    // 登录的user.otherInfo.IsEvaluationAdmin（是否考核管理员）如果等于true,则下面信息回显当前登录人的机构和用户信息，且为可编辑状态，可编辑状态下，调用选人点point_21选择人员并回显机构和人员信息，否则只回显当前登录人的机构和用户信息，不可编辑\r\n    // if (this.user.otherInfo.userOtherInfo.isEvaluationAdmin) {\r\n    //   this.disabled = false\r\n    // } else {\r\n    //   this.disabled = true\r\n    // }\r\n    // *************************************************************************************************************************\r\n  },\r\n  inject: ['tabDelJump'],\r\n  methods: {\r\n    /**\r\n* 限制上传附件的文件类型\r\n*/\r\n    handleFile (file, fileList) {\r\n    },\r\n    /**\r\n   * 上传附件请求方法\r\n  */\r\n    fileUpload (files) {\r\n      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''\r\n      const param = new FormData()\r\n      param.append('module', 'ta')\r\n      param.append('siteId', JSON.parse(areaId))\r\n      param.append('attachment', files.file)\r\n      this.$api.proposal.proposalfile(param).then(res => {\r\n        var { data } = res\r\n        data[0].name = data[0].fileName\r\n        this.file.push(data[0])\r\n        console.log(this.file)\r\n      })\r\n    },\r\n    /**\r\n   * 删除附件\r\n  */\r\n    beforeRemove (file, fileList) {\r\n      var fileData = this.file\r\n      this.file = fileData.filter(item => item.id !== file.id)\r\n    },\r\n    /**\r\n     *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    select (row) {\r\n      this.form.officeName = row ? row.label : ''\r\n    },\r\n    // 获取详情\r\n    async templatePageInfo () {\r\n      const res = await this.$api.AssessmentOrgan.reqWorkDetails(this.id)\r\n      const data = res.data\r\n      if (data.attachment) {\r\n        // data.attachment.uid = data.attachment.id\r\n        // data.attachment.fileName = data.attachment.oldName // 附件名称\r\n        // this.file.push(data.attachment)\r\n\r\n        data.attachment.forEach(item => {\r\n          item.uid = item.id\r\n          item.fileName = item.oldName // 附件名称\r\n          this.file.push(item)\r\n        })\r\n      }\r\n      const { title, publishTime, publishUserId, publishUserName, officeId, officeName, content, auditStatus } = res.data\r\n\r\n      this.form = { title, publishTime, publishUserId, publishUserName, officeId, officeName, content, auditStatus }\r\n    },\r\n    // 提交\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var data = {\r\n            id: this.id,\r\n            title: this.form.title,\r\n            publishTime: this.form.publishTime,\r\n            publishUserId: this.form.publishUserId,\r\n            publishUserName: this.form.publishUserName,\r\n            content: this.form.content,\r\n            officeId: this.form.officeId,\r\n            officeName: this.form.officeName,\r\n            // *******\r\n            auditStatus: this.form.auditStatus,\r\n            type: '通知',\r\n            attachmentId: this.$refs.upload.obtainId().join(',') // 获取附件id\r\n          }\r\n          // data.org = JSON.parse(sessionStorage.getItem('user' + this.$logo())).officeId\r\n\r\n          const url = this.id ? '/peacetimemonth/edit' : '/peacetimemonth/add'\r\n          this.$api.AssessmentOrgan.reqAddWorkDetails(url, data).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.tabDelJump()\r\n            }\r\n          })\r\n        } else {\r\n          this.$message.warning('请输入必填项')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    resetForm () {\r\n      this.$confirm('是否离开当前页面', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tabDelJump()\r\n      }).catch(() => {\r\n\r\n      })\r\n    },\r\n    // 重置按钮\r\n    resetForm1 () {\r\n      this.$confirm('是否重置当前输入的内容', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.form.content = ''\r\n        this.$refs.form.resetFields()\r\n      }).catch(() => {\r\n      })\r\n    },\r\n\r\n    focus () {\r\n      this.userShow = !this.userShow\r\n    },\r\n    // 选择用户的回调\r\n    userCallback (data, type) {\r\n      if (type) {\r\n        this.userData = data\r\n        this.form.publishUserName = data[0].name\r\n        this.form.officeName = data[0].officeName\r\n        this.form.publishUserId = data[0].userId\r\n      }\r\n      this.userShow = !this.userShow\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.MonthlyWorkRecordAdd {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n  background-color: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);\r\n  padding: 20px 30px;\r\n\r\n  .wang-editor {\r\n    //解决内容输入过长时能自动换行\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n  }\r\n}\r\n</style>\r\n"]}]}