{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\finishDetail.vue?vue&type=style&index=0&id=7c4442ef&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\finishDetail.vue", "mtime": 1752541693790}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZmluaXNoRGV0YWlsIHsNCiAgd2lkdGg6IDExMDBweDsNCiAgaGVpZ2h0OiA2MDBweDsNCiAgcGFkZGluZzogMXB4IDQwcHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIC5xZC1idG4tYm94IHsNCiAgICBwYWRkaW5nLWJvdHRvbTogNXB4Ow0KICB9DQoNCiAgLmxpc3RWaWV3IHsNCiAgICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDEzMnB4KTsNCiAgICAudGFibGVTdHlsZSB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGhlaWdodDogNTAwcHg7DQogICAgICBvdmVyZmxvdy15OiBzY3JvbGw7DQogICAgICBvdmVyZmxvdy14OiBoaWRkZW47DQogICAgfQ0KICB9DQoNCiAgLnRhYmxlWnkgew0KICAgIGhlaWdodDogNTAwcHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["finishDetail.vue"], "names": [], "mappings": ";AAqSA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "finishDetail.vue", "sourceRoot": "src/views/AssessmentOrgan/BusinessObjectives", "sourcesContent": ["<template>\r\n  <!-- 点击完成情况 -->\r\n  <div class=\"finishDetail\">\r\n    <div class=\"buttonColumn\">\r\n      <!-- 新增/删除 按钮 -->\r\n      <div class=\"qd-btn-box\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"finishStatus\"\r\n          >新增\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          plain\r\n          @click=\"handleBatchDelete\"\r\n          >删除\r\n        </el-button>\r\n\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-circle-check\"\r\n          v-permissions=\"\r\n            'auth:business:checkPass'\r\n              ? 'auth:business:checkPass'\r\n              : 'auth:innovation:checkPass'\r\n          \"\r\n          @click=\"passClick(2)\"\r\n          >审核通过\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-remove-outline\"\r\n          v-permissions=\"\r\n            'auth:business:checkNotPass'\r\n              ? 'auth:business:checkNotPass'\r\n              : 'auth:innovation:checkNotPass'\r\n          \"\r\n          @click=\"passClick(3)\"\r\n          >审核不通过\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"listView\">\r\n      <zy-table>\r\n        <el-table\r\n          slot=\"zytable\"\r\n          :data=\"tableData\"\r\n          row-key=\"id\"\r\n          ref=\"multipleTable\"\r\n          @select=\"selected\"\r\n          @select-all=\"selectedAll\"\r\n          :header-cell-style=\"{ background: '#eef1f6', color: '#606266' }\"\r\n          tooltip-effect=\"dark\"\r\n          class=\"tableStyle\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\"> </el-table-column>\r\n\r\n          <el-table-column\r\n            label=\"标题\"\r\n            show-overflow-tooltip\r\n            prop=\"title\"\r\n            width=\"450\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" @click=\"modify(scope.row)\" size=\"small\">\r\n                {{ scope.row.title }}\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"审核状态\" width=\"110\" prop=\"auditStatus\">\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"overTime\" label=\"完成时间\" width=\"220\">\r\n            <template slot-scope=\"scope\">\r\n              <div>{{ $format(scope.row.overTime).substr(0, 16) }}</div>\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"auditStatus\"\r\n                         label=\"审核状态\"\r\n                         show-overflow-tooltip>\r\n          <template slot-scope=\"scope\"> -->\r\n          <!-- <div v-if=\"scope.row.auditStatus == '1' \"> 待审核 </div>\r\n                <div v-if=\"scope.row.auditStatus == '0' \"> 审核通过</div> -->\r\n          <!-- <div>{{scope.row.auditStatus}}</div>\r\n          </template>\r\n        </el-table-column> -->\r\n\r\n          <el-table-column label=\"操作\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                type=\"text\"\r\n                @click=\"handleClick(scope.row)\"\r\n                size=\"small\"\r\n              >\r\n                编辑</el-button\r\n              >\r\n\r\n              <el-button\r\n                type=\"text\"\r\n                @click=\"handleDelete(scope.row.id)\"\r\n                class=\"delBtn\"\r\n                size=\"small\"\r\n              >\r\n                删除</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </zy-table>\r\n    </div>\r\n\r\n    <div class=\"paging_box\">\r\n      <el-pagination\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page.sync=\"currentPage\"\r\n        :page-sizes=\"[10, 20, 30, 40]\"\r\n        :page-size.sync=\"pageSize\"\r\n        background\r\n        layout=\"total, prev, pager, next, sizes, jumper\"\r\n        :total=\"total\"\r\n      >\r\n      </el-pagination>\r\n    </div>\r\n\r\n    <zy-pop-up v-model=\"showFinish\" class=\"titleStyle\" title=\"完成情况\">\r\n      <newFinishDetail :id=\"id\" :uid=\"uid\" @newCallback=\"newCallback\">\r\n      </newFinishDetail>\r\n    </zy-pop-up>\r\n\r\n    <zy-pop-up\r\n      v-model=\"showFinishDetail\"\r\n      class=\"titleStyle\"\r\n      title=\"完成情况详情\"\r\n      :beforeClose=\"updateList\"\r\n    >\r\n      <FinishDetailPop :id=\"id\" :uid=\"uid\" @newCallback=\"newCallback\">\r\n      </FinishDetailPop>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n<script>\r\nimport newFinishDetail from './newFinishDetail.vue'\r\nimport FinishDetailPop from './FinishDetailPop.vue'\r\nimport tableData from '@mixins/tableData'\r\n\r\nexport default {\r\n  name: 'finishDetail',\r\n  mixins: [tableData],\r\n  components: {\r\n    newFinishDetail,\r\n    FinishDetailPop\r\n  },\r\n  data () {\r\n    return {\r\n      showFinish: false,\r\n      showFinishDetail: false,\r\n      tableData: [],\r\n      evaluationId: '',\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      uid: '', // 完成情况列表的id\r\n      currentPage: 1,\r\n      total: 10\r\n\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  inject: ['newTab'],\r\n\r\n  mounted () {\r\n    if (this.id) {\r\n      this.getfinishDetailList()\r\n    }\r\n  },\r\n  methods: {\r\n    updateList () {\r\n      this.showFinishDetail = false\r\n      this.getfinishDetailList()\r\n    },\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckWork(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 审核\r\n    async getCheckWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckFinishDetail({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getfinishDetailList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n    newCallback () {\r\n      this.showFinish = false // 关闭弹窗\r\n      this.showFinishDetail = false\r\n      this.getfinishDetailList() // 重新调用(更新)一次列表\r\n    },\r\n    // 请求后台数据 获取列表信息\r\n    async getfinishDetailList () {\r\n      const res = await this.$api.AssessmentOrgan.reqfinishDetailList({\r\n        evaluationId: this.id, // TODO:工作目标或创新创优id(需检查是否传错)\r\n        pageNo: this.currentPage,\r\n        pageSize: this.pageSize,\r\n        // *******************\r\n        memberType: this.memberType,\r\n        auditStatus: this.auditStatus\r\n      })\r\n      var { data, total } = res\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n      this.selectObj = []\r\n      this.selectData = []\r\n      this.$nextTick(function () {\r\n        this.memoryChecked()\r\n      })\r\n      // this.choose = []\r\n      // this.selectObjData = []\r\n    },\r\n    // 新增 完成情况\r\n    finishStatus () {\r\n      this.showFinish = true\r\n      this.uid = 0 // 将newFinishDetail组件的属性:uid设置为0 (false) 达到新增页面效果\r\n    },\r\n    // 编辑\r\n    handleClick (row) {\r\n      this.uid = row.id\r\n      this.showFinish = true\r\n    },\r\n    // 详情\r\n    modify (row) {\r\n      this.uid = row.id\r\n      this.showFinishDetail = true\r\n    },\r\n    // 删除按钮方法  批量删除\r\n    handleDelete (ids) { // TODO: handleDelete 和 handleBatchDelete 1.组合使用为新增/删除栏的删除功能 2.单独使用handleDelete则为操作项的删除功能\r\n      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$api.AssessmentOrgan.reqDelFinishDetail({ ids }).then((res) => {\r\n          if (res.errcode === 200) {\r\n            this.getfinishDetailList()// 删除后更新页面\r\n            this.$message.success('删除成功')\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('取消删除')\r\n        // this.getfinishDetailList()\r\n        return false\r\n      })\r\n    },\r\n    handleBatchDelete () {\r\n      if (this.choose.length === 0) {\r\n        return this.$message.warning('请选择想要删除的项')\r\n      }\r\n      this.handleDelete(this.choose.join(','))\r\n    },\r\n    // 底部页签\r\n    handleSizeChange () {\r\n      this.getfinishDetailList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getfinishDetailList()\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.finishDetail {\r\n  width: 1100px;\r\n  height: 600px;\r\n  padding: 1px 40px;\r\n  overflow: hidden;\r\n  .qd-btn-box {\r\n    padding-bottom: 5px;\r\n  }\r\n\r\n  .listView {\r\n    height: calc(100% - 132px);\r\n    .tableStyle {\r\n      width: 100%;\r\n      height: 500px;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n    }\r\n  }\r\n\r\n  .tableZy {\r\n    height: 500px;\r\n  }\r\n}\r\n</style>\r\n"]}]}