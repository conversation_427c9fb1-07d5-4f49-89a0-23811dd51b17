{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\sinceManagement-zx\\SinceSituation\\MyColumn.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\sinceManagement-zx\\SinceSituation\\MyColumn.vue", "mtime": 1754616762320}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdNeUNvbHVtbicsCiAgcHJvcHM6IHsKICAgIGNvbDogewogICAgICB0eXBlOiBPYmplY3QKICAgIH0KICB9Cn07"}, {"version": 3, "mappings": "AASA;EACAA,gBADA;EAEAC;IACAC;MACAC;IADA;EADA;AAFA", "names": ["name", "props", "col", "type"], "sourceRoot": "src/views/sinceManagement-zx/SinceSituation", "sources": ["MyColumn.vue"], "sourcesContent": ["<template>\r\n  <el-table-column :prop=\"col.fieldName\" :label=\"col.label\" align=\"center\" width=\"100\"\r\n    :class-name=\"col.fieldName === 'userName' ? 'username' : ''\">\r\n    <MyColumn v-for=\"(item, index) in col.children\" :key=\"index\" :col=\"item\">\r\n    </MyColumn>\r\n  </el-table-column>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MyColumn',\r\n  props: {\r\n    col: {\r\n      type: Object\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style>\r\n.username {\r\n  color: #3657c0;\r\n}\r\n\r\n.el-table__row .username .cell {\r\n  color: #3657c0;\r\n  cursor: pointer;\r\n}\r\n</style>\r\n"]}]}