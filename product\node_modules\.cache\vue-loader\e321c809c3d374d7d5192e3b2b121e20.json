{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\finishDetail.vue?vue&type=style&index=0&id=36a124e4&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\finishDetail.vue", "mtime": 1752541693830}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZmluaXNoRGV0YWlsIHsNCiAgd2lkdGg6IDExMDBweDsNCiAgaGVpZ2h0OiAxMDAlOw0KICBwYWRkaW5nOiAxcHggNDBweDsNCg0KICAucWQtYnRuLWJveCB7DQogICAgcGFkZGluZy1ib3R0b206IDVweDsNCiAgfQ0KDQogIC5saXN0VmlldyB7DQogICAgaGVpZ2h0OiAxMDAlOw0KDQogICAgLnRhYmxlU3R5bGUgew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgICBoZWlnaHQ6IDUwMHB4Ow0KICAgICAgb3ZlcmZsb3cteTogc2Nyb2xsOw0KICAgICAgb3ZlcmZsb3cteDogaGlkZGVuOw0KICAgIH0NCiAgfQ0KDQogIC50YWJsZVp5IHsNCiAgICBoZWlnaHQ6IDUwMHB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["finishDetail.vue"], "names": [], "mappings": ";AAoTA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "finishDetail.vue", "sourceRoot": "src/views/AssessmentOrgan/InnovationExcellence", "sourcesContent": ["<template>\r\n  <!-- 点击完成情况 -->\r\n  <div class=\"finishDetail\">\r\n    <div class=\"buttonColumn\">\r\n      <!-- 新增/删除 按钮 -->\r\n      <div class=\"qd-btn-box\">\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-plus\"\r\n                   @click=\"finishStatus\">新增\r\n        </el-button>\r\n        <el-button type=\"danger\"\r\n                   icon=\"el-icon-delete\"\r\n                   plain\r\n                   @click=\"handleBatchDelete\">删除\r\n        </el-button>\r\n\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   v-permissions=\"'auth:innovation:checkPass'\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n        <el-button type=\"danger\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   v-permissions=\"'auth:innovation:checkNotPass'\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"listView\">\r\n\r\n      <el-table :data=\"tableData\"\r\n                row-key=\"id\"\r\n                ref=\"multipleTable\"\r\n                @select=\"selected\"\r\n                @select-all=\"selectedAll\"\r\n                :header-cell-style=\"{background:'#eef1f6',color:'#606266'}\"\r\n                tooltip-effect=\"dark\"\r\n                class=\"tableStyle\">\r\n        <el-table-column type=\"selection\"\r\n                         width=\"55\">\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"标题\"\r\n                         show-overflow-tooltip\r\n                         prop=\"title\"\r\n                         width=\"450\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\"\r\n                       @click=\"modify(scope.row)\"\r\n                       size=\"small\"> {{scope.row.title}}\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"审核状态\"\r\n                         width=\"110\"\r\n                         prop=\"auditStatus\">\r\n\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"overTime\"\r\n                         label=\"完成时间\"\r\n                         width=\"220\">\r\n          <template slot-scope=\"scope\">\r\n            <div> {{$format(scope.row.overTime).substr(0,16)}} </div>\r\n          </template>\r\n        </el-table-column>\r\n        <!-- <el-table-column prop=\"auditStatus\"\r\n                         label=\"审核状态\"\r\n                         show-overflow-tooltip>\r\n          <template slot-scope=\"scope\"> -->\r\n        <!-- <div v-if=\"scope.row.auditStatus == '1' \"> 待审核 </div>\r\n                <div v-if=\"scope.row.auditStatus == '0' \"> 审核通过</div> -->\r\n        <!-- <div>{{scope.row.auditStatus}}</div>\r\n          </template>\r\n        </el-table-column> -->\r\n\r\n        <el-table-column label=\"操作\"\r\n                         width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\"\r\n                       @click=\"handleClick(scope.row)\"\r\n                       size=\"small\"> 编辑</el-button>\r\n\r\n            <el-button type=\"text\"\r\n                       @click=\"handleDelete(scope.row.id)\"\r\n                       class=\"delBtn\"\r\n                       size=\"small\"> 删除</el-button>\r\n\r\n          </template>\r\n        </el-table-column>\r\n\r\n      </el-table>\r\n\r\n    </div>\r\n\r\n    <div class=\"paging_box\">\r\n      <el-pagination @size-change=\"handleSizeChange\"\r\n                     @current-change=\"handleCurrentChange\"\r\n                     :current-page.sync=\"currentPage\"\r\n                     :page-sizes=\"[10, 20, 30, 40]\"\r\n                     :page-size.sync=\"pageSize\"\r\n                     background\r\n                     layout=\"total, prev, pager, next, sizes, jumper\"\r\n                     :total=\"total\">\r\n      </el-pagination>\r\n    </div>\r\n\r\n    <zy-pop-up v-model=\"showFinish\"\r\n               class=\"titleStyle\"\r\n               title=\"完成情况\">\r\n      <newFinishDetail :id=\"id\"\r\n                       :uid=\"uid\"\r\n                       @newCallback=\"newCallback\">\r\n      </newFinishDetail>\r\n    </zy-pop-up>\r\n\r\n    <zy-pop-up v-model=\"showFinishDetail\"\r\n               class=\"titleStyle\"\r\n               title=\"完成情况详情\"\r\n               :beforeClose=\"updateList\">\r\n\r\n      <FinishDetailPop :id=\"id\"\r\n                       :uid=\"uid\"\r\n                       @newCallback=\"newCallback\">\r\n      </FinishDetailPop>\r\n\r\n    </zy-pop-up>\r\n\r\n  </div>\r\n</template>\r\n<script>\r\n// import newFinishDetail from '../newFinishDetail.vue'\r\nimport newFinishDetail from '../BusinessObjectives/newFinishDetail'\r\n\r\nimport FinishDetailPop from './FinishDetailPop.vue'\r\nimport tableData from '@mixins/tableData'\r\n\r\nexport default {\r\n  name: 'finishDetail',\r\n  mixins: [tableData],\r\n  components: {\r\n    newFinishDetail,\r\n    FinishDetailPop\r\n  },\r\n  data () {\r\n    return {\r\n      showFinish: false,\r\n      showFinishDetail: false,\r\n\r\n      tableData: [],\r\n      selectData: [],\r\n      selectObj: [],\r\n\r\n      evaluationId: '',\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      uid: '', // 完成情况列表的id\r\n      currentPage: 1,\r\n      total: 10\r\n\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  inject: ['newTab'],\r\n\r\n  mounted () {\r\n    if (this.id) {\r\n      this.getfinishDetailList()\r\n    }\r\n  },\r\n  methods: {\r\n    updateList () {\r\n      this.showFinishDetail = false\r\n      this.getfinishDetailList()\r\n    },\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckWork(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 审核\r\n    async getCheckWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckFinishDetail({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getfinishDetailList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n    newCallback () {\r\n      this.showFinish = false // 关闭弹窗\r\n      this.showFinishDetail = false\r\n      this.getfinishDetailList() // 重新调用(更新)一次列表\r\n    },\r\n    // 请求后台数据 获取列表信息\r\n    async getfinishDetailList () {\r\n      const res = await this.$api.AssessmentOrgan.reqfinishDetailList({\r\n        evaluationId: this.id, // TODO:工作目标或创新创优id(需检查是否传错)\r\n        pageNo: this.currentPage,\r\n        pageSize: this.pageSize,\r\n        // *******************\r\n        memberType: this.memberType,\r\n        auditStatus: this.auditStatus\r\n      })\r\n      var { data, total } = res\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n    },\r\n    // 新增 完成情况\r\n    finishStatus () {\r\n      this.showFinish = true\r\n      this.uid = 0 // 将newFinishDetail组件的属性:uid设置为0 (false) 达到新增页面效果\r\n    },\r\n    // 编辑\r\n    handleClick (row) {\r\n      this.uid = row.id\r\n      this.showFinish = true\r\n    },\r\n    // 详情\r\n    modify (row) {\r\n      this.uid = row.id\r\n      this.showFinishDetail = true\r\n    },\r\n    // 删除按钮方法  批量删除\r\n    handleDelete (ids) { // TODO: handleDelete 和 handleBatchDelete 1.组合使用为新增/删除栏的删除功能 2.单独使用handleDelete则为操作项的删除功能\r\n      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$api.AssessmentOrgan.reqDelFinishDetail({ ids }).then((res) => {\r\n          if (res.errcode === 200) {\r\n            this.getfinishDetailList()// 删除后更新页面\r\n            this.$message.success('删除成功')\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('取消删除')\r\n        // this.getfinishDetailList()\r\n        return false\r\n      })\r\n    },\r\n    handleBatchDelete () {\r\n      if (this.selectData.length === 0) {\r\n        return this.$message.warning('请选择想要删除的项')\r\n      }\r\n      this.handleDelete(this.selectData.join(','))\r\n    },\r\n    select (selection, row) {\r\n      var arr = this.selectData\r\n      if (this.selectObj[row.id]) {\r\n        arr.forEach((item, index) => {\r\n          if (item === row.id) {\r\n            arr.splice(index, 1)\r\n          }\r\n        })\r\n        delete this.selectObj[row.id]\r\n      } else {\r\n        this.selectObj[row.id] = row.id\r\n        arr.push(row.id)\r\n        this.selectData = arr\r\n      }\r\n    },\r\n    selectAll (selection) {\r\n      this.selectData = []\r\n      this.selectObj = []\r\n      if (selection.length) {\r\n        selection.forEach((item, index) => {\r\n          this.selectObj[item.id] = item.id\r\n          this.selectData.push(item.id)\r\n        })\r\n      }\r\n    },\r\n    // 底部页签\r\n    handleSizeChange () {\r\n      this.getfinishDetailList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getfinishDetailList()\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.finishDetail {\r\n  width: 1100px;\r\n  height: 100%;\r\n  padding: 1px 40px;\r\n\r\n  .qd-btn-box {\r\n    padding-bottom: 5px;\r\n  }\r\n\r\n  .listView {\r\n    height: 100%;\r\n\r\n    .tableStyle {\r\n      width: 100%;\r\n      height: 500px;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n    }\r\n  }\r\n\r\n  .tableZy {\r\n    height: 500px;\r\n  }\r\n}\r\n</style>\r\n"]}]}