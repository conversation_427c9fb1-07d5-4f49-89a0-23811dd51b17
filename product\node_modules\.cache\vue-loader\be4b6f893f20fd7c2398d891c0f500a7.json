{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue", "mtime": 1756287513677}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCAqIGFzIGVjaGFydHMgZnJvbSAnZWNoYXJ0cycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUGllQ2hhcnQnLAogIHByb3BzOiB7CiAgICBpZDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgbmFtZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgY2hhcnREYXRhOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0KICB9LAogIGRhdGEgKCkgewogICAgcmV0dXJuIHsKICAgICAgY2hhcnQ6IG51bGwsCiAgICAgIGF1dG9IaWdobGlnaHRUaW1lcjogbnVsbCwgLy8g6Ieq5Yqo6auY5Lqu5a6a5pe25ZmoCiAgICAgIGN1cnJlbnRIaWdobGlnaHRJbmRleDogLTEsIC8vIOW9k+W<PERSON><PERSON>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"}, {"version": 3, "sources": ["PieChart.vue"], "names": [], "mappings": ";AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PieChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"pie-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'PieChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    name: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      autoHighlightTimer: null, // 自动高亮定时器\n      currentHighlightIndex: -1, // 当前高亮的数据项索引\n      // 预定义的颜色数组，按顺序分配给数据项\n      colors: [\n        '#4FC3F7', '#FFF67C', '#66BB6A', '#FFA726',\n        '#FF7043', '#AB47BC', '#5C6BC0', '#42A5F5',\n        '#FFCA28', '#4CAF50', '#EF5350', '#8D6E63',\n        '#9C27B0', '#3F51B5', '#2196F3', '#00BCD4',\n        '#FF9800', '#795548', '#607D8B', '#E91E63'\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    // 清除自动高亮定时器\n    if (this.autoHighlightTimer) {\n      clearInterval(this.autoHighlightTimer)\n    }\n    if (this.chart) {\n      this.chart.dispose()\n    }\n  },\n  methods: {\n    getColor (index, item) {\n      // 如果数据项中有颜色信息，优先使用数据中的颜色\n      if (item && item.color) {\n        return item.color\n      }\n      // 否则使用预定义的颜色数组\n      return this.colors[index % this.colors.length]\n    },\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00d4ff',\n          borderWidth: 1,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: this.id === 'category_distribution' ? 'horizontal' : 'vertical',\n          right: this.id === 'reply-type-pie' ? '0%' : this.id === 'category_distribution' ? null : this.id === 'proposal-statistics' ? '0%' : '5%',\n          left: this.id === 'category_distribution' ? 'center' : null,\n          top: this.id === 'reply-type-pie' ? '38%' : this.id === 'category_distribution' ? null : 'center',\n          bottom: this.id === 'category_distribution' ? 20 : null,\n          itemWidth: this.id === 'reply-type-pie' ? 12 : 5,\n          itemHeight: this.id === 'reply-type-pie' ? 6 : 5,\n          icon: this.id === 'reply-type-pie' ? null : 'circle',\n          itemGap: this.id === 'reply-type-pie' ? 45 : this.id === 'category_distribution' ? 30 : this.id === 'proposal-statistics' ? 12 : 25,\n          textStyle: {\n            color: '#fff',\n            fontSize: 12,\n            fontFamily: 'Microsoft YaHei'\n          },\n          formatter: (name) => {\n            const item = this.chartData.find(d => d.name === name)\n            if (this.id === 'reply-type-pie') {\n              return `${name}`\n            } else {\n              return `${name}  ${item ? item.value : ''}%`\n            }\n          }\n        },\n        series: [\n          {\n            name: this.name,\n            type: 'pie',\n            radius: this.id === 'category_distribution' ? ['30%', '45%'] : this.id === 'proposal-statistics' ? ['60%', '85%'] : this.id === 'reply-type-pie' ? ['40%', '70%'] : ['55%', '80%'],\n            center: this.id === 'category_distribution' ? ['50%', '25%'] : this.id === 'proposal-statistics' ? ['22%', '50%'] : this.id === 'reply-type-pie' ? ['50%', '50%'] : ['30%', '50%'],\n            avoidLabelOverlap: false,\n            emphasis: {\n              scale: true,\n              scaleSize: 10\n            },\n            label: {\n              show: true,\n              position: 'center',\n              fontSize: 16,\n              color: '#fff',\n              formatter: this.name\n            },\n            labelLine: {\n              show: false\n            },\n            itemStyle: {\n              borderWidth: 3,\n              borderColor: '#07345F' // 用大屏背景色\n            },\n            data: this.chartData.map((item, index) => ({\n              value: item.value,\n              name: item.name,\n              itemStyle: { color: this.getColor(index, item) }\n            }))\n          },\n          {\n            type: 'pie',\n            radius: this.id === 'category_distribution' ? ['50%', '51%'] : this.id === 'proposal-statistics' ? ['94%', '95%'] : this.id === 'reply-type-pie' ? ['1%', '1%'] : ['88%', '89%'],\n            center: this.id === 'category_distribution' ? ['50%', '25%'] : this.id === 'proposal-statistics' ? ['22%', '50%'] : this.id === 'reply-type-pie' ? ['50%', '50%'] : ['30%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: '#2f689a'\n                }\n              }\n            ],\n            label: {\n              show: false\n            }\n          }\n        ],\n        graphic: [\n          {\n            type: 'circle',\n            left: this.id === 'category_distribution' ? '36.5%' : this.id === 'proposal-statistics' ? '12%' : this.id === 'reply-type-pie' ? '40%' : '17%',\n            top: this.id === 'category_distribution' ? '13%' : this.id === 'proposal-statistics' ? '23%' : this.id === 'reply-type-pie' ? '35%' : '26%',\n            shape: {\n              cx: 0,\n              cy: 0,\n              r: this.id === 'category_distribution' ? 60 : this.id === 'proposal-statistics' ? 40 : this.id === 'reply-type-pie' ? 0 : 50\n            },\n            style: {\n              fill: 'none',\n              lineWidth: this.id === 'category_distribution' ? 4 : 3,\n              stroke: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: '#23E1FF' },\n                  { offset: 1, color: 'rgba(35,225,255,0)' }\n                ]\n              }\n            },\n            z: 10,\n            silent: true,\n            position: [0, 0]\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 监听窗口大小变化\n      window.addEventListener('resize', () => {\n        if (this.chart) {\n          this.chart.resize()\n        }\n      })\n      // 启动自动高亮效果\n      this.startAutoHighlight()\n      // 添加鼠标事件监听\n      this.chart.on('mouseover', () => {\n        this.stopAutoHighlight()\n      })\n      this.chart.on('mouseout', () => {\n        this.startAutoHighlight()\n      })\n    },\n\n    // 开始自动高亮效果\n    startAutoHighlight () {\n      if (this.chartData.length === 0) return\n\n      this.autoHighlightTimer = setInterval(() => {\n        // 取消当前高亮和tooltip\n        if (this.currentHighlightIndex >= 0) {\n          this.chart.dispatchAction({\n            type: 'downplay',\n            seriesIndex: 0,\n            dataIndex: this.currentHighlightIndex\n          })\n          // 隐藏tooltip\n          this.chart.dispatchAction({\n            type: 'hideTip'\n          })\n        }\n\n        // 高亮下一个数据项\n        this.currentHighlightIndex = (this.currentHighlightIndex + 1) % this.chartData.length\n        this.chart.dispatchAction({\n          type: 'highlight',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n\n        // 显示tooltip\n        this.chart.dispatchAction({\n          type: 'showTip',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n      }, 2000) // 每2秒切换一次\n    },\n\n    // 停止自动高亮效果\n    stopAutoHighlight () {\n      if (this.autoHighlightTimer) {\n        clearInterval(this.autoHighlightTimer)\n        this.autoHighlightTimer = null\n      }\n      // 取消所有高亮\n      if (this.chart && this.currentHighlightIndex >= 0) {\n        this.chart.dispatchAction({\n          type: 'downplay',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n        this.currentHighlightIndex = -1\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pie-chart {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n</style>\n"]}]}