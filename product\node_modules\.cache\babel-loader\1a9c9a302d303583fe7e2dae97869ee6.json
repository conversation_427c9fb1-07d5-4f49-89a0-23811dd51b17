{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\general-custom-topic\\custom-topic-add\\custom-topic-add.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\general-custom-topic\\custom-topic-add\\custom-topic-add.vue", "mtime": 1752541697687}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAkFA;EACAA,wBADA;;EAEAC;IACA;MACAC;QACAF,QADA;QAEAG,QAFA;QAGAC,WAHA;QAIAC,iBAJA;QAKAC;MALA,CADA;MAQAC;QACAP,OACA;UAAAQ;UAAAC;UAAAC;QAAA,CADA,CADA;QAIAN,UACA;UAAAI;UAAAC;UAAAC;QAAA,CADA;MAJA,CARA;MAgBAC;IAhBA;EAkBA,CArBA;;EAsBAC,yBAtBA;;EAuBAC;IACA;MACA;IACA,CAFA,MAEA;MACA;IACA;EACA,CA7BA;;EA8BAC;IACA;MACA;MACA;QAAAb;MAAA;MACA;MACA;MACA;MACA;MACA;;MACA;QACAA;UACAc;UACAA;QACA,CAHA;QAIA;MACA;IACA,CAhBA;;IAiBA;AACA;AACA;IACAC;MACA;QACA;UACA;UACAC;UACA;;UACA;YACAC;UACA;;UACA;YACAC,WADA;YAEAC,uBAFA;YAGApB,oBAHA;YAIAG,oBAJA;YAKAC,0BALA;YAMAiB,qBANA;YAOAhB,sCAPA;YAQAC;UARA,GASAgB,IATA,CASAC;YACA;cAAAC;cAAAC;YAAA;;YACA;cACA;gBACAhB,eADA;gBAEAiB;cAFA;cAIA;YACA;UACA,CAlBA;QAmBA,CA1BA,MA0BA;UACA;YACAjB,iBADA;YAEAiB;UAFA;UAIA;QACA;MACA,CAlCA;IAmCA,CAxDA;;IAyDA;AACA;AACA;IACAC;MACA;IACA;;EA9DA;AA9BA", "names": ["name", "data", "form", "sort", "pubDate", "externalLinks", "content", "rules", "required", "message", "trigger", "file", "props", "mounted", "methods", "item", "submitForm", "attach", "url", "id", "columnId", "attachmentIds", "then", "res", "<PERSON><PERSON><PERSON>", "errmsg", "type", "cancel"], "sourceRoot": "src/views/wisdomWarehouse/general-custom-topic/custom-topic-add", "sources": ["custom-topic-add.vue"], "sourcesContent": ["<template>\r\n  <div class=\"committee-data-add\">\r\n    <el-form\r\n      :model=\"form\"\r\n      :rules=\"rules\"\r\n      inline\r\n      ref=\"form\"\r\n      label-position=\"top\"\r\n      class=\"newForm\"\r\n    >\r\n      <el-form-item\r\n        label=\"标题\"\r\n        prop=\"name\"\r\n        class=\"form-title\"\r\n      >\r\n        <el-input\r\n          placeholder=\"请输入标题\"\r\n          v-model=\"form.name\"\r\n          clearable\r\n        >\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item\r\n        label=\"发布时间\"\r\n        prop=\"pubDate\"\r\n        class=\"form-input\"\r\n      >\r\n        <el-date-picker\r\n          v-model=\"form.pubDate\"\r\n          type=\"datetime\"\r\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n          placeholder=\"选择发布时间\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"排序\"\r\n                    class=\"form-input\">\r\n        <el-input-number style=\"width:100%;\"\r\n                         :min=\"1\"\r\n                         placeholder=\"请输入排序\"\r\n                         v-model=\"form.sort\"\r\n                         clearable>\r\n        </el-input-number>\r\n      </el-form-item>\r\n      <el-form-item\r\n        label=\"上传附件\"\r\n        class=\"form-upload\"\r\n      >\r\n        <zy-upload-file\r\n          ref=\"upload\"\r\n          :data=\"file\"\r\n          placeholder=\"仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式\"\r\n        ></zy-upload-file>\r\n      </el-form-item>\r\n      <!-- <el-form-item\r\n        label=\"外部链接\"\r\n        class=\"form-title\"\r\n      >\r\n        <el-input\r\n          placeholder=\"请输入外部链接\"\r\n          v-model=\"form.externalLinks\"\r\n          clearable\r\n        >\r\n        </el-input>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item\r\n        label=\"内容\"\r\n        class=\"form-ue\"\r\n      >\r\n        <wang-editor v-model='form.content'></wang-editor>\r\n      </el-form-item> -->\r\n      <div class=\"form-button\">\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"submitForm('form')\"\r\n        >提交</el-button>\r\n        <el-button @click=\"cancel\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'committeeDataAdd',\r\n  data () {\r\n    return {\r\n      form: {\r\n        name: '',\r\n        sort: '',\r\n        pubDate: '',\r\n        externalLinks: '',\r\n        content: ''\r\n      },\r\n      rules: {\r\n        name: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n        pubDate: [\r\n          { required: true, message: '选择发布时间', trigger: 'blur' }\r\n        ]\r\n      },\r\n      file: []\r\n    }\r\n  },\r\n  props: ['id', 'columnId'],\r\n  mounted () {\r\n    if (this.id) {\r\n      this.informationListInfo()\r\n    } else {\r\n      this.form.pubDate = this.$format(new Date(), 'YYYY-MM-DD HH:mm:ss')\r\n    }\r\n  },\r\n  methods: {\r\n    async informationListInfo () {\r\n      const res = await this.$api.wisdomWarehouse.assemblyInfo(this.id)\r\n      var { data } = res\r\n      this.form.name = data.name\r\n      this.form.sort = data.sort\r\n      this.form.pubDate = data.pubDate\r\n      this.form.externalLinks = data.externalLinks\r\n      this.form.content = data.content\r\n      if (data.attachmentList) {\r\n        data.attachmentList.forEach((item, index) => {\r\n          item.uid = item.id\r\n          item.name = item.fileName\r\n        })\r\n        this.file = data.attachmentList\r\n      }\r\n    },\r\n    /**\r\n   * 提交提案\r\n  */\r\n    submitForm (formName, type) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var attach = []\r\n          attach = this.$refs.upload.obtainId().join(',')\r\n          var url = '/assembly/add'\r\n          if (this.id) {\r\n            url = '/assembly/edit'\r\n          }\r\n          this.$api.wisdomWarehouse.addAssembly(url, {\r\n            id: this.id,\r\n            columnId: this.columnId,\r\n            name: this.form.name,\r\n            sort: this.form.sort,\r\n            pubDate: this.form.pubDate,\r\n            attachmentIds: attach,\r\n            externalLinks: this.form.externalLinks,\r\n            content: this.form.content\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('callback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    /**\r\n   * 取消按钮\r\n  */\r\n    cancel () {\r\n      this.$emit('callback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './committee-data-add.scss';\r\n</style>\r\n"]}]}