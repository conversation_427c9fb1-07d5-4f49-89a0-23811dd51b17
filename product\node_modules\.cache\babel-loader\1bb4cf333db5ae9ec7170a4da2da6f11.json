{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\general-custom-topic\\custom-topic-details\\custom-topic-details.vue?vue&type=template&id=694d92bf&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\general-custom-topic\\custom-topic-details\\custom-topic-details.vue", "mtime": 1752541697693}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "_s", "details", "name", "pubDate", "domProps", "innerHTML", "content", "attachmentList", "undefined", "length", "_l", "item", "index", "key", "fileName", "attrs", "href", "filePath", "target", "on", "click", "$event", "download", "_e", "staticRenderFns", "_withStripped"], "sources": ["D:/zy/xm/pc/qdzx/product/src/views/wisdomWarehouse/general-custom-topic/custom-topic-details/custom-topic-details.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"custom-project-details\" }, [\n    _c(\"div\", { staticClass: \"custom-project-details-title\" }, [\n      _vm._v(_vm._s(_vm.details.name)),\n    ]),\n    _c(\"div\", { staticClass: \"custom-project-details-xx\" }, [\n      _c(\"div\", { staticClass: \"custom-project-details-tiem\" }, [\n        _vm._v(\"发布时间：\" + _vm._s(_vm.details.pubDate)),\n      ]),\n    ]),\n    _c(\"div\", {\n      staticClass: \"custom-project-details-content\",\n      domProps: { innerHTML: _vm._s(_vm.details.content) },\n    }),\n    _vm.details.attachmentList !== undefined &&\n    _vm.details.attachmentList.length > 0\n      ? _c(\n          \"div\",\n          { staticClass: \"fileBox\" },\n          [\n            _c(\"div\", { staticClass: \"file_title\" }, [_vm._v(\" 资讯附件 \")]),\n            _vm._l(_vm.details.attachmentList, function (item, index) {\n              return _c(\"div\", { key: index, staticClass: \"fileListt\" }, [\n                _c(\"div\", { staticClass: \"file_item\" }, [\n                  _c(\"div\", { staticClass: \"file_name\" }, [\n                    _vm._v(\" \" + _vm._s(item.fileName) + \" \"),\n                  ]),\n                  _c(\"div\", { staticClass: \"file_load\" }, [\n                    _c(\"div\", { staticClass: \"load_text\" }, [\n                      _c(\n                        \"a\",\n                        { attrs: { href: item.filePath, target: \"_blank\" } },\n                        [_vm._v(\"预览\")]\n                      ),\n                    ]),\n                    _c(\"div\", { staticClass: \"shu\" }),\n                    _c(\n                      \"div\",\n                      {\n                        staticClass: \"load_text\",\n                        on: {\n                          click: function ($event) {\n                            return _vm.download(item)\n                          },\n                        },\n                      },\n                      [_vm._v(\"下载\")]\n                    ),\n                  ]),\n                ]),\n              ])\n            }),\n          ],\n          2\n        )\n      : _vm._e(),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAT,GAAkB;EAC7B,IAAIC,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAmD,CAC1DF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAyD,CACzDH,GAAG,CAACI,EAAJ,CAAOJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,OAAJ,CAAYC,IAAnB,CAAP,CADyD,CAAzD,CADwD,EAI1DN,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAsD,CACtDF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAwD,CACxDH,GAAG,CAACI,EAAJ,CAAO,UAAUJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,OAAJ,CAAYE,OAAnB,CAAjB,CADwD,CAAxD,CADoD,CAAtD,CAJwD,EAS1DP,EAAE,CAAC,KAAD,EAAQ;IACRE,WAAW,EAAE,gCADL;IAERM,QAAQ,EAAE;MAAEC,SAAS,EAAEV,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,OAAJ,CAAYK,OAAnB;IAAb;EAFF,CAAR,CATwD,EAa1DX,GAAG,CAACM,OAAJ,CAAYM,cAAZ,KAA+BC,SAA/B,IACAb,GAAG,CAACM,OAAJ,CAAYM,cAAZ,CAA2BE,MAA3B,GAAoC,CADpC,GAEIb,EAAE,CACA,KADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACEF,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAuC,CAACH,GAAG,CAACI,EAAJ,CAAO,QAAP,CAAD,CAAvC,CADJ,EAEEJ,GAAG,CAACe,EAAJ,CAAOf,GAAG,CAACM,OAAJ,CAAYM,cAAnB,EAAmC,UAAUI,IAAV,EAAgBC,KAAhB,EAAuB;IACxD,OAAOhB,EAAE,CAAC,KAAD,EAAQ;MAAEiB,GAAG,EAAED,KAAP;MAAcd,WAAW,EAAE;IAA3B,CAAR,EAAkD,CACzDF,EAAE,CAAC,KAAD,EAAQ;MAAEE,WAAW,EAAE;IAAf,CAAR,EAAsC,CACtCF,EAAE,CAAC,KAAD,EAAQ;MAAEE,WAAW,EAAE;IAAf,CAAR,EAAsC,CACtCH,GAAG,CAACI,EAAJ,CAAO,MAAMJ,GAAG,CAACK,EAAJ,CAAOW,IAAI,CAACG,QAAZ,CAAN,GAA8B,GAArC,CADsC,CAAtC,CADoC,EAItClB,EAAE,CAAC,KAAD,EAAQ;MAAEE,WAAW,EAAE;IAAf,CAAR,EAAsC,CACtCF,EAAE,CAAC,KAAD,EAAQ;MAAEE,WAAW,EAAE;IAAf,CAAR,EAAsC,CACtCF,EAAE,CACA,GADA,EAEA;MAAEmB,KAAK,EAAE;QAAEC,IAAI,EAAEL,IAAI,CAACM,QAAb;QAAuBC,MAAM,EAAE;MAA/B;IAAT,CAFA,EAGA,CAACvB,GAAG,CAACI,EAAJ,CAAO,IAAP,CAAD,CAHA,CADoC,CAAtC,CADoC,EAQtCH,EAAE,CAAC,KAAD,EAAQ;MAAEE,WAAW,EAAE;IAAf,CAAR,CARoC,EAStCF,EAAE,CACA,KADA,EAEA;MACEE,WAAW,EAAE,WADf;MAEEqB,EAAE,EAAE;QACFC,KAAK,EAAE,UAAUC,MAAV,EAAkB;UACvB,OAAO1B,GAAG,CAAC2B,QAAJ,CAAaX,IAAb,CAAP;QACD;MAHC;IAFN,CAFA,EAUA,CAAChB,GAAG,CAACI,EAAJ,CAAO,IAAP,CAAD,CAVA,CAToC,CAAtC,CAJoC,CAAtC,CADuD,CAAlD,CAAT;EA6BD,CA9BD,CAFF,CAHA,EAqCA,CArCA,CAFN,GAyCIJ,GAAG,CAAC4B,EAAJ,EAtDsD,CAAnD,CAAT;AAwDD,CA3DD;;AA4DA,IAAIC,eAAe,GAAG,EAAtB;AACA9B,MAAM,CAAC+B,aAAP,GAAuB,IAAvB;AAEA,SAAS/B,MAAT,EAAiB8B,eAAjB"}]}