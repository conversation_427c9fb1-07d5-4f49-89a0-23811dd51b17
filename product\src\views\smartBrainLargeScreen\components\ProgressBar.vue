<template>
  <div class="progress-bar-container">
    <div class="progress-item" v-for="(item, index) in progressData" :key="index">
      <div class="progress-label">
        <!-- <span class="label-text">{{ item.label }}</span> -->
        <span class="label-value">{{ item.value }}件</span>
        <span class="label-percent">占答复总件数{{ item.percent }}%</span>
      </div>
      <div class="progress-track">
        <div class="progress-fill" :style="{
          width: item.percent + '%',
          background: getProgressGradient(item.color)
        }"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProgressBar',
  props: {
    progressData: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  methods: {
    getProgressGradient (color) {
      // 根据颜色生成对应的渐变色
      if (color === '#00d4ff') {
        // 蓝色渐变
        return 'linear-gradient(90deg, rgba(31, 198, 255, 0) 0%, #1FC6FF 100%)'
      } else if (color === '#ffd700') {
        // 黄色渐变
        return 'linear-gradient(90deg, rgba(255, 215, 0, 0) 0%, #FFD700 100%)'
      } else {
        // 默认渐变，使用传入的颜色
        return `linear-gradient(90deg, ${color}00 0%, ${color} 100%)`
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.progress-bar-container {
  width: 100%;
  padding: 20px;

  .progress-item {
    margin-bottom: 25px;

    &:last-child {
      margin-bottom: 0;
    }

    .progress-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      color: #fff;

      .label-text {
        font-weight: 500;
      }

      .label-value {
        font-size: 12px;
        color: #FFFFFF;
      }

      .label-percent {
        font-size: 12px;
        color: #FFFFFF;
      }
    }

    .progress-track {
      width: 100%;
      height: 8px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      overflow: hidden;
      position: relative;

      .progress-fill {
        height: 100%;
        border-radius: 4px;
        transition: width 0.8s ease-in-out;
        position: relative;
      }
    }
  }
}
</style>
