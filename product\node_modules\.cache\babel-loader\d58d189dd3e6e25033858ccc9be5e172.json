{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecordAdd\\MonthlyWorkRecordAdd.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecordAdd\\MonthlyWorkRecordAdd.vue", "mtime": 1752541693848}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAoGA;EACAA,4BADA;;EAEAC;IACA;MACAC,wBADA;MAEAC,+DAFA;MAEA;MACAC;QACAC,SADA;QAEAC,eAFA;QAGAC,iBAHA;QAIAC,mBAJA;QAKAC,YALA;QAMAC,cANA;QAOAC,WAPA;QAQA;QACAC,gBATA;QAUAC;MAVA,CAHA;MAgBAC;QACAT,QACA;UAAAU;UAAAC;UAAAC;QAAA,CADA,CADA;QAIAT,kBACA;UAAAO;UAAAC;UAAAC;QAAA,CADA,CAJA;QAOAR,WACA;UAAAM;UAAAC;UAAAC;QAAA,CADA,CAPA;QAUAX,cACA;UAAAS;UAAAC;UAAAC;QAAA,CADA;MAVA,CAhBA;MA8BAC,QA9BA;MA+BAC,eA/BA;MAgCAC,cAhCA;MAiCAC,YAjCA;MAkCAC;IAlCA;EAoCA,CAvCA;;EAwCAC;IACA;IACA;;IACA;MACA;IACA,CALA,CAMA;;;IACA;IACA;IACA;IACA;IACA;MAAAC;MAAAd;MAAAD;MAAAT;MAAAyB;MAAAC;MAAAC;IAAA,GAXA,CAaA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA,CA7DA;;EA8DAC,sBA9DA;EA+DAC;IACA;AACA;AACA;IACAC,4BACA,CALA;;IAMA;AACA;AACA;IACAC;MACA;MACA;MACAC;MACAA;MACAA;MACA;QACA;UAAA/B;QAAA;QACAA;QACA;QACAgC;MACA,CALA;IAMA,CArBA;;IAsBA;AACA;AACA;IACAC;MACA;MACA;IACA,CA5BA;;IA6BA;AACA;AACA;IACA;MACA;MACA;QAAAjC;MAAA;MACA;IACA,CApCA;;IAqCAkC;MACA;IACA,CAvCA;;IAwCA;IACA;MACA;MACA;;MACA;QACA;QACA;QACA;QAEAlC;UACAmC;UACAA,6BAFA,CAEA;;UACA;QACA,CAJA;MAKA;;MACA;QAAA/B;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAE;MAAA;MAEA;QAAAR;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAE;MAAA;IACA,CA1DA;;IA2DA;IACAwB;MACA;QACA;UACA;YACAnC,WADA;YAEAG,sBAFA;YAGAC,kCAHA;YAIAC,sCAJA;YAKAC,0CALA;YAMAG,0BANA;YAOAF,4BAPA;YAQAC,gCARA;YASA;YACAG,kCAVA;YAWAyB,UAXA;YAYA1B,oDAZA,CAYA;;UAZA,EADA,CAeA;;UAEA;UACA;YACA;cAAA2B;cAAAC;YAAA;;YACA;cACA;gBACAxB,eADA;gBAEAsB;cAFA;cAIA;YACA;UACA,CATA;QAUA,CA5BA,MA4BA;UACA;UACA;QACA;MACA,CAjCA;IAkCA,CA/FA;;IAgGA;IACAG;MACA;QACAC,uBADA;QAEAC,sBAFA;QAGAL;MAHA,GAIAM,IAJA,CAIA;QACA;MACA,CANA,EAMAC,KANA,CAMA,OAEA,CARA;IASA,CA3GA;;IA4GA;IACAC;MACA;QACAJ,uBADA;QAEAC,sBAFA;QAGAL;MAHA,GAIAM,IAJA,CAIA;QACA;QACA;MACA,CAPA,EAOAC,KAPA,CAOA,OACA,CARA;IASA,CAvHA;;IAyHAE;MACA;IACA,CA3HA;;IA4HA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;;MACA;IACA;;EArIA;AA/DA", "names": ["name", "data", "id", "user", "form", "title", "publishTime", "publishUserId", "publishUserName", "officeId", "officeName", "content", "attachmentId", "auditStatus", "rules", "required", "message", "trigger", "file", "disabled", "officeData", "userData", "userShow", "mounted", "mobile", "position", "userId", "userName", "inject", "methods", "handleFile", "fileUpload", "param", "console", "beforeRemove", "select", "item", "submitForm", "type", "<PERSON><PERSON><PERSON>", "errmsg", "resetForm", "confirmButtonText", "cancelButtonText", "then", "catch", "resetForm1", "focus", "userCallback"], "sourceRoot": "src/views/AssessmentOrgan/MonthlyWorkRecordAdd", "sources": ["MonthlyWorkRecordAdd.vue"], "sourcesContent": ["<template>\r\n  <!-- 新建/编辑 月工作纪实 -->\r\n  <div class=\"MonthlyWorkRecordAdd\">\r\n    <div class=\"add-form-title\">{{ id? '编辑' : '新增'}}</div>\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"qd-form\">\r\n      <el-form-item label=\"标题\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"title\">\r\n        <el-input placeholder=\"请输入标题\"\r\n                  v-model=\"form.title\">\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"所属个人\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"publishUserName\">\r\n        <el-input placeholder=\"请选择所属个人\"\r\n                  :disabled=\"disabled\"\r\n                  readonly\r\n                  @focus=\"focus\"\r\n                  v-model=\"form.publishUserName\">\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"部门\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"officeId\">\r\n        <zy-select width=\"222\"\r\n                   node-key=\"id\"\r\n                   v-model=\"form.officeId\"\r\n                   :data=\"officeData\"\r\n                   @select=\"select\"\r\n                   placeholder=\"请选择部门\">\r\n        </zy-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"发布时间\"\r\n                    class=\"form-item-wd50\"\r\n                    prop=\"publishTime\">\r\n        <el-date-picker v-model=\"form.publishTime\"\r\n                        type=\"datetime\"\r\n                        value-format=\"timestamp\"\r\n                        placeholder=\"选择日期时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <br>\r\n      <el-form-item label=\"上传附件\"\r\n                    class=\"form-item-wd100 form-upload\">\r\n        <zy-upload-file ref=\"upload\"\r\n                        module=\"notice\"\r\n                        :data=\"file\"\r\n                        :max=\"10\"\r\n                        placeholder=\"仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式\">\r\n        </zy-upload-file>\r\n      </el-form-item>\r\n\r\n      <!-- <el-form-item label=\"上传附件\"\r\n                    class=\"form-upload\">\r\n        <el-upload class=\"form-upload-demo\"\r\n                   drag\r\n                   action=\"/\"\r\n                   :before-remove=\"beforeRemove\"\r\n                   :before-upload=\"handleFile\"\r\n                   :http-request=\"fileUpload\"\r\n                   :file-list=\"file\"\r\n                   multiple>\r\n          <div class=\"el-upload__text\">将附件拖拽至此区域，或<em>点击上传</em></div>\r\n          <div class=\"el-upload__tip\">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>\r\n        </el-upload>\r\n      </el-form-item> -->\r\n\r\n      <el-form-item label=\"内容\"\r\n                    class=\"form-item-wd100\">\r\n        <wang-editor v-model='form.content'></wang-editor>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"form-footer-btn\">\r\n      <el-button type=\"primary\"\r\n                 size=\"small\"\r\n                 @click=\"submitForm('form')\">提交</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm1('form')\">重置</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm('form')\">取消</el-button>\r\n    </div>\r\n    <zy-pop-up v-model=\"userShow\"\r\n               title=\"选择所属个人\">\r\n      <candidates-user point=\"point_21\"\r\n                       :max=\"1\"\r\n                       :data=\"userData\"\r\n                       @userCallback=\"userCallback\"></candidates-user>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'MonthlyWorkRecordAdd',\r\n  data () {\r\n    return {\r\n      id: this.$route.query.id,\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())), // 获取当前登录人信息\r\n      form: {\r\n        title: '',\r\n        publishTime: '',\r\n        publishUserId: '',\r\n        publishUserName: '',\r\n        officeId: '',\r\n        officeName: '',\r\n        content: '',\r\n        // ******\r\n        attachmentId: '',\r\n        auditStatus: ''\r\n\r\n      },\r\n      rules: {\r\n        title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n        publishUserName: [\r\n          { required: true, message: '请输入所属个人', trigger: 'blur' }\r\n        ],\r\n        officeId: [\r\n          { required: true, message: '请输入部门', trigger: 'blur' }\r\n        ],\r\n        publishTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ]\r\n      },\r\n      file: [],\r\n      disabled: false,\r\n      officeData: [],\r\n      userData: [],\r\n      userShow: false\r\n    }\r\n  },\r\n  mounted () {\r\n    this.treeList()\r\n    this.form.publishTime = new Date().getTime()\r\n    if (this.id) {\r\n      this.templatePageInfo()\r\n    }\r\n    // 需根据登录信息做判断\r\n    this.form.officeName = this.user.officeName\r\n    this.form.officeId = this.user.officeId\r\n    this.form.publishUserId = this.user.id\r\n    this.form.publishUserName = this.user.userName\r\n    this.userData = [{ mobile: this.user.mobile, officeName: this.user.officeName, officeId: this.user.officeId, name: this.user.userName, position: this.user.position, userId: this.user.id, userName: this.user.userName }]\r\n\r\n    // TODO:测试环境暂时注释,提交时再解开*************************************************************************************\r\n    // 登录的user.otherInfo.IsEvaluationAdmin（是否考核管理员）如果等于true,则下面信息回显当前登录人的机构和用户信息，且为可编辑状态，可编辑状态下，调用选人点point_21选择人员并回显机构和人员信息，否则只回显当前登录人的机构和用户信息，不可编辑\r\n    // if (this.user.otherInfo.userOtherInfo.isEvaluationAdmin) {\r\n    //   this.disabled = false\r\n    // } else {\r\n    //   this.disabled = true\r\n    // }\r\n    // *************************************************************************************************************************\r\n  },\r\n  inject: ['tabDelJump'],\r\n  methods: {\r\n    /**\r\n* 限制上传附件的文件类型\r\n*/\r\n    handleFile (file, fileList) {\r\n    },\r\n    /**\r\n   * 上传附件请求方法\r\n  */\r\n    fileUpload (files) {\r\n      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''\r\n      const param = new FormData()\r\n      param.append('module', 'ta')\r\n      param.append('siteId', JSON.parse(areaId))\r\n      param.append('attachment', files.file)\r\n      this.$api.proposal.proposalfile(param).then(res => {\r\n        var { data } = res\r\n        data[0].name = data[0].fileName\r\n        this.file.push(data[0])\r\n        console.log(this.file)\r\n      })\r\n    },\r\n    /**\r\n   * 删除附件\r\n  */\r\n    beforeRemove (file, fileList) {\r\n      var fileData = this.file\r\n      this.file = fileData.filter(item => item.id !== file.id)\r\n    },\r\n    /**\r\n     *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    select (row) {\r\n      this.form.officeName = row ? row.label : ''\r\n    },\r\n    // 获取详情\r\n    async templatePageInfo () {\r\n      const res = await this.$api.AssessmentOrgan.reqWorkDetails(this.id)\r\n      const data = res.data\r\n      if (data.attachment) {\r\n        // data.attachment.uid = data.attachment.id\r\n        // data.attachment.fileName = data.attachment.oldName // 附件名称\r\n        // this.file.push(data.attachment)\r\n\r\n        data.attachment.forEach(item => {\r\n          item.uid = item.id\r\n          item.fileName = item.oldName // 附件名称\r\n          this.file.push(item)\r\n        })\r\n      }\r\n      const { title, publishTime, publishUserId, publishUserName, officeId, officeName, content, auditStatus } = res.data\r\n\r\n      this.form = { title, publishTime, publishUserId, publishUserName, officeId, officeName, content, auditStatus }\r\n    },\r\n    // 提交\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var data = {\r\n            id: this.id,\r\n            title: this.form.title,\r\n            publishTime: this.form.publishTime,\r\n            publishUserId: this.form.publishUserId,\r\n            publishUserName: this.form.publishUserName,\r\n            content: this.form.content,\r\n            officeId: this.form.officeId,\r\n            officeName: this.form.officeName,\r\n            // *******\r\n            auditStatus: this.form.auditStatus,\r\n            type: '通知',\r\n            attachmentId: this.$refs.upload.obtainId().join(',') // 获取附件id\r\n          }\r\n          // data.org = JSON.parse(sessionStorage.getItem('user' + this.$logo())).officeId\r\n\r\n          const url = this.id ? '/peacetimemonth/edit' : '/peacetimemonth/add'\r\n          this.$api.AssessmentOrgan.reqAddWorkDetails(url, data).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.tabDelJump()\r\n            }\r\n          })\r\n        } else {\r\n          this.$message.warning('请输入必填项')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    resetForm () {\r\n      this.$confirm('是否离开当前页面', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tabDelJump()\r\n      }).catch(() => {\r\n\r\n      })\r\n    },\r\n    // 重置按钮\r\n    resetForm1 () {\r\n      this.$confirm('是否重置当前输入的内容', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.form.content = ''\r\n        this.$refs.form.resetFields()\r\n      }).catch(() => {\r\n      })\r\n    },\r\n\r\n    focus () {\r\n      this.userShow = !this.userShow\r\n    },\r\n    // 选择用户的回调\r\n    userCallback (data, type) {\r\n      if (type) {\r\n        this.userData = data\r\n        this.form.publishUserName = data[0].name\r\n        this.form.officeName = data[0].officeName\r\n        this.form.publishUserId = data[0].userId\r\n      }\r\n      this.userShow = !this.userShow\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.MonthlyWorkRecordAdd {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n  background-color: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);\r\n  padding: 20px 30px;\r\n\r\n  .wang-editor {\r\n    //解决内容输入过长时能自动换行\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n  }\r\n}\r\n</style>\r\n"]}]}