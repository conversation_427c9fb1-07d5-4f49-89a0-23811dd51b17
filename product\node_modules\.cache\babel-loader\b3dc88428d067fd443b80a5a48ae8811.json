{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tree\\zy-tree.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tree\\zy-tree.vue", "mtime": 1752541693607}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICd6eVRyZWUnLAoKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaWQ6IHRoaXMudmFsdWUKICAgIH07CiAgfSwKCiAgcHJvcHM6IHsKICAgIHZhbHVlOiBbU3RyaW5nLCBOdW1iZXIsIEFycmF5LCBPYmplY3RdLAogICAgLy8g5pWw5o2uCiAgICB0cmVlOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICBkZWZhdWx0OiAoKSA9PiBbXQogICAgfSwKICAgIC8vIOagkee7k+aehOmFjee9rgogICAgcHJvcHM6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiAoKSA9PiB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGNoaWxkcmVuOiAnY2hpbGRyZW4nLAogICAgICAgICAgbGFiZWw6ICdsYWJlbCcKICAgICAgICB9OwogICAgICB9CiAgICB9LAogICAgLy8g5piv5ZCm56aB55SoCiAgICBkaXNhYmxlZDogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfSwKICAgIC8vIOWuveW6pgogICAgd2lkdGg6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnMjk2JwogICAgfSwKICAgIG5vZGVLZXk6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnaWQnCiAgICB9LAogICAgYW55a2V5OiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9CiAgfSwKICBtb2RlbDogewogICAgcHJvcDogJ3ZhbHVlJywKICAgIGV2ZW50OiAnaWQnCiAgfSwKICB3YXRjaDogewogICAgdmFsdWUodmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICB0aGlzLmlkID0gdmFsOwogICAgICB9CiAgICB9LAoKICAgIGlkKHZhbCkgewogICAgICB0aGlzLiRlbWl0KCdpZCcsIHZhbCk7CiAgICB9CgogIH0sCiAgbWV0aG9kczogewogICAgc2VsZWN0ZWRDbGljayhpdGVtKSB7CiAgICAgIHRoaXMuJGVtaXQoJ29uLXRyZWUtY2xpY2snLCBpdGVtKTsKICAgIH0KCiAgfQp9Ow=="}, {"version": 3, "mappings": "AAeA;EACAA,cADA;;EAEAC;IACA;MACAC;IADA;EAGA,CANA;;EAOAC;IACAC,sCADA;IAEA;IACAC;MACAC,WADA;MAEAC;IAFA,CAHA;IAOA;IACAJ;MACAG,YADA;MAEAC;QACA;UACAC,oBADA;UAEAC;QAFA;MAIA;IAPA,CARA;IAiBA;IACAC;MACAJ,aADA;MAEAC;IAFA,CAlBA;IAsBA;IACAI;MACAL,YADA;MAEAC;IAFA,CAvBA;IA2BAK;MACAN,YADA;MAEAC;IAFA,CA3BA;IA+BAM;MACAP,WADA;MAEAC;QACA;MACA;IAJA;EA/BA,CAPA;EA6CAO;IACAC,aADA;IAEAC;EAFA,CA7CA;EAiDAC;IACAb;MACA;QACA;MACA;IACA,CALA;;IAMAF;MACA;IACA;;EARA,CAjDA;EA2DAgB;IACAC;MACA;IACA;;EAHA;AA3DA", "names": ["name", "data", "id", "props", "value", "tree", "type", "default", "children", "label", "disabled", "width", "nodeKey", "anykey", "model", "prop", "event", "watch", "methods", "selectedClick"], "sourceRoot": "src/components/zy-tree", "sources": ["zy-tree.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-tree\">\r\n    <el-scrollbar class=\"zy-tree-box\">\r\n      <zy-tree-components\r\n        :tree=\"tree\"\r\n        v-model=\"id\"\r\n        :anykey=\"anykey\"\r\n        :props=\"props\"\r\n        :node-key=\"nodeKey\"\r\n        @on-tree-click=\"selectedClick\"\r\n      ></zy-tree-components>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyTree',\r\n  data () {\r\n    return {\r\n      id: this.value\r\n    }\r\n  },\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    // 数据\r\n    tree: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 宽度\r\n    width: {\r\n      type: String,\r\n      default: '296'\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    anykey: {\r\n      type: Array,\r\n      default: function () {\r\n        return []\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.id = val\r\n      }\r\n    },\r\n    id (val) {\r\n      this.$emit('id', val)\r\n    }\r\n  },\r\n  methods: {\r\n    selectedClick (item) {\r\n      this.$emit('on-tree-click', item)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './zy-tree.scss';\r\n</style>\r\n"]}]}