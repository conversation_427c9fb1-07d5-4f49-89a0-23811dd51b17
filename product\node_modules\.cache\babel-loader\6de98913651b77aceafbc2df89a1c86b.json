{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\preview-code\\preview-code.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\preview-code\\preview-code.vue", "mtime": 1752541693464}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHZ1ZVFyIGZyb20gJ3Z1ZS1xcic7CmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICB2dWVRcgogIH0sCiAgbmFtZTogJ3ByZXZpZXctY29kZScsCiAgcHJvcHM6IHsKICAgIHVybDogU3RyaW5nLAogICAgdGlwczogU3RyaW5nCiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVjbG9zZSgpIHsKICAgICAgdGhpcy4kZW1pdCgnY2FuY2VsJyk7CiAgICB9CgogIH0KfTs="}, {"version": 3, "mappings": "AAYA;AACA;EACAA;IAAAC;EAAA,CADA;EAEAC,oBAFA;EAGAC;IACAC,WADA;IAEAC;EAFA,CAHA;EAOAC;IACAC;MACA;IACA;;EAHA;AAPA", "names": ["components", "vueQr", "name", "props", "url", "tips", "methods", "handleclose"], "sourceRoot": "src/components/preview-code", "sources": ["preview-code.vue"], "sourcesContent": ["<template>\r\n  <div class=\"qr-mark\">\r\n    <div class=\"icon-box\" @click=\"handleclose\">\r\n      <i class=\"el-icon-close\"></i>\r\n    </div>\r\n    <div class=\"qr-box\">\r\n      <vueQr :text=\"url\" :size=\"200\"></vueQr>\r\n    </div>\r\n    <p class=\"tips\">打开APP扫描二维码签到{{tips}}</p>\r\n  </div>\r\n</template>\r\n<script>\r\nimport vueQr from 'vue-qr'\r\nexport default {\r\n  components: { vueQr },\r\n  name: 'preview-code',\r\n  props: {\r\n    url: String,\r\n    tips: String\r\n  },\r\n  methods: {\r\n    handleclose () {\r\n      this.$emit('cancel')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.qr-mark {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  top: 0;\r\n  left: 0;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  z-index: 1000;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  .icon-box {\r\n    width: 24px;\r\n    height: 24px;\r\n    text-align: center;\r\n    line-height: 24px;\r\n    border-radius: 24px;\r\n    cursor: pointer;\r\n    margin-left: 275px;\r\n    margin-bottom: 25px;\r\n    font-size: 18px;\r\n    background-color: #fff;\r\n  }\r\n  .tips {\r\n    font-size: 20px;\r\n    line-height: 36px;\r\n    color: #fff;\r\n    margin-top: 25px;\r\n  }\r\n}\r\n</style>\r\n"]}]}