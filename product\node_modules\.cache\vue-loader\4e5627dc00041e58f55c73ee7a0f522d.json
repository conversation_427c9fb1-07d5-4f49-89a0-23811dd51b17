{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-calendar\\zy-calendar.vue?vue&type=style&index=0&id=00580429&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-calendar\\zy-calendar.vue", "mtime": 1660102037658}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3p5LWNhbGVuZGFyLnNjc3MiOw0K"}, {"version": 3, "sources": ["zy-calendar.vue"], "names": [], "mappings": ";AAiRA", "file": "zy-calendar.vue", "sourceRoot": "src/components/zy-calendar", "sourcesContent": ["<template>\r\n  <div class=\"zy-calendar\">\r\n    <div class=\"zy-calendar-selected\"\r\n         v-if=\"showHeader\">\r\n      <transition name=\"fadeY\">\r\n        <div class=\"years\"\r\n             v-if=\"yearsshow\">{{years}}</div>\r\n      </transition>\r\n      <transition name=\"fadeY\">\r\n        <div class=\"time\"\r\n             v-if=\"flag\">{{month+'&nbsp;&nbsp;'+Whatday}}</div>\r\n      </transition>\r\n    </div>\r\n    <section class=\"zy_container\">\r\n      <div class=\"zy_content_all\">\r\n        <div class=\"zy_top_changge\">\r\n          <li @click=\"PreMonth(myDate,false)\">\r\n            <div class=\"zy_jiantou1\"></div>\r\n          </li>\r\n          <li class=\"zy_content_li\">{{dateTop}}</li>\r\n          <li @click=\"NextMonth(myDate,false)\">\r\n            <div class=\"zy_jiantou2\"></div>\r\n          </li>\r\n        </div>\r\n        <div class=\"zy_content\">\r\n          <div class=\"zy_content_item\"\r\n               v-for=\"(tag,index) in textTop\"\r\n               :key=\"index\">\r\n            <div class=\"zy_top_tag\">{{tag}}</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"zy_content\">\r\n          <div class=\"zy_content_item\"\r\n               v-for=\"(item,index) in list\"\r\n               :key=\"index\"\r\n               @click=\"clickDay(item,index)\">\r\n            <div class=\"zy_item_date\"\r\n                 :class=\"[{ zy_isMark: item.isMark},{zy_other_dayhide:item.otherMonth!=='nowMonth'},{zy_want_dayhide:item.dayHide},{zy_isToday:item.isToday},{zy_chose_day:item.chooseDay},setClass(item)]\">{{item.id}}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n<script>\r\nimport timeUtil from './timeUtil'\r\nexport default {\r\n  name: 'zyCalendar',\r\n  data () {\r\n    return {\r\n      years: '',\r\n      month: '',\r\n      Whatday: '',\r\n      yearsshow: true,\r\n      flag: true,\r\n      myDate: [],\r\n      list: [],\r\n      historyChose: [],\r\n      dateTop: ''\r\n    }\r\n  },\r\n  props: {\r\n    markDate: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    markDateMore: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    textTop: {\r\n      type: Array,\r\n      default: () => ['一', '二', '三', '四', '五', '六', '日']\r\n    },\r\n    sundayStart: {\r\n      type: Boolean,\r\n      default: () => false\r\n    },\r\n    agoDayHide: {\r\n      type: String,\r\n      default: '0'\r\n    },\r\n    futureDayHide: {\r\n      type: String,\r\n      default: '2554387200'\r\n    },\r\n    showHeader: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  created () {\r\n    this.intStart()\r\n    this.myDate = new Date()\r\n    this.selected(this.getNowFormatDate(this.myDate))\r\n  },\r\n  methods: {\r\n    getWeekDate (data) {\r\n      var now = new Date(data)\r\n      var day = now.getDay()\r\n      var weeks = new Array('星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六')// eslint-disable-line\r\n      var week = weeks[day]\r\n      return week\r\n    },\r\n    getNowFormatDate (data) {\r\n      var date = data\r\n      var seperator1 = '-'\r\n      var year = date.getFullYear()\r\n      var month = date.getMonth() + 1\r\n      var strDate = date.getDate()\r\n      if (month >= 1 && month <= 9) {\r\n        month = '0' + month\r\n      }\r\n      if (strDate >= 0 && strDate <= 9) {\r\n        strDate = '0' + strDate\r\n      }\r\n      var currentdate = year + seperator1 + month + seperator1 + strDate\r\n      return currentdate\r\n    },\r\n    selected (data) {\r\n      this.Whatday = this.getWeekDate(data)\r\n      if (this.years !== data.slice(0, 4)) {\r\n        this.years = data.slice(0, 4)\r\n        this.yearsshow = false\r\n        setTimeout(() => {\r\n          this.yearsshow = true\r\n        }, 200)\r\n      }\r\n      if (this.month !== data.slice(5).replace('/', '-')) {\r\n        this.month = data.slice(5).replace('/', '-')\r\n        this.flag = false\r\n        setTimeout(() => {\r\n          this.flag = true\r\n        }, 200)\r\n      }\r\n    },\r\n    intStart () {\r\n      timeUtil.sundayStart = this.sundayStart\r\n    },\r\n    setClass (data) {\r\n      const obj = {}\r\n      obj[data.markClassName] = data.markClassName\r\n      return obj\r\n    },\r\n    clickDay: function (item, index) {\r\n      if (item.otherMonth === 'nowMonth' && !item.dayHide) {\r\n        this.getList(this.myDate, item.date)\r\n      }\r\n      if (item.otherMonth !== 'nowMonth') {\r\n        item.otherMonth === 'preMonth'\r\n          ? this.PreMonth(item.date)\r\n          : this.NextMonth(item.date)\r\n      }\r\n    },\r\n    ChoseMonth: function (date, isChosedDay = true) {\r\n      date = timeUtil.dateFormat(date)\r\n      this.myDate = new Date(date)\r\n      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate))\r\n      if (isChosedDay) {\r\n        this.getList(this.myDate, date, isChosedDay)\r\n      } else {\r\n        this.getList(this.myDate)\r\n      }\r\n    },\r\n    PreMonth: function (date, isChosedDay = true) {\r\n      date = timeUtil.dateFormat(date)\r\n      this.myDate = timeUtil.getOtherMonth(this.myDate, 'preMonth')\r\n      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate))\r\n      if (isChosedDay) {\r\n        this.getList(this.myDate, date, isChosedDay)\r\n      } else {\r\n        this.getList(this.myDate)\r\n      }\r\n    },\r\n    NextMonth: function (date, isChosedDay = true) {\r\n      date = timeUtil.dateFormat(date)\r\n      this.myDate = timeUtil.getOtherMonth(this.myDate, 'nextMonth')\r\n      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate))\r\n      if (isChosedDay) {\r\n        this.getList(this.myDate, date, isChosedDay)\r\n      } else {\r\n        this.getList(this.myDate)\r\n      }\r\n    },\r\n    forMatArgs: function () {\r\n      let markDate = this.markDate\r\n      let markDateMore = this.markDateMore\r\n      markDate = markDate.map(k => {\r\n        return timeUtil.dateFormat(k)\r\n      })\r\n      markDateMore = markDateMore.map(k => {\r\n        k.date = timeUtil.dateFormat(k.date)\r\n        return k\r\n      })\r\n      return [markDate, markDateMore]\r\n    },\r\n    getList: function (date, chooseDay, isChosedDay = true) {\r\n      const [markDate, markDateMore] = this.forMatArgs()\r\n      this.dateTop = `${date.getFullYear()}年${date.getMonth() + 1}月`\r\n      const arr = timeUtil.getMonthList(this.myDate)\r\n      for (let i = 0; i < arr.length; i++) {\r\n        let markClassName = ''\r\n        const k = arr[i]\r\n        k.chooseDay = false\r\n        const nowTime = k.date\r\n        const t = new Date(nowTime).getTime() / 1000\r\n        for (const c of markDateMore) {\r\n          if (c.date === nowTime) {\r\n            markClassName = c.className || ''\r\n          }\r\n        }\r\n        k.markClassName = markClassName\r\n        k.isMark = markDate.indexOf(nowTime) > -1\r\n        k.dayHide = t < this.agoDayHide || t > this.futureDayHide\r\n        if (k.isToday) {\r\n          this.$emit('isToday', nowTime)\r\n        }\r\n        const flag = !k.dayHide && k.otherMonth === 'nowMonth'\r\n        if (chooseDay && chooseDay === nowTime && flag) {\r\n          this.$emit('choseDay', nowTime)\r\n          this.selected(nowTime)\r\n          this.historyChose.push(nowTime)\r\n          k.chooseDay = true\r\n        } else if (\r\n          this.historyChose[this.historyChose.length - 1] === nowTime &&\r\n          !chooseDay &&\r\n          flag\r\n        ) {\r\n          k.chooseDay = true\r\n        }\r\n      }\r\n      this.list = arr\r\n    }\r\n  },\r\n  mounted () {\r\n    this.getList(this.myDate)\r\n  },\r\n  watch: {\r\n    markDate: {\r\n      handler (val, oldVal) {\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    },\r\n    markDateMore: {\r\n      handler (val, oldVal) {\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    },\r\n    agoDayHide: {\r\n      handler (val, oldVal) {\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    },\r\n    futureDayHide: {\r\n      handler (val, oldVal) {\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    },\r\n    sundayStart: {\r\n      handler (val, oldVal) {\r\n        this.intStart()\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-calendar.scss\";\r\n</style>\r\n"]}]}