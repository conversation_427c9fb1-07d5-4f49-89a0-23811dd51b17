{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\DoubleQuote\\DoubleDetails.vue?vue&type=style&index=0&id=236ab623&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\DoubleQuote\\DoubleDetails.vue", "mtime": 1752541693814}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DoubleDetails.vue"], "names": [], "mappings": ";AAoNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "DoubleDetails.vue", "sourceRoot": "src/views/AssessmentOrgan/DoubleQuote", "sourcesContent": ["<template>\r\n  <!-- 双招双引详情 -->\r\n  <div class=\"detailComment scrollBar \">\r\n    <div class=\"officeDetial-title\"> {{form.title}} </div>\r\n\r\n    <div class=\"relevantInformation\">\r\n\r\n      <div class=\"officeDetial-org\">\r\n        <div class=\"org-item\"> 类型 : <span> {{doubleClass}}</span> </div>\r\n        <div class=\"org-item\"> 所属个人: <span> {{form.publishUserName}}</span> </div>\r\n      </div>\r\n      <div class=\"officeDetial-org\">\r\n        <div class=\"org-item\"> 部门： <span> {{form.officeName}}</span> </div>\r\n        <div class=\"org-item\"> 时间： <span>{{ $format(form.publishTime)  }}</span></div>\r\n\r\n      </div>\r\n      <div>\r\n        <el-button type=\"primary\"\r\n                   v-if=\"this.approve==='true'\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n\r\n        <el-button type=\"danger\"\r\n                   v-if=\"this.noApprove==='true'\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n    <!-- 内容 -->\r\n    <div class=\"contBox\">\r\n      <!-- <div v-if=\"manuscriptFlag\"\r\n           class=\"content\"\r\n           v-html=\"manuscriptData.content\"> </div> -->\r\n      <div class=\"content\"\r\n           v-if=\"form.content\"\r\n           v-html=\"form.content\"></div>\r\n    </div>\r\n\r\n    <!-- *****附件***** -->\r\n    <div class=\"fileBox\"\r\n         v-if=\"form.attachmentInfo\">\r\n      <div class=\"file_title\"\r\n           v-if=\"form.attachmentInfo.length!==0\"> 资讯附件 </div>\r\n      <div class=\"fileListt\">\r\n\r\n        <div class=\"file_item\"\r\n             v-for=\"(item,index) in form.attachmentInfo\"\r\n             :key=\"index\">\r\n\r\n          <div class=\"file_name\"> {{item.oldName}} </div>\r\n\r\n          <div class=\"file_load\">\r\n            <div class=\"load_text\"><a :href=\"item.fullPath\"\r\n                 target=\"_blank\">预览</a>\r\n            </div>\r\n            <div class=\"shu\"></div>\r\n            <div class=\"load_text\"\r\n                 @click=\"download(item)\">下载</div>\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n// 套用模板:src\\views\\socialpublic-qd\\detailComment\\detailComment.vue\r\n// 修改了import引入的三个文件的相对路径\r\n// import handinfo from '../../socialpublic-qd/socialpulicManage/Allsocialpublic/handinfo/handinfo'\r\n// import similarity from '../../socialpublic-qd/info/submitSocialpublic/similarity/similarity'\r\n// import Typos from '../../socialpublic-qd/info/submitSocialpublic/Typos/Typos'\r\nimport { mapActions, mapState } from 'vuex'\r\n\r\nexport default {\r\n  name: 'DoubleDetails',\r\n  // components: { handinfo, similarity, Typos },\r\n  components: {},\r\n\r\n  data () {\r\n    return {\r\n      approve: this.$route.query.approve,\r\n      noApprove: this.$route.query.noApprove,\r\n      doubleTypeData: [],\r\n      ids: [],\r\n      TyposShow: false,\r\n      // SimilarityShow: false,\r\n      form: {},\r\n      fileList: [],\r\n      manuscriptData: {},\r\n      all: this.$route.query.all || false,\r\n      rowId: this.$route.query.rowId,\r\n      manuscriptFlag: false,\r\n      isexcellent: this.$route.query.isexcellent || false,\r\n      helper: this.$route.query.helper || false,\r\n      similar: '0%',\r\n      suspend: 0,\r\n      wrong: 0,\r\n      show: true,\r\n      clickTime: ''\r\n\r\n    }\r\n  },\r\n  inject: ['openoffice', 'openPdf'],\r\n  created () {\r\n    this.getDoubleQuoteDetails()\r\n  },\r\n  methods: {\r\n    passClick (auditStatus) {\r\n      this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.getCheckDouble(this.$route.query.rowId, auditStatus)\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消操作'\r\n        })\r\n      })\r\n    },\r\n    // 审核 (加分项)\r\n    async getCheckDouble (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckDouble({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n\r\n    /**\r\n        *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_same_double'\r\n      })\r\n      var { data } = res\r\n      this.doubleTypeData = data.evaluation_same_double\r\n    },\r\n\r\n    // **************\r\n    ...mapActions(['getDoubleQuoteDetails']),\r\n    // ******************\r\n    showHelper () {\r\n      if (new Date().getTime() - this.clickTime > 1000) {\r\n        this.clickTime = new Date().getTime()\r\n        this.show = !this.show\r\n      }\r\n    },\r\n\r\n    async getcorrector () {\r\n      const res = await this.$api.publicOpinionNew.corrector({\r\n        title: '',\r\n        content: this.form.content,\r\n        sessions: '',\r\n        times: ''\r\n      })\r\n      var data = JSON.parse(res.data)\r\n\r\n      this.suspend = data.detail.length\r\n      this.wrong = data.suspend_detail.length\r\n    },\r\n    // 预览(另一种方法替代)\r\n    // priew (data) {\r\n    //   const arr = ['doc', 'docx', 'xlsx']\r\n    //   if (arr.includes(data.extension)) {\r\n    //     this.openoffice(data.openUrl)\r\n    //   }\r\n    // },\r\n\r\n    // 后台获取详情数据(部分还需修改)\r\n    async getDoubleQuoteDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqDoubleQuoteDetails(this.$route.query.rowId)\r\n      var { data } = res\r\n\r\n      this.form = data\r\n    },\r\n    // 通用的附件下载方法download 只需要改对应的附件id和名字\r\n    download (item) {\r\n      this.$api.proposal.downloadFile({ id: item.id }, item.oldName)\r\n    },\r\n    callback (data) {\r\n      this.TyposShow = false\r\n      console.log(data)\r\n    }\r\n  },\r\n  mounted () {\r\n    this.dictionaryPubkvs()\r\n  },\r\n  computed: {\r\n    // 测试: 通过映射函数 获取title信息\r\n    ...mapState(['title']),\r\n    doubleClass () {\r\n      var double1 = ''\r\n      this.doubleTypeData.forEach(item => {\r\n        if (item.id === this.form.doubleType) {\r\n          double1 = item.value\r\n        }\r\n      })\r\n      return double1\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" >\r\n.detailComment {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding-top: 33px;\r\n  background: #fff;\r\n  .officeDetial-title {\r\n    font-size: 26px;\r\n    font-size: 24px;\r\n    font-family: PingFang SC;\r\n    font-weight: 800;\r\n    color: #333333;\r\n    line-height: 36px;\r\n    text-align: center;\r\n    margin: 29px;\r\n  }\r\n  .relevantInformation {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    .officeDetial-org {\r\n      font-size: $textSize14;\r\n      font-family: PingFang SC;\r\n      font-weight: 400;\r\n      line-height: 24px;\r\n      // display: flex;\r\n      // justify-content: space-around;\r\n      // padding-left: 40px;\r\n      .org-item {\r\n        color: #999999;\r\n        // margin-right: 140px;\r\n        // min-width: 300px;\r\n        span {\r\n          margin-left: 38px;\r\n        }\r\n      }\r\n      // .org-item + .org-item {\r\n      //     margin-left: 140px;\r\n      // }\r\n    }\r\n  }\r\n\r\n  .contBox {\r\n    margin-top: 20px;\r\n    border-top: 2px solid #ebebeb;\r\n    display: flex;\r\n\r\n    .content {\r\n      flex: 1;\r\n      padding: 30px 40px;\r\n      line-height: 30px;\r\n      min-height: 500px;\r\n    }\r\n    .content + .content {\r\n      border-left: 1px solid #ebebeb;\r\n    }\r\n  }\r\n\r\n  .similarityImg {\r\n    padding-left: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    img {\r\n      width: 80px;\r\n      cursor: pointer;\r\n    }\r\n    .analysisReslut {\r\n      height: 60px;\r\n      line-height: 60px;\r\n      padding: 0 44px;\r\n      // background: #f5f5fb;\r\n      background-image: url(\"../../../assets/qdimg/round.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      border-radius: 20px;\r\n      span {\r\n        font-size: 26px;\r\n        font-family: PangMenZhengDao;\r\n        font-weight: 700;\r\n        color: #007bff;\r\n        line-height: 36px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n  .manuscript {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-right: 40px;\r\n    padding-bottom: 20px;\r\n    .yuangoa {\r\n      flex: 1;\r\n      span {\r\n        font-size: 26px;\r\n        font-family: PingFang SC;\r\n        font-weight: 500;\r\n        color: #999999;\r\n        line-height: 36px;\r\n        margin-left: 40px;\r\n      }\r\n      span + span {\r\n        margin-left: 50%;\r\n      }\r\n    }\r\n  }\r\n  .fileBox {\r\n    width: 100%;\r\n    background: #ffffff;\r\n    border-radius: 10px;\r\n    padding: 30px;\r\n    .file_title {\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-bottom: 23px;\r\n    }\r\n    .fileListt {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      justify-content: space-between;\r\n      .file_item {\r\n        width: 48%;\r\n        background: #f5f5fb;\r\n        flex-shrink: 0;\r\n        height: 60px;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0 18px;\r\n        margin-bottom: 10px;\r\n        .file_type {\r\n          width: 32px;\r\n          height: 32px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .file_name {\r\n          margin-left: 12px;\r\n          flex: 1;\r\n          cursor: pointer;\r\n        }\r\n        .file_load {\r\n          display: flex;\r\n          align-items: center;\r\n          .load_text {\r\n            font-size: $textSize16;\r\n            font-family: PingFang SC;\r\n            font-weight: 500;\r\n            color: #007bff;\r\n            line-height: 36px;\r\n            cursor: pointer;\r\n          }\r\n          .shu {\r\n            width: 2px;\r\n            height: 22px;\r\n            background: #4f96fe;\r\n            margin: 0 12px;\r\n          }\r\n          .del {\r\n            width: 24px;\r\n            height: 24px;\r\n            margin-left: 23px;\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .hanldtype {\r\n    display: flex;\r\n    padding: 20px 30px;\r\n    justify-content: space-between;\r\n    > div {\r\n      font-weight: 700;\r\n    }\r\n  }\r\n  .handinfo {\r\n    width: 100%;\r\n    .hanldClounm .hanldCont .el-checkbox {\r\n      width: 25%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}