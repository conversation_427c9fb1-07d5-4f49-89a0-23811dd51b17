{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\wang-editor\\wang-editor.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\wang-editor\\wang-editor.vue", "mtime": 1752541693505}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAYA;AACA;AACA;AACA;EAAAA;AAAA;AACA;EACAC,mBADA;EAEAC;IACAC;MACAC;IADA,CADA;IAIAC;MACAC,aADA;MAEAC;IAFA,CAJA;IAQAC;MACAF,YADA;MAEAC;IAFA,CARA;IAYAE;MACAH,YADA;MAEAC;IAFA;EAZA,CAFA;;EAmBAG;IACA;MACAC,UADA;MAEAC,YAFA;MAGAC;IAHA;EAKA,CAzBA;;EA0BAC;IACAX;MACA;QACA;UACA;QACA;MACA;IACA;;EAPA,CA1BA;EAmCAY;IACAC,aADA;IACA;IACAC,cAFA,CAEA;;EAFA,CAnCA;EAuCAC;IACA;IACAC;MACA;MACA;MACA;IACA,CANA;;IAOA;IACAC;MACA;;MACAC,kCAFA,CAEA;;MACA;QACA;QACA;QACAA,sBAHA,CAGA;;QACAA;;QACAV,gBALA,CAKA;;QACAU,kCANA,CAOA;;;QACA;UACA;QACA;MACA,CAXA;IAYA,CAvBA;;IAwBA;IACAC;MACA,iBADA,CAEA;;;MACA;QACAC,0BADA;QACA;QACAC,sBAFA;QAEA;QACAC,WAHA;QAGA;QACA;QACAC,QACA,MADA,EACA;QACA,MAFA,EAEA;QACA,UAHA,EAGA;QACA,UAJA,EAIA;QACA,QALA,EAKA;QACA,QANA,EAMA;QACA,YAPA,EAOA;QACA,WARA,EAQA;QACA,eATA,EASA;QACA,WAVA,EAUA;QACA,WAXA,EAWA;QACA,MAZA,EAYA;QACA,MAbA,EAaA;QACA,SAdA,EAcA;QACA,OAfA,EAeA;QACA,UAhBA,EAgBA;QACA,OAjBA,EAiBA;QACA,OAlBA,EAkBA;QACA,OAnBA,EAmBA;QACA;QACA,MArBA,EAqBA;QACA,MAtBA,EAsBA;QACA,KAvBA,CAuBA;QAvBA,CALA;QA8BAC,iBA9BA;QA8BA;QACAC;UACA;UACAC;UACAA;;UACAR;;UACA;UACAA;;UACA;YACAA;UACA,CAFA,MAEA;YACAA;UACA;QACA,CA3CA;QA4CAS;UACA;YACAT,oBACA,YADA,EACA,uEADA;UAGA;QACA,CAlDA;QAmDAU;UACA;UACAC;UACAC;YACAC;UACA,CAFA;QAGA;MAzDA,EAHA,CA8DA;;MACAb;MACAA;IACA;;EA1FA,CAvCA;;EAmIAc;IACA;IACA;MACAC;QACA;QACA,2BACA;AACA;AACA,mBAHA;QAKA;MACA;;MAEAC;QACA;QACAC;QACA;QACAC;QACAA;QACAA;QACAA;QACAA;QACA5B;MACA;;MAEA6B;;IAvBA;;IAyBAC;IACA;EACA;;AAhKA", "names": ["BtnMenu", "name", "props", "value", "required", "disabled", "type", "default", "placeholder", "max", "data", "editor", "editorId", "contentLength", "watch", "model", "prop", "event", "methods", "randomId", "initEditor", "_this", "setConfig", "uploadImgShowBase64", "pasteFilterStyle", "zIndex", "menus", "showLinkImg", "onchange", "html", "onlineVideoCallback", "customUploadImg", "formData", "axios", "insertImgFn", "created", "constructor", "clickHandler", "console", "str", "tryChangeActive", "WangEditor"], "sourceRoot": "src/components/wang-editor", "sources": ["wang-editor.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div :id=\"editorId\"\r\n         class=\"wang-editor\"></div>\r\n    <p v-if=\"contentLength > $props.max\"\r\n       style=\"color: red;\">\r\n      已超出最大{{ $props.max }}字数限制！\r\n    </p>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 引入富文本编辑器\r\nimport WangEditor from 'wangeditor'\r\nimport axios from 'axios'\r\nconst { BtnMenu } = WangEditor\r\nexport default {\r\n  name: 'wang-editor',\r\n  props: {\r\n    value: {\r\n      required: true\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: () => '请输入正文'\r\n    },\r\n    max: {\r\n      type: Number,\r\n      default: () => 9999999\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      editor: '',\r\n      editorId: '',\r\n      contentLength: 0\r\n    }\r\n  },\r\n  watch: {\r\n    value (newval) {\r\n      if (this.editor) {\r\n        if (newval !== this.editor.txt.html()) {\r\n          this.editor.txt.html(newval)\r\n        }\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value', // 这个字段，是指父组件设置 v-model 时，将变量值传给子组件的 msg\r\n    event: 'input'// 这个字段，是指父组件监听 parent-event 事件\r\n  },\r\n  methods: {\r\n    // // 生成一个随机不重复id,可以通过时间和随机数生成\r\n    randomId () {\r\n      const baseId = 'wang_editor'\r\n      const now = new Date().getTime()\r\n      return `${baseId}_${now}`\r\n    },\r\n    // 初始化编辑器\r\n    initEditor () {\r\n      const _this = this\r\n      _this.editorId = _this.randomId()// 生成一个id\r\n      this.$nextTick(() => {\r\n        // 获取实例,wangEditor是被注册在window的\r\n        const editor = new WangEditor('#' + _this.editorId)\r\n        _this.editor = editor// 将实例保存待调用其他api\r\n        _this.setConfig()\r\n        editor.create()// 开始创建编辑器；\r\n        _this.editor.txt.html(this.value)\r\n        // 设置是否可编辑\r\n        if (this.disabled !== 'undefined') {\r\n          this.editor.$textElem.attr('contenteditable', !this.disabled)\r\n        }\r\n      })\r\n    },\r\n    // 创建富文本编辑器\r\n    setConfig () {\r\n      var _this = this\r\n      // 开始创建\r\n      const setting = {\r\n        uploadImgShowBase64: false, // 是否允许上传base64位图片\r\n        pasteFilterStyle: true, // 是否过滤粘贴的样式\r\n        zIndex: 100, // 设置层叠位置\r\n        // 菜单列表\r\n        menus: [\r\n          'head', // 标题\r\n          'bold', // 粗体\r\n          'fontSize', // 字号\r\n          'fontName', // 字体\r\n          'italic', // 斜体\r\n          'indent', // 缩进\r\n          'lineHeight', // 行高\r\n          'underline', // 下划线\r\n          'strikeThrough', // 删除线\r\n          'foreColor', // 文字颜色\r\n          'backColor', // 背景颜色\r\n          'link', // 插入链接\r\n          'list', // 列表\r\n          'justify', // 对齐方式\r\n          'quote', // 引用\r\n          'emoticon', // 表情\r\n          'image', // 插入图片\r\n          'table', // 表格\r\n          'video', // 插入视频\r\n          // 'code', // 插入代码\r\n          'undo', // 撤销\r\n          'redo', // 恢复\r\n          'qgs' // 恢复\r\n        ],\r\n        showLinkImg: true, // 是否显示“网络图片”tab\r\n        onchange: function (html) {\r\n          // console.log('html===>', html)\r\n          html = html.replace(/<strong>(.*?)<\\/strong>/g, '$1')\r\n          html = html.replace(/<b>(.*?)<\\/b>/g, '$1')\r\n          _this.$emit('input', html)\r\n          const text = html\r\n          _this.contentLength = text.length\r\n          if (_this.contentLength > _this.$props.max) {\r\n            _this.$emit('restrictions', `已超出最大${_this.$props.max}字数限制！`)\r\n          } else {\r\n            _this.$emit('restrictions', '')\r\n          }\r\n        },\r\n        onlineVideoCallback: v => {\r\n          if (v.endsWith('.mp4')) {\r\n            _this.editor.cmd.do(\r\n              'insertHTML', `<video src=\"${v}\" controls=\"controls\" style=\"max-width:100%\"></video>`\r\n            )\r\n          }\r\n        },\r\n        customUploadImg: (resultFiles, insertImgFn) => {\r\n          const formData = new FormData()\r\n          formData.append('upfile', resultFiles[0])\r\n          axios.post(`${_this.$api.general.baseURL()}/ueditor/exec?action=uploadimage`, formData).then(res => {\r\n            insertImgFn(res.data.url)\r\n          })\r\n        }\r\n      }\r\n      // 配置给编辑器\r\n      _this.editor.config = Object.assign(_this.editor.config, setting)\r\n      _this.editor.config.placeholder = _this.$props.placeholder\r\n    }\r\n  },\r\n  created () {\r\n    // 创建editor实例\r\n    class AlertMenu extends BtnMenu {\r\n      constructor(editor) {\r\n        // data-title属性表示当鼠标悬停在该按钮上时提示该按钮的功能简述\r\n        const $elem = WangEditor.$(\r\n          `<div class=\"w-e-menu\" data-title=\"清除格式\">\r\n               <svg style=\"width:14px;heigth:14px;\" viewBox=\"0 0 1024 1024\"><path d=\"M969.382408 288.738615l-319.401123-270.852152a67.074236 67.074236 0 0 0-96.459139 5.74922l-505.931379 574.922021a68.35184 68.35184 0 0 0-17.886463 47.910169 74.101061 74.101061 0 0 0 24.274486 47.910168l156.50655 132.232065h373.060512L975.131628 383.281347a67.074236 67.074236 0 0 0-5.74922-96.459139z m-440.134747 433.746725H264.144729l-90.071117-78.572676c-5.74922-5.74922-12.137243-12.137243-12.137243-17.886463a36.411728 36.411728 0 0 1 5.749221-24.274485l210.804741-240.828447 265.102932 228.691204z m-439.495945 180.781036h843.218964a60.047411 60.047411 0 1 1 0 120.733624H89.751716a60.047411 60.047411 0 1 1 0-120.733624z m0 0\"></path></svg>\r\n            </div>`\r\n        )\r\n        super($elem, editor)\r\n      }\r\n\r\n      clickHandler () {\r\n        var editor = this.editor\r\n        console.log('editor===>>', editor)\r\n        var str = editor.txt.html()\r\n        str = str.replace(/<xml>[\\s\\S]*?<\\/xml>/ig, '')\r\n        str = str.replace(/<style>[\\s\\S]*?<\\/style>/ig, '')\r\n        str = str.replace(/<\\/?[^>]*>/g, '')\r\n        str = str.replace(/[ | ]*\\n/g, '\\n')\r\n        str = str.replace(/&nbsp;/ig, '')\r\n        editor.txt.html(str)\r\n      }\r\n\r\n      tryChangeActive () { }\r\n    }\r\n    WangEditor.registerMenu('qgs', AlertMenu)\r\n    this.initEditor()\r\n  }\r\n}\r\n\r\n</script>\r\n<style lang=\"scss\">\r\n.wang-editor {\r\n  width: 100%;\r\n\r\n  .w-e-text-container {\r\n    .w-e-text {\r\n      img {\r\n        width: 80%;\r\n        height: auto;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}