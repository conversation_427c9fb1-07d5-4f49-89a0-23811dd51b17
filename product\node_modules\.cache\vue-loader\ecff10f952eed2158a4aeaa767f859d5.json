{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-export\\zy-export.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-export\\zy-export.vue", "mtime": 1752541693526}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["zy-export.vue"], "names": [], "mappings": ";AAmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-export.vue", "sourceRoot": "src/components/zy-export", "sourcesContent": ["<template>\r\n  <div class=\"zy-export scrollBar\">\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-position=\"top\"\r\n             class=\"newForm\">\r\n      <el-form-item label=\"导出字段：\"\r\n                    prop=\"field\"\r\n                    class=\"form-title\">\r\n        <el-checkbox-group v-model=\"form.field\">\r\n          <el-checkbox v-for=\"item in field\"\r\n                       :label=\"item.id\"\r\n                       :key=\"item.id\">{{item.key}}</el-checkbox>\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <div class=\"form-button\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n\r\n    <div class=\"progress-box\"\r\n         v-if=\"show\"\r\n         @click.stop>\r\n      <div class=\"progress\">\r\n        <el-progress :percentage=\"percentage\"\r\n                     :color=\"customColor\"></el-progress>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport exportExcel from '@mixins/exportExcel'\r\nexport default {\r\n  name: 'zyExport',\r\n  data () {\r\n    return {\r\n      name: '',\r\n      percentage: 0,\r\n      customColor: '#94070A',\r\n      form: {\r\n        field: []\r\n      },\r\n      rules: {\r\n        field: [\r\n          { required: true, message: '请选择导出Excel的字段', trigger: 'blur' }\r\n        ]\r\n      },\r\n      field: [],\r\n      data: [],\r\n      show: false,\r\n      dataShow: false,\r\n      exporttypes: false\r\n    }\r\n  },\r\n  props: {\r\n    type: [String, Number, Array, Object],\r\n    params: {\r\n      type: Object,\r\n      default: () => {\r\n        return {}\r\n      }\r\n    },\r\n    excelId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  mixins: [exportExcel],\r\n  created () {\r\n    this.exportFields()\r\n    this.exportDatas()\r\n  },\r\n  watch: {\r\n    data (val) {\r\n      if (val.length && this.dataShow) {\r\n        this.exportExcelMethods()\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    async exportFields () {\r\n      const res = await this.$api.general.exportFields({ exportType: this.type })\r\n      res.data.fields.forEach((item, index) => {\r\n        item.id = index\r\n        if (item.checked) {\r\n          this.form.field.push(index)\r\n        }\r\n      })\r\n      this.name = res.data.name\r\n      this.field = res.data.fields\r\n    },\r\n    async exportDatas () {\r\n      var params = {}\r\n      if (!this.excelId) {\r\n        params = this.params\r\n      } else {\r\n        params.ids = this.excelId\r\n      }\r\n      params.exportType = this.type\r\n      const res = await this.$api.general.exportDatas(params)\r\n      this.exporttypes = true\r\n      this.data = res.data\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          this.show = true\r\n          this.setInterval = setInterval(() => {\r\n            if (this.percentage >= 90) {\r\n              clearInterval(this.setInterval)\r\n            } else {\r\n              this.percentage = this.percentage + 9\r\n            }\r\n          }, 200)\r\n          if (this.data.length || this.exporttypes) {\r\n            setTimeout(() => {\r\n              this.exportExcelMethods()\r\n            }, 400)\r\n          } else {\r\n            this.dataShow = true\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: '导出字段不能为空！',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    exportExcelMethods () {\r\n      this.percentage = 100\r\n      var field = []\r\n      this.form.field.forEach(item => {\r\n        field.push(this.fieldMethods(item))\r\n      })\r\n      this.exportExcel(this.name, field, this.data)\r\n      clearInterval(this.setInterval)\r\n      setTimeout(() => {\r\n        this.show = false\r\n        this.exporttypes = false\r\n        this.percentage = 0\r\n      }, 1000)\r\n    },\r\n    fieldMethods (id) {\r\n      var arr = {}\r\n      this.field.forEach(item => {\r\n        if (item.id === id) {\r\n          arr = item\r\n        }\r\n      })\r\n      return arr\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('callback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-export.scss\";\r\n</style>\r\n"]}]}