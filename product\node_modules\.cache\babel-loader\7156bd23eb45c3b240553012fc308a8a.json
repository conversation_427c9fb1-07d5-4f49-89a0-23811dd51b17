{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecord\\detailsContents.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecord\\detailsContents.vue", "mtime": 1752541693846}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA6DA;AAEA;EACAA,uBADA;EAEAC,cAFA;;EAIAC;IACA;MACAC,OADA;MAEAC,gBAFA;MAGA;MACAC,QAJA;MAKAC,YALA;MAMAC,kBANA;MAOAC,mCAPA;MAQAC,8BARA;MAUAC,kCAVA;MAWAC,sCAXA;MAaAC,qBAbA;MAcAC,mDAdA;MAeAC,yCAfA;MAgBAC,aAhBA;MAiBAC,UAjBA;MAkBAC,QAlBA;MAmBAC,UAnBA;MAoBAC;IApBA;EAuBA,CA5BA;;EA6BAC,iCA7BA;;EA8BAC;IACA;EACA,CAhCA;;EAiCAC,WACA,CAlCA;;EAmCAC;IAEAC;MACA;QACAC,uBADA;QAEAC,sBAFA;QAGAC;MAHA,GAIAC,IAJA,CAIA;QACA;MACA,CANA,EAMAC,KANA,CAMA;QACA;UACAF,YADA;UAEAG;QAFA;MAIA,CAXA;IAYA,CAfA;;IAgBA;IACA;MACA;QAAA3B;QAAA4B;MAAA;MACA;QAAAC;QAAAC;MAAA;;MACA;QACA;UACAH,eADA;UAEAH;QAFA;MAIA;IACA,CA1BA;;IA4BA,iCA5BA;;IA6BAO;MACA;QACA;QACA;MACA;IACA,CAlCA;;IAoCA;MACA;QACAC,SADA;QAEAC,0BAFA;QAGAC,YAHA;QAIAC;MAJA;MAMA;MAEA;MACA;IACA,CA/CA;;IAgDA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;MACA;MACA;QAAApC;MAAA;MACA;;MACA;QACA,kEADA,CACA;MACA;IACA,CA/DA;;IAgEA;IACAqC;MACA;QAAAC;MAAA;IACA,CAnEA;;IAoEAC;MACA;MACAC;IACA;;EAvEA,CAnCA;EA6GAC,YACA;IACA;EAFA;AA7GA", "names": ["name", "components", "data", "ids", "TyposShow", "form", "fileList", "manuscriptData", "all", "rowId", "approve", "noApprove", "manuscriptFlag", "isexcellent", "helper", "similar", "suspend", "wrong", "show", "clickTime", "inject", "created", "mounted", "methods", "passClick", "confirmButtonText", "cancelButtonText", "type", "then", "catch", "message", "auditStatus", "<PERSON><PERSON><PERSON>", "errmsg", "showHelper", "title", "content", "sessions", "times", "download", "id", "callback", "console", "computed"], "sourceRoot": "src/views/AssessmentOrgan/MonthlyWorkRecord", "sources": ["detailsContents.vue"], "sourcesContent": ["<template>\r\n  <div class=\"detailComment scrollBar \">\r\n    <div class=\"officeDetial-title\"> {{form.title}} </div>\r\n    <div class=\"officeDetial-org\">\r\n      <div class=\"org-item\"> 所属个人: <span> {{form.publishUserName}}</span> </div>\r\n      <div class=\"org-item\"> 时间： <span>{{ $format(form.publishTime).substr(0,16)}}</span> </div>\r\n      <div class=\"org-item\"> 部门： <span> {{form.officeName}}</span> </div>\r\n      <div>\r\n\r\n        <el-button type=\"primary\"\r\n                   size=\"small\"\r\n                   v-if=\"this.approve==='true'\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n\r\n        <el-button type=\"danger\"\r\n                   size=\"small\"\r\n                   v-if=\"this.noApprove==='true'\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n      </div>\r\n\r\n    </div>\r\n    <!-- 内容 -->\r\n    <div class=\"contBox\">\r\n      <div class=\"content\"\r\n           v-if=\"form.content\"\r\n           v-html=\"form.content\">\r\n      </div>\r\n    </div>\r\n\r\n    <!-- *****附件***** -->\r\n    <div class=\"fileBox\"\r\n         v-if=\"form.attachment\">\r\n      <div class=\"file_title\"\r\n           v-if=\"form.attachment.length!==0\"> 附件 </div>\r\n\r\n      <div class=\"fileListt\">\r\n        <div class=\"file_item\"\r\n             v-for=\"(item,index) in form.attachment \"\r\n             :key=\"index\">\r\n\r\n          <div class=\"file_name\"> {{item.oldName}} </div>\r\n\r\n          <div class=\"file_load\">\r\n            <div class=\"load_text\"><a :href=\"item.fullPath\"\r\n                 target=\"_blank\">预览</a>\r\n            </div>\r\n            <div class=\"shu\"></div>\r\n            <div class=\"load_text\"\r\n                 @click=\"download(item)\">下载</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { mapActions, mapState } from 'vuex'\r\n\r\nexport default {\r\n  name: 'detailsContents',\r\n  components: {},\r\n\r\n  data () {\r\n    return {\r\n      ids: [],\r\n      TyposShow: false,\r\n      // SimilarityShow: false,\r\n      form: {},\r\n      fileList: [],\r\n      manuscriptData: {},\r\n      all: this.$route.query.all || false,\r\n      rowId: this.$route.query.rowId,\r\n\r\n      approve: this.$route.query.approve,\r\n      noApprove: this.$route.query.noApprove,\r\n\r\n      manuscriptFlag: false,\r\n      isexcellent: this.$route.query.isexcellent || false,\r\n      helper: this.$route.query.helper || false,\r\n      similar: '0%',\r\n      suspend: 0,\r\n      wrong: 0,\r\n      show: true,\r\n      clickTime: ''\r\n\r\n    }\r\n  },\r\n  inject: ['openoffice', 'openPdf'],\r\n  created () {\r\n    this.getWorkDetails()\r\n  },\r\n  mounted () {\r\n  },\r\n  methods: {\r\n\r\n    passClick (auditStatus) {\r\n      this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.getCheckMonthlyWork(this.$route.query.rowId, auditStatus)\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消操作'\r\n        })\r\n      })\r\n    },\r\n    // 审核 (加分项)\r\n    async getCheckMonthlyWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckMonthlyWork({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n\r\n    ...mapActions(['getWorkDetails']),\r\n    showHelper () {\r\n      if (new Date().getTime() - this.clickTime > 1000) {\r\n        this.clickTime = new Date().getTime()\r\n        this.show = !this.show\r\n      }\r\n    },\r\n\r\n    async getcorrector () {\r\n      const res = await this.$api.publicOpinionNew.corrector({\r\n        title: '',\r\n        content: this.form.content,\r\n        sessions: '',\r\n        times: ''\r\n      })\r\n      var data = JSON.parse(res.data)\r\n\r\n      this.suspend = data.detail.length\r\n      this.wrong = data.suspend_detail.length\r\n    },\r\n    // 预览(另一种方法替代)\r\n    // priew (data) {\r\n    //   const arr = ['doc', 'docx', 'xlsx']\r\n    //   if (arr.includes(data.extension)) {\r\n    //     this.openoffice(data.openUrl)\r\n    //   }\r\n    // },\r\n\r\n    async getWorkDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqWorkDetails(this.$route.query.rowId)\r\n      var { data } = res\r\n      this.form = data\r\n      if (this.form.content) {\r\n        this.form.content = this.form.content.replace(/&amp;nbsp;/g, ' ') // 消除空格字符\r\n      }\r\n    },\r\n    // 通用的附件下载方法download 只需要改对应的附件id和名字\r\n    download (data) {\r\n      this.$api.proposal.downloadFile({ id: this.form.attachment.id }, this.form.attachment.oldName)\r\n    },\r\n    callback (data) {\r\n      this.TyposShow = false\r\n      console.log(data)\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    // 测试: 通过映射函数 获取title信息\r\n    ...mapState(['title'])\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" >\r\n.detailComment {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding-top: 33px;\r\n  background: #fff;\r\n  .officeDetial-title {\r\n    font-size: 26px;\r\n    font-size: 24px;\r\n    font-family: PingFang SC;\r\n    font-weight: 800;\r\n    color: #333333;\r\n    line-height: 36px;\r\n    text-align: center;\r\n    margin: 29px;\r\n  }\r\n  .officeDetial-org {\r\n    font-size: $textSize14;\r\n    font-family: PingFang SC;\r\n    font-weight: 400;\r\n    line-height: 36px;\r\n    display: flex;\r\n    justify-content: space-around;\r\n    // padding-left: 40px;\r\n    .org-item {\r\n      color: #999999;\r\n      // margin-right: 140px;\r\n      // min-width: 300px;\r\n      span {\r\n        margin-left: 38px;\r\n      }\r\n    }\r\n    // .org-item + .org-item {\r\n    //     margin-left: 140px;\r\n    // }\r\n  }\r\n\r\n  .contBox {\r\n    margin-top: 20px;\r\n    border-top: 2px solid #ebebeb;\r\n    display: flex;\r\n\r\n    .content {\r\n      flex: 1;\r\n      padding: 30px 40px;\r\n      line-height: 30px;\r\n      min-height: 500px;\r\n    }\r\n    .content + .content {\r\n      border-left: 1px solid #ebebeb;\r\n    }\r\n  }\r\n\r\n  .similarityImg {\r\n    padding-left: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    img {\r\n      width: 80px;\r\n      cursor: pointer;\r\n    }\r\n    .analysisReslut {\r\n      height: 60px;\r\n      line-height: 60px;\r\n      padding: 0 44px;\r\n      // background: #f5f5fb;\r\n      background-image: url(\"../../../assets/qdimg/round.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      border-radius: 20px;\r\n      span {\r\n        font-size: 26px;\r\n        font-family: PangMenZhengDao;\r\n        font-weight: 700;\r\n        color: #007bff;\r\n        line-height: 36px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n  .manuscript {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-right: 40px;\r\n    padding-bottom: 20px;\r\n    .yuangoa {\r\n      flex: 1;\r\n      span {\r\n        font-size: 26px;\r\n        font-family: PingFang SC;\r\n        font-weight: 500;\r\n        color: #999999;\r\n        line-height: 36px;\r\n        margin-left: 40px;\r\n      }\r\n      span + span {\r\n        margin-left: 50%;\r\n      }\r\n    }\r\n  }\r\n  .fileBox {\r\n    width: 100%;\r\n    background: #ffffff;\r\n    border-radius: 10px;\r\n    padding: 30px;\r\n    .file_title {\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-bottom: 23px;\r\n    }\r\n    .fileListt {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      justify-content: space-between;\r\n      .file_item {\r\n        width: 48%;\r\n        background: #f5f5fb;\r\n        flex-shrink: 0;\r\n        height: 60px;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0 18px;\r\n        margin-bottom: 10px;\r\n        .file_type {\r\n          width: 32px;\r\n          height: 32px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .file_name {\r\n          margin-left: 12px;\r\n          flex: 1;\r\n          cursor: pointer;\r\n        }\r\n        .file_load {\r\n          display: flex;\r\n          align-items: center;\r\n          .load_text {\r\n            font-size: $textSize16;\r\n            font-family: PingFang SC;\r\n            font-weight: 500;\r\n            color: #007bff;\r\n            line-height: 36px;\r\n            cursor: pointer;\r\n          }\r\n          .shu {\r\n            width: 2px;\r\n            height: 22px;\r\n            background: #4f96fe;\r\n            margin: 0 12px;\r\n          }\r\n          .del {\r\n            width: 24px;\r\n            height: 24px;\r\n            margin-left: 23px;\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .hanldtype {\r\n    display: flex;\r\n    padding: 20px 30px;\r\n    justify-content: space-between;\r\n    > div {\r\n      font-weight: 700;\r\n    }\r\n  }\r\n  .handinfo {\r\n    width: 100%;\r\n    .hanldClounm .hanldCont .el-checkbox {\r\n      width: 25%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}