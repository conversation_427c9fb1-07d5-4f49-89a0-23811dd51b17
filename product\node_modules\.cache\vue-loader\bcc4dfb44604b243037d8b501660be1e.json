{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\WordCloud.vue?vue&type=style&index=0&id=9846883e&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\WordCloud.vue", "mtime": 1756282591803}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci53b3JkLWNsb3VkLWNvbnRhaW5lciB7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwp9Cg=="}, {"version": 3, "sources": ["WordCloud.vue"], "names": [], "mappings": ";AAgIA;AACA;AACA;AACA", "file": "WordCloud.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div :id=\"chartId\" class=\"word-cloud-container\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport 'echarts-wordcloud'\nexport default {\n  name: 'WordCloud',\n  props: {\n    chartId: {\n      type: String,\n      default: 'wordCloud'\n    },\n    words: {\n      type: Array,\n      required: true,\n      default: () => []\n      // 期望格式: [{ text: '经济建设', weight: 10 }, { text: '人才培养', weight: 8 }, ...]\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      // 预定义颜色数组，模拟图片中的颜色效果\n      colors: [\n        '#4FC3F7', // 浅蓝色\n        '#26C6DA', // 青色\n        '#66BB6A', // 绿色\n        '#FFA726', // 橙色\n        '#FF7043', // 橙红色\n        '#AB47BC', // 紫色\n        '#5C6BC0', // 蓝紫色\n        '#42A5F5', // 蓝色\n        '#FFCA28', // 黄色\n        '#4CAF50', // 绿色\n        '#EF5350', // 红色\n        '#A1E2FF', // 浅蓝色\n        '#00BCD4', // 青蓝色\n        '#FF9800', // 深橙色\n        '#9C27B0' // 深紫色\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.chartId)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.$nextTick(() => {\n        this.updateChart()\n      })\n      // 监听窗口大小变化\n      window.addEventListener('resize', this.resizeChart)\n      // 添加点击事件\n      this.chart.on('click', (params) => {\n        this.$emit('word-click', {\n          text: params.data[3], // 散点图数据格式 [x, y, value, name]\n          weight: params.data[2]\n        })\n      })\n    },\n    updateChart () {\n      if (!this.chart) return\n      // 按照你提供的样式配置词云\n      const option = {\n        backgroundColor: 'transparent',\n        tooltip: {\n          show: true,\n          position: 'top',\n          textStyle: {\n            fontSize: 16\n          }\n        },\n        series: [{\n          type: 'wordCloud',\n          // 网格大小，各项之间间距\n          gridSize: 20,\n          sizeRange: [20, 40],\n          size: 0.6,\n          rotationRange: [0, 0],\n          drawOutOfBound: false,\n          // 位置相关设置\n          left: 'center',\n          top: 'center',\n          right: null,\n          bottom: null,\n          width: '100%',\n          height: '100%',\n          textStyle: {\n            normal: {\n              color: function () {\n                return 'rgb(' + [\n                  Math.round(Math.random() * 200 + 55),\n                  Math.round(Math.random() * 200 + 55),\n                  Math.round(Math.random() * 200 + 55)\n                ].join(',') + ')'\n              }\n            },\n            emphasis: {\n              shadowBlur: 10,\n              shadowColor: '#2ac'\n            }\n          },\n          data: this.words\n        }]\n      }\n      this.chart.setOption(option, true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.word-cloud-container {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}