{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tree-components\\zy-tree-components.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tree-components\\zy-tree-components.vue", "mtime": 1752541693604}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICd6eVRyZWVDb21wb25lbnRzJywNCiAgZGF0YSAoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHRyZWVJZDogdGhpcy52YWx1ZSwNCiAgICAgIHRyZWVEYXRhOiBbXQ0KICAgIH0NCiAgfSwNCiAgcHJvcHM6IHsNCiAgICB2YWx1ZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9LA0KICAgIGtleXdvcmQ6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfSwNCiAgICB0cmVlOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIFtdDQogICAgICB9DQogICAgfSwNCiAgICBoaWVyYXJjaHk6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDANCiAgICB9LA0KICAgIG5vZGVLZXk6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICdpZCcNCiAgICB9LA0KICAgIC8vIOagkee7k+aehOmFjee9rg0KICAgIHByb3BzOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgY2hpbGRyZW46ICdjaGlsZHJlbicsDQogICAgICAgICAgbGFiZWw6ICdsYWJlbCcNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgZGV0ZXJtaW5lOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIGNoaWxkOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIGFueWtleTogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiBmdW5jdGlvbiAoKSB7DQogICAgICAgIHJldHVybiBbXQ0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgbW9kZWw6IHsNCiAgICBwcm9wOiAndmFsdWUnLA0KICAgIGV2ZW50OiAnaWQnDQogIH0sDQogIHdhdGNoOiB7DQogICAgdmFsdWUgKHZhbCkgew0KICAgICAgdGhpcy50cmVlSWQgPSB2YWwNCiAgICAgIHRoaXMuc2VsZWN0ZWRNZXRob2RzKCkNCiAgICB9LA0KICAgIHRyZWVJZCAodmFsKSB7DQogICAgICB0aGlzLiRlbWl0KCdpZCcsIHZhbCkNCiAgICB9LA0KICAgIHRyZWUgKHZhbCkgew0KICAgICAgdGhpcy50cmVlRGF0YUNvcGUodGhpcy5kZWVwQ29weSh0aGlzLnRyZWUpKQ0KICAgIH0sDQogICAgYW55a2V5ICh2YWwpIHsNCiAgICAgIHRoaXMudHJlZURhdGFDb3BlKHRoaXMuZGVlcENvcHkodGhpcy50cmVlKSkNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQgKCkgew0KICAgIHRoaXMudHJlZURhdGFDb3BlKHRoaXMuZGVlcENvcHkodGhpcy50cmVlKSwgdHJ1ZSkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIHBhZGRpbmcgKGluZGV4KSB7DQogICAgICB2YXIgaGllcmFyY2h5ID0gMTggKiBpbmRleA0KICAgICAgcmV0dXJuIGhpZXJhcmNoeQ0KICAgIH0sDQogICAgdHJlZURhdGFDb3BlIChkYXRhKSB7DQogICAgICBkYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIGl0ZW0uc2VsZWN0ZWQgPSBmYWxzZQ0KICAgICAgICBpZiAodGhpcy50cmVlSWQgPT09IGl0ZW1bdGhpcy5ub2RlS2V5XSkgew0KICAgICAgICAgIGl0ZW0uc2VsZWN0ZWQgPSB0cnVlDQogICAgICAgIH0NCiAgICAgICAgaWYgKGl0ZW1bdGhpcy5wcm9wcy5jaGlsZHJlbl0ubGVuZ3RoKSB7DQogICAgICAgICAgaWYgKCh0eXBlb2YgaXRlbS5hY3RpdmUpID09PSAndW5kZWZpbmVkJykgeyAvLyBlc2xpbnQtZGlzYWJsZS1saW5lDQogICAgICAgICAgICBpdGVtLmFjdGl2ZSA9IGZhbHNlDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuYW55a2V5LmZvckVhY2goaXRlbXMgPT4gew0KICAgICAgICAgICAgaWYgKGl0ZW1bdGhpcy5ub2RlS2V5XSA9PT0gaXRlbXMpIHsNCiAgICAgICAgICAgICAgaXRlbS5hY3RpdmUgPSB0cnVlDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHRoaXMudHJlZURhdGEgPSBkYXRhDQogICAgICB0aGlzLnRyZWVoaWVyYXJjaHkodGhpcy50cmVlRGF0YSwgdHJ1ZSkNCiAgICB9LA0KICAgIHRyZWVoaWVyYXJjaHkgKGRhdGEsIHR5cGUpIHsNCiAgICAgIGRhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgaWYgKGl0ZW1bdGhpcy5ub2RlS2V5XSA9PT0gdGhpcy50cmVlSWQpIHsNCiAgICAgICAgICBjb25zdCByZXN1bHQgPSB0aGlzLm1ha2VEYXRhKGl0ZW0pDQogICAgICAgICAgaWYgKCF0eXBlKSB7DQogICAgICAgICAgICB0aGlzLiRlbWl0KCdvbi10cmVlLWNsaWNrJywgcmVzdWx0KQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBpZiAoaXRlbVt0aGlzLnByb3BzLmNoaWxkcmVuXS5sZW5ndGgpIHsNCiAgICAgICAgICB0aGlzLnRyZWVoaWVyYXJjaHkoaXRlbVt0aGlzLnByb3BzLmNoaWxkcmVuXSwgdHlwZSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNlbGVjdGVkIChkYXRhKSB7DQogICAgICB0aGlzLnRyZWVJZCA9IGRhdGFbdGhpcy5ub2RlS2V5XQ0KICAgIH0sDQogICAgc2VsZWN0ZWRNZXRob2RzICgpIHsNCiAgICAgIGlmICh0aGlzLmhpZXJhcmNoeSA9PT0gMCkgew0KICAgICAgICB0aGlzLnRyZWVoaWVyYXJjaHkodGhpcy50cmVlRGF0YSkNCiAgICAgIH0NCiAgICAgIGNvbnN0IHRyZWVEYXRhID0gdGhpcy50cmVlRGF0YQ0KICAgICAgdHJlZURhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5zZWxlY3RlZCA9IGZhbHNlDQogICAgICAgIGlmICh0aGlzLnRyZWVJZCA9PT0gaXRlbVt0aGlzLm5vZGVLZXldKSB7DQogICAgICAgICAgaXRlbS5zZWxlY3RlZCA9IHRydWUNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHRoaXMudHJlZURhdGEgPSB0cmVlRGF0YQ0KICAgIH0sDQogICAgc3ViVHJlZSAoZGF0YSkgew0KICAgICAgaWYgKCF0aGlzLmNoaWxkKSB7DQogICAgICAgIHRoaXMudHJlZUlkID0gZGF0YVt0aGlzLm5vZGVLZXldDQogICAgICB9DQogICAgICBjb25zdCB0cmVlRGF0YSA9IHRoaXMudHJlZURhdGENCiAgICAgIHRyZWVEYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIGlmIChpdGVtW3RoaXMubm9kZUtleV0gPT09IGRhdGFbdGhpcy5ub2RlS2V5XSkgew0KICAgICAgICAgIGl0ZW0uYWN0aXZlID0gIWl0ZW0uYWN0aXZlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICB0aGlzLnRyZWVEYXRhID0gdHJlZURhdGENCiAgICB9LA0KICAgIHN1YlRyZWVpY29uIChkYXRhKSB7DQogICAgICBjb25zdCB0cmVlRGF0YSA9IHRoaXMudHJlZURhdGENCiAgICAgIHRyZWVEYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIGlmIChpdGVtW3RoaXMubm9kZUtleV0gPT09IGRhdGFbdGhpcy5ub2RlS2V5XSkgew0KICAgICAgICAgIGl0ZW0uYWN0aXZlID0gIWl0ZW0uYWN0aXZlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICB0aGlzLnRyZWVEYXRhID0gdHJlZURhdGENCiAgICB9LA0KICAgIGRlZXBDb3B5IChkYXRhKSB7DQogICAgICB2YXIgdCA9IHRoaXMudHlwZShkYXRhKQ0KICAgICAgdmFyIG8NCiAgICAgIHZhciBpDQogICAgICB2YXIgbmkNCiAgICAgIGlmICh0ID09PSAnYXJyYXknKSB7DQogICAgICAgIG8gPSBbXQ0KICAgICAgfSBlbHNlIGlmICh0ID09PSAnb2JqZWN0Jykgew0KICAgICAgICBvID0ge30NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiBkYXRhDQogICAgICB9DQogICAgICBpZiAodCA9PT0gJ2FycmF5Jykgew0KICAgICAgICBmb3IgKGkgPSAwLCBuaSA9IGRhdGEubGVuZ3RoOyBpIDwgbmk7IGkrKykgew0KICAgICAgICAgIG8ucHVzaCh0aGlzLmRlZXBDb3B5KGRhdGFbaV0pKQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBvDQogICAgICB9IGVsc2UgaWYgKHQgPT09ICdvYmplY3QnKSB7DQogICAgICAgIGZvciAoaSBpbiBkYXRhKSB7DQogICAgICAgICAgb1tpXSA9IHRoaXMuZGVlcENvcHkoZGF0YVtpXSkNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gbw0KICAgICAgfQ0KICAgIH0sDQogICAgdHlwZSAob2JqKSB7DQogICAgICB2YXIgdG9TdHJpbmcgPSBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nDQogICAgICB2YXIgbWFwID0gew0KICAgICAgICAnW29iamVjdCBCb29sZWFuXSc6ICdib29sZWFuJywNCiAgICAgICAgJ1tvYmplY3QgTnVtYmVyXSc6ICdudW1iZXInLA0KICAgICAgICAnW29iamVjdCBTdHJpbmddJzogJ3N0cmluZycsDQogICAgICAgICdbb2JqZWN0IEZ1bmN0aW9uXSc6ICdmdW5jdGlvbicsDQogICAgICAgICdbb2JqZWN0IEFycmF5XSc6ICdhcnJheScsDQogICAgICAgICdbb2JqZWN0IERhdGVdJzogJ2RhdGUnLA0KICAgICAgICAnW29iamVjdCBSZWdFeHBdJzogJ3JlZ0V4cCcsDQogICAgICAgICdbb2JqZWN0IFVuZGVmaW5lZF0nOiAndW5kZWZpbmVkJywNCiAgICAgICAgJ1tvYmplY3QgTnVsbF0nOiAnbnVsbCcsDQogICAgICAgICdbb2JqZWN0IE9iamVjdF0nOiAnb2JqZWN0Jw0KICAgICAgfQ0KICAgICAgcmV0dXJuIG1hcFt0b1N0cmluZy5jYWxsKG9iaildDQogICAgfSwNCiAgICBtYWtlRGF0YSAoZGF0YSkgew0KICAgICAgY29uc3QgdCA9IHRoaXMudHlwZShkYXRhKQ0KICAgICAgbGV0IG8NCiAgICAgIGlmICh0ID09PSAnYXJyYXknKSB7DQogICAgICAgIG8gPSBbXQ0KICAgICAgfSBlbHNlIGlmICh0ID09PSAnb2JqZWN0Jykgew0KICAgICAgICBvID0ge30NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiBkYXRhDQogICAgICB9DQogICAgICBpZiAodCA9PT0gJ2FycmF5Jykgew0KICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGRhdGEubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICBvLnB1c2godGhpcy5tYWtlRGF0YShkYXRhW2ldKSkNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIGlmICh0ID09PSAnb2JqZWN0Jykgew0KICAgICAgICBmb3IgKGNvbnN0IGkgaW4gZGF0YSkgew0KICAgICAgICAgIGlmIChpICE9ICdhY3RpdmUnICYmIGkgIT0gJ3NlbGVjdGVkJykgey8vIGVzbGludC1kaXNhYmxlLWxpbmUNCiAgICAgICAgICAgIG9baV0gPSB0aGlzLm1ha2VEYXRhKGRhdGFbaV0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gbw0KICAgIH0sDQogICAgZmlsdGVyVHJlZURhdGEgKGRhdGEpIHsNCiAgICAgIGlmICh0aGlzLmRldGVybWluZSkgew0KICAgICAgICByZXR1cm4gZGF0YQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMua2V5d29yZCA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuIGRhdGENCiAgICAgIH0NCiAgICAgIHJldHVybiB0aGlzLmZpbHRlclRyZWUoZGF0YSwgdGhpcy5zaG9wZmlsdGVyTm9kZSkgfHwgW10NCiAgICB9LA0KICAgIGZpbHRlclRyZWUgKG5vZGVzLCBwcmVkaWNhdGUpIHsNCiAgICAgIGlmICh0aGlzLmhpZXJhcmNoeSAhPT0gMCkgew0KICAgICAgICByZXR1cm4gbm9kZXMNCiAgICAgIH0NCiAgICAgIGlmICghbm9kZXMgfHwgIW5vZGVzLmxlbmd0aCkgcmV0dXJuIHZvaWQgMCAvLyBlc2xpbnQtZGlzYWJsZS1saW5lDQogICAgICBjb25zdCBjaGlsZHJlbiA9IFtdDQogICAgICBmb3IgKGxldCBub2RlIG9mIG5vZGVzKSB7DQogICAgICAgIG5vZGUgPSBPYmplY3QuYXNzaWduKHt9LCBub2RlKQ0KICAgICAgICBjb25zdCBzdWIgPSB0aGlzLmZpbHRlclRyZWUobm9kZVt0aGlzLnByb3BzLmNoaWxkcmVuXSwgcHJlZGljYXRlKQ0KICAgICAgICBpZiAoKHN1YiAmJiBzdWIubGVuZ3RoKSB8fCBwcmVkaWNhdGUobm9kZSkpIHsNCiAgICAgICAgICBzdWIgJiYgKG5vZGVbdGhpcy5wcm9wcy5jaGlsZHJlbl0gPSBzdWIpDQogICAgICAgICAgaWYgKHRoaXMua2V5d29yZCkgew0KICAgICAgICAgICAgbm9kZS5hY3RpdmUgPSB0cnVlDQogICAgICAgICAgfQ0KICAgICAgICAgIGNoaWxkcmVuLnB1c2gobm9kZSkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGNoaWxkcmVuLmxlbmd0aCA/IGNoaWxkcmVuIDogdm9pZCAwIC8vIGVzbGludC1kaXNhYmxlLWxpbmUNCiAgICB9LA0KICAgIHNob3BmaWx0ZXJOb2RlIChkYXRhKSB7DQogICAgICByZXR1cm4gZGF0YVt0aGlzLnByb3BzLmxhYmVsXS5pbmNsdWRlcyh0aGlzLmtleXdvcmQpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["zy-tree-components.vue"], "names": [], "mappings": ";AAsDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-tree-components.vue", "sourceRoot": "src/components/zy-tree-components", "sourcesContent": ["<template>\r\n  <div class=\"zy-tree-components\">\r\n    <div v-for=\"treeItem in filterTreeData(treeData)\" :key=\"treeItem.id\">\r\n      <div\r\n        @click=\"selected(treeItem)\"\r\n        v-if=\"!treeItem[props.children].length\"\r\n        :style=\"{ paddingLeft: padding(hierarchy) + 'px' }\"\r\n        :class=\"[\r\n          'zy-tree-components-item',\r\n          treeItem.selected ? 'zy-tree-components-item-selected' : ''\r\n        ]\"\r\n      >\r\n        <div class=\"zy-tree-components-item-icon\"></div>\r\n        <div class=\"zy-tree-components-item-text\">\r\n          {{ treeItem[props.label] }}\r\n        </div>\r\n      </div>\r\n      <div v-else>\r\n        <div\r\n          @click=\"subTree(treeItem)\"\r\n          :style=\"{ paddingLeft: padding(hierarchy) + 'px' }\"\r\n          :class=\"[\r\n            'zy-tree-components-item',\r\n            treeItem.active ? 'zy-tree-components-item-active' : '',\r\n            treeItem.selected ? 'zy-tree-components-item-selected' : ''\r\n          ]\"\r\n        >\r\n          <div\r\n            class=\"zy-tree-components-item-icon\"\r\n            @click.stop=\"subTreeicon(treeItem)\"\r\n          >\r\n            <i class=\"el-icon-caret-right\"></i>\r\n          </div>\r\n          <div class=\"zy-tree-components-item-text\">\r\n            {{ treeItem[props.label] }}\r\n          </div>\r\n        </div>\r\n        <el-collapse-transition>\r\n          <zy-tree-components\r\n            v-if=\"treeItem.active\"\r\n            :anykey=\"anykey\"\r\n            :child=\"child\"\r\n            :props=\"props\"\r\n            v-model=\"treeId\"\r\n            :nodeKey=\"nodeKey\"\r\n            :hierarchy=\"hierarchy + 1\"\r\n            :tree=\"treeItem[props.children]\"\r\n          ></zy-tree-components>\r\n        </el-collapse-transition>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyTreeComponents',\r\n  data () {\r\n    return {\r\n      treeId: this.value,\r\n      treeData: []\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    keyword: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    tree: {\r\n      type: Array,\r\n      default: function () {\r\n        return []\r\n      }\r\n    },\r\n    hierarchy: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    determine: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    child: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    anykey: {\r\n      type: Array,\r\n      default: function () {\r\n        return []\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      this.treeId = val\r\n      this.selectedMethods()\r\n    },\r\n    treeId (val) {\r\n      this.$emit('id', val)\r\n    },\r\n    tree (val) {\r\n      this.treeDataCope(this.deepCopy(this.tree))\r\n    },\r\n    anykey (val) {\r\n      this.treeDataCope(this.deepCopy(this.tree))\r\n    }\r\n  },\r\n  created () {\r\n    this.treeDataCope(this.deepCopy(this.tree), true)\r\n  },\r\n  methods: {\r\n    padding (index) {\r\n      var hierarchy = 18 * index\r\n      return hierarchy\r\n    },\r\n    treeDataCope (data) {\r\n      data.forEach(item => {\r\n        item.selected = false\r\n        if (this.treeId === item[this.nodeKey]) {\r\n          item.selected = true\r\n        }\r\n        if (item[this.props.children].length) {\r\n          if ((typeof item.active) === 'undefined') { // eslint-disable-line\r\n            item.active = false\r\n          }\r\n          this.anykey.forEach(items => {\r\n            if (item[this.nodeKey] === items) {\r\n              item.active = true\r\n            }\r\n          })\r\n        }\r\n      })\r\n      this.treeData = data\r\n      this.treehierarchy(this.treeData, true)\r\n    },\r\n    treehierarchy (data, type) {\r\n      data.forEach(item => {\r\n        if (item[this.nodeKey] === this.treeId) {\r\n          const result = this.makeData(item)\r\n          if (!type) {\r\n            this.$emit('on-tree-click', result)\r\n          }\r\n        }\r\n        if (item[this.props.children].length) {\r\n          this.treehierarchy(item[this.props.children], type)\r\n        }\r\n      })\r\n    },\r\n    selected (data) {\r\n      this.treeId = data[this.nodeKey]\r\n    },\r\n    selectedMethods () {\r\n      if (this.hierarchy === 0) {\r\n        this.treehierarchy(this.treeData)\r\n      }\r\n      const treeData = this.treeData\r\n      treeData.forEach(item => {\r\n        item.selected = false\r\n        if (this.treeId === item[this.nodeKey]) {\r\n          item.selected = true\r\n        }\r\n      })\r\n      this.treeData = treeData\r\n    },\r\n    subTree (data) {\r\n      if (!this.child) {\r\n        this.treeId = data[this.nodeKey]\r\n      }\r\n      const treeData = this.treeData\r\n      treeData.forEach(item => {\r\n        if (item[this.nodeKey] === data[this.nodeKey]) {\r\n          item.active = !item.active\r\n        }\r\n      })\r\n      this.treeData = treeData\r\n    },\r\n    subTreeicon (data) {\r\n      const treeData = this.treeData\r\n      treeData.forEach(item => {\r\n        if (item[this.nodeKey] === data[this.nodeKey]) {\r\n          item.active = !item.active\r\n        }\r\n      })\r\n      this.treeData = treeData\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    },\r\n    makeData (data) {\r\n      const t = this.type(data)\r\n      let o\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (let i = 0; i < data.length; i++) {\r\n          o.push(this.makeData(data[i]))\r\n        }\r\n      } else if (t === 'object') {\r\n        for (const i in data) {\r\n          if (i != 'active' && i != 'selected') {// eslint-disable-line\r\n            o[i] = this.makeData(data[i])\r\n          }\r\n        }\r\n      }\r\n      return o\r\n    },\r\n    filterTreeData (data) {\r\n      if (this.determine) {\r\n        return data\r\n      }\r\n      if (this.keyword === '') {\r\n        return data\r\n      }\r\n      return this.filterTree(data, this.shopfilterNode) || []\r\n    },\r\n    filterTree (nodes, predicate) {\r\n      if (this.hierarchy !== 0) {\r\n        return nodes\r\n      }\r\n      if (!nodes || !nodes.length) return void 0 // eslint-disable-line\r\n      const children = []\r\n      for (let node of nodes) {\r\n        node = Object.assign({}, node)\r\n        const sub = this.filterTree(node[this.props.children], predicate)\r\n        if ((sub && sub.length) || predicate(node)) {\r\n          sub && (node[this.props.children] = sub)\r\n          if (this.keyword) {\r\n            node.active = true\r\n          }\r\n          children.push(node)\r\n        }\r\n      }\r\n      return children.length ? children : void 0 // eslint-disable-line\r\n    },\r\n    shopfilterNode (data) {\r\n      return data[this.props.label].includes(this.keyword)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './zy-tree-components.scss';\r\n</style>\r\n"]}]}