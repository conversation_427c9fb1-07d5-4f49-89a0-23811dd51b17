{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\sinceManagement-zx\\SinceReport\\SinceReportAdd.vue?vue&type=style&index=0&id=fda5d6aa&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\sinceManagement-zx\\SinceReport\\SinceReportAdd.vue", "mtime": 1752541697057}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouU2luY2VSZXBvcnRBZGQgew0KICB3aWR0aDogMTAwMHB4Ow0KICBoZWlnaHQ6IDEwMCU7DQogIHBhZGRpbmc6IDI0cHg7DQoNCiAgLmZvcm0tdXNlci1ib3ggew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIG1pbi1oZWlnaHQ6IDQwcHg7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2Ow0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtd3JhcDogd3JhcDsNCiAgICBwYWRkaW5nOiAwIDE2cHg7DQogICAgcGFkZGluZy1yaWdodDogNDBweDsNCiAgICAvLyBvdmVyZmxvdzogaGlkZGVuOw0KICAgIHBhZGRpbmctdG9wOiA2cHg7DQoNCiAgICAuZm9ybS11c2VyLWJveC10ZXh0IHsNCiAgICAgIGNvbG9yOiAjOTk5Ow0KICAgICAgZm9udC1zaXplOiAkdGV4dFNpemUxNDsNCiAgICAgIHBhZGRpbmctYm90dG9tOiA2cHg7DQogICAgICBsaW5lLWhlaWdodDogMjhweDsNCiAgICB9DQoNCiAgICAuZWwtdGFnIHsNCiAgICAgIG1hcmdpbi1ib3R0b206IDZweDsNCiAgICAgIG1hcmdpbi1yaWdodDogMTJweDsNCiAgICB9DQoNCiAgICAmOmhvdmVyIHsNCiAgICAgIGJvcmRlci1jb2xvcjogIzE5OWJjNTsNCiAgICB9DQoNCiAgICAmOmZvY3VzIHsNCiAgICAgIGJvcmRlci1jb2xvcjogIzE5OWJjNTsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["SinceReportAdd.vue"], "names": [], "mappings": ";AAyQA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "SinceReportAdd.vue", "sourceRoot": "src/views/sinceManagement-zx/SinceReport", "sourcesContent": ["<template>\r\n  <div class=\"SinceReportAdd\">\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-position=\"top\"\r\n             class=\"newForm\">\r\n      <el-form-item label=\"委员姓名\"\r\n                    prop=\"userName\"\r\n                    class=\"form-title\">\r\n        <div class=\"form-user-box\"\r\n             @click=\"userClick\">\r\n          <div v-if=\"!userData.length\"\r\n               class=\"form-user-box-text\">请选择委员</div>\r\n          <el-tag v-for=\"tag in userData\"\r\n                  :key=\"tag.userId\"\r\n                  size=\"medium\"\r\n                  :closable=\"type\"\r\n                  :disable-transitions=\"false\"\r\n                  @close.stop=\"remove(tag)\">\r\n            {{tag.name}}\r\n          </el-tag>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item class=\"form-input\"\r\n                    label=\"界别\">\r\n        <el-input placeholder=\"请输入界别\"\r\n                  type=\"text\"\r\n                  v-model=\"form.deleId\"\r\n                  disabled\r\n                  clearable>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"委员证号\"\r\n                    class=\"form-input\">\r\n        <el-input placeholder=\"请输入委员证号\"\r\n                  v-model=\"form.memberNo\"\r\n                  disabled\r\n                  clearable>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"年份\"\r\n                    prop=\"year\"\r\n                    class=\"form-input\">\r\n        <el-date-picker v-model=\"form.year\"\r\n                        type=\"year\"\r\n                        value-format='yyyy'\r\n                        :disabled=\"type\"\r\n                        placeholder=\"请选择年份\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"上传附件\"\r\n                    class=\"form-upload\">\r\n        <el-upload class=\"form-upload-demo\"\r\n                   drag\r\n                   action=\"/\"\r\n                   :before-remove=\"beforeRemove\"\r\n                   :before-upload=\"handleImg_data\"\r\n                   :http-request=\"imgUpload_data\"\r\n                   :file-list=\"file\"\r\n                   multiple>\r\n          <div class=\"el-upload__text\">将附件拖拽至此区域，或<em>点击上传</em></div>\r\n          <div class=\"el-upload__tip\">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>\r\n        </el-upload>\r\n      </el-form-item>\r\n      <el-form-item label=\"内容\"\r\n                    prop=\"content\"\r\n                    class=\"form-ue\">\r\n        <!-- <UEditor v-model=\"form.content\"></UEditor> -->\r\n\r\n        <wang-editor v-model='form.content'></wang-editor>\r\n      </el-form-item>\r\n\r\n      <div class=\"form-button\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"cancel\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n    <zy-pop-up v-model=\"userShow\"\r\n               title=\"选择代表\">\r\n      <candidates-user point=\"point_15\"\r\n                       :data=\"userData\"\r\n                       :max=\"1\"\r\n                       @userCallback=\"userCallback\"></candidates-user>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data () {\r\n    return {\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())),\r\n      form: {\r\n        id: '',\r\n        userName: '',\r\n        deleId: '',\r\n        memberNo: '',\r\n        year: '',\r\n        content: ''\r\n      },\r\n      rules: {\r\n        userName: [\r\n          { required: true, message: '请选择代表', trigger: 'blur' }\r\n        ],\r\n        year: [\r\n          { required: true, message: '请选择年份', trigger: 'blur' }\r\n        ],\r\n        content: [\r\n          { required: true, message: '请输入内容', trigger: 'blur' }\r\n        ]\r\n      },\r\n      file: [],\r\n      userShow: false,\r\n      userData: []\r\n\r\n    }\r\n  },\r\n  props: ['id', 'type'],\r\n  mounted () {\r\n    if (this.id) {\r\n      this.dutyreportInfo()\r\n    } else {\r\n      if (this.type) {\r\n        this.userData.push({ userId: this.user.id, name: this.user.userName, userName: this.user.userName + this.user.mobile, mobile: this.user.mobile, position: this.user.position })\r\n        this.form.year = this.$format('', 'YYYY')\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    userData (val) {\r\n      if (this.userData.length) {\r\n        this.getMyInfo(val[0].userId)\r\n      } else {\r\n        this.form.id = ''\r\n        this.form.userName = ''\r\n        this.form.deleId = ''\r\n        this.form.memberNo = ''\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    async getMyInfo (userId) {\r\n      const res = await this.$api.sinceManagement.dutyreportGetMyInfo({\r\n        userId: userId\r\n      })\r\n      var { data } = res\r\n      this.form.memberNo = data.memberNo\r\n      this.form.deleId = data.deleId\r\n      this.form.id = data.userId\r\n      this.form.userName = data.userName\r\n    },\r\n    async dutyreportInfo () {\r\n      const res = await this.$api.sinceManagement.dutyreportInfo(this.id)\r\n      var { data } = res\r\n      this.userData.push({ userId: data.userId, name: data.userName, userName: data.userName, mobile: '', position: '' })\r\n      if (data.attachmentList) {\r\n        data.attachmentList.forEach((item, index) => {\r\n          item.uid = item.id\r\n          item.name = item.fileName\r\n        })\r\n        this.file = data.attachmentList\r\n      }\r\n      this.form.content = data.content\r\n      this.form.year = data.year\r\n    },\r\n    userClick () {\r\n      if (this.type) {\r\n        return false\r\n      }\r\n      this.userShow = !this.userShow\r\n    },\r\n    userCallback (data, type) {\r\n      if (type) {\r\n        this.userData = data\r\n      }\r\n      this.userShow = !this.userShow\r\n    },\r\n    // 移除tag\r\n    remove (data) {\r\n      if (this.type) {\r\n        return false\r\n      }\r\n      var userData = this.userData\r\n      this.userData = userData.filter(item => item.userId !== data.userId)\r\n    },\r\n    handleImg_data (file, fileList) {\r\n    },\r\n    /**\r\n* 上传附件请求方法\r\n*/\r\n    imgUpload_data (files) {\r\n      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''\r\n      const param = new FormData()\r\n      param.append('module', 'materiainfoFile')\r\n      param.append('siteId', JSON.parse(areaId))\r\n      param.append('attachment', files.file)\r\n      this.$api.proposal.proposalfile(param).then(res => {\r\n        var { data } = res\r\n        data[0].name = data[0].fileName\r\n        this.file.push(data[0])\r\n      })\r\n    },\r\n    /**\r\n  * 删除附件\r\n */\r\n    beforeRemove (file, fileList) {\r\n      var fileData = this.file\r\n      this.fileData = fileData.filter(item => item.id !== file.id)\r\n    },\r\n    submitForm (formName, type) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var url = ''\r\n          if (this.type) {\r\n            url = '/dutyreport/addMyDutyReport'\r\n          } else {\r\n            url = '/dutyreport/add'\r\n          }\r\n          if (this.id) {\r\n            url = '/dutyreport/edit?'\r\n          }\r\n          var attachmentIds = []\r\n          this.file.forEach(item => {\r\n            attachmentIds.push(item.id)\r\n          })\r\n          this.$api.general.generalAdd(url, {\r\n            id: this.id,\r\n            userId: this.form.id,\r\n            userName: this.form.userName,\r\n            deleId: this.form.deleId,\r\n            memberNo: this.form.memberNo,\r\n            year: this.form.year,\r\n            attachmentIds: attachmentIds.join(','),\r\n            content: this.form.content\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('callback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 取消按钮\r\n    */\r\n    cancel () {\r\n      this.$emit('callback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SinceReportAdd {\r\n  width: 1000px;\r\n  height: 100%;\r\n  padding: 24px;\r\n\r\n  .form-user-box {\r\n    width: 100%;\r\n    min-height: 40px;\r\n    background-color: #fff;\r\n    border: 1px solid #dcdfe6;\r\n    border-radius: 4px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    padding: 0 16px;\r\n    padding-right: 40px;\r\n    // overflow: hidden;\r\n    padding-top: 6px;\r\n\r\n    .form-user-box-text {\r\n      color: #999;\r\n      font-size: $textSize14;\r\n      padding-bottom: 6px;\r\n      line-height: 28px;\r\n    }\r\n\r\n    .el-tag {\r\n      margin-bottom: 6px;\r\n      margin-right: 12px;\r\n    }\r\n\r\n    &:hover {\r\n      border-color: #199bc5;\r\n    }\r\n\r\n    &:focus {\r\n      border-color: #199bc5;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}