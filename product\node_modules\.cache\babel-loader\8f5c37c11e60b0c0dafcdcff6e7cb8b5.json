{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu-tree\\zy-menu-tree.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu-tree\\zy-menu-tree.vue", "mtime": 1752541693532}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA2BA;EACAA,kBADA;;EAEAC;IACA;MACAC,kBADA;MAEAC;IAFA;EAIA,CAPA;;EAQAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,WADA;MAEAC;QACA;MACA;IAJA,CALA;IAWAE;MACAH,YADA;MAEAC;IAFA,CAXA;IAeAG;MACAJ,aADA;MAEAC;IAFA,CAfA;IAmBAI;MACAL,YADA;MAEAC;IAFA,CAnBA;IAuBAH;MACAE,YADA;MAEAC;QACA;UACAK,oBADA;UAEAC,cAFA;UAGAH;QAHA;MAKA;IARA;EAvBA,CARA;EA0CAI;IACAC,aADA;IAEAC;EAFA,CA1CA;;EA8CAC;IACA;EACA,CAhDA;;EAiDAC;IACAV;MACA;IACA,CAHA;;IAIAH;MACA;MACA;IACA,CAPA;;IAQAH;MACA;IACA;;EAVA,CAjDA;EA6DAiB;IACAC;MACA;;MACA;QACA;UACA;YACAV;UACA,CAFA,MAEA;YACAA;UACA;QACA,CANA,MAMA;UACA;YACAA;UACA,CAFA,MAEA;YACAA;UACA;QACA;MACA,CAdA,MAcA;QACA;UACA;YACAA;UACA,CAFA,MAEA;YACAA;UACA;QACA,CANA,MAMA;UACA;YACAA;UACA,CAFA,MAEA;YACAA;UACA;QACA;MACA;;MACA;IACA,CAjCA;;IAkCAW;MACA;MACA;IACA,CArCA;;IAsCAC;MACArB;QACA;UACAsB;;UACA;YACAA;UACA;QACA,CALA,MAKA;UACAA;QACA;MACA,CATA;MAUA;MACA;IACA,CAnDA;;IAoDAC;MACA;MACAC;QACA;UACAF;QACA;MACA,CAJA;IAKA,CA3DA;;IA4DA;IACA;IACA;IACAG;MACA,iCADA,CAEA;MACA;IACA,CAnEA;;IAoEAC;MACA;QACA;MACA;;MACA;MACAF;QACA;UACAF;;UACA;YACAA;;YACA;cACA;gBAAAK;cAAA;YACA;UACA;QACA;MACA,CAVA;IAWA,CApFA;;IAqFAC;MACA5B;QACA;UACA6B;;UACA;YACA;cAAAF;YAAA;UACA;;UACA;UACA;QACA;;QACA;UACA;QACA;MACA,CAZA;IAaA,CAnGA;;IAoGAG;MACA;MACA;MACA;MACA;;MACA;QACAC;MACA,CAFA,MAEA;QACAA;MACA,CAFA,MAEA;QACA;MACA;;MACA;QACA;UACAA;QACA;;QACA;MACA,CALA,MAKA;QACA;UACAA;QACA;;QACA;MACA;IACA,CA3HA;;IA4HA1B;MACA;MACA;QACA,6BADA;QAEA,2BAFA;QAGA,2BAHA;QAIA,+BAJA;QAKA,yBALA;QAMA,uBANA;QAOA,2BAPA;QAQA,iCARA;QASA,uBATA;QAUA;MAVA;MAYA;IACA,CA3IA;;IA4IA2B;MACA;MACA;;MACA;QACAD;MACA,CAFA,MAEA;QACAA;MACA,CAFA,MAEA;QACA;MACA;;MACA;QACA;UACAA;QACA;MACA,CAJA,MAIA;QACA;UACA;YAAA;YACAA;UACA;QACA;MACA;;MACA;IACA;;EAlKA;AA7DA", "names": ["name", "data", "menuId", "menuItem", "props", "value", "type", "default", "menu", "hierarchy", "show", "nodeKey", "children", "label", "model", "prop", "event", "created", "watch", "methods", "judge", "padding", "menudata", "item", "submenu", "arr", "selected", "selectedId", "path", "menuhierarchy", "sessionStorage", "deepCopy", "o", "makeData"], "sourceRoot": "src/components/zy-menu-tree", "sources": ["zy-menu-tree.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-menu-tree scrollBar\">\r\n    <div v-for=\"(menu, index) in menuItem\"\r\n         :key=\"index\">\r\n      <div :class=\"['menu-item', menu.active?'menu-item-active':'']\"\r\n           :style=\"{ paddingLeft: padding(hierarchy) + 'px' }\"\r\n           v-if=\"judge(menu,false)\"\r\n           @click=\"selected(menu)\">{{menu[props.label]}}</div>\r\n      <div v-if=\"judge(menu,true)\">\r\n        <div class=\"menu-item menu-item-title\"\r\n             :style=\"{ paddingLeft: padding(hierarchy) + 'px' }\"\r\n             @click=\"submenu(menu)\">{{menu[props.label]}} <div :class=\"['menu-icon',menu.hidden? 'menu-icon-active':'']\"></div>\r\n        </div>\r\n        <el-collapse-transition>\r\n          <zy-menu-tree v-if=\"menu.hidden\"\r\n                        :show=\"show\"\r\n                        :menu=\"menu[props.children]\"\r\n                        :props=\"props\"\r\n                        v-model=\"menuId\"\r\n                        :nodeKey=\"nodeKey\"\r\n                        :hierarchy=\"hierarchy+1\"></zy-menu-tree>\r\n        </el-collapse-transition>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyMenuTree',\r\n  data () {\r\n    return {\r\n      menuId: this.value,\r\n      menuItem: []\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    menu: {\r\n      type: Array,\r\n      default: function () {\r\n        return []\r\n      }\r\n    },\r\n    hierarchy: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    show: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label',\r\n          show: 'show'\r\n        }\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  created () {\r\n    this.menudata(this.deepCopy(this.menu))\r\n  },\r\n  watch: {\r\n    menu () {\r\n      this.menudata(this.deepCopy(this.menu))\r\n    },\r\n    value (val) {\r\n      this.menuId = val\r\n      this.selectedId()\r\n    },\r\n    menuId (val) {\r\n      this.$emit('id', val)\r\n    }\r\n  },\r\n  methods: {\r\n    judge (data, type) {\r\n      var show = false\r\n      if (this.show) {\r\n        if (type) {\r\n          if (data[this.props.children].length !== 0 && data[this.props.show]) {\r\n            show = true\r\n          } else {\r\n            show = false\r\n          }\r\n        } else {\r\n          if (data[this.props.children].length === 0 && data[this.props.show]) {\r\n            show = true\r\n          } else {\r\n            show = false\r\n          }\r\n        }\r\n      } else {\r\n        if (type) {\r\n          if (data[this.props.children].length) {\r\n            show = true\r\n          } else {\r\n            show = false\r\n          }\r\n        } else {\r\n          if (data[this.props.children].length) {\r\n            show = false\r\n          } else {\r\n            show = true\r\n          }\r\n        }\r\n      }\r\n      return show\r\n    },\r\n    padding (index) {\r\n      var hierarchy = 24 + (16 * index)\r\n      return hierarchy\r\n    },\r\n    menudata (data) {\r\n      data.forEach(item => {\r\n        if (item[this.props.children].length === 0) {\r\n          item.active = false\r\n          if (this.menuId === item[this.nodeKey]) {\r\n            item.active = true\r\n          }\r\n        } else {\r\n          item.hidden = false\r\n        }\r\n      })\r\n      this.menuItem = data\r\n      this.selectedId()\r\n    },\r\n    submenu (data) {\r\n      const arr = this.menuItem\r\n      arr.forEach(item => {\r\n        if (item[this.nodeKey] === data[this.nodeKey]) {\r\n          item.hidden = !item.hidden\r\n        }\r\n      })\r\n    },\r\n    // rowClick (result) {\r\n    //   this.$emit('on-row-click', result)\r\n    // },\r\n    selected (data) {\r\n      this.menuId = data[this.nodeKey]\r\n      // let result = this.makeData(data)\r\n      // this.$emit('on-row-click', result)\r\n    },\r\n    selectedId (type) {\r\n      if (this.hierarchy === 0) {\r\n        this.menuhierarchy(this.menuItem)\r\n      }\r\n      const arr = this.menuItem\r\n      arr.forEach(item => {\r\n        if (item[this.props.children].length === 0) {\r\n          item.active = false\r\n          if (item[this.nodeKey] === this.menuId) {\r\n            item.active = true\r\n            if (this.$route.path !== item.to) {\r\n              this.$router.push({ path: item.to })\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    menuhierarchy (data) {\r\n      data.forEach(item => {\r\n        if (item[this.nodeKey] === this.menuId) {\r\n          sessionStorage.setItem('curMenuItem', JSON.stringify(item))\r\n          if (this.$route.path !== item.to) {\r\n            this.$router.push({ path: item.to })\r\n          }\r\n          const result = this.makeData(item)\r\n          this.$emit('on-row-click', result)\r\n        }\r\n        if (item[this.props.children].length) {\r\n          this.menuhierarchy(item[this.props.children])\r\n        }\r\n      })\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    },\r\n    makeData (data) {\r\n      const t = this.type(data)\r\n      let o\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (let i = 0; i < data.length; i++) {\r\n          o.push(this.makeData(data[i]))\r\n        }\r\n      } else if (t === 'object') {\r\n        for (const i in data) {\r\n          if (i != 'active' && i != 'hidden') {// eslint-disable-line\r\n            o[i] = this.makeData(data[i])\r\n          }\r\n        }\r\n      }\r\n      return o\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-menu-tree.scss\";\r\n</style>\r\n"]}]}