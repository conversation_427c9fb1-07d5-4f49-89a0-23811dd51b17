{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-sliding\\zy-sliding.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-sliding\\zy-sliding.vue", "mtime": 1752541693576}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["zy-sliding.vue"], "names": [], "mappings": ";AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-sliding.vue", "sourceRoot": "src/components/zy-sliding", "sourcesContent": ["<template>\r\n  <div class=\"zy-sliding\"\r\n       ref=\"zy-sliding\">\r\n    <div class=\"zy-sliding-box\">\r\n      <div class=\"zy-sliding-item-box\">\r\n        <div :class=\"['zy-sliding-item',item.class?'zy-sliding-item-a':'']\"\r\n             v-for=\"(item, index) in slidingList\"\r\n             @click=\"slidingClick(item)\"\r\n             :key=\"index\">\r\n          {{item.name}}\r\n        </div>\r\n        <div class=\"zy-sliding-item-sliding\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zySliding',\r\n  data () {\r\n    return {\r\n      slidingId: this.value,\r\n      slidingList: []\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String\r\n    },\r\n    sliding: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    props: {\r\n      type: Object,\r\n      default: () => { }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  mounted () {\r\n    this.slidingData(this.deepCopy(this.sliding))\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.slidingList.forEach(item => {\r\n          if (item.id === this.value) {\r\n            this.slidingClick(item, true)\r\n          }\r\n        })\r\n      }\r\n    },\r\n    sliding (val) {\r\n      this.slidingData(this.deepCopy(this.sliding))\r\n    },\r\n    slidingList (val) {\r\n      if (val.length) {\r\n        this.slidingList.forEach(item => {\r\n          if (item.id === this.value) {\r\n            this.slidingClick(item)\r\n          }\r\n        })\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    slidingClick (data, type) {\r\n      if (this.value !== data.id) {\r\n        this.$emit('id', data.id)\r\n        this.returnData(data)\r\n      }\r\n      if (type) {\r\n        this.returnData(data)\r\n      }\r\n      this.slidingList.forEach(item => {\r\n        item.class = false\r\n        if (item.id === data.id) {\r\n          item.class = true\r\n        }\r\n      })\r\n      this.$nextTick(() => {\r\n        this.slidingBox()\r\n        this.slidingIocation()\r\n      })\r\n    },\r\n    returnData (data) {\r\n      var arr = []\r\n      this.sliding.forEach(item => {\r\n        if (this.props) {\r\n          if (data.id === item[this.props.id]) {\r\n            arr = item\r\n          }\r\n        } else {\r\n          if (data.id === item.id) {\r\n            arr = item\r\n          }\r\n        }\r\n      })\r\n      this.$emit('sliding-click', arr)\r\n    },\r\n    slidingIocation () {\r\n      const slidingItem = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-a')\r\n      const sliding = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-sliding')\r\n      sliding.style.width = `${slidingItem.offsetWidth}px`\r\n      sliding.style.transform = `translateX(${slidingItem.offsetLeft}px)`\r\n      sliding.style.transitionDuration = '.3s'\r\n    },\r\n    slidingBox () {\r\n      var sliding = this.$refs['zy-sliding'].querySelector('.zy-sliding-box')\r\n      var itemBox = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-box')\r\n      var item = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-a')\r\n      if (sliding.offsetWidth < itemBox.offsetWidth) {\r\n        itemBox.style.transform = 'translateX(0px)'\r\n        itemBox.style.transitionDuration = '.4s'\r\n        if (itemBox.offsetWidth === item.offsetLeft + item.offsetWidth) {\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - sliding.offsetWidth}px)`\r\n        } else if (sliding.offsetWidth / 2 > itemBox.offsetWidth - item.offsetLeft) {\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - sliding.offsetWidth}px)`\r\n        } else {\r\n          itemBox.style.transform = `translateX(-${item.offsetLeft - sliding.offsetWidth / 2}px)`\r\n        }\r\n      }\r\n    },\r\n    slidingData (data) {\r\n      this.initData(data)\r\n      this.slidingList = data\r\n    },\r\n    initData (items) {\r\n      items.forEach((item, index) => {\r\n        if (this.props) {\r\n          if ((typeof item.id) === 'undefined') { // eslint-disable-line\r\n            item.id = item[this.props.id]\r\n          }\r\n          if ((typeof item.name) === 'undefined') { // eslint-disable-line\r\n            item.name = item[this.props.name]\r\n          }\r\n        }\r\n        if ((typeof item.class) === 'undefined') { // eslint-disable-line\r\n          item.class = false\r\n        }\r\n        if (this.value === item.id) {\r\n          item.class = true\r\n          this.$emit('sliding-click', item)\r\n        }\r\n      })\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    },\r\n    makeData (data) {\r\n      const t = this.type(data)\r\n      let o\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (let i = 0; i < data.length; i++) {\r\n          o.push(this.makeData(data[i]))\r\n        }\r\n      } else if (t === 'object') {\r\n        for (const i in data) {\r\n          if (i != 'class') {// eslint-disable-line\r\n            o[i] = this.makeData(data[i])\r\n          }\r\n        }\r\n      }\r\n      return o\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-sliding.scss\";\r\n</style>\r\n"]}]}