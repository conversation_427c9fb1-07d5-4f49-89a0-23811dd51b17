{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu\\zy-menu-children.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu\\zy-menu-children.vue", "mtime": 1752541693535}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICd6eU1lbnVDaGlsZHJlbicsDQogIHByb3BzOiB7DQogICAgdmFsdWU6IFtTdHJpbmcsIE51bWJlciwgQXJyYXksIE9iamVjdF0sDQogICAgbWVudTogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgbGV2ZWw6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDENCiAgICB9LA0KICAgIC8vIOagkee7k+aehOmFjee9rg0KICAgIHByb3BzOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgY2hpbGRyZW46ICdjaGlsZHJlbicsDQogICAgICAgICAgbGFiZWw6ICdsYWJlbCcsDQogICAgICAgICAgaWQ6ICdpZCcsDQogICAgICAgICAgdG86ICd0bycsDQogICAgICAgICAgaXNTaG93OiAnaXNTaG93JywNCiAgICAgICAgICBzaG93VmFsdWU6IHRydWUNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGlzU2hvd0NoaWxkcmVuIChtZW51KSB7DQogICAgICBsZXQgaXNTaG93ID0gZmFsc2UNCiAgICAgIGlmIChtZW51W3RoaXMucHJvcHMuY2hpbGRyZW5dLmxlbmd0aCkgew0KICAgICAgICBpc1Nob3cgPSBtZW51W3RoaXMucHJvcHMuY2hpbGRyZW5dLnNvbWUoaXRlbSA9PiBpdGVtW3RoaXMucHJvcHMuaXNTaG93XSA9PT0gdGhpcy5wcm9wcy5zaG93VmFsdWUpDQogICAgICB9DQogICAgICByZXR1cm4gaXNTaG93DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["zy-menu-children.vue"], "names": [], "mappings": ";AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-menu-children.vue", "sourceRoot": "src/components/zy-menu", "sourcesContent": ["<template>\r\n  <div>\r\n    <div v-for=\"(item) in menu\"\r\n         :key=\"item[props.id]\">\r\n      <el-submenu :index=\"item[props.id]\"\r\n                  v-if=\"isShowChildren(item)\">\r\n        <template #title>\r\n          <div class=\"zy-menu-icon\"\r\n               v-if=\"level==1\">\r\n            <img :src=\"item[props.icon]\"\r\n                 alt=\"\">\r\n          </div>\r\n          <span :class=\"[level==1?'menu-color':'']\">{{item[props.label]}}</span>\r\n        </template>\r\n        <zy-menu-children :menu=\"item[props.children]\"\r\n                          :level=\"level+1\"\r\n                          :value=\"value\"\r\n                          :props=\"props\"></zy-menu-children>\r\n      </el-submenu>\r\n      <el-menu-item :index=\"item[props.id]\"\r\n                    v-else>\r\n        <div class=\"zy-menu-icon\"\r\n             v-if=\"level==1\">\r\n          <img :src=\"item[props.icon]\"\r\n               alt=\"\">\r\n        </div>\r\n        <span slot=\"title\"\r\n              :class=\"[level==1?'menu-color':'']\">{{item[props.label]}}</span>\r\n      </el-menu-item>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyMenuChildren',\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    menu: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    level: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label',\r\n          id: 'id',\r\n          to: 'to',\r\n          isShow: 'isShow',\r\n          showValue: true\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    isShowChildren (menu) {\r\n      let isShow = false\r\n      if (menu[this.props.children].length) {\r\n        isShow = menu[this.props.children].some(item => item[this.props.isShow] === this.props.showValue)\r\n      }\r\n      return isShow\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}