{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\general\\general.vue?vue&type=style&index=0&id=35172f17&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\general\\general.vue", "mtime": 1752541695848}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL2dlbmVyYWwuc2NzcyI7DQouYnRuYm94IHsNCiAgdGV4dC1hbGlnbjogcmlnaHQ7DQogIG1hcmdpbjogMTBweDsNCiAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICAvLyAuZWwtYnV0dG9uIHsNCiAgLy8gICAgIGZvbnQtc2l6ZTogJHRleHRTaXplMTY7DQogIC8vICAgICBjb2xvcjogI2ZmZjsNCiAgLy8gICAgIGJhY2tncm91bmQ6ICNmNTZjNmM7DQogIC8vIH0NCn0NCg=="}, {"version": 3, "sources": ["general.vue"], "names": [], "mappings": ";AA8OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "general.vue", "sourceRoot": "src/views/general", "sourcesContent": ["<template>\r\n  <el-container class=\"general\"\r\n                v-loading=\"loading\"\r\n                :element-loading-text=\"text\">\r\n    <el-header class=\"general-header\"\r\n               height=\"100px\">\r\n      <div class=\"general-header-log-box\">\r\n        <div class=\"general-header-log\"></div>\r\n        <div class=\"general-header-text-box\">\r\n          <!-- <div class=\"general-header-name\">{{moduleName}}</div> -->\r\n          <div class=\"general-header-name\"></div>\r\n          <div class=\"general-header-module-name\">{{name}}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"general-header-operation\">\r\n        <el-popover placement=\"bottom\"\r\n                    width=\"152\"\r\n                    trigger=\"click\">\r\n          <div class=\"sizeSwitch\"\r\n               slot=\"reference\">\r\n            <template v-if=\"fontSize == 1\">超大号字</template>\r\n            <template v-if=\"fontSize == 2\">大号字</template>\r\n            <template v-if=\"fontSize == 3\">标准字号</template>\r\n            <i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n          </div>\r\n          <div class=\"sizeSwitchItem\"\r\n               @click=\"fontSizeClick(1)\"\r\n               :class=\"{sizeSwitchItemA:fontSize == 1}\">超大号字</div>\r\n          <div class=\"sizeSwitchItem\"\r\n               @click=\"fontSizeClick(2)\"\r\n               :class=\"{sizeSwitchItemA:fontSize == 2}\">大号字</div>\r\n          <div class=\"sizeSwitchItem\"\r\n               @click=\"fontSizeClick(3)\"\r\n               :class=\"{sizeSwitchItemA:fontSize == 3}\">标准字号</div>\r\n        </el-popover>\r\n\r\n        <div class=\"general-header-home\"\r\n             @click=\"returnClick\"></div>\r\n        <div class=\"general-header-help\"\r\n             v-if=\"helpShow.length\"\r\n             @click=\"help\"></div>\r\n        <div class=\"general-header-set\"\r\n             v-if=\"systemShow.length\"\r\n             @click=\"system\"></div>\r\n        <div class=\"general-header-user\"\r\n             v-if=\"user\">\r\n          <!-- <div class=\"general-header-user-name\">{{user.userName}}</div> -->\r\n          <div class=\"general-header-user-img\">\r\n            <img :src=\"user.headImg\"\r\n                 alt=\"\">\r\n          </div>\r\n        </div>\r\n        <div class=\"general-exit\"\r\n             @click=\"exit()\">退出</div>\r\n      </div>\r\n    </el-header>\r\n    <el-container class=\"general-container\">\r\n      <el-aside width=\"248px\"\r\n                class=\"general-aside\">\r\n        <zy-menu v-model=\"menuId\"\r\n                 :menu=\"menuData\"\r\n                 @select=\"menuSelect\"\r\n                 :props=\"{ children: 'children', label: 'name', id: 'menuId' ,to: 'to',icon:'iconUrl', isShow: 'isShow', showValue: true}\"></zy-menu>\r\n      </el-aside>\r\n      <el-main class=\"general-main\">\r\n        <div class=\"general-main-breadcrumb\">\r\n          <el-breadcrumb separator=\"/\">\r\n            <el-breadcrumb-item v-for=\"(item,index) in crumbsData\"\r\n                                @click.native=\"crumbsClcik(item,index)\"\r\n                                :to=\"{ path: item.to, query: item.params }\"\r\n                                :key=\"item.id\">{{item.name}}</el-breadcrumb-item>\r\n          </el-breadcrumb>\r\n        </div>\r\n        <div class=\"general-main-content scrollBar\">\r\n\r\n          <zy-pop-up v-model=\"officeShow\"\r\n                     title=\"文件预览\">\r\n            <div class=\"btnbox\">\r\n              <el-button type=\"danger\"\r\n                         @click=\"dowload('word')\">无法打开，请点击按钮</el-button>\r\n            </div>\r\n            <div id=\"OfficeDiv\"\r\n                 ref=\"OfficeDiv\"\r\n                 class=\"OfficeDiv\"\r\n                 style=\"width:1160px; height: 700px;\">\r\n            </div>\r\n          </zy-pop-up>\r\n          <zy-pop-up v-model=\"pdfshow\"\r\n                     title=\"文件预览\">\r\n            <div class=\"btnbox\">\r\n              <el-button type=\"danger\"\r\n                         @click=\"dowload('pdf')\">无法打开，请点击按钮</el-button>\r\n            </div>\r\n            <div id=\"pdfDiv\"\r\n                 ref=\"pdfDiv\"\r\n                 style=\"width:1160px; height: 700px;\">\r\n            </div>\r\n          </zy-pop-up>\r\n          <keep-alive :include=\"includes\">\r\n            <router-view :key=\"$route.fullPath\" />\r\n          </keep-alive>\r\n        </div>\r\n      </el-main>\r\n    </el-container>\r\n  </el-container>\r\n</template>\r\n<script>\r\nimport mixins from '../../mixins'\r\nimport mixinsGeneral from '../../mixins/general'\r\nimport { mapActions } from 'vuex'\r\nimport zyMenu from '../../components/zy-menu/zy-menu.vue'\r\nimport woffice from './office.js'\r\nimport webOfficeTplPdf from '../../../public/static/js/iWebPDF2018.js'\r\n// import { WebOpenUrlPdf } from '../../../public/static/js/PDF2018.js'\r\nexport default {\r\n  components: { zyMenu },\r\n  name: 'general',\r\n  data () {\r\n    return {\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())),\r\n      moduleName: JSON.parse(sessionStorage.getItem('generalName' + this.$logo())),\r\n      name: JSON.parse(sessionStorage.getItem('name')),\r\n      loading: false,\r\n      text: '正在加载中',\r\n      officeShow: false,\r\n      pdfshow: false,\r\n      webOfficeTplPdf: webOfficeTplPdf,\r\n      fontSize: 3\r\n    }\r\n  },\r\n  mounted () {\r\n    this.fontSize = JSON.parse(localStorage.getItem('fontSize')) || 3\r\n    this.$nextTick(() => {\r\n      window.addEventListener('scroll', this.scrollMenu, true)\r\n    })\r\n  },\r\n  watch: {\r\n    officeShow () {\r\n      if (!this.officeShow) {\r\n        this.webOfficeObj.WebClose()\r\n        console.log(this.webOfficeObj)\r\n      }\r\n    }\r\n  },\r\n  destroyed () {\r\n    window.removeEventListener('scroll', this.scrollMenu)\r\n  },\r\n  provide () {\r\n    return {\r\n      newTab: this.newTab, // 打开新一级面包屑方法，以前使用newTab打开tab页的页面不用改\r\n      tabDelJump: this.tabDelJump, // 关闭当前面包屑跳转到上一级面包屑方法，以前调用tabDelJump关闭tab页的不用改  （注意：不用传参）\r\n      jumpMenu: this.jumpMenu, // 没啥用，为了不然页面报错\r\n      tabNameDelete: this.tabNameDelete, // 也是关闭当前面包屑跳转到上一级面包屑方法，为了兼容以前页面调用的方法 （注意：不用传参）\r\n      loadingprovide: this.loadingprovide,\r\n      loadingtext: this.loadingtext,\r\n      matchingMenu: this.matchingMenu,\r\n      openoffice: this.openoffice,\r\n      openPdf: this.openPdf\r\n\r\n    }\r\n  },\r\n  mixins: [mixins, mixinsGeneral, woffice],\r\n  methods: {\r\n    fontSizeClick (type) {\r\n      this.fontSize = type\r\n      this.$fontSize(type)\r\n    },\r\n    openoffice (openUrl) {\r\n      this.officeShow = true\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          const OfficeDiv = this.$refs.OfficeDiv\r\n          OfficeDiv.innerHTML = this.webOfficeTpl\r\n          this.webOfficeObj = new WebOffice2015() // eslint-disable-line \r\n          this.webOfficeObj.setObj(document.getElementById('WebOffice2015'))\r\n          setTimeout(() => {\r\n            this.initWebOfficeObject(openUrl)\r\n          }, 1000)\r\n        }, 1000)\r\n      })\r\n    },\r\n    openPdf (openUrl) {\r\n      this.pdfshow = true\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          const pdfDiv = this.$refs.pdfDiv\r\n          pdfDiv.innerHTML = this.webOfficeTplPdf\r\n          setTimeout(() => {\r\n            const addins = document.getElementById('iWebPDF2018').iWebPDFFun\r\n            if (addins != null) {\r\n              addins.WebOpenUrlFile(openUrl)\r\n            }\r\n          }, 1000)\r\n        }, 1000)\r\n      })\r\n    },\r\n    ...mapActions('position', ['GET_top']),\r\n    loadingprovide (loading) {\r\n      this.loading = loading\r\n    },\r\n    loadingtext (text) {\r\n      this.text = text\r\n    },\r\n    scrollMenu (e) {\r\n      var arr = ['/activityNew']\r\n      if (arr.includes(this.$route.path)) {\r\n        var top = this.$refs.scrollMenuRef.scrollTop\r\n        this.GET_top(top)\r\n      }\r\n    },\r\n    dowload (type) {\r\n      const elink = document.createElement('a')\r\n      elink.style.display = 'none'\r\n      elink.href = ''\r\n      if (type === 'word') {\r\n        elink.href = './static/office2015/iWebOffice2015.msi'\r\n      } else {\r\n        elink.href = './static/pdf2018/iWebPDF2018.exe'\r\n      }\r\n      document.body.appendChild(elink)\r\n      elink.click()\r\n      document.body.removeChild(elink)\r\n      // const elink = document.createElement('a')\r\n      // elink.style.display = 'none'\r\n      // elink.download = '../../../public/static/office2015/msiexec.exe'\r\n      // if (type === 'word') {\r\n      // } else {\r\n      //   elink.download = '../../../public/static/pdf2018/iWebPDF2018.exe'\r\n      // }\r\n      // document.body.appendChild(elink)\r\n      // elink.click()\r\n      // document.body.removeChild(elink)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"./general.scss\";\r\n.btnbox {\r\n  text-align: right;\r\n  margin: 10px;\r\n  margin-right: 20px;\r\n  // .el-button {\r\n  //     font-size: $textSize16;\r\n  //     color: #fff;\r\n  //     background: #f56c6c;\r\n  // }\r\n}\r\n</style>\r\n"]}]}