{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\CircularProgress.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\CircularProgress.vue", "mtime": 1756285769449}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CircularProgress.vue"], "names": [], "mappings": ";AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CircularProgress.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"circular-progress\" :id=\"id\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'CircularProgress',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    percentage: {\n      type: Number,\n      required: true,\n      default: 0\n    },\n    label: {\n      type: String,\n      required: true,\n      default: ''\n    },\n    color: {\n      type: String,\n      default: '#00d4ff'\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  watch: {\n    percentage: {\n      handler () {\n        this.updateChart()\n      }\n    }\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.updateChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n\n    updateChart () {\n      if (!this.chart) return\n      const option = {\n        graphic: [\n          // 百分比数字 - 最高优先级\n          {\n            type: 'text',\n            left: 'center',\n            top: '30%',\n            z: 1000,\n            style: {\n              text: this.percentage + '%',\n              fontSize: 32,\n              fontWeight: 'bold',\n              fill: '#fff',\n              textShadowColor: 'rgba(0, 0, 0, 1)',\n              textShadowBlur: 15,\n              textShadowOffsetX: 3,\n              textShadowOffsetY: 3\n            }\n          },\n          // 标签文字 - 最高优先级\n          {\n            type: 'text',\n            left: 'center',\n            top: '58%',\n            z: 1000,\n            style: {\n              text: this.label,\n              fontSize: 16,\n              fontWeight: '500',\n              fill: '#fff',\n              textShadowColor: 'rgba(0, 0, 0, 1)',\n              textShadowBlur: 12,\n              textShadowOffsetX: 2,\n              textShadowOffsetY: 2\n            }\n          }\n        ],\n        series: [\n          {\n            type: 'pie',\n            radius: ['0%', '75%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            z: 100,\n            data: [\n              {\n                value: this.percentage,\n                itemStyle: {\n                  color: {\n                    type: 'linear',\n                    x: 0,\n                    y: 0,\n                    x2: 1,\n                    y2: 1,\n                    colorStops: [\n                      { offset: 0, color: this.color },\n                      { offset: 0.3, color: this.color + 'E6' },\n                      { offset: 0.7, color: this.color + 'B3' },\n                      { offset: 1, color: this.color + '80' }\n                    ]\n                  },\n                  shadowBlur: 25,\n                  shadowColor: this.color + '80',\n                  shadowOffsetX: 0,\n                  shadowOffsetY: 0\n                }\n              },\n              {\n                value: 100 - this.percentage,\n                itemStyle: {\n                  color: {\n                    type: 'radial',\n                    x: 0.5,\n                    y: 0.5,\n                    r: 0.8,\n                    colorStops: [\n                      { offset: 0, color: 'rgba(255, 255, 255, 0.0)' },\n                      { offset: 0.7, color: 'rgba(255, 255, 255, 0.0)' },\n                      { offset: 1, color: 'rgba(255, 255, 255, 0.0)' }\n                    ]\n                  }\n                }\n              }\n            ],\n            label: { show: false },\n            labelLine: { show: false },\n            animation: true,\n            animationDuration: 2000,\n            animationEasing: 'cubicOut'\n          }\n        ]\n      }\n      this.chart.setOption(option)\n    },\n\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.circular-progress {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 50; // 确保图表容器在背景图上方\n\n  // 添加整体的发光效果\n  &::before {\n    content: '';\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    width: 90%;\n    height: 90%;\n    border-radius: 50%;\n    background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, transparent 70%);\n    z-index: 2; // 发光效果在背景图上方，但在图表下方\n  }\n}\n</style>\n"]}]}