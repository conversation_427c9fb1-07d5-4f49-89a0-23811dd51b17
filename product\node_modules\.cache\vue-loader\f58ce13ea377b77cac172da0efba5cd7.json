{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecordAdd\\MonthlyWorkRecordAdd.vue?vue&type=style&index=0&id=7d3a9005&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecordAdd\\MonthlyWorkRecordAdd.vue", "mtime": 1752541693848}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouTW9udGhseVdvcmtSZWNvcmRBZGQgew0KICB3aWR0aDogMTAwJTsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogMTBweDsNCiAgYm94LXNoYWRvdzogMHB4IDRweCA2cHggMHB4IHJnYmEoMjMzLCAyMzMsIDIzMywgMC40KTsNCiAgcGFkZGluZzogMjBweCAzMHB4Ow0KDQogIC53YW5nLWVkaXRvciB7DQogICAgLy/op6PlhrPlhoXlrrnovpPlhaXov4fplb/ml7bog73oh6rliqjmjaLooYwNCiAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["MonthlyWorkRecordAdd.vue"], "names": [], "mappings": ";AA6SA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "MonthlyWorkRecordAdd.vue", "sourceRoot": "src/views/AssessmentOrgan/MonthlyWorkRecordAdd", "sourcesContent": ["<template>\r\n  <!-- 新建/编辑 月工作纪实 -->\r\n  <div class=\"MonthlyWorkRecordAdd\">\r\n    <div class=\"add-form-title\">{{ id? '编辑' : '新增'}}</div>\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"qd-form\">\r\n      <el-form-item label=\"标题\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"title\">\r\n        <el-input placeholder=\"请输入标题\"\r\n                  v-model=\"form.title\">\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"所属个人\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"publishUserName\">\r\n        <el-input placeholder=\"请选择所属个人\"\r\n                  :disabled=\"disabled\"\r\n                  readonly\r\n                  @focus=\"focus\"\r\n                  v-model=\"form.publishUserName\">\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"部门\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"officeId\">\r\n        <zy-select width=\"222\"\r\n                   node-key=\"id\"\r\n                   v-model=\"form.officeId\"\r\n                   :data=\"officeData\"\r\n                   @select=\"select\"\r\n                   placeholder=\"请选择部门\">\r\n        </zy-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"发布时间\"\r\n                    class=\"form-item-wd50\"\r\n                    prop=\"publishTime\">\r\n        <el-date-picker v-model=\"form.publishTime\"\r\n                        type=\"datetime\"\r\n                        value-format=\"timestamp\"\r\n                        placeholder=\"选择日期时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <br>\r\n      <el-form-item label=\"上传附件\"\r\n                    class=\"form-item-wd100 form-upload\">\r\n        <zy-upload-file ref=\"upload\"\r\n                        module=\"notice\"\r\n                        :data=\"file\"\r\n                        :max=\"10\"\r\n                        placeholder=\"仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式\">\r\n        </zy-upload-file>\r\n      </el-form-item>\r\n\r\n      <!-- <el-form-item label=\"上传附件\"\r\n                    class=\"form-upload\">\r\n        <el-upload class=\"form-upload-demo\"\r\n                   drag\r\n                   action=\"/\"\r\n                   :before-remove=\"beforeRemove\"\r\n                   :before-upload=\"handleFile\"\r\n                   :http-request=\"fileUpload\"\r\n                   :file-list=\"file\"\r\n                   multiple>\r\n          <div class=\"el-upload__text\">将附件拖拽至此区域，或<em>点击上传</em></div>\r\n          <div class=\"el-upload__tip\">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>\r\n        </el-upload>\r\n      </el-form-item> -->\r\n\r\n      <el-form-item label=\"内容\"\r\n                    class=\"form-item-wd100\">\r\n        <wang-editor v-model='form.content'></wang-editor>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"form-footer-btn\">\r\n      <el-button type=\"primary\"\r\n                 size=\"small\"\r\n                 @click=\"submitForm('form')\">提交</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm1('form')\">重置</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm('form')\">取消</el-button>\r\n    </div>\r\n    <zy-pop-up v-model=\"userShow\"\r\n               title=\"选择所属个人\">\r\n      <candidates-user point=\"point_21\"\r\n                       :max=\"1\"\r\n                       :data=\"userData\"\r\n                       @userCallback=\"userCallback\"></candidates-user>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'MonthlyWorkRecordAdd',\r\n  data () {\r\n    return {\r\n      id: this.$route.query.id,\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())), // 获取当前登录人信息\r\n      form: {\r\n        title: '',\r\n        publishTime: '',\r\n        publishUserId: '',\r\n        publishUserName: '',\r\n        officeId: '',\r\n        officeName: '',\r\n        content: '',\r\n        // ******\r\n        attachmentId: '',\r\n        auditStatus: ''\r\n\r\n      },\r\n      rules: {\r\n        title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n        publishUserName: [\r\n          { required: true, message: '请输入所属个人', trigger: 'blur' }\r\n        ],\r\n        officeId: [\r\n          { required: true, message: '请输入部门', trigger: 'blur' }\r\n        ],\r\n        publishTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ]\r\n      },\r\n      file: [],\r\n      disabled: false,\r\n      officeData: [],\r\n      userData: [],\r\n      userShow: false\r\n    }\r\n  },\r\n  mounted () {\r\n    this.treeList()\r\n    this.form.publishTime = new Date().getTime()\r\n    if (this.id) {\r\n      this.templatePageInfo()\r\n    }\r\n    // 需根据登录信息做判断\r\n    this.form.officeName = this.user.officeName\r\n    this.form.officeId = this.user.officeId\r\n    this.form.publishUserId = this.user.id\r\n    this.form.publishUserName = this.user.userName\r\n    this.userData = [{ mobile: this.user.mobile, officeName: this.user.officeName, officeId: this.user.officeId, name: this.user.userName, position: this.user.position, userId: this.user.id, userName: this.user.userName }]\r\n\r\n    // TODO:测试环境暂时注释,提交时再解开*************************************************************************************\r\n    // 登录的user.otherInfo.IsEvaluationAdmin（是否考核管理员）如果等于true,则下面信息回显当前登录人的机构和用户信息，且为可编辑状态，可编辑状态下，调用选人点point_21选择人员并回显机构和人员信息，否则只回显当前登录人的机构和用户信息，不可编辑\r\n    // if (this.user.otherInfo.userOtherInfo.isEvaluationAdmin) {\r\n    //   this.disabled = false\r\n    // } else {\r\n    //   this.disabled = true\r\n    // }\r\n    // *************************************************************************************************************************\r\n  },\r\n  inject: ['tabDelJump'],\r\n  methods: {\r\n    /**\r\n* 限制上传附件的文件类型\r\n*/\r\n    handleFile (file, fileList) {\r\n    },\r\n    /**\r\n   * 上传附件请求方法\r\n  */\r\n    fileUpload (files) {\r\n      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''\r\n      const param = new FormData()\r\n      param.append('module', 'ta')\r\n      param.append('siteId', JSON.parse(areaId))\r\n      param.append('attachment', files.file)\r\n      this.$api.proposal.proposalfile(param).then(res => {\r\n        var { data } = res\r\n        data[0].name = data[0].fileName\r\n        this.file.push(data[0])\r\n        console.log(this.file)\r\n      })\r\n    },\r\n    /**\r\n   * 删除附件\r\n  */\r\n    beforeRemove (file, fileList) {\r\n      var fileData = this.file\r\n      this.file = fileData.filter(item => item.id !== file.id)\r\n    },\r\n    /**\r\n     *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    select (row) {\r\n      this.form.officeName = row ? row.label : ''\r\n    },\r\n    // 获取详情\r\n    async templatePageInfo () {\r\n      const res = await this.$api.AssessmentOrgan.reqWorkDetails(this.id)\r\n      const data = res.data\r\n      if (data.attachment) {\r\n        // data.attachment.uid = data.attachment.id\r\n        // data.attachment.fileName = data.attachment.oldName // 附件名称\r\n        // this.file.push(data.attachment)\r\n\r\n        data.attachment.forEach(item => {\r\n          item.uid = item.id\r\n          item.fileName = item.oldName // 附件名称\r\n          this.file.push(item)\r\n        })\r\n      }\r\n      const { title, publishTime, publishUserId, publishUserName, officeId, officeName, content, auditStatus } = res.data\r\n\r\n      this.form = { title, publishTime, publishUserId, publishUserName, officeId, officeName, content, auditStatus }\r\n    },\r\n    // 提交\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var data = {\r\n            id: this.id,\r\n            title: this.form.title,\r\n            publishTime: this.form.publishTime,\r\n            publishUserId: this.form.publishUserId,\r\n            publishUserName: this.form.publishUserName,\r\n            content: this.form.content,\r\n            officeId: this.form.officeId,\r\n            officeName: this.form.officeName,\r\n            // *******\r\n            auditStatus: this.form.auditStatus,\r\n            type: '通知',\r\n            attachmentId: this.$refs.upload.obtainId().join(',') // 获取附件id\r\n          }\r\n          // data.org = JSON.parse(sessionStorage.getItem('user' + this.$logo())).officeId\r\n\r\n          const url = this.id ? '/peacetimemonth/edit' : '/peacetimemonth/add'\r\n          this.$api.AssessmentOrgan.reqAddWorkDetails(url, data).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.tabDelJump()\r\n            }\r\n          })\r\n        } else {\r\n          this.$message.warning('请输入必填项')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    resetForm () {\r\n      this.$confirm('是否离开当前页面', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tabDelJump()\r\n      }).catch(() => {\r\n\r\n      })\r\n    },\r\n    // 重置按钮\r\n    resetForm1 () {\r\n      this.$confirm('是否重置当前输入的内容', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.form.content = ''\r\n        this.$refs.form.resetFields()\r\n      }).catch(() => {\r\n      })\r\n    },\r\n\r\n    focus () {\r\n      this.userShow = !this.userShow\r\n    },\r\n    // 选择用户的回调\r\n    userCallback (data, type) {\r\n      if (type) {\r\n        this.userData = data\r\n        this.form.publishUserName = data[0].name\r\n        this.form.officeName = data[0].officeName\r\n        this.form.publishUserId = data[0].userId\r\n      }\r\n      this.userShow = !this.userShow\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.MonthlyWorkRecordAdd {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n  background-color: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);\r\n  padding: 20px 30px;\r\n\r\n  .wang-editor {\r\n    //解决内容输入过长时能自动换行\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n  }\r\n}\r\n</style>\r\n"]}]}