{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\UEditors\\UEditor.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\UEditors\\UEditor.vue", "mtime": 1752541693432}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["UEditor.vue"], "names": [], "mappings": ";AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "UEditor.vue", "sourceRoot": "src/components/UEditors", "sourcesContent": ["<template>\r\n  <div class=\"hello\">\r\n    <vue-ueditor-wrap v-model=\"model\"\r\n                      @ready=\"ready\"\r\n                      :config=\"myConfig\"\r\n                      ref=\"editors\"></vue-ueditor-wrap>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport VueUeditorWrap from 'vue-ueditor-wrap'\r\nexport default {\r\n  name: 'UEditors',\r\n  components: {\r\n    VueUeditorWrap// eslint-disable-line\r\n  },\r\n  data () {\r\n    return {\r\n      myConfig: {\r\n        // 是否跟随内容撑开\r\n        autoHeightEnabled: false,\r\n        elementPathEnabled: false,\r\n        wordCount: true,\r\n        pasteplain: true, // 纯文本模式\r\n        // 高度\r\n        initialFrameHeight: 280,\r\n        // 宽度\r\n        initialFrameWidth: '100%',\r\n        // 图片上传的路径\r\n        serverUrl: `${this.$api.general.baseURL()}/ueditor/exec`,\r\n        // serverUrl: `http://*************/ueditor/exec`,\r\n        // 资源依赖的路径\r\n        UEDITOR_HOME_URL: './UEditor/',\r\n        toolbars: [\r\n          // ['undo', 'redo', 'bold', 'italic', 'underline'], // 第一行工具栏按钮\r\n          // ['justifyleft', 'justifycenter', 'justifyright', 'justifyjustify'], // 第二行工具栏按钮\r\n          // ['insertunorderedlist', 'insertorderedlist', 'blockquote'] // 第三行工具栏按钮\r\n          // ['link', 'unlink', 'insertimage'], // 第四行工具栏按钮\r\n          // ['inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts'],\r\n          // ['indent', 'autotypeset'] // /第五行工具栏按钮\r\n          ['autotypeset'] // 第五行工具栏按钮\r\n        ],\r\n        // 鼠标右键的功能，但是一般不用编辑器带的功能，因为编辑器自带的不能右键复制，所以这个变量设置为空数组\r\n        contextMenu: [],\r\n        lineheight: [1, 1.25, 1.5, 1.75, 2, 2.25, 2.5, 2.75, 3, 3.25, 3.5, 3.75, 4],\r\n        autotypeset: {\r\n          mergeEmptyline: true, // 合并空行\r\n          removeClass: true, // 去掉冗余的class\r\n          removeEmptyline: true, // 去掉空行\r\n          textAlign: 'left', // 段落的排版方式，可以是 left,right,center,justify 去掉这个属性表示不执行排版\r\n          imageBlockLine: 'center', // 图片的浮动方式，独占一行剧中,左右浮动，默认: center,left,right,none 去掉这个属性表示不执行排版\r\n          pasteFilter: true, // 根据规则过滤没事粘贴进来的内容\r\n          clearFontSize: true, // 去掉所有的内嵌字号，使用编辑器默认的字号\r\n          clearFontFamily: true, // 去掉所有的内嵌字体，使用编辑器默认的字体\r\n          removeEmptyNode: true, // 去掉空节点\r\n          // 可以去掉的标签\r\n          removeTagNames: { 标签名字: 1 },\r\n          indent: true, // 行首缩进\r\n          indentValue: '2em', // 行首缩进的大小\r\n          symbolConver: 'tobdc',\r\n          bdc2sb: false,\r\n          tobdc: true\r\n          // ignoreChars: /[\\uFF10-\\uFF19]/g\r\n        },\r\n        maximumWords: 10000,\r\n        zIndex: 999,\r\n        fontfamily: [\r\n          { label: '', name: '宋体, SimSun', val: '宋体, SimSun' },\r\n          { label: '', name: '微软雅黑, Microsoft YaHei', val: '微软雅黑, Microsoft YaHei' },\r\n          { label: '', name: '黑体, SimHei', val: '黑体, SimHei' }\r\n          // 添加其他字体选项\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    model (newValue) {\r\n      this.checkAndRemoveSpan(newValue)\r\n      this.handleContent()\r\n      // setTimeout(() => {\r\n      //   this.formatContent(this.$refs.editor.editor)\r\n      // }, 200)\r\n    }\r\n  },\r\n  computed: {\r\n    model: {\r\n      get () {\r\n        return this.value\r\n      },\r\n      set (value) {\r\n        this.$emit('input', value)\r\n      }\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String\r\n    },\r\n    maximumWords: {\r\n      type: Number\r\n    }\r\n    // toolbars: {}\r\n  },\r\n  created () {\r\n    if (this.maximumWords) {\r\n      this.myConfig.maximumWords = this.maximumWords\r\n    }\r\n  },\r\n  methods: {\r\n    formatContent (editor) {\r\n      console.log('触发')\r\n      // editor.execCommand('removeFormat')\r\n      editor.body.innerHTML = editor.body.innerHTML.replace(/\\s*style=\"[^\"]*\"/g, ' ')\r\n      // 调用UEditor的命令进行自动排版\r\n      editor.execCommand('autotypeset', {\r\n        mergeEmptyline: true, // 合并空行\r\n        removeClass: true, // 去掉冗余的class\r\n        removeEmptyline: true, // 去掉空行\r\n        textAlign: 'left', // 段落的排版方式，可以是 left,right,center,justify 去掉这个属性表示不执行排版\r\n        imageBlockLine: 'center', // 图片的浮动方式，独占一行剧中,左右浮动，默认: center,left,right,none 去掉这个属性表示不执行排版\r\n        pasteFilter: true, // 根据规则过滤没事粘贴进来的内容\r\n        clearFontSize: true, // 去掉所有的内嵌字号，使用编辑器默认的字号\r\n        clearFontFamily: true, // 去掉所有的内嵌字体，使用编辑器默认的字体\r\n        removeEmptyNode: true, // 去掉空节点\r\n        // 可以去掉的标签\r\n        removeTagNames: { 标签名字: 1 },\r\n        indent: true, // 行首缩进\r\n        indentValue: '2em', // 行首缩进的大小\r\n        symbolConver: 'tobdc',\r\n        bdc2sb: false,\r\n        tobdc: true\r\n      })\r\n    },\r\n    handleContent () {\r\n      // 处理编辑器内容\r\n      // 在自动排版后执行的操作\r\n      // console.log('文本已自动排版')\r\n      var _this = this\r\n      var processedContent = this.model.replace(/[\\uff21-\\uff3a\\uff41-\\uff5a\\uff10-\\uff19／％．]/g, function (char) {\r\n        // 忽略数字、冒号、逗号和句号的转换\r\n        return String.fromCharCode(char.charCodeAt(0) - 65248)\r\n      })\r\n      // 将处理后的内容设置回编辑器\r\n      // setTimeout(() => {\r\n      _this.model = processedContent\r\n      // }, 200)\r\n      // console.log(processedContent)\r\n      // console.log('变化')\r\n    },\r\n    checkAndRemoveSpan (content) {\r\n      const hasSpan = /<span\\b[^>]*>/i.test(content)\r\n      if (hasSpan) {\r\n        const newContent = content.replace(/<span\\b[^>]*>/gi, '<span>')\r\n        this.model = newContent\r\n      }\r\n    },\r\n    blur () {\r\n      this.$emit('blur')\r\n      // this.handleContent()\r\n    },\r\n    ready (editor) {\r\n      setTimeout(() => {\r\n        this.formatContent(editor)\r\n      }, 500)\r\n      console.log(editor)\r\n      // 监听粘贴事件\r\n      editor.addListener('afterpaste', () => {\r\n        this.formatContent(editor)\r\n      })\r\n      editor.addListener('blur', this.blur)\r\n      editor.addListener('handleContent', this.handleContent)\r\n      // console.log(editor.getContent())\r\n      editor.ready(() => {\r\n        editor.execCommand('fontfamily', '宋体') // 字体\r\n        editor.execCommand('lineheight', 2) // 行间距\r\n        editor.execCommand('fontsize', '26px') // 字号\r\n        editor.execCommand('forecolor', '#262626') // 字体颜色\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.hello {\r\n  width: 100%;\r\n\r\n  h1,\r\n  h2 {\r\n    font-weight: normal;\r\n  }\r\n\r\n  ul {\r\n    list-style-type: none;\r\n    padding: 0;\r\n  }\r\n\r\n  li {\r\n    display: inline-block;\r\n    margin: 0 10px;\r\n  }\r\n\r\n  a {\r\n    color: #42b983;\r\n  }\r\n\r\n  .hint {\r\n    color: red;\r\n    margin-bottom: 10px;\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .hint>img {\r\n    width: 20px;\r\n    height: 20px;\r\n    border: 1px solid red;\r\n    margin: 0 5px;\r\n  }\r\n}\r\n</style>\r\n"]}]}