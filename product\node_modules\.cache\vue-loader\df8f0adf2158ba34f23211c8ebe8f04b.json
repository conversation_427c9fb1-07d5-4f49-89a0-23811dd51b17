{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\CompilationColumn\\CompilationColumnNew\\CompilationColumnNew.vue?vue&type=template&id=15b010a2&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\CompilationColumn\\CompilationColumnNew\\CompilationColumnNew.vue", "mtime": 1752541697662}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "ref", "attrs", "model", "form", "rules", "inline", "label", "prop", "placeholder", "clearable", "value", "name", "callback", "$$v", "$set", "expression", "props", "type", "year", "staticStyle", "width", "min", "sort", "isTop", "_v", "isPushApp", "on", "click", "$event", "submitForm", "resetForm", "staticRenderFns", "_withStripped"], "sources": ["D:/zy/xm/pc/qdzx/product/src/views/wisdomWarehouse/CompilationColumn/CompilationColumnNew/CompilationColumnNew.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"CompilationColumnNew\" },\n    [\n      _c(\n        \"el-form\",\n        {\n          ref: \"form\",\n          staticClass: \"newForm\",\n          attrs: {\n            model: _vm.form,\n            rules: _vm.rules,\n            inline: \"\",\n            \"label-position\": \"top\",\n          },\n        },\n        [\n          _c(\n            \"el-form-item\",\n            {\n              staticClass: \"form-input\",\n              attrs: { label: \"栏目名称\", prop: \"name\" },\n            },\n            [\n              _c(\"el-input\", {\n                attrs: { placeholder: \"请输入栏目名称\", clearable: \"\" },\n                model: {\n                  value: _vm.form.name,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.form, \"name\", $$v)\n                  },\n                  expression: \"form.name\",\n                },\n              }),\n            ],\n            1\n          ),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"所属年份\", props: \"year\" } },\n            [\n              _c(\"el-date-picker\", {\n                attrs: { type: \"year\", placeholder: \"选择年\" },\n                model: {\n                  value: _vm.form.year,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.form, \"year\", $$v)\n                  },\n                  expression: \"form.year\",\n                },\n              }),\n            ],\n            1\n          ),\n          _c(\n            \"el-form-item\",\n            {\n              staticClass: \"form-input\",\n              attrs: { label: \"排序\", prop: \"sort\" },\n            },\n            [\n              _c(\"el-input-number\", {\n                staticStyle: { width: \"296px\" },\n                attrs: { placeholder: \"请输入排序\", min: 1, clearable: \"\" },\n                model: {\n                  value: _vm.form.sort,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.form, \"sort\", $$v)\n                  },\n                  expression: \"form.sort\",\n                },\n              }),\n            ],\n            1\n          ),\n          _c(\n            \"el-form-item\",\n            { staticClass: \"form-input\", attrs: { label: \"是否置顶\" } },\n            [\n              _c(\n                \"el-radio-group\",\n                {\n                  model: {\n                    value: _vm.form.isTop,\n                    callback: function ($$v) {\n                      _vm.$set(_vm.form, \"isTop\", $$v)\n                    },\n                    expression: \"form.isTop\",\n                  },\n                },\n                [\n                  _c(\"el-radio\", { attrs: { label: \"1\" } }, [_vm._v(\"置顶\")]),\n                  _c(\"el-radio\", { attrs: { label: \"0\" } }, [_vm._v(\"不置顶\")]),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-form-item\",\n            { staticClass: \"form-input\", attrs: { label: \"是否APP显示\" } },\n            [\n              _c(\n                \"el-radio-group\",\n                {\n                  model: {\n                    value: _vm.form.isPushApp,\n                    callback: function ($$v) {\n                      _vm.$set(_vm.form, \"isPushApp\", $$v)\n                    },\n                    expression: \"form.isPushApp\",\n                  },\n                },\n                [\n                  _c(\"el-radio\", { attrs: { label: \"1\" } }, [_vm._v(\"是\")]),\n                  _c(\"el-radio\", { attrs: { label: \"0\" } }, [_vm._v(\"否\")]),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            { staticClass: \"form-button\" },\n            [\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.submitForm(\"form\")\n                    },\n                  },\n                },\n                [_vm._v(\"确定\")]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  on: {\n                    click: function ($event) {\n                      return _vm.resetForm(\"form\")\n                    },\n                  },\n                },\n                [_vm._v(\"取消\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAT,GAAkB;EAC7B,IAAIC,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CACP,KADO,EAEP;IAAEE,WAAW,EAAE;EAAf,CAFO,EAGP,CACEF,EAAE,CACA,SADA,EAEA;IACEG,GAAG,EAAE,MADP;IAEED,WAAW,EAAE,SAFf;IAGEE,KAAK,EAAE;MACLC,KAAK,EAAEN,GAAG,CAACO,IADN;MAELC,KAAK,EAAER,GAAG,CAACQ,KAFN;MAGLC,MAAM,EAAE,EAHH;MAIL,kBAAkB;IAJb;EAHT,CAFA,EAYA,CACER,EAAE,CACA,cADA,EAEA;IACEE,WAAW,EAAE,YADf;IAEEE,KAAK,EAAE;MAAEK,KAAK,EAAE,MAAT;MAAiBC,IAAI,EAAE;IAAvB;EAFT,CAFA,EAMA,CACEV,EAAE,CAAC,UAAD,EAAa;IACbI,KAAK,EAAE;MAAEO,WAAW,EAAE,SAAf;MAA0BC,SAAS,EAAE;IAArC,CADM;IAEbP,KAAK,EAAE;MACLQ,KAAK,EAAEd,GAAG,CAACO,IAAJ,CAASQ,IADX;MAELC,QAAQ,EAAE,UAAUC,GAAV,EAAe;QACvBjB,GAAG,CAACkB,IAAJ,CAASlB,GAAG,CAACO,IAAb,EAAmB,MAAnB,EAA2BU,GAA3B;MACD,CAJI;MAKLE,UAAU,EAAE;IALP;EAFM,CAAb,CADJ,CANA,EAkBA,CAlBA,CADJ,EAqBElB,EAAE,CACA,cADA,EAEA;IAAEI,KAAK,EAAE;MAAEK,KAAK,EAAE,MAAT;MAAiBU,KAAK,EAAE;IAAxB;EAAT,CAFA,EAGA,CACEnB,EAAE,CAAC,gBAAD,EAAmB;IACnBI,KAAK,EAAE;MAAEgB,IAAI,EAAE,MAAR;MAAgBT,WAAW,EAAE;IAA7B,CADY;IAEnBN,KAAK,EAAE;MACLQ,KAAK,EAAEd,GAAG,CAACO,IAAJ,CAASe,IADX;MAELN,QAAQ,EAAE,UAAUC,GAAV,EAAe;QACvBjB,GAAG,CAACkB,IAAJ,CAASlB,GAAG,CAACO,IAAb,EAAmB,MAAnB,EAA2BU,GAA3B;MACD,CAJI;MAKLE,UAAU,EAAE;IALP;EAFY,CAAnB,CADJ,CAHA,EAeA,CAfA,CArBJ,EAsCElB,EAAE,CACA,cADA,EAEA;IACEE,WAAW,EAAE,YADf;IAEEE,KAAK,EAAE;MAAEK,KAAK,EAAE,IAAT;MAAeC,IAAI,EAAE;IAArB;EAFT,CAFA,EAMA,CACEV,EAAE,CAAC,iBAAD,EAAoB;IACpBsB,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAT,CADO;IAEpBnB,KAAK,EAAE;MAAEO,WAAW,EAAE,OAAf;MAAwBa,GAAG,EAAE,CAA7B;MAAgCZ,SAAS,EAAE;IAA3C,CAFa;IAGpBP,KAAK,EAAE;MACLQ,KAAK,EAAEd,GAAG,CAACO,IAAJ,CAASmB,IADX;MAELV,QAAQ,EAAE,UAAUC,GAAV,EAAe;QACvBjB,GAAG,CAACkB,IAAJ,CAASlB,GAAG,CAACO,IAAb,EAAmB,MAAnB,EAA2BU,GAA3B;MACD,CAJI;MAKLE,UAAU,EAAE;IALP;EAHa,CAApB,CADJ,CANA,EAmBA,CAnBA,CAtCJ,EA2DElB,EAAE,CACA,cADA,EAEA;IAAEE,WAAW,EAAE,YAAf;IAA6BE,KAAK,EAAE;MAAEK,KAAK,EAAE;IAAT;EAApC,CAFA,EAGA,CACET,EAAE,CACA,gBADA,EAEA;IACEK,KAAK,EAAE;MACLQ,KAAK,EAAEd,GAAG,CAACO,IAAJ,CAASoB,KADX;MAELX,QAAQ,EAAE,UAAUC,GAAV,EAAe;QACvBjB,GAAG,CAACkB,IAAJ,CAASlB,GAAG,CAACO,IAAb,EAAmB,OAAnB,EAA4BU,GAA5B;MACD,CAJI;MAKLE,UAAU,EAAE;IALP;EADT,CAFA,EAWA,CACElB,EAAE,CAAC,UAAD,EAAa;IAAEI,KAAK,EAAE;MAAEK,KAAK,EAAE;IAAT;EAAT,CAAb,EAAwC,CAACV,GAAG,CAAC4B,EAAJ,CAAO,IAAP,CAAD,CAAxC,CADJ,EAEE3B,EAAE,CAAC,UAAD,EAAa;IAAEI,KAAK,EAAE;MAAEK,KAAK,EAAE;IAAT;EAAT,CAAb,EAAwC,CAACV,GAAG,CAAC4B,EAAJ,CAAO,KAAP,CAAD,CAAxC,CAFJ,CAXA,EAeA,CAfA,CADJ,CAHA,EAsBA,CAtBA,CA3DJ,EAmFE3B,EAAE,CACA,cADA,EAEA;IAAEE,WAAW,EAAE,YAAf;IAA6BE,KAAK,EAAE;MAAEK,KAAK,EAAE;IAAT;EAApC,CAFA,EAGA,CACET,EAAE,CACA,gBADA,EAEA;IACEK,KAAK,EAAE;MACLQ,KAAK,EAAEd,GAAG,CAACO,IAAJ,CAASsB,SADX;MAELb,QAAQ,EAAE,UAAUC,GAAV,EAAe;QACvBjB,GAAG,CAACkB,IAAJ,CAASlB,GAAG,CAACO,IAAb,EAAmB,WAAnB,EAAgCU,GAAhC;MACD,CAJI;MAKLE,UAAU,EAAE;IALP;EADT,CAFA,EAWA,CACElB,EAAE,CAAC,UAAD,EAAa;IAAEI,KAAK,EAAE;MAAEK,KAAK,EAAE;IAAT;EAAT,CAAb,EAAwC,CAACV,GAAG,CAAC4B,EAAJ,CAAO,GAAP,CAAD,CAAxC,CADJ,EAEE3B,EAAE,CAAC,UAAD,EAAa;IAAEI,KAAK,EAAE;MAAEK,KAAK,EAAE;IAAT;EAAT,CAAb,EAAwC,CAACV,GAAG,CAAC4B,EAAJ,CAAO,GAAP,CAAD,CAAxC,CAFJ,CAXA,EAeA,CAfA,CADJ,CAHA,EAsBA,CAtBA,CAnFJ,EA2GE3B,EAAE,CACA,KADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACEF,EAAE,CACA,WADA,EAEA;IACEI,KAAK,EAAE;MAAEgB,IAAI,EAAE;IAAR,CADT;IAEES,EAAE,EAAE;MACFC,KAAK,EAAE,UAAUC,MAAV,EAAkB;QACvB,OAAOhC,GAAG,CAACiC,UAAJ,CAAe,MAAf,CAAP;MACD;IAHC;EAFN,CAFA,EAUA,CAACjC,GAAG,CAAC4B,EAAJ,CAAO,IAAP,CAAD,CAVA,CADJ,EAaE3B,EAAE,CACA,WADA,EAEA;IACE6B,EAAE,EAAE;MACFC,KAAK,EAAE,UAAUC,MAAV,EAAkB;QACvB,OAAOhC,GAAG,CAACkC,SAAJ,CAAc,MAAd,CAAP;MACD;IAHC;EADN,CAFA,EASA,CAAClC,GAAG,CAAC4B,EAAJ,CAAO,IAAP,CAAD,CATA,CAbJ,CAHA,EA4BA,CA5BA,CA3GJ,CAZA,EAsJA,CAtJA,CADJ,CAHO,EA6JP,CA7JO,CAAT;AA+JD,CAlKD;;AAmKA,IAAIO,eAAe,GAAG,EAAtB;AACApC,MAAM,CAACqC,aAAP,GAAuB,IAAvB;AAEA,SAASrC,MAAT,EAAiBoC,eAAjB"}]}