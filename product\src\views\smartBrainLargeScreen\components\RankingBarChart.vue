<template>
  <div :id="id" class="ranking-bar-chart"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'RankingBar<PERSON><PERSON>',
  props: {
    id: {
      type: String,
      required: true
    },
    chartData: {
      type: Array,
      required: true,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    showValues: {
      type: Boolean,
      default: true
    },
    maxValue: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      chart: null
    }
  },
  mounted () {
    this.initChart()
  },
  beforeDestroy () {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.resizeChart)
  },
  watch: {
    chartData: {
      handler () {
        this.renderChart()
      },
      deep: true
    }
  },
  methods: {
    initChart () {
      const chartContainer = document.getElementById(this.id)
      if (!chartContainer) return
      this.chart = echarts.init(chartContainer)
      this.renderChart()
      window.addEventListener('resize', this.resizeChart)
    },
    getOption () {
      const xAxisData = this.chartData.map(item => item.name)
      const seriesData = this.chartData.map(item => item.value)
      return {
        grid: {
          left: '0%',
          right: '0%',
          bottom: '0%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.2)'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            color: '#D9E6FF',
            fontSize: 12,
            interval: 0,
            rotate: 35,
            margin: 8,
            formatter: (value) => {
              return value
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 5,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            color: '#D9E6FF',
            fontSize: 12
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              type: 'dashed'
            }
          }
        },
        series: [
          {
            type: 'bar',
            data: seriesData,
            barWidth: 20,
            showBackground: false,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#00E4FF' },
                  { offset: 0.5, color: '#0090FF' },
                  { offset: 1, color: '#004080' }
                ]
              },
              borderRadius: [4, 4, 0, 0],
              shadowColor: 'rgba(0, 212, 255, 0.3)',
              shadowBlur: 10,
              shadowOffsetY: 2
            },
            emphasis: {
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: '#00FFFF' },
                    { offset: 0.5, color: '#00AAFF' },
                    { offset: 1, color: '#0066CC' }
                  ]
                },
                shadowColor: 'rgba(0, 255, 255, 0.5)',
                shadowBlur: 15
              }
            },
            label: {
              show: this.showValues,
              position: 'top',
              color: '#FFFFFF',
              fontSize: 12,
              formatter: '{c}'
            }
          }
        ],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 20, 40, 0.9)',
          borderColor: '#00D4FF',
          borderWidth: 1,
          textStyle: {
            color: '#FFFFFF',
            fontSize: 12
          },
          formatter: function (params) {
            const data = params[0]
            return `${data.name}<br/>人数: ${data.value}人`
          }
          // formatter: (params) => {
          //   const data = params[0]
          //   return `<div style="padding: 5px;">
          //     <div style="color: #00D4FF; font-weight: bold;">${data.name}</div>
          //     <div style="margin-top: 5px;">数量: <span style="color: #00E4FF; font-weight: bold;">${data.value}</span></div>
          //   </div>`
          // }
        }
      }
    },
    renderChart () {
      if (!this.chart || !this.chartData.length) return
      this.chart.setOption(this.getOption(), true)
    },
    resizeChart () {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ranking-bar-chart {
  width: 100%;
  height: 100%;
}
</style>
