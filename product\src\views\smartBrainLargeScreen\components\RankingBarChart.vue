<template>
  <div :id="id" class="ranking-bar-chart"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'RankingBar<PERSON><PERSON>',
  props: {
    id: {
      type: String,
      required: true
    },
    chartData: {
      type: Array,
      required: true,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    showValues: {
      type: Boolean,
      default: true
    },
    maxValue: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      chart: null
    }
  },
  mounted () {
    this.initChart()
  },
  beforeDestroy () {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart () {
      const chartContainer = document.getElementById(this.id)
      if (!chartContainer) return
      this.chart = echarts.init(chartContainer)
      this.renderChart()
      window.addEventListener('resize', this.resizeChart)
    },
    getOption () {
      const xAxisData = this.chartData.map(item => item.name)
      const seriesData = this.chartData.map(item => item.value)
      // 计算柱子宽度
      // const dom = 300
      const barWidth = 20
      // 生成渐变色数组
      const colors = []
      for (let i = 0; i < this.chartData.length; i++) {
        colors.push({
          type: 'linear',
          x: 0,
          x2: 1,
          y: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#006BBC' // 最左边
            }, {
              offset: 0.5,
              color: 'rgba(31,198,255,0.2)' // 左边的右边 颜色
            }, {
              offset: 0.5,
              color: '#006BBC ' // 右边的左边 颜色
            }, {
              offset: 1,
              color: '#3dc8ca'
            }
          ]
        })
      }

      return {
        // 提示框
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00D4FF',
          borderWidth: 1,
          axisPointer: {
            type: 'shadow'
          },
          textStyle: {
            color: '#FFFFFF'
          },
          formatter: function (params) {
            const data = params[0]
            return `${data.name}<br/>人数: ${data.value}人`
          }
        },
        // 区域位置
        grid: {
          left: '0%',
          right: '0%',
          bottom: '0%',
          top: '5%',
          containLabel: true
        },
        // X轴
        xAxis: {
          data: xAxisData,
          type: 'category',
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.2)'
            }
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            color: '#D9E6FF',
            fontSize: 12,
            interval: 0,
            rotate: 35,
            margin: 8,
            formatter: (value) => {
              return value
            }
          }
        },
        // y轴
        yAxis: {
          type: 'value',
          show: true,
          splitNumber: 5,
          axisLine: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              type: 'dashed'
            }
          },
          axisLabel: {
            show: true,
            color: '#D9E6FF',
            fontSize: 12
          }
        },
        series: [
          {
            type: 'bar',
            barWidth: barWidth,
            itemStyle: {
              normal: {
                color: function (params) {
                  return colors[params.dataIndex % colors.length]
                }
              }
            },
            label: {
              show: true,
              position: 'top',
              color: '#FFFFFF',
              fontSize: 12,
              align: 'center'
            },
            data: seriesData
          },
          {
            z: 2,
            type: 'pictorialBar',
            data: seriesData,
            symbol: 'diamond',
            symbolOffset: [0, '50%'],
            symbolSize: [barWidth, barWidth * 0.5],
            itemStyle: {
              normal: {
                color: function (params) {
                  return colors[params.dataIndex % colors.length]
                }
              }
            }
          },
          {
            z: 3,
            type: 'pictorialBar',
            symbolPosition: 'end',
            data: seriesData,
            symbol: 'diamond',
            symbolOffset: [0, '-50%'],
            symbolSize: [barWidth, barWidth * 0.5],
            itemStyle: {
              normal: {
                borderWidth: 0,
                color: function (params) {
                  return colors[params.dataIndex % colors.length].colorStops[0].color
                }
              }
            }
          }
        ]
      }
    },
    renderChart () {
      if (!this.chart || !this.chartData.length) return
      this.chart.setOption(this.getOption(), true)
    },
    resizeChart () {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ranking-bar-chart {
  width: 100%;
  height: 100%;
}
</style>
