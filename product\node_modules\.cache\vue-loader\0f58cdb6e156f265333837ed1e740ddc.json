{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tabs\\zy-tabs.vue?vue&type=style&index=0&id=545db129&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tabs\\zy-tabs.vue", "mtime": 1752541693597}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3p5LXRhYnMuc2NzcyI7DQo="}, {"version": 3, "sources": ["zy-tabs.vue"], "names": [], "mappings": ";AA2PA", "file": "zy-tabs.vue", "sourceRoot": "src/components/zy-tabs", "sourcesContent": ["<template>\r\n  <div class=\"zy-tabs\">\r\n    <div class=\"zy-tabs-left\"\r\n         v-if=\"show&&offset>0||offset!=0\"\r\n         @click.stop=\"tabsLeft\"><i class=\"el-icon-d-arrow-left\"></i></div>\r\n    <div class=\"zy-tabs-right\"\r\n         v-if=\"show&&offset<biggest\"\r\n         @click.stop=\"tabsRight\"><i class=\"el-icon-d-arrow-right\"></i></div>\r\n    <div class=\"zy-tabs-box\">\r\n      <div class=\"zy-tabs-item-list\">\r\n        <div :class=\"['zy-tabs-item',item.class?'zy-tabs-item-active':'']\"\r\n             v-for=\"(item, index) in tabsData\"\r\n             :key=\"index\"\r\n             @click=\"selected(item)\">\r\n          <div class=\"zy-tabs-item-number\">{{item.number}}</div>\r\n          <div class=\"zy-tabs-item-text\">{{item.name}}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyTabs',\r\n  data () {\r\n    return {\r\n      tabsData: [],\r\n      show: false,\r\n      offset: 0,\r\n      biggest: 0,\r\n      screenWidth: document.body.clientWidth,\r\n      timer: false\r\n    }\r\n  },\r\n  props: {\r\n    value: {},\r\n    tabsList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    shift: {\r\n      type: Number,\r\n      default: 168\r\n    },\r\n    props: {\r\n      type: Object,\r\n      default: () => { }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  created () {\r\n    this.tabsCopyData(this.deepCopy(this.tabsList))\r\n  },\r\n  mounted () {\r\n    this.biggestClick()\r\n    // 绑定onresize事件 监听屏幕变化设置宽\r\n    this.$nextTick(() => {\r\n      this.screenWidth = document.body.clientWidth\r\n    })\r\n    window.onresize = () => {\r\n      return (() => {\r\n        window.screenWidth = document.body.clientWidth\r\n        this.screenWidth = window.screenWidth\r\n      })()\r\n    }\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      this.tabsData.forEach(item => {\r\n        if (item.id === val) {\r\n          this.selected(item)\r\n        }\r\n      })\r\n    },\r\n    tabsList (val) {\r\n      this.tabsCopyData(this.deepCopy(this.tabsList))\r\n    },\r\n    tabsData (val) {\r\n      if (val.length) {\r\n        this.tabsData.forEach(item => {\r\n          if (item.id === this.value) {\r\n            this.selected(item)\r\n          }\r\n        })\r\n      }\r\n    },\r\n    screenWidth (val) {\r\n      if (!this.timer) {\r\n        this.screenWidth = val\r\n        this.timer = true\r\n        this.biggestClick()\r\n        setTimeout(() => {\r\n          this.timer = false\r\n        }, 400)\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    selected (data) {\r\n      this.selectedClick(data)\r\n      var arr = this.tabsData\r\n      arr.forEach(item => {\r\n        item.class = false\r\n        if (item.id === data.id) {\r\n          this.$emit('id', item.id)\r\n          item.class = true\r\n        }\r\n      })\r\n      setTimeout(() => {\r\n        this.tabsBox()\r\n      }, 300)\r\n    },\r\n    selectedClick (data) {\r\n      var arr = []\r\n      this.tabsList.forEach((item, index) => {\r\n        if (!JSON.stringify(this.props) == '{}') {// eslint-disable-line\r\n          if (data.id === item[this.props.id]) {\r\n            arr = item\r\n          }\r\n        } else {\r\n          if (data.id === item.id) {\r\n            arr = item\r\n          }\r\n        }\r\n      })\r\n      this.$emit('tabs-click', arr)\r\n    },\r\n    biggestClick () {\r\n      var tabBox = document.querySelector('.zy-tabs-box')\r\n      var itemBox = document.querySelector('.zy-tabs-item-list')\r\n      if (itemBox.offsetWidth > tabBox.offsetWidth) {\r\n        this.show = true\r\n        this.biggest = itemBox.offsetWidth - tabBox.offsetWidth\r\n      } else {\r\n        this.show = false\r\n      }\r\n    },\r\n    tabsLeft () {\r\n      var itemBox = document.querySelector('.zy-tabs-item-list')\r\n      var offset = this.offset - this.shift\r\n      if (this.offset - this.shift <= 0) {\r\n        offset = 0\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      this.offset = offset\r\n    },\r\n    tabsRight () {\r\n      var itemBox = document.querySelector('.zy-tabs-item-list')\r\n      var offset = this.offset + this.shift\r\n      if (this.biggest < this.offset + this.shift) {\r\n        offset = this.biggest\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      this.offset = offset\r\n    },\r\n    tabsBox () {\r\n      var tabBox = document.querySelector('.zy-tabs-box')\r\n      var itemBox = document.querySelector('.zy-tabs-item-list')\r\n      var item = document.querySelector('.zy-tabs-item-active')\r\n      if (tabBox.offsetWidth < itemBox.offsetWidth) {\r\n        itemBox.style.transform = 'translateX(0px)'\r\n        itemBox.style.transitionDuration = '.4s'\r\n        if (itemBox.offsetWidth === item.offsetLeft + item.offsetWidth) {\r\n          this.offset = itemBox.offsetWidth - tabBox.offsetWidth\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`\r\n        } else if (tabBox.offsetWidth / 2 > itemBox.offsetWidth - item.offsetLeft) {\r\n          this.offset = itemBox.offsetWidth - tabBox.offsetWidth\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`\r\n        } else {\r\n          if (item.offsetLeft - tabBox.offsetWidth / 2 < 0) {\r\n            this.offset = 0\r\n          } else {\r\n            this.offset = item.offsetLeft - tabBox.offsetWidth / 2\r\n          }\r\n          itemBox.style.transform = `translateX(-${item.offsetLeft - tabBox.offsetWidth / 2}px)`\r\n        }\r\n      }\r\n    },\r\n    tabsCopyData (data) {\r\n      this.initData(data)\r\n      this.tabsData = data\r\n    },\r\n    initData (items) {\r\n      items.forEach((item, index) => {\r\n        if ((typeof item.id) === 'undefined') { // eslint-disable-line\r\n          item.id = item[this.props.id]\r\n        }\r\n        if ((typeof item.name) === 'undefined') { // eslint-disable-line\r\n          item.name = item[this.props.name]\r\n        }\r\n        if ((typeof item.number) === 'undefined') { // eslint-disable-line\r\n          item.number = item[this.props.number]\r\n        }\r\n        if ((typeof item.class) === 'undefined') { // eslint-disable-line\r\n          item.class = false\r\n        }\r\n        if (this.tabId === item.id) {\r\n          item.class = true\r\n        }\r\n      })\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    }\r\n  },\r\n  beforeDestroy () {\r\n    window.onresize = null\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-tabs.scss\";\r\n</style>\r\n"]}]}