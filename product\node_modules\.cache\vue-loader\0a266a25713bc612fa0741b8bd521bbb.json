{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-cascader\\zy-cascader.vue?vue&type=style&index=0&id=29bb2e29&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-cascader\\zy-cascader.vue", "mtime": 1752541693518}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3p5LWNhc2NhZGVyLnNjc3MiOw0K"}, {"version": 3, "sources": ["zy-cascader.vue"], "names": [], "mappings": ";AAwKA", "file": "zy-cascader.vue", "sourceRoot": "src/components/zy-cascader", "sourcesContent": ["<template>\r\n  <div class=\"zy-cascader\"\r\n       :style=\"{ width: width + 'px' }\">\r\n    <el-popover popper-class=\"zy-cascader-popover\"\r\n                :trigger=\"trigger\"\r\n                :disabled=\"disabled\"\r\n                :width=\"width\"\r\n                v-model=\"options_show\">\r\n      <el-input slot=\"reference\"\r\n                clearable\r\n                @blur=\"blur\"\r\n                @focus=\"focus\"\r\n                ref=\"zy-cascader\"\r\n                v-model=\"input\"\r\n                @clear=\"remove\"\r\n                :disabled=\"disabled\"\r\n                :placeholder=\"inputText\">\r\n        <i slot=\"suffix\"\r\n           v-if=\"input == ''\"\r\n           :class=\"['zy-cascader-icon', 'el-icon-arrow-down', options_show ? 'zy-cascader-icon-a' : '']\"></i>\r\n      </el-input>\r\n      <el-scrollbar class=\"zy-cascader-box\">\r\n        <zy-tree-components :tree=\"data\"\r\n                            v-model=\"id\"\r\n                            :child=\"child\"\r\n                            :props=\"props\"\r\n                            :keyword=\"input\"\r\n                            :node-key=\"nodeKey\"\r\n                            :determine=\"determine\"\r\n                            @on-tree-click=\"selectedClick\"></zy-tree-components>\r\n      </el-scrollbar>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyCascader',\r\n  data () {\r\n    return {\r\n      id: this.value,\r\n      input: '',\r\n      selectData: {},\r\n      options_show: false,\r\n      inputText: '',\r\n      determine: false\r\n    }\r\n  },\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    // 数据\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    // 触发方式 click/focus/hover/manual\r\n    trigger: {\r\n      type: String,\r\n      default: 'click'\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '请选择内容'\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 宽度\r\n    width: {\r\n      type: String,\r\n      default: '296'\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    child: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  created () {\r\n    this.inputText = this.placeholder\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.id = val\r\n        this.determine = true\r\n      } else {\r\n        this.id = ''\r\n        this.input = ''\r\n        this.inputText = this.placeholder\r\n      }\r\n    },\r\n    id (val) {\r\n      this.$emit('id', val)\r\n    },\r\n    selectData (val) {\r\n      this.$emit('select', val)\r\n    },\r\n    options_show () {\r\n      if (this.input === '' && this.id && !this.options_show) {\r\n        this.determine = true\r\n        this.input = this.selectedText\r\n        this.inputText = this.selectedText\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    blur () {\r\n      if (this.input === '' && this.id && !this.options_show) {\r\n        this.determine = true\r\n        this.input = this.selectedText\r\n        this.inputText = this.selectedText\r\n      }\r\n    },\r\n    focus () {\r\n      this.determine = false\r\n      if (this.input && this.id) {\r\n        this.input = ''\r\n        this.inputText = this.selectedText\r\n      }\r\n    },\r\n    // 下拉框选中事件\r\n    selectedClick (data) {\r\n      // if (this.selectedText === data[this.props.label]) {\r\n      //   return\r\n      // }\r\n      this.input = data[this.props.label]\r\n      this.selectData = data\r\n      this.options_show = false\r\n      this.inputText = data[this.props.label]\r\n      this.selectedText = data[this.props.label]\r\n      this.determine = true\r\n      this.$refs['zy-cascader'].focus()\r\n      setTimeout(() => {\r\n        this.$refs['zy-cascader'].blur()\r\n      }, 22)\r\n    },\r\n    remove () {\r\n      this.id = ''\r\n      this.input = ''\r\n      this.inputText = this.placeholder\r\n      this.selectData = {}\r\n      setTimeout(() => {\r\n        this.options_show = false\r\n      }, 22)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-cascader.scss\";\r\n</style>\r\n"]}]}