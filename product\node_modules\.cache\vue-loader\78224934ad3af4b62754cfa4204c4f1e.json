{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\finishDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\finishDetail.vue", "mtime": 1752541693790}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["finishDetail.vue"], "names": [], "mappings": ";AA+IA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "finishDetail.vue", "sourceRoot": "src/views/AssessmentOrgan/BusinessObjectives", "sourcesContent": ["<template>\r\n  <!-- 点击完成情况 -->\r\n  <div class=\"finishDetail\">\r\n    <div class=\"buttonColumn\">\r\n      <!-- 新增/删除 按钮 -->\r\n      <div class=\"qd-btn-box\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"finishStatus\"\r\n          >新增\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          plain\r\n          @click=\"handleBatchDelete\"\r\n          >删除\r\n        </el-button>\r\n\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-circle-check\"\r\n          v-permissions=\"\r\n            'auth:business:checkPass'\r\n              ? 'auth:business:checkPass'\r\n              : 'auth:innovation:checkPass'\r\n          \"\r\n          @click=\"passClick(2)\"\r\n          >审核通过\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-remove-outline\"\r\n          v-permissions=\"\r\n            'auth:business:checkNotPass'\r\n              ? 'auth:business:checkNotPass'\r\n              : 'auth:innovation:checkNotPass'\r\n          \"\r\n          @click=\"passClick(3)\"\r\n          >审核不通过\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"listView\">\r\n      <zy-table>\r\n        <el-table\r\n          slot=\"zytable\"\r\n          :data=\"tableData\"\r\n          row-key=\"id\"\r\n          ref=\"multipleTable\"\r\n          @select=\"selected\"\r\n          @select-all=\"selectedAll\"\r\n          :header-cell-style=\"{ background: '#eef1f6', color: '#606266' }\"\r\n          tooltip-effect=\"dark\"\r\n          class=\"tableStyle\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\"> </el-table-column>\r\n\r\n          <el-table-column\r\n            label=\"标题\"\r\n            show-overflow-tooltip\r\n            prop=\"title\"\r\n            width=\"450\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" @click=\"modify(scope.row)\" size=\"small\">\r\n                {{ scope.row.title }}\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"审核状态\" width=\"110\" prop=\"auditStatus\">\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"overTime\" label=\"完成时间\" width=\"220\">\r\n            <template slot-scope=\"scope\">\r\n              <div>{{ $format(scope.row.overTime).substr(0, 16) }}</div>\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"auditStatus\"\r\n                         label=\"审核状态\"\r\n                         show-overflow-tooltip>\r\n          <template slot-scope=\"scope\"> -->\r\n          <!-- <div v-if=\"scope.row.auditStatus == '1' \"> 待审核 </div>\r\n                <div v-if=\"scope.row.auditStatus == '0' \"> 审核通过</div> -->\r\n          <!-- <div>{{scope.row.auditStatus}}</div>\r\n          </template>\r\n        </el-table-column> -->\r\n\r\n          <el-table-column label=\"操作\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                type=\"text\"\r\n                @click=\"handleClick(scope.row)\"\r\n                size=\"small\"\r\n              >\r\n                编辑</el-button\r\n              >\r\n\r\n              <el-button\r\n                type=\"text\"\r\n                @click=\"handleDelete(scope.row.id)\"\r\n                class=\"delBtn\"\r\n                size=\"small\"\r\n              >\r\n                删除</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </zy-table>\r\n    </div>\r\n\r\n    <div class=\"paging_box\">\r\n      <el-pagination\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page.sync=\"currentPage\"\r\n        :page-sizes=\"[10, 20, 30, 40]\"\r\n        :page-size.sync=\"pageSize\"\r\n        background\r\n        layout=\"total, prev, pager, next, sizes, jumper\"\r\n        :total=\"total\"\r\n      >\r\n      </el-pagination>\r\n    </div>\r\n\r\n    <zy-pop-up v-model=\"showFinish\" class=\"titleStyle\" title=\"完成情况\">\r\n      <newFinishDetail :id=\"id\" :uid=\"uid\" @newCallback=\"newCallback\">\r\n      </newFinishDetail>\r\n    </zy-pop-up>\r\n\r\n    <zy-pop-up\r\n      v-model=\"showFinishDetail\"\r\n      class=\"titleStyle\"\r\n      title=\"完成情况详情\"\r\n      :beforeClose=\"updateList\"\r\n    >\r\n      <FinishDetailPop :id=\"id\" :uid=\"uid\" @newCallback=\"newCallback\">\r\n      </FinishDetailPop>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n<script>\r\nimport newFinishDetail from './newFinishDetail.vue'\r\nimport FinishDetailPop from './FinishDetailPop.vue'\r\nimport tableData from '@mixins/tableData'\r\n\r\nexport default {\r\n  name: 'finishDetail',\r\n  mixins: [tableData],\r\n  components: {\r\n    newFinishDetail,\r\n    FinishDetailPop\r\n  },\r\n  data () {\r\n    return {\r\n      showFinish: false,\r\n      showFinishDetail: false,\r\n      tableData: [],\r\n      evaluationId: '',\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      uid: '', // 完成情况列表的id\r\n      currentPage: 1,\r\n      total: 10\r\n\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  inject: ['newTab'],\r\n\r\n  mounted () {\r\n    if (this.id) {\r\n      this.getfinishDetailList()\r\n    }\r\n  },\r\n  methods: {\r\n    updateList () {\r\n      this.showFinishDetail = false\r\n      this.getfinishDetailList()\r\n    },\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckWork(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 审核\r\n    async getCheckWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckFinishDetail({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getfinishDetailList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n    newCallback () {\r\n      this.showFinish = false // 关闭弹窗\r\n      this.showFinishDetail = false\r\n      this.getfinishDetailList() // 重新调用(更新)一次列表\r\n    },\r\n    // 请求后台数据 获取列表信息\r\n    async getfinishDetailList () {\r\n      const res = await this.$api.AssessmentOrgan.reqfinishDetailList({\r\n        evaluationId: this.id, // TODO:工作目标或创新创优id(需检查是否传错)\r\n        pageNo: this.currentPage,\r\n        pageSize: this.pageSize,\r\n        // *******************\r\n        memberType: this.memberType,\r\n        auditStatus: this.auditStatus\r\n      })\r\n      var { data, total } = res\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n      this.selectObj = []\r\n      this.selectData = []\r\n      this.$nextTick(function () {\r\n        this.memoryChecked()\r\n      })\r\n      // this.choose = []\r\n      // this.selectObjData = []\r\n    },\r\n    // 新增 完成情况\r\n    finishStatus () {\r\n      this.showFinish = true\r\n      this.uid = 0 // 将newFinishDetail组件的属性:uid设置为0 (false) 达到新增页面效果\r\n    },\r\n    // 编辑\r\n    handleClick (row) {\r\n      this.uid = row.id\r\n      this.showFinish = true\r\n    },\r\n    // 详情\r\n    modify (row) {\r\n      this.uid = row.id\r\n      this.showFinishDetail = true\r\n    },\r\n    // 删除按钮方法  批量删除\r\n    handleDelete (ids) { // TODO: handleDelete 和 handleBatchDelete 1.组合使用为新增/删除栏的删除功能 2.单独使用handleDelete则为操作项的删除功能\r\n      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$api.AssessmentOrgan.reqDelFinishDetail({ ids }).then((res) => {\r\n          if (res.errcode === 200) {\r\n            this.getfinishDetailList()// 删除后更新页面\r\n            this.$message.success('删除成功')\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('取消删除')\r\n        // this.getfinishDetailList()\r\n        return false\r\n      })\r\n    },\r\n    handleBatchDelete () {\r\n      if (this.choose.length === 0) {\r\n        return this.$message.warning('请选择想要删除的项')\r\n      }\r\n      this.handleDelete(this.choose.join(','))\r\n    },\r\n    // 底部页签\r\n    handleSizeChange () {\r\n      this.getfinishDetailList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getfinishDetailList()\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.finishDetail {\r\n  width: 1100px;\r\n  height: 600px;\r\n  padding: 1px 40px;\r\n  overflow: hidden;\r\n  .qd-btn-box {\r\n    padding-bottom: 5px;\r\n  }\r\n\r\n  .listView {\r\n    height: calc(100% - 132px);\r\n    .tableStyle {\r\n      width: 100%;\r\n      height: 500px;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n    }\r\n  }\r\n\r\n  .tableZy {\r\n    height: 500px;\r\n  }\r\n}\r\n</style>\r\n"]}]}