{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\networkDiscuss\\networkDiscussBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\networkDiscuss\\networkDiscussBox.vue", "mtime": 1756371721154}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["networkDiscussBox.vue"], "names": [], "mappings": ";AAoGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "networkDiscussBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/networkDiscuss", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>网络议政</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 网络议政整体情况 -->\r\n        <div class=\"discussion_overall_situation\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">网络议政整体情况</span>\r\n          </div>\r\n          <div class=\"discussion_overall_situation_content\">\r\n            <!-- 发布网络议政 -->\r\n            <div class=\"stat_card\">\r\n              <div class=\"stat_circle\"\r\n                :style=\"{ backgroundImage: 'url(' + require('@/assets/largeScreen/icon_release_network.png') + ')' }\">\r\n                <div class=\"stat_number\">103</div>\r\n              </div>\r\n              <div class=\"stat_label\">发布网络议政</div>\r\n            </div>\r\n\r\n            <!-- 累计参与人次 -->\r\n            <div class=\"stat_card\">\r\n              <div class=\"stat_circle\"\r\n                :style=\"{ backgroundImage: 'url(' + require('@/assets/largeScreen/icon_participation_count.png') + ')' }\">\r\n                <div class=\"stat_number\">10023</div>\r\n              </div>\r\n              <div class=\"stat_label\">累计参与人次</div>\r\n            </div>\r\n\r\n            <!-- 累计征求意见 -->\r\n            <div class=\"stat_card\">\r\n              <div class=\"stat_circle\"\r\n                :style=\"{ backgroundImage: 'url(' + require('@/assets/largeScreen/icon_solicited_opinions.png') + ')' }\">\r\n                <div class=\"stat_number\">256</div>\r\n              </div>\r\n              <div class=\"stat_label\">累计征求意见</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 热词分析 -->\r\n        <div class=\"hot_word_analysis\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">热词分析</span>\r\n          </div>\r\n          <div class=\"hot_word_analysis_content\">\r\n            <WordCloud chart-id=\"hotWordChart\" :words=\"hotWordsData\" @word-click=\"onWordClick\" />\r\n          </div>\r\n        </div>\r\n        <!-- 发布单位征集次数统计 -->\r\n        <div class=\"publish_unit_collections\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">发布单位征集次数统计</span>\r\n          </div>\r\n          <div class=\"publish_unit_collections_content\">\r\n            <BarChart id=\"publish_unit_collections\" :chart-data=\"publishUnitCollectionsData\" :legendShow=\"true\"\r\n              legendName=\"征集次数\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"right-panel\">\r\n        <!-- 最热话题 -->\r\n        <div class=\"hottest_topic\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">最热话题</span>\r\n          </div>\r\n          <div class=\"hottest_topic_list\">\r\n            <div v-for=\"(item, index) in hottestTopicData\" :key=\"item.id\" class=\"hottest_topic_item\"\r\n              :class=\"{ 'with-bg-image': index % 2 === 0, 'with-bg-color': index % 2 === 1 }\">\r\n              <div class=\"hottest_topic_content\">\r\n                <img src=\"../../../assets/largeScreen/icon_hottest_topic.png\" alt=\"\" class=\"hottest_topic_icon\">\r\n                <div class=\"hottest_topic_title\">{{ item.title }}</div>\r\n                <div class=\"hottest_topic_unit\">{{ item.unit }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport WordCloud from '../components/WordCloud.vue'\r\nimport BarChart from '../components/BarChart.vue'\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    WordCloud,\r\n    BarChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      hotWordsData: [\r\n        { name: '经济建设', value: 10 },\r\n        { name: '人才培养', value: 9 },\r\n        { name: 'AI技术', value: 6 },\r\n        { name: '改革创新', value: 7 },\r\n        { name: '教育', value: 5 },\r\n        { name: '车辆交通', value: 6 },\r\n        { name: '旅游', value: 5 },\r\n        { name: '公共安全', value: 7 },\r\n        { name: '智能化', value: 6 },\r\n        { name: '电梯故障', value: 4 },\r\n        { name: '社会保障', value: 6 },\r\n        { name: '环境保护', value: 5 },\r\n        { name: '医疗卫生', value: 7 },\r\n        { name: '文化建设', value: 4 },\r\n        { name: '科技创新', value: 8 }\r\n      ],\r\n      hottestTopicData: [\r\n        {\r\n          id: 1,\r\n          title: '市政协社会和法制工作办公室围绕\"居家适老化改造\"开展专题调研',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '\"与民同行 共创共赢\"新格局下民营企业转型发展座谈会召开',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '\"惠民生·基层行\"义诊活动温暖人心',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '市科技局面复市政协科技界别提案',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '市政协召开\"推进数字化转型\"专题协商会',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 6,\r\n          title: '政协委员深入基层开展\"三服务\"活动',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 1,\r\n          title: '市政协社会和法制工作办公室围绕\"居家适老化改造\"开展专题调研',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '\"与民同行 共创共赢\"新格局下民营企业转型发展座谈会召开',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '\"惠民生·基层行\"义诊活动温暖人心',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '市科技局面复市政协科技界别提案',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '市政协召开\"推进数字化转型\"专题协商会',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 6,\r\n          title: '政协委员深入基层开展\"三服务\"活动',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 1,\r\n          title: '市政协社会和法制工作办公室围绕\"居家适老化改造\"开展专题调研',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '\"与民同行 共创共赢\"新格局下民营企业转型发展座谈会召开',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '\"惠民生·基层行\"义诊活动温暖人心',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '市科技局面复市政协科技界别提案',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '市政协召开\"推进数字化转型\"专题协商会',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 6,\r\n          title: '政协委员深入基层开展\"三服务\"活动',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 1,\r\n          title: '市政协社会和法制工作办公室围绕\"居家适老化改造\"开展专题调研',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '\"与民同行 共创共赢\"新格局下民营企业转型发展座谈会召开',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '\"惠民生·基层行\"义诊活动温暖人心',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '市科技局面复市政协科技界别提案',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '市政协召开\"推进数字化转型\"专题协商会',\r\n          unit: '市政协'\r\n        },\r\n        {\r\n          id: 6,\r\n          title: '政协委员深入基层开展\"三服务\"活动',\r\n          unit: '市政协'\r\n        }\r\n      ],\r\n      // 各专委会提案数数据\r\n      publishUnitCollectionsData: [\r\n        { name: '提案委', value: 43 },\r\n        { name: '经济委', value: 67 },\r\n        { name: '农业农村委', value: 84 },\r\n        { name: '人口资源环境委', value: 52 },\r\n        { name: '教科卫体委', value: 36 },\r\n        { name: '社会和法制委', value: 66 },\r\n        { name: '民族宗教委', value: 26 },\r\n        { name: '港澳台侨外事委', value: 60 },\r\n        { name: '文化文史和学习委', value: 46 }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 词云点击事件\r\n    onWordClick (word) {\r\n      console.log('词汇点击:', word)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 2fr 1fr;\r\n      grid-template-rows: 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 630px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 网络议政整体情况\r\n      .discussion_overall_situation {\r\n        background: url('../../../assets/largeScreen/discussion_overall_situation_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n        height: 400px;\r\n\r\n        .discussion_overall_situation_content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n\r\n          .stat_card {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            text-align: center;\r\n\r\n            .stat_circle {\r\n              width: 140px;\r\n              height: 140px;\r\n              background-size: contain;\r\n              background-repeat: no-repeat;\r\n              background-position: center;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              position: relative;\r\n              margin-bottom: 15px;\r\n\r\n              .stat_number {\r\n                font-size: 28px;\r\n                font-weight: bold;\r\n                color: #ffffff;\r\n                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n                z-index: 2;\r\n              }\r\n            }\r\n\r\n            .stat_label {\r\n              font-size: 16px;\r\n              color: #ffffff;\r\n              font-weight: 500;\r\n              margin-top: 5px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 热词分析\r\n      .hot_word_analysis {\r\n        background: url('../../../assets/largeScreen/hot_word_analysis_bg2.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n        height: 400px;\r\n\r\n        .hot_word_analysis_content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 发布单位征集次数统计\r\n      .publish_unit_collections {\r\n        background: url('../../../assets/largeScreen/publish_unit_collections_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        grid-column: 1 / -1; // 跨越三列（讨论组人员统计在第三行）\r\n        grid-row: 2; // 明确指定在第三行\r\n        height: 550px;\r\n\r\n        .publish_unit_collections_content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 最热话题\r\n      .hottest_topic {\r\n        background: url('../../../assets/largeScreen/hottest_topic_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 100%;\r\n\r\n        .hottest_topic_list {\r\n          margin-top: 70px;\r\n          margin-left: 14px;\r\n          margin-right: 14px;\r\n          height: calc(100% - 70px);\r\n          overflow-y: auto;\r\n\r\n          &::-webkit-scrollbar {\r\n            width: 4px;\r\n          }\r\n\r\n          &::-webkit-scrollbar-track {\r\n            background: rgba(0, 30, 60, 0.3);\r\n            border-radius: 2px;\r\n          }\r\n\r\n          &::-webkit-scrollbar-thumb {\r\n            background: rgba(0, 212, 255, 0.4);\r\n            border-radius: 2px;\r\n\r\n            &:hover {\r\n              background: rgba(0, 212, 255, 0.6);\r\n            }\r\n          }\r\n\r\n          .hottest_topic_item {\r\n            margin-bottom: 12px;\r\n            overflow: hidden;\r\n            position: relative;\r\n\r\n            &:last-child {\r\n              margin-bottom: 0;\r\n            }\r\n\r\n            // 奇数项 - 背景图片样式\r\n            &.with-bg-image {\r\n              background: url('../../../assets/largeScreen/table_bg.png') no-repeat;\r\n              background-size: 100% 100%;\r\n              background-position: center;\r\n            }\r\n\r\n            // 偶数项 - 背景颜色样式\r\n            &.with-bg-color {\r\n              background: rgba(6, 79, 219, 0.05);\r\n            }\r\n\r\n            .hottest_topic_content {\r\n              padding: 12px 15px;\r\n              position: relative;\r\n              z-index: 2;\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n\r\n              .hottest_topic_icon {\r\n                width: 15px;\r\n                height: 18px;\r\n                margin-right: 10px;\r\n              }\r\n\r\n              .hottest_topic_title {\r\n                flex: 1;\r\n                color: #fff;\r\n                font-size: 16px;\r\n                margin-right: 16px;\r\n                // 文本溢出处理\r\n                display: -webkit-box;\r\n                -webkit-line-clamp: 1;\r\n                -webkit-box-orient: vertical;\r\n                overflow: hidden;\r\n                text-overflow: ellipsis;\r\n              }\r\n\r\n              .hottest_topic_unit {\r\n                flex-shrink: 0;\r\n                font-size: 16px;\r\n                color: #FFFFFF;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}