{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\DoubleQuote\\DoubleQuoteAddOrEdit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\DoubleQuote\\DoubleQuoteAddOrEdit.vue", "mtime": 1752541693820}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAuGA;EACAA,4BADA;;EAEAC;IACA;MACAC,wBADA;MAEAC,+DAFA;MAGAC,kBAHA;MAIAC;QACAH,MADA;QAEAI,cAFA;QAGAC,SAHA;QAIAC,eAJA;QAKAC,iBALA;QAMAC,mBANA;QAOAC,YAPA;QAQAC,cARA;QASAC,WATA;QAUA;QACAC,gBAXA;QAYAC;MAZA,CAJA;MAmBAC;QACAV,aACA;UAAAW;UAAAC;UAAAC;QAAA,CADA,CADA;QAIAZ,QACA;UAAAU;UAAAC;UAAAC;QAAA,CADA,CAJA;QAOAT,kBACA;UAAAO;UAAAC;UAAAC;QAAA,CADA,CAPA;QAUAR,WACA;UAAAM;UAAAC;UAAAC;QAAA,CADA,CAVA;QAaAX,cACA;UAAAS;UAAAC;UAAAC;QAAA,CADA;MAbA,CAnBA;MAoCAC,QApCA;MAqCAC,eArCA;MAsCAC,cAtCA;MAuCAC,YAvCA;MAwCA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;IA/CA;EAiDA,CApDA;;EAqDAC;IACA;IACA;IACA;;IAEA;MACA;IACA;;IACA;IACA;IACA;IACA;IACA;MAAAC;MAAAd;MAAAD;MAAAX;MAAA2B;MAAAC;MAAAC;IAAA,GAZA,CAcA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA,CA3EA;;EA4EAC,sBA5EA;EA6EAC;IACA;AACA;AACA;IACA;MACA;MACA;QAAA9B;MAAA;MACA;IACA,CARA;;IASA+B;MACA;IACA,CAXA;;IAYA;AACA;AACA;IACA;MACA;QACAC;MADA;MAGA;QAAAhC;MAAA;MACA;IACA,CArBA;;IAsBA;IACA;MACA;MACA;;MACA;QACAA;UACAiC;UACAA,6BAFA,CAEA;;UACA;QACA,CAJA;MAKA,CATA,CAUA;;;MACA;QAAAhC;QAAAK;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAE;QAAAT;MAAA,aAXA,CAaA;MACA;MACA;MACA;;MAEA;QAAAJ;QAAAK;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAE;QAAAT;MAAA;IACA,CA1CA;;IA2CA;IACA6B;MACA;QACA;UACA;YACAjC,gBADA;YAEAI,gCAFA;YAGAC,sBAHA;YAIAC,kCAJA;YAKAC,sCALA;YAMAC,0CANA;YAOAG,0BAPA;YAQAF,4BARA;YASAC,gCATA;YAWAG,kCAXA;YAYAqB,UAZA;YAcAtB,oDAdA,CAcA;;UAdA,EADA,CAiBA;;UAEA;UACA;YACA;cAAAuB;cAAAC;YAAA;;YACA;cACA;gBACApB,eADA;gBAEAkB;cAFA;cAIA;YACA;UACA,CATA;QAUA,CA9BA,MA8BA;UACA;UACA;QACA;MACA,CAnCA;IAoCA,CAjFA;;IAkFA;IACAG;MACA;QACAC,uBADA;QAEAC,sBAFA;QAGAL;MAHA,GAIAM,IAJA,CAIA;QACA;MACA,CANA,EAMAC,KANA,CAMA,OAEA,CARA;IASA,CA7FA;;IA8FA;IACAC;MACA;QACAJ,uBADA;QAEAC,sBAFA;QAGAL;MAHA,GAIAM,IAJA,CAIA;QACA;QACA;MACA,CAPA,EAOAC,KAPA,CAOA,OACA,CARA;IASA,CAzGA;;IA2GAE;MACA;IACA,CA7GA;;IA8GA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;;MACA;IACA;;EAvHA;AA7EA", "names": ["name", "data", "id", "user", "doubleTypeData", "form", "doubleType", "title", "publishTime", "publishUserId", "publishUserName", "officeId", "officeName", "content", "attachmentId", "auditStatus", "rules", "required", "message", "trigger", "file", "disabled", "officeData", "userData", "userShow", "mounted", "mobile", "position", "userId", "userName", "inject", "methods", "select", "types", "item", "submitForm", "type", "<PERSON><PERSON><PERSON>", "errmsg", "resetForm", "confirmButtonText", "cancelButtonText", "then", "catch", "resetForm1", "focus", "userCallback"], "sourceRoot": "src/views/AssessmentOrgan/DoubleQuote", "sources": ["DoubleQuoteAddOrEdit.vue"], "sourcesContent": ["<template>\r\n  <!-- 新建/编辑 双招双引 -->\r\n  <div class=\"DoubleQuoteAddOrEdit\">\r\n    <div class=\"add-form-title\">{{ id? '编辑' : '新增'}}</div>\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"qd-form\">\r\n\r\n      <el-form-item label=\"类型\"\r\n                    class=\"form-item-wd100 leixin\"\r\n                    prop=\"doubleType\">\r\n        <el-select width=\"900\"\r\n                   v-model=\"form.doubleType\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择类型\">\r\n          <el-option v-for=\"item in doubleTypeData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <!--  -->\r\n\r\n      <!--  -->\r\n      <el-form-item label=\"标题\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"title\">\r\n        <el-input placeholder=\"请输入标题\"\r\n                  v-model=\"form.title\">\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"所属个人\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"publishUserName\">\r\n        <el-input placeholder=\"请选择所属个人\"\r\n                  :disabled=\"disabled\"\r\n                  readonly\r\n                  @focus=\"focus\"\r\n                  v-model=\"form.publishUserName\">\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"部门\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"officeId\">\r\n        <zy-select width=\"222\"\r\n                   node-key=\"id\"\r\n                   v-model=\"form.officeId\"\r\n                   :data=\"officeData\"\r\n                   @select=\"select\"\r\n                   placeholder=\"请选择部门\">\r\n        </zy-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"发布时间\"\r\n                    class=\"form-item-wd50\"\r\n                    prop=\"publishTime\">\r\n        <el-date-picker v-model=\"form.publishTime\"\r\n                        type=\"datetime\"\r\n                        value-format=\"timestamp\"\r\n                        placeholder=\"选择日期时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <br>\r\n      <el-form-item label=\"上传附件\"\r\n                    class=\"form-item-wd100 form-upload\">\r\n        <zy-upload-file ref=\"upload\"\r\n                        module=\"notice\"\r\n                        :data=\"file\"\r\n                        :max=\"10\"\r\n                        placeholder=\"仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式\"></zy-upload-file>\r\n      </el-form-item>\r\n      <el-form-item label=\"内容\"\r\n                    class=\"form-item-wd100\">\r\n        <wang-editor v-model='form.content'></wang-editor>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"form-footer-btn\">\r\n      <el-button type=\"primary\"\r\n                 size=\"small\"\r\n                 @click=\"submitForm('form')\">提交</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm1('form')\">重置</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm('form')\">取消</el-button>\r\n    </div>\r\n    <zy-pop-up v-model=\"userShow\"\r\n               title=\"选择所属个人\">\r\n      <candidates-user point=\"point_21\"\r\n                       :max=\"1\"\r\n                       :data=\"userData\"\r\n                       @userCallback=\"userCallback\"></candidates-user>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'DoubleQuoteAddOrEdit',\r\n  data () {\r\n    return {\r\n      id: this.$route.query.id,\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())),\r\n      doubleTypeData: [],\r\n      form: {\r\n        id: '',\r\n        doubleType: '',\r\n        title: '',\r\n        publishTime: '',\r\n        publishUserId: '',\r\n        publishUserName: '',\r\n        officeId: '',\r\n        officeName: '',\r\n        content: '',\r\n        // ******\r\n        attachmentId: '',\r\n        auditStatus: ''\r\n\r\n      },\r\n      rules: {\r\n        doubleType: [\r\n          { required: true, message: '请选择类型', trigger: 'blur' }\r\n        ],\r\n        title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n        publishUserName: [\r\n          { required: true, message: '请输入所属个人', trigger: 'blur' }\r\n        ],\r\n        officeId: [\r\n          { required: true, message: '请输入部门', trigger: 'blur' }\r\n        ],\r\n        publishTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ]\r\n      },\r\n      file: [],\r\n      disabled: false,\r\n      officeData: [],\r\n      userData: [],\r\n      // returnTypelist: [\r\n      //   { value: '文本', id: '文本' },\r\n      //   { value: '单选', id: '单选' },\r\n      //   { value: '多选', id: '多选' }\r\n      // ],\r\n      // time: [],\r\n      // userData: []\r\n      userShow: false\r\n    }\r\n  },\r\n  mounted () {\r\n    this.dictionaryPubkvs()\r\n    this.treeList()\r\n    this.form.publishTime = new Date().getTime()\r\n\r\n    if (this.id) {\r\n      this.getDoubleQuoteDetails()\r\n    }\r\n    this.form.officeName = this.user.officeName\r\n    this.form.officeId = this.user.officeId\r\n    this.form.publishUserId = this.user.id\r\n    this.form.publishUserName = this.user.userName\r\n    this.userData = [{ mobile: this.user.mobile, officeName: this.user.officeName, officeId: this.user.officeId, name: this.user.userName, position: this.user.position, userId: this.user.id, userName: this.user.userName }]\r\n\r\n    // TODO:测试环境暂时注释,提交时再解开*************************************************************************************\r\n    // 登录的user.otherInfo.IsEvaluationAdmin（是否考核管理员）如果等于true,则下面信息回显当前登录人的机构和用户信息，且为可编辑状态，可编辑状态下，调用选人点point_21选择人员并回显机构和人员信息，否则只回显当前登录人的机构和用户信息，不可编辑\r\n    // if (this.user.otherInfo.userOtherInfo.isEvaluationAdmin) {\r\n    //   this.disabled = false\r\n    // } else {\r\n    //   this.disabled = true\r\n    // }\r\n    // *************************************************************************************************************************\r\n  },\r\n  inject: ['tabDelJump'],\r\n  methods: {\r\n    /**\r\n     *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    select (row) {\r\n      this.form.officeName = row ? row.label : ''\r\n    },\r\n    /**\r\n     *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_same_double'\r\n      })\r\n      var { data } = res\r\n      this.doubleTypeData = data.evaluation_same_double\r\n    },\r\n    // 获取详情\r\n    async getDoubleQuoteDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqDoubleQuoteDetails(this.id)\r\n      const data = res.data\r\n      if (data.attachmentInfo) {\r\n        data.attachmentInfo.forEach(item => {\r\n          item.uid = item.id\r\n          item.fileName = item.oldName // 附件名称\r\n          this.file.push(item)\r\n        })\r\n      }\r\n      // TODO:待修改(参照新建工作目标)\r\n      const { id, title, publishTime, publishUserId, publishUserName, officeId, officeName, content, auditStatus, doubleType } = res.data\r\n\r\n      // this.file = attachment //报错原因:传入的附件attachment为对象 不能直接赋值给file数组\r\n      // for (const i in attachmentInfo) { // 方法:将对象转为数组(因为传入的附件数据为对象)\r\n      //   this.file.push(attachmentInfo[i])\r\n      // }\r\n\r\n      this.form = { id, title, publishTime, publishUserId, publishUserName, officeId, officeName, content, auditStatus, doubleType }\r\n    },\r\n    // 提交\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var data = {\r\n            id: this.form.id,\r\n            doubleType: this.form.doubleType,\r\n            title: this.form.title,\r\n            publishTime: this.form.publishTime,\r\n            publishUserId: this.form.publishUserId,\r\n            publishUserName: this.form.publishUserName,\r\n            content: this.form.content,\r\n            officeId: this.form.officeId,\r\n            officeName: this.form.officeName,\r\n\r\n            auditStatus: this.form.auditStatus,\r\n            type: '通知',\r\n\r\n            attachmentId: this.$refs.upload.obtainId().join(',') // 获取附件id\r\n          }\r\n          // data.org = JSON.parse(sessionStorage.getItem('user' + this.$logo())).officeId\r\n\r\n          const url = this.id ? '/samedouble/edit' : '/samedouble/add'\r\n          this.$api.AssessmentOrgan.reqAddDoubleQuote(url, data).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.tabDelJump()\r\n            }\r\n          })\r\n        } else {\r\n          this.$message.warning('请输入必填项')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    resetForm () {\r\n      this.$confirm('是否离开当前页面', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tabDelJump()\r\n      }).catch(() => {\r\n\r\n      })\r\n    },\r\n    // 重置按钮\r\n    resetForm1 () {\r\n      this.$confirm('是否重置当前输入的内容', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.form.content = ''\r\n        this.$refs.form.resetFields()\r\n      }).catch(() => {\r\n      })\r\n    },\r\n\r\n    focus () {\r\n      this.userShow = !this.userShow\r\n    },\r\n    // 选择用户的回调\r\n    userCallback (data, type) {\r\n      if (type) {\r\n        this.userData = data\r\n        this.form.publishUserName = data[0].name\r\n        this.form.officeName = data[0].officeName\r\n        this.form.publishUserId = data[0].userId\r\n      }\r\n      this.userShow = !this.userShow\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.DoubleQuoteAddOrEdit {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n  background-color: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);\r\n  padding: 20px 30px;\r\n\r\n  .el-select {\r\n    display: block;\r\n    width: 500px;\r\n  }\r\n\r\n  .leixin {\r\n    // width: 500px;\r\n  }\r\n\r\n  .wang-editor {\r\n    //解决内容输入过长时能自动换行\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n  }\r\n}\r\n</style>\r\n"]}]}