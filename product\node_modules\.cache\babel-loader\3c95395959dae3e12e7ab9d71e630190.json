{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-widget\\zy-widget.vue?vue&type=template&id=4dae42f5&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-widget\\zy-widget.vue", "mtime": 1752541693628}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uIHJlbmRlcigpIHsKICB2YXIgX3ZtID0gdGhpcywKICAgICAgX2MgPSBfdm0uX3NlbGYuX2M7CgogIHJldHVybiBfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJ6eS13aWRnZXQiCiAgfSwgW19jKCJzcGFuIiwgewogICAgc3RhdGljQ2xhc3M6ICJsYWJlbCIsCiAgICBzdHlsZTogYHdpZHRoOiAke192bS5sYWJlbFd9O2AKICB9LCBbX3ZtLl92KF92bS5fcyhfdm0ubGFiZWwpKV0pLCBfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJ6eS13aWRnZXQtc2xvdCIKICB9LCBbX3ZtLl90KCJkZWZhdWx0IildLCAyKV0pOwp9OwoKdmFyIHN0YXRpY1JlbmRlckZucyA9IFtdOwpyZW5kZXIuX3dpdGhTdHJpcHBlZCA9IHRydWU7CmV4cG9ydCB7IHJlbmRlciwgc3RhdGljUmVuZGVyRm5zIH07"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "style", "labelW", "_v", "_s", "label", "_t", "staticRenderFns", "_withStripped"], "sources": ["D:/zy/xm/pc/qdzx/product/src/components/zy-widget/zy-widget.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"zy-widget\" }, [\n    _c(\"span\", { staticClass: \"label\", style: `width: ${_vm.labelW};` }, [\n      _vm._v(_vm._s(_vm.label)),\n    ]),\n    _c(\"div\", { staticClass: \"zy-widget-slot\" }, [_vm._t(\"default\")], 2),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAT,GAAkB;EAC7B,IAAIC,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAAsC,CAC7CF,EAAE,CAAC,MAAD,EAAS;IAAEE,WAAW,EAAE,OAAf;IAAwBC,KAAK,EAAG,UAASJ,GAAG,CAACK,MAAO;EAApD,CAAT,EAAmE,CACnEL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAACO,EAAJ,CAAOP,GAAG,CAACQ,KAAX,CAAP,CADmE,CAAnE,CAD2C,EAI7CP,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA2C,CAACH,GAAG,CAACS,EAAJ,CAAO,SAAP,CAAD,CAA3C,EAAgE,CAAhE,CAJ2C,CAAtC,CAAT;AAMD,CATD;;AAUA,IAAIC,eAAe,GAAG,EAAtB;AACAX,MAAM,CAACY,aAAP,GAAuB,IAAvB;AAEA,SAASZ,MAAT,EAAiBW,eAAjB"}]}