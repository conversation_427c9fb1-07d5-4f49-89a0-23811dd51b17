{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu\\zy-menu.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu\\zy-menu.vue", "mtime": 1752541693540}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHp5TWVudUNoaWxkcmVuIGZyb20gJy4venktbWVudS1jaGlsZHJlbic7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnenlNZW51JywKCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGtleTogJycKICAgIH07CiAgfSwKCiAgcHJvcHM6IHsKICAgIHZhbHVlOiBbU3RyaW5nLCBOdW1iZXIsIEFycmF5LCBPYmplY3RdLAogICAgbWVudTogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0sCiAgICB0ZXh0Q29sb3I6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfSwKICAgIGJhY2tncm91bmRDb2xvcjogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAogICAgYWN0aXZlVGV4dENvbG9yOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0sCiAgICAvLyDmoJHnu5PmnoTphY3nva4KICAgIHByb3BzOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogKCkgPT4gewogICAgICAgIHJldHVybiB7CiAgICAgICAgICBjaGlsZHJlbjogJ2NoaWxkcmVuJywKICAgICAgICAgIGxhYmVsOiAnbGFiZWwnLAogICAgICAgICAgaWQ6ICdpZCcsCiAgICAgICAgICB0bzogJ3RvJywKICAgICAgICAgIGljb246ICdpY29uVXJsJywKICAgICAgICAgIGlzU2hvdzogJ2lzU2hvdycsCiAgICAgICAgICBzaG93VmFsdWU6IHRydWUKICAgICAgICB9OwogICAgICB9CiAgICB9CiAgfSwKICBlbWl0czogWydzZWxlY3QnXSwKICBjb21wb25lbnRzOiB7CiAgICB6eU1lbnVDaGlsZHJlbgogIH0sCiAgd2F0Y2g6IHsKICAgIHZhbHVlKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgdGhpcy5rZXkgPSB2YWw7CiAgICAgICAgdGhpcy5zZWxlY3REYXRhKHRoaXMubWVudSwgdmFsKTsKICAgICAgfQogICAgfQoKICB9LAoKICBtb3VudGVkKCkgewogICAgdGhpcy5rZXkgPSB0aGlzLnZhbHVlOwogICAgdGhpcy5zZWxlY3REYXRhKHRoaXMubWVudSwgdGhpcy52YWx1ZSk7CiAgfSwKCiAgbWV0aG9kczogewogICAgc2VsZWN0KGtleSkgewogICAgICB0aGlzLmtleSA9IGtleTsKICAgICAgdGhpcy4kZW1pdCgnaW5wdXQnLCBrZXkpOwogICAgICB0aGlzLnNlbGVjdERhdGEodGhpcy5tZW51LCBrZXkpOwogICAgfSwKCiAgICBzZWxlY3REYXRhKGRhdGEsIGlkKSB7CiAgICAgIGRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpZiAoaXRlbVt0aGlzLnByb3BzLmNoaWxkcmVuXS5sZW5ndGggPT09IDApIHsKICAgICAgICAgIGlmIChpdGVtW3RoaXMucHJvcHMuaWRdID09PSBpZCkgewogICAgICAgICAgICB0aGlzLiRlbWl0KCdzZWxlY3QnLCBpdGVtKTsKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5zZWxlY3REYXRhKGl0ZW1bdGhpcy5wcm9wcy5jaGlsZHJlbl0sIGlkKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfQoKICB9Cn07"}, {"version": 3, "mappings": "AAkBA;AACA;EACAA,cADA;;EAEAC;IACA;MACAC;IADA;EAGA,CANA;;EAOAC;IACAC,sCADA;IAEAC;MACAC,WADA;MAEAC;IAFA,CAFA;IAMAC;MACAF,YADA;MAEAC;IAFA,CANA;IAUAE;MACAH,YADA;MAEAC;IAFA,CAVA;IAcAG;MACAJ,YADA;MAEAC;IAFA,CAdA;IAkBA;IACAJ;MACAG,YADA;MAEAC;QACA;UACAI,oBADA;UAEAC,cAFA;UAGAC,QAHA;UAIAC,QAJA;UAKAC,eALA;UAMAC,gBANA;UAOAC;QAPA;MASA;IAZA;EAnBA,CAPA;EAyCAC,iBAzCA;EA0CAC;IACAC;EADA,CA1CA;EA6CAC;IACAjB;MACA;QACA;QACA;MACA;IACA;;EANA,CA7CA;;EAqDAkB;IACA;IACA;EACA,CAxDA;;EAyDAC;IACAC;MACA;MACA;MACA;IACA,CALA;;IAMAC;MACAxB;QACA;UACA;YACA;UACA;QACA,CAJA,MAIA;UACA;QACA;MACA,CARA;IASA;;EAhBA;AAzDA", "names": ["name", "data", "key", "props", "value", "menu", "type", "default", "textColor", "backgroundColor", "activeTextColor", "children", "label", "id", "to", "icon", "isShow", "showValue", "emits", "components", "zyMenuChildren", "watch", "mounted", "methods", "select", "selectData"], "sourceRoot": "src/components/zy-menu", "sources": ["zy-menu.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar class=\"zy-menu\">\r\n    <el-menu\r\n      :default-active=\"key\"\r\n      :background-color=\"backgroundColor\"\r\n      :text-color=\"textColor\"\r\n      :active-text-color=\"activeTextColor\"\r\n      @select=\"select\"\r\n    >\r\n      <zy-menu-children\r\n        :menu=\"menu\"\r\n        :value=\"key\"\r\n        :props=\"props\"\r\n      ></zy-menu-children>\r\n    </el-menu>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nimport zyMenuChildren from './zy-menu-children'\r\nexport default {\r\n  name: 'zyMenu',\r\n  data () {\r\n    return {\r\n      key: ''\r\n    }\r\n  },\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    menu: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    textColor: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    backgroundColor: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    activeTextColor: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label',\r\n          id: 'id',\r\n          to: 'to',\r\n          icon: 'iconUrl',\r\n          isShow: 'isShow',\r\n          showValue: true\r\n        }\r\n      }\r\n    }\r\n  },\r\n  emits: ['select'],\r\n  components: {\r\n    zyMenuChildren\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.key = val\r\n        this.selectData(this.menu, val)\r\n      }\r\n    }\r\n  },\r\n  mounted () {\r\n    this.key = this.value\r\n    this.selectData(this.menu, this.value)\r\n  },\r\n  methods: {\r\n    select (key) {\r\n      this.key = key\r\n      this.$emit('input', key)\r\n      this.selectData(this.menu, key)\r\n    },\r\n    selectData (data, id) {\r\n      data.forEach(item => {\r\n        if (item[this.props.children].length === 0) {\r\n          if (item[this.props.id] === id) {\r\n            this.$emit('select', item)\r\n          }\r\n        } else {\r\n          this.selectData(item[this.props.children], id)\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './zy-menu.scss';\r\n</style>\r\n"]}]}