<template>
  <div class="big-screen" ref="bigScreen">
    <div class="screen-header">
      <div class="header-left">
        <span class="date-time">{{ currentTime }}</span>
        <span class="weather">晴 24℃ 东南风</span>
      </div>
      <div class="header-center">
        <img src="../../../assets/largeScreen/top_header_txt.png" alt="" style="height: 50px;">
      </div>
      <div class="header-right"></div>
    </div>
    <div class="screen-content">
      <div class="left-panel">
        <!-- 委员统计 -->
        <div class="committee_statistics">
          <div class="header_box">
            <span class="header_text_left" @click="handleCommitteeClick">委员统计</span>
            <span class="header_text_right">十二届二次</span>
          </div>
          <div class="committee_statistics_content">
            <div class="committee_statistics_num">
              <div v-for="(item, index) in committeeStatisticsNum" :key="index" class="num_box">
                <img :src="item.icon" alt="" class="num_icon">
                <div>
                  <div class="num_label">{{ item.label }}</div>
                  <div class="num_value" :style="`color:${item.color}`">{{ item.value }}</div>
                </div>
              </div>
            </div>
            <div class="committee_statistics_chart">
              <BarScrollChart id="committee-statistics" :showCount="5" :chart-data="committeeBarData" />
            </div>
          </div>
        </div>
        <!-- 提案统计 -->
        <div class="proposal_statistics">
          <div class="header_box">
            <span class="header_text_left" @click="handleProposalClick">提案统计</span>
            <span class="header_text_right">十二届二次会议</span>
            <span class="header_text_center">提交提案总数：<span>873</span>件</span>
          </div>
          <div class="proposal_statistics_content">
            <div class="proposal_statistics_num">
              <div v-for="(item, index) in proposalStatisticsNum" :key="index" class="num_box">
                <img :src="item.icon" alt="" class="num_icon">
                <div>
                  <div class="num_label">{{ item.label }}</div>
                  <div class="num_value" :style="`color:${item.color}`">{{ item.value }}<span class="num_unit">{{
                    item.unit }}</span></div>
                </div>
              </div>
            </div>
            <div class="proposal_statistics_chart">
              <PieChart id="proposal-statistics" :chart-data="proposalChartData" :name="proposalChartName" />
            </div>
          </div>
        </div>
        <!-- 工作动态 -->
        <div class="work_dynamics">
          <div class="header_box">
            <span class="header_text_left">工作动态</span>
            <span class="header_text_right">本年</span>
          </div>
          <div class="work_dynamics_content">
            <div class="dynamics-list">
              <div v-for="(item, index) in workDynamicsData" :key="item.id" class="dynamics-item"
                :class="{ 'with-bg-image': index % 2 === 0, 'with-bg-color': index % 2 === 1 }">
                <div class="dynamics-content">
                  <div class="dynamics-title">{{ item.title }}</div>
                  <div class="dynamics-date">{{ item.date }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="center-panel">
        <!-- 地图 -->
        <div class="map_box">
          <MapComponent :data="mapData" :areaId="areaId + ''" :areaName="areaName" @region-click="handleRegionClick" />
        </div>
        <!-- 履职统计 -->
        <div class="performance_statistics">
          <div class="header_box">
            <span class="header_text_left" @click="handlePerformanceClick">履职统计</span>
            <span class="header_text_right">十二届二次</span>
          </div>
          <div class="performance_statistics_content">
            <div class="table-container">
              <!-- 固定表头 -->
              <div class="table-header">
                <div class="header-cell">姓名</div>
                <div class="header-cell">会议活动</div>
                <div class="header-cell">政协提案</div>
                <div class="header-cell">社情民意</div>
                <div class="header-cell">议政建言</div>
                <div class="header-cell">读书心得</div>
                <div class="header-cell">委员培训</div>
                <div class="header-cell"></div> <!-- 滚动条占位 -->
              </div>
              <!-- 可滚动内容 -->
              <div class="table-body">
                <div class="table-row" v-for="(item, index) in performanceData" :key="index">
                  <div class="table-cell name-col">{{ item.name }}</div>
                  <div class="table-cell meeting-col">{{ item.meeting }}</div>
                  <div class="table-cell proposal-col">{{ item.proposal }}</div>
                  <div class="table-cell opinion-col">{{ item.opinion }}</div>
                  <div class="table-cell suggestion-col">{{ item.suggestion }}
                  </div>
                  <div class="table-cell reading-col">{{ item.reading }}</div>
                  <div class="table-cell training-col">{{ item.training }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right-panel">
        <!-- 社情民意 -->
        <div class="social">
          <div class="header_box">
            <span class="header_text_left">社情民意</span>
            <span class="header_text_right">本年</span>
          </div>
          <div class="social_content">
            <div class="social-data-container">
              <div class="left-data-item">
                <div class="left-data-label">委员报送</div>
                <div class="left-data-value">总数<span>{{ socialData.memberSubmit.count }}</span>篇</div>
                <div class="left-data-detail">采用<span>{{ socialData.memberSubmit.adopted }}</span> 篇</div>
              </div>
              <div class="center-chart">
                <div class="progress-content">
                  <div class="total-number">{{ socialData.total }}</div>
                  <div class="total-label">总数</div>
                </div>
              </div>
              <div class="right-data-item">
                <div class="right-data-label">单位报送</div>
                <div class="right-data-value">总数<span>{{ socialData.unitSubmit.count }}</span>篇</div>
                <div class="right-data-detail">采用<span>{{ socialData.unitSubmit.adopted }}</span> 篇</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 会议活动 -->
        <div class="conference_activities">
          <div class="header_box">
            <span class="header_text_left">会议活动</span>
            <span class="header_text_right">本年</span>
          </div>
          <div class="conference_activities_content">
            <div class="activities-grid">
              <div v-for="(item, index) in conferenceActivitiesData" :key="index" class="activity-item"
                :class="getItemClass(item.name)">
                <div class="activity-value">{{ item.value }}</div>
                <div class="activity-name">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 网络议政 -->
        <div class="discussions">
          <div class="header_box">
            <span class="header_text_left" @click="handleNetWorkClick">网络议政</span>
            <span class="header_text_right"></span>
          </div>
          <div class="discussions_content">
            <!-- 统计数据区域 -->
            <div class="statistics-section">
              <div v-for="(item, index) in discussionsData.statistics" :key="index" class="stat-item">
                <div class="stat-dot"></div>
                <div class="stat-info">
                  <span class="stat-name">{{ item.name }}</span>
                  <span class="stat-value">{{ item.value }}</span>
                  <span class="stat-unit">{{ item.unit }}</span>
                </div>
              </div>
            </div>

            <!-- 最热话题区域 -->
            <div class="hot-topics-section">
              <div class="hot-topics-header">
                <img src="../../../assets/largeScreen/icon_hot.png" alt="热门" class="hot-icon">
                <span class="hot-title">最热话题</span>
              </div>
              <div class="topics-list">
                <div v-for="(topic, index) in discussionsData.hotTopics" :key="index" class="topic-item">
                  <div class="topic-dot"></div>
                  <span class="topic-text">{{ topic }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useIndex } from '../screen.js'
import MapComponent from '../components/MapComponent.vue'
import PieChart from '../components/PieChart.vue'
import BarScrollChart from '../components/BarScrollChart.vue'

export default {
  name: 'BigScreen',
  components: {
    MapComponent,
    PieChart,
    BarScrollChart
  },
  data () {
    return {
      currentTime: '',
      // 委员统计
      committeeStatisticsNum: [
        { icon: require('../../../assets/largeScreen/icon_cppcc_member.png'), label: '政协委员（人）', value: '10095', color: '#ffffff' },
        { icon: require('../../../assets/largeScreen/icon_committee_member.png'), label: '政协常委', value: '8742', color: '#FCD603' }
      ],
      // 委员统计柱状图数据
      committeeBarData: [
        { name: '中共', value: 32 },
        { name: '民革', value: 15 },
        { name: '民盟', value: 14 },
        { name: '民建', value: 13 },
        { name: '民进', value: 12 },
        { name: '农工', value: 10 },
        { name: '致公', value: 8 },
        { name: '九三', value: 7 },
        { name: '台盟', value: 6 },
        { name: '无党派', value: 5 }
      ],
      // 提案统计
      proposalStatisticsNum: [
        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '委员提案', value: '456', unit: '件', color: '#0DBCDB' },
        { icon: require('../../../assets/largeScreen/icon_circles_proposal.png'), label: '界别提案', value: '354', unit: '件', color: '#0058FF' },
        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '组织提案', value: '211', unit: '件', color: '#1AEBDD' }
      ],
      // 提案统计图表数据
      proposalChartData: [
        { name: '政府制约', value: 22.52 },
        { name: '县区市政', value: 18.33 },
        { name: '司法法治', value: 15.73 },
        { name: '区市政府', value: 11.34 },
        { name: '科技工商', value: 9.56 },
        { name: '教育文化', value: 8.09 },
        { name: '派出机构', value: 4.21 },
        { name: '社会事业', value: 3.71 },
        { name: '企事业', value: 3.65 },
        { name: '农村卫生', value: 3.21 },
        { name: '其他机构', value: 1.86 },
        { name: '各群体他', value: 1.02 }
      ],
      proposalChartName: '提案统计',
      // 工作动态数据
      workDynamicsData: [
        {
          id: 1,
          title: '市政协社会和法制工作办公室围绕"居家适老化改造"开展专题调研',
          date: '2025-06-03'
        },
        {
          id: 2,
          title: '"与民同行 共创共赢"新格局下民营企业转型发展座谈会召开',
          date: '2025-05-30'
        },
        {
          id: 3,
          title: '"惠民生·基层行"义诊活动温暖人心',
          date: '2025-05-30'
        },
        {
          id: 4,
          title: '市科技局面复市政协科技界别提案',
          date: '2025-05-30'
        },
        {
          id: 5,
          title: '市政协召开"推进数字化转型"专题协商会',
          date: '2025-05-28'
        },
        {
          id: 6,
          title: '政协委员深入基层开展"三服务"活动',
          date: '2025-05-25'
        }
      ],
      // 履职统计数据
      performanceData: [
        { name: '马平安', meeting: 515, proposal: 15, opinion: 0, suggestion: 0, reading: 0, training: 0 },
        { name: '马波', meeting: 400, proposal: 0, opinion: 0, suggestion: 12, reading: 0, training: 15 },
        { name: '王玉民', meeting: 490, proposal: 1, opinion: 2, suggestion: 0, reading: 4, training: 25 },
        { name: '王俊宝', meeting: 500, proposal: 0, opinion: 4, suggestion: 1, reading: 5, training: 60 },
        { name: '李明', meeting: 320, proposal: 8, opinion: 1, suggestion: 3, reading: 2, training: 18 },
        { name: '张华', meeting: 280, proposal: 5, opinion: 0, suggestion: 2, reading: 1, training: 12 },
        { name: '刘强', meeting: 450, proposal: 3, opinion: 6, suggestion: 0, reading: 3, training: 35 },
        { name: '陈静', meeting: 380, proposal: 2, opinion: 3, suggestion: 4, reading: 6, training: 28 }
      ],
      // 社情民意数据
      socialData: {
        memberSubmit: {
          count: 345,
          adopted: 21
        },
        unitSubmit: {
          count: 547,
          adopted: 79
        },
        total: 1057
      },
      // 会议活动数据
      conferenceActivitiesData: [
        { name: '会议次数', value: 201 },
        { name: '活动次数', value: 310 },
        { name: '会议人数', value: 2412 },
        { name: '活动人数', value: 4015 }
      ],
      // 网络议政数据
      discussionsData: {
        statistics: [
          { name: '发布议题', value: 72, unit: '个' },
          { name: '累计参与人次', value: 39301, unit: '次' },
          { name: '累计征求意见', value: 12306, unit: '条' }
        ],
        hotTopics: [
          '推进黄河国家文化公园建设',
          '持续推进黄河流域生态保护修复，助力"先行区"建设',
          '全面加强新时代中小学劳动教育'
        ]
      },
      mapData: [
        { name: '市南区', value: '1200', areaId: '370202', adcode: 370202 },
        { name: '市北区', value: '1300', areaId: '370203', adcode: 370203 },
        { name: '黄岛区', value: '850', areaId: '370211', adcode: 370211 },
        { name: '崂山区', value: '700', areaId: '370212', adcode: 370212 },
        { name: '李沧区', value: '1000', areaId: '370213', adcode: 370213 },
        { name: '城阳区', value: '1100', areaId: '370214', adcode: 370214 },
        { name: '即墨区', value: '950', areaId: '370215', adcode: 370215 },
        { name: '胶州市', value: '800', areaId: '370281', adcode: 370281 },
        { name: '平度市', value: '1400', areaId: '370283', adcode: 370283 },
        { name: '莱西市', value: '600', areaId: '370285', adcode: 370285 }
      ],
      areaId: JSON.parse(sessionStorage.getItem('areaId' + this.$logo())),
      areaName: '青岛市'
    }
  },
  computed: {
  },
  mounted () {
    this.initScreen()
    this.updateTime()
    this.timeInterval = setInterval(this.updateTime, 1000)
  },
  beforeDestroy () {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    initScreen () {
      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)
      calcRate()
      windowDraw()
    },
    updateTime () {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    getItemClass (name) {
      if (name.includes('会议')) {
        return 'meeting-item'
      } else if (name.includes('活动')) {
        return 'activity-item-bg'
      }
      return ''
    },
    handleRegionClick (region) {
      console.log('选中地区:', region)
      // 这里可以添加地区点击后的业务逻辑
      // 比如显示该地区的详细数据等
    },
    // 打开委员统计
    handleCommitteeClick () {
      this.$router.push({ path: '/committeeStatisticsBox', query: { route: '/committeeStatisticsBox' } })
    },
    // 打开提案统计
    handleProposalClick () {
      this.$router.push({ path: '/proposalStatisticsBox', query: { route: '/proposalStatisticsBox' } })
    },
    // 打开履职统计
    handlePerformanceClick () {
      this.$router.push({ path: '/performanceStatisticsBox', query: { route: '/performanceStatisticsBox' } })
    },
    // 打开网络议政
    handleNetWorkClick () {
      this.$router.push({ path: '/networkDiscussBox', query: { route: '/networkDiscussBox' } })
    }
  }
}
</script>

<style lang="scss" scoped>
.big-screen {
  width: 1920px;
  height: 1080px;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transform-origin: left top;
  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;
  background-size: cover;
  background-position: center;

  .screen-header {
    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;
    background-size: 100% 100%;
    background-position: center;
    height: 65px;
    display: flex;
    align-items: center;
    padding: 0 40px;

    .header-left {
      display: flex;
      gap: 20px;
      font-size: 14px;
      color: #8cc8ff;
      flex: 1;
    }

    .header-center {
      width: 60%;
      text-align: center;
    }

    .header-right {
      flex: 1;
    }
  }

  .screen-content {
    height: calc(100% - 65px);
    display: flex;
    padding: 35px 20px 0 20px;
    gap: 30px;

    .header_box {
      position: absolute;
      top: 15px;
      left: 24px;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header_text_left {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        cursor: pointer;
      }

      .header_text_right {
        font-size: 15px;
        color: #FFD600;
      }

      .header_text_center {
        font-size: 15px;
        color: #FFFFFF;
        display: flex;
        align-items: center;

        span {
          font-weight: 500;
          font-size: 24px;
          color: #02FBFB;
          margin: 0 10px 0 6px;
        }
      }
    }

    .left-panel,
    .right-panel {
      width: 470px;
      display: flex;
      flex-direction: column;
      gap: 20px 30px;
    }

    .left-panel {
      .committee_statistics {
        background: url('../../../assets/largeScreen/committee_statistics_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 320px;
        width: 100%;

        .committee_statistics_content {
          height: 100%;
          margin-top: 72px;
          margin-left: 20px;
          margin-right: 20px;

          .committee_statistics_num {
            display: flex;
            align-items: center;
            justify-content: space-around;

            .num_box {
              display: flex;
              align-items: center;

              .num_icon {
                width: 64px;
                height: 64px;
                margin-right: 14px;
              }

              .num_label {
                font-size: 15px;
                color: #B4C0CC;
                margin-bottom: 14px;
              }

              .num_value {
                font-weight: bold;
                font-size: 26px;
                color: #FFFFFF;
              }
            }
          }

          .committee_statistics_chart {
            width: 100%;
            height: 180px;
          }
        }
      }

      .proposal_statistics {
        background: url('../../../assets/largeScreen/proposal_statistics_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 310px;
        width: 100%;

        .proposal_statistics_content {
          height: 100%;
          margin-top: 72px;
          margin-left: 20px;
          margin-right: 20px;

          .proposal_statistics_num {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .num_box {
              display: flex;
              align-items: center;

              .num_icon {
                width: 54px;
                height: 54px;
                margin-right: 10px;
              }

              .num_label {
                font-size: 14px;
                color: #FFFFFF;
                margin-bottom: 5px;
              }

              .num_value {
                font-size: 20px;
                color: #0DBCDB;
                font-weight: 500;

                .num_unit {
                  font-size: 14px;
                  color: #FFFFFF;
                  font-weight: normal;
                  margin-left: 4px;
                }
              }
            }
          }

          .proposal_statistics_chart {
            height: 150px;
            margin-top: 20px;
          }
        }
      }

      .work_dynamics {
        background: url('../../../assets/largeScreen/work_dynamics_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 270px;
        width: 100%;

        .work_dynamics_content {
          height: 100%;
          margin-top: 65px;
          margin-left: 14px;
          margin-right: 14px;

          .dynamics-list {
            height: calc(100% - 70px);
            overflow-y: auto;

            &::-webkit-scrollbar {
              width: 4px;
            }

            &::-webkit-scrollbar-track {
              background: rgba(0, 30, 60, 0.3);
              border-radius: 2px;
            }

            &::-webkit-scrollbar-thumb {
              background: rgba(0, 212, 255, 0.4);
              border-radius: 2px;

              &:hover {
                background: rgba(0, 212, 255, 0.6);
              }
            }

            .dynamics-item {
              margin-bottom: 12px;
              overflow: hidden;
              position: relative;

              &:last-child {
                margin-bottom: 0;
              }

              // 奇数项 - 背景图片样式
              &.with-bg-image {
                background: url('../../../assets/largeScreen/table_bg.png') no-repeat;
                background-size: 100% 100%;
                background-position: center;
              }

              // 偶数项 - 背景颜色样式
              &.with-bg-color {
                background: rgba(6, 79, 219, 0.05);
              }

              .dynamics-content {
                padding: 12px 15px;
                position: relative;
                z-index: 2;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .dynamics-title {
                  flex: 1;
                  color: #fff;
                  font-size: 16px;
                  margin-right: 16px;
                  // 文本溢出处理
                  display: -webkit-box;
                  -webkit-line-clamp: 1;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                .dynamics-date {
                  flex-shrink: 0;
                  font-size: 16px;
                  color: #FFFFFF;
                }
              }
            }
          }
        }
      }
    }

    .right-panel {
      .social {
        background: url('../../../assets/largeScreen/social_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 290px;
        width: 100%;

        .social_content {
          height: 190px;
          margin-top: 75px;
          margin-left: 12px;
          margin-right: 12px;
          background: url('../../../assets/largeScreen/social_content_bg.png') no-repeat;
          background-size: 100% 100%;
          background-position: center;

          .social-data-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 100%;

            .left-data-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              text-align: center;
              flex: 1;
              margin-right: 20px;

              .left-data-label {
                font-size: 14px;
                color: #19ECFF;
                margin-bottom: 20px;
                font-family: DIN-BoldItalic, DIN-BoldItalic;
              }

              .left-data-value {
                font-size: 14px;
                color: #FFFFFF;
                margin-bottom: 15px;
                font-family: DIN-BoldItalic, DIN-BoldItalic;

                span {
                  font-weight: 400;
                  font-size: 20px;
                  color: #FFD600;
                  margin: 0 5px;
                }
              }

              .left-data-detail {
                font-size: 14px;
                color: #FFFFFF;
                font-family: DIN-BoldItalic, DIN-BoldItalic;

                span {
                  font-weight: 400;
                  font-size: 20px;
                  color: #19ECFF;
                  margin: 0 5px;
                }
              }
            }

            .center-chart {
              flex: 1;
              display: flex;
              justify-content: center;
              align-items: center;

              .progress-content {
                position: absolute;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .total-number {
                  font-weight: 500;
                  font-size: 24px;
                  color: #FFD600;
                  margin-bottom: 8px;
                }

                .total-label {
                  font-size: 14px;
                  color: #ffffff;
                }
              }
            }

            .right-data-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              text-align: center;
              flex: 1;
              margin-left: 20px;

              .right-data-label {
                font-size: 14px;
                color: #19ECFF;
                margin-bottom: 20px;
                font-family: DIN-BoldItalic, DIN-BoldItalic;
              }

              .right-data-value {
                font-size: 14px;
                color: #FFFFFF;
                margin-bottom: 15px;
                font-family: DIN-BoldItalic, DIN-BoldItalic;

                span {
                  font-weight: 400;
                  font-size: 20px;
                  color: #FFD600;
                  margin: 0 5px;
                }
              }

              .right-data-detail {
                font-size: 14px;
                color: #FFFFFF;
                font-family: DIN-BoldItalic, DIN-BoldItalic;

                span {
                  font-weight: 400;
                  font-size: 20px;
                  color: #19ECFF;
                  margin: 0 5px;
                }
              }
            }
          }
        }
      }

      .conference_activities {
        background: url('../../../assets/largeScreen/conference_activities_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 290px;
        width: 100%;

        .conference_activities_content {
          margin-top: 70px;
          margin-left: 20px;
          margin-right: 20px;

          .activities-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 15px;
            height: 100%;

            .activity-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              background-size: 100% 100%;
              background-repeat: no-repeat;
              background-position: center;
              height: 92px;

              .activity-value {
                font-weight: 500;
                font-size: 32px;
                color: #FFFFFF;
                line-height: 24px;
                margin-bottom: 12px;
                font-family: DIN-BoldItalic, DIN-BoldItalic;
              }

              .activity-name {
                font-size: 16px;
                color: #FFFFFF;
                font-family: DIN-BoldItalic, DIN-BoldItalic;
              }

              &.meeting-item {
                background-image: url('../../../assets/largeScreen/icon_meeting_item_bg.png');
              }

              &.activity-item-bg {
                background-image: url('../../../assets/largeScreen/icon_activity_item_bg.png');
              }
            }
          }
        }
      }

      .discussions {
        background: url('../../../assets/largeScreen/discussions_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 320px;
        width: 100%;

        .discussions_content {
          margin-top: 75px;
          margin-left: 20px;
          margin-right: 20px;
          height: calc(100% - 90px);
          display: flex;
          flex-direction: column;

          .statistics-section {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 20px;
            margin-bottom: 25px;

            .stat-item {
              display: flex;
              align-items: center;
              gap: 12px;

              .stat-dot {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                background: linear-gradient(180deg, #00EEFF 0%, #E1FDFF 100%);
                flex-shrink: 0;
                margin-top: 5px;
              }

              .stat-info {
                display: flex;
                align-items: center;
                gap: 8px;

                .stat-name {
                  font-size: 15px;
                  color: #FFFFFF;
                  font-family: DIN-BoldItalic, DIN-BoldItalic;
                }

                .stat-value {
                  font-weight: 500;
                  font-size: 20px;
                  color: #FFD600;
                  font-family: DIN-BoldItalic, DIN-BoldItalic;
                }

                .stat-unit {
                  font-size: 15px;
                  color: #FFFFFF;
                  font-family: DIN-BoldItalic, DIN-BoldItalic;
                }
              }
            }
          }

          .hot-topics-section {
            flex: 1;
            background: rgba(31, 198, 255, 0.16);
            padding: 12px 16px;

            .hot-topics-header {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 15px;

              .hot-icon {
                width: 20px;
                height: 20px;
              }

              .hot-title {
                font-size: 16px;
                color: #02FBFB;
                font-weight: 500;
                font-family: DIN-BoldItalic, DIN-BoldItalic;
              }
            }

            .topics-list {
              display: flex;
              flex-direction: column;
              gap: 12px;

              .topic-item {
                display: flex;
                align-items: flex-start;
                gap: 10px;

                .topic-dot {
                  width: 6px;
                  height: 6px;
                  border-radius: 50%;
                  background: rgba(217, 217, 217, 0.5);
                  margin-top: 8px;
                  flex-shrink: 0;
                }

                .topic-text {
                  font-size: 14px;
                  color: #FFFFFF;
                  line-height: 22px;
                  font-family: DIN-BoldItalic, DIN-BoldItalic;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
              }
            }
          }
        }
      }
    }

    .center-panel {
      flex: 1;
      gap: 20px;
      display: flex;
      flex-direction: column;

      .map_box {
        background: url('../../../assets/largeScreen/map_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        height: 650px;
        width: 100%;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .performance_statistics {
        background: url('../../../assets/largeScreen/performance_statistics_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 270px;
        width: 100%;

        .performance_statistics_content {
          height: 100%;
          margin-top: 65px;
          margin-left: 16px;
          margin-right: 16px;

          .table-container {
            height: calc(100% - 75px);
            display: flex;
            flex-direction: column;
            border: 1px solid rgba(0, 212, 255, 0.2);
            overflow: hidden;
            /* 使用CSS Grid确保列对齐 */
            --name-col-width: 120px;
            --scrollbar-width: 6px;

            .table-header {
              display: grid;
              grid-template-columns: var(--name-col-width) repeat(6, 1fr) var(--scrollbar-width);
              border-bottom: 1px solid rgba(0, 212, 255, 0.4);
              position: sticky;
              top: 0;
              z-index: 10;

              .header-cell {
                padding: 12px 8px;
                text-align: center;
                color: #E6F7FF;
                font-size: 15px;
                border-right: 1px solid rgba(0, 212, 255, 0.3);
                display: flex;
                align-items: center;
                justify-content: center;

                &:last-child {
                  border-right: none;
                  background: transparent;
                  border: none;
                }

                // &.name-col {
                //   background: rgba(0, 100, 180, 0.9);
                //   font-weight: 600;
                // }
              }
            }

            .table-body {
              flex: 1;
              overflow-y: auto;

              &::-webkit-scrollbar {
                width: 6px;
              }

              &::-webkit-scrollbar-track {
                background: rgba(0, 30, 60, 0.3);
                border-radius: 3px;
              }

              &::-webkit-scrollbar-thumb {
                background: rgba(0, 212, 255, 0.4);
                border-radius: 3px;

                &:hover {
                  background: rgba(0, 212, 255, 0.6);
                }
              }

              .table-row {
                display: grid;
                grid-template-columns: var(--name-col-width) repeat(6, 1fr);
                border-bottom: 1px solid rgba(0, 212, 255, 0.4);
                transition: all 0.3s ease;

                &:hover {
                  background: rgba(0, 212, 255, 0.1);
                }

                .table-cell {
                  padding: 12px 8px;
                  text-align: center;
                  color: #FFFFFF;
                  font-size: 14px;
                  border-right: 1px solid rgba(0, 212, 255, 0.4);
                  transition: all 0.3s ease;
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  &:last-child {
                    border-right: none;
                  }

                  &.name-col {
                    background: rgba(0, 60, 120, 0.4);
                    color: #FFF;
                    font-weight: 500;
                  }

                  &.meeting-col {
                    background: rgba(10, 63, 111, 0.4);
                    color: #59F7CA;
                    font-weight: 500;
                    font-size: 16px;
                  }

                  &.proposal-col {
                    background: rgba(10, 63, 111, 0.4);
                    font-weight: 500;
                    font-size: 16px;
                    color: #00FFF7;
                  }

                  &.opinion-col {
                    background: rgba(10, 63, 111, 0.4);
                    font-weight: 500;
                    font-size: 16px;
                    color: #FF386B;
                  }

                  &.suggestion-col {
                    background: rgba(10, 63, 111, 0.4);
                    font-weight: 500;
                    font-size: 16px;
                    color: #81C4E4;
                  }

                  &.reading-col {
                    background: rgba(10, 63, 111, 0.4);
                    font-weight: 500;
                    font-size: 16px;
                    color: #387BFD;
                  }

                  &.training-col {
                    background: rgba(10, 63, 111, 0.4);
                    font-weight: 500;
                    font-size: 16px;
                    color: #FF911F;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
