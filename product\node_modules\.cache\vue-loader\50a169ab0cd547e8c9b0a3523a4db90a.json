{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu\\zy-menu.vue?vue&type=style&index=0&id=1540d2eb&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu\\zy-menu.vue", "mtime": 1752541693540}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICcuL3p5LW1lbnUuc2Nzcyc7DQo="}, {"version": 3, "sources": ["zy-menu.vue"], "names": [], "mappings": ";AAiGA", "file": "zy-menu.vue", "sourceRoot": "src/components/zy-menu", "sourcesContent": ["<template>\r\n  <el-scrollbar class=\"zy-menu\">\r\n    <el-menu\r\n      :default-active=\"key\"\r\n      :background-color=\"backgroundColor\"\r\n      :text-color=\"textColor\"\r\n      :active-text-color=\"activeTextColor\"\r\n      @select=\"select\"\r\n    >\r\n      <zy-menu-children\r\n        :menu=\"menu\"\r\n        :value=\"key\"\r\n        :props=\"props\"\r\n      ></zy-menu-children>\r\n    </el-menu>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nimport zyMenuChildren from './zy-menu-children'\r\nexport default {\r\n  name: 'zyMenu',\r\n  data () {\r\n    return {\r\n      key: ''\r\n    }\r\n  },\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    menu: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    textColor: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    backgroundColor: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    activeTextColor: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label',\r\n          id: 'id',\r\n          to: 'to',\r\n          icon: 'iconUrl',\r\n          isShow: 'isShow',\r\n          showValue: true\r\n        }\r\n      }\r\n    }\r\n  },\r\n  emits: ['select'],\r\n  components: {\r\n    zyMenuChildren\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.key = val\r\n        this.selectData(this.menu, val)\r\n      }\r\n    }\r\n  },\r\n  mounted () {\r\n    this.key = this.value\r\n    this.selectData(this.menu, this.value)\r\n  },\r\n  methods: {\r\n    select (key) {\r\n      this.key = key\r\n      this.$emit('input', key)\r\n      this.selectData(this.menu, key)\r\n    },\r\n    selectData (data, id) {\r\n      data.forEach(item => {\r\n        if (item[this.props.children].length === 0) {\r\n          if (item[this.props.id] === id) {\r\n            this.$emit('select', item)\r\n          }\r\n        } else {\r\n          this.selectData(item[this.props.children], id)\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './zy-menu.scss';\r\n</style>\r\n"]}]}