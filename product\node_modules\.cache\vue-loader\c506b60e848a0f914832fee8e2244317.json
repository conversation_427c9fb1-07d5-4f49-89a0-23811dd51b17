{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-calendar\\zy-calendar.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-calendar\\zy-calendar.vue", "mtime": 1660102037658}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["zy-calendar.vue"], "names": [], "mappings": ";AA6CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-calendar.vue", "sourceRoot": "src/components/zy-calendar", "sourcesContent": ["<template>\r\n  <div class=\"zy-calendar\">\r\n    <div class=\"zy-calendar-selected\"\r\n         v-if=\"showHeader\">\r\n      <transition name=\"fadeY\">\r\n        <div class=\"years\"\r\n             v-if=\"yearsshow\">{{years}}</div>\r\n      </transition>\r\n      <transition name=\"fadeY\">\r\n        <div class=\"time\"\r\n             v-if=\"flag\">{{month+'&nbsp;&nbsp;'+Whatday}}</div>\r\n      </transition>\r\n    </div>\r\n    <section class=\"zy_container\">\r\n      <div class=\"zy_content_all\">\r\n        <div class=\"zy_top_changge\">\r\n          <li @click=\"PreMonth(myDate,false)\">\r\n            <div class=\"zy_jiantou1\"></div>\r\n          </li>\r\n          <li class=\"zy_content_li\">{{dateTop}}</li>\r\n          <li @click=\"NextMonth(myDate,false)\">\r\n            <div class=\"zy_jiantou2\"></div>\r\n          </li>\r\n        </div>\r\n        <div class=\"zy_content\">\r\n          <div class=\"zy_content_item\"\r\n               v-for=\"(tag,index) in textTop\"\r\n               :key=\"index\">\r\n            <div class=\"zy_top_tag\">{{tag}}</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"zy_content\">\r\n          <div class=\"zy_content_item\"\r\n               v-for=\"(item,index) in list\"\r\n               :key=\"index\"\r\n               @click=\"clickDay(item,index)\">\r\n            <div class=\"zy_item_date\"\r\n                 :class=\"[{ zy_isMark: item.isMark},{zy_other_dayhide:item.otherMonth!=='nowMonth'},{zy_want_dayhide:item.dayHide},{zy_isToday:item.isToday},{zy_chose_day:item.chooseDay},setClass(item)]\">{{item.id}}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n<script>\r\nimport timeUtil from './timeUtil'\r\nexport default {\r\n  name: 'zyCalendar',\r\n  data () {\r\n    return {\r\n      years: '',\r\n      month: '',\r\n      Whatday: '',\r\n      yearsshow: true,\r\n      flag: true,\r\n      myDate: [],\r\n      list: [],\r\n      historyChose: [],\r\n      dateTop: ''\r\n    }\r\n  },\r\n  props: {\r\n    markDate: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    markDateMore: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    textTop: {\r\n      type: Array,\r\n      default: () => ['一', '二', '三', '四', '五', '六', '日']\r\n    },\r\n    sundayStart: {\r\n      type: Boolean,\r\n      default: () => false\r\n    },\r\n    agoDayHide: {\r\n      type: String,\r\n      default: '0'\r\n    },\r\n    futureDayHide: {\r\n      type: String,\r\n      default: '2554387200'\r\n    },\r\n    showHeader: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  created () {\r\n    this.intStart()\r\n    this.myDate = new Date()\r\n    this.selected(this.getNowFormatDate(this.myDate))\r\n  },\r\n  methods: {\r\n    getWeekDate (data) {\r\n      var now = new Date(data)\r\n      var day = now.getDay()\r\n      var weeks = new Array('星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六')// eslint-disable-line\r\n      var week = weeks[day]\r\n      return week\r\n    },\r\n    getNowFormatDate (data) {\r\n      var date = data\r\n      var seperator1 = '-'\r\n      var year = date.getFullYear()\r\n      var month = date.getMonth() + 1\r\n      var strDate = date.getDate()\r\n      if (month >= 1 && month <= 9) {\r\n        month = '0' + month\r\n      }\r\n      if (strDate >= 0 && strDate <= 9) {\r\n        strDate = '0' + strDate\r\n      }\r\n      var currentdate = year + seperator1 + month + seperator1 + strDate\r\n      return currentdate\r\n    },\r\n    selected (data) {\r\n      this.Whatday = this.getWeekDate(data)\r\n      if (this.years !== data.slice(0, 4)) {\r\n        this.years = data.slice(0, 4)\r\n        this.yearsshow = false\r\n        setTimeout(() => {\r\n          this.yearsshow = true\r\n        }, 200)\r\n      }\r\n      if (this.month !== data.slice(5).replace('/', '-')) {\r\n        this.month = data.slice(5).replace('/', '-')\r\n        this.flag = false\r\n        setTimeout(() => {\r\n          this.flag = true\r\n        }, 200)\r\n      }\r\n    },\r\n    intStart () {\r\n      timeUtil.sundayStart = this.sundayStart\r\n    },\r\n    setClass (data) {\r\n      const obj = {}\r\n      obj[data.markClassName] = data.markClassName\r\n      return obj\r\n    },\r\n    clickDay: function (item, index) {\r\n      if (item.otherMonth === 'nowMonth' && !item.dayHide) {\r\n        this.getList(this.myDate, item.date)\r\n      }\r\n      if (item.otherMonth !== 'nowMonth') {\r\n        item.otherMonth === 'preMonth'\r\n          ? this.PreMonth(item.date)\r\n          : this.NextMonth(item.date)\r\n      }\r\n    },\r\n    ChoseMonth: function (date, isChosedDay = true) {\r\n      date = timeUtil.dateFormat(date)\r\n      this.myDate = new Date(date)\r\n      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate))\r\n      if (isChosedDay) {\r\n        this.getList(this.myDate, date, isChosedDay)\r\n      } else {\r\n        this.getList(this.myDate)\r\n      }\r\n    },\r\n    PreMonth: function (date, isChosedDay = true) {\r\n      date = timeUtil.dateFormat(date)\r\n      this.myDate = timeUtil.getOtherMonth(this.myDate, 'preMonth')\r\n      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate))\r\n      if (isChosedDay) {\r\n        this.getList(this.myDate, date, isChosedDay)\r\n      } else {\r\n        this.getList(this.myDate)\r\n      }\r\n    },\r\n    NextMonth: function (date, isChosedDay = true) {\r\n      date = timeUtil.dateFormat(date)\r\n      this.myDate = timeUtil.getOtherMonth(this.myDate, 'nextMonth')\r\n      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate))\r\n      if (isChosedDay) {\r\n        this.getList(this.myDate, date, isChosedDay)\r\n      } else {\r\n        this.getList(this.myDate)\r\n      }\r\n    },\r\n    forMatArgs: function () {\r\n      let markDate = this.markDate\r\n      let markDateMore = this.markDateMore\r\n      markDate = markDate.map(k => {\r\n        return timeUtil.dateFormat(k)\r\n      })\r\n      markDateMore = markDateMore.map(k => {\r\n        k.date = timeUtil.dateFormat(k.date)\r\n        return k\r\n      })\r\n      return [markDate, markDateMore]\r\n    },\r\n    getList: function (date, chooseDay, isChosedDay = true) {\r\n      const [markDate, markDateMore] = this.forMatArgs()\r\n      this.dateTop = `${date.getFullYear()}年${date.getMonth() + 1}月`\r\n      const arr = timeUtil.getMonthList(this.myDate)\r\n      for (let i = 0; i < arr.length; i++) {\r\n        let markClassName = ''\r\n        const k = arr[i]\r\n        k.chooseDay = false\r\n        const nowTime = k.date\r\n        const t = new Date(nowTime).getTime() / 1000\r\n        for (const c of markDateMore) {\r\n          if (c.date === nowTime) {\r\n            markClassName = c.className || ''\r\n          }\r\n        }\r\n        k.markClassName = markClassName\r\n        k.isMark = markDate.indexOf(nowTime) > -1\r\n        k.dayHide = t < this.agoDayHide || t > this.futureDayHide\r\n        if (k.isToday) {\r\n          this.$emit('isToday', nowTime)\r\n        }\r\n        const flag = !k.dayHide && k.otherMonth === 'nowMonth'\r\n        if (chooseDay && chooseDay === nowTime && flag) {\r\n          this.$emit('choseDay', nowTime)\r\n          this.selected(nowTime)\r\n          this.historyChose.push(nowTime)\r\n          k.chooseDay = true\r\n        } else if (\r\n          this.historyChose[this.historyChose.length - 1] === nowTime &&\r\n          !chooseDay &&\r\n          flag\r\n        ) {\r\n          k.chooseDay = true\r\n        }\r\n      }\r\n      this.list = arr\r\n    }\r\n  },\r\n  mounted () {\r\n    this.getList(this.myDate)\r\n  },\r\n  watch: {\r\n    markDate: {\r\n      handler (val, oldVal) {\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    },\r\n    markDateMore: {\r\n      handler (val, oldVal) {\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    },\r\n    agoDayHide: {\r\n      handler (val, oldVal) {\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    },\r\n    futureDayHide: {\r\n      handler (val, oldVal) {\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    },\r\n    sundayStart: {\r\n      handler (val, oldVal) {\r\n        this.intStart()\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-calendar.scss\";\r\n</style>\r\n"]}]}