{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\SinceSituation\\SinceDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\SinceSituation\\SinceDetail.vue", "mtime": 1752541697669}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHNpbmNlRGF0YSBmcm9tICcuL3NpbmNlRGF0YVQnOwpleHBvcnQgZGVmYXVsdCB7CiAgbWl4aW5zOiBbc2luY2VEYXRhXSwKICBuYW1lOiAnU2luY2VEZXRhaWwnLAoKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaGVhZGVybGlzdDogW10sCiAgICAgIGRlc3RhaWxzOiB7fSwKICAgICAgY3VyclllYXI6ICcnCiAgICB9OwogIH0sCgogIHByb3BzOiB7CiAgICByb3dJZDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAogICAgeWVhcjogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9CiAgfSwKICB3YXRjaDogewogICAgY3VyclllYXIoKSB7CiAgICAgIHRoaXMuZ2V0RHV0eURldGFpbCgpOwogICAgfQoKICB9LAoKICBjcmVhdGVkKCkgewogICAgdGhpcy5jdXJyWWVhciA9IHRoaXMueWVhciB8fCB0aGlzLiRmb3JtYXQoJycsICdZWVlZJyk7CiAgICB0aGlzLmdldER1dHlEZXRhaWwoKTsKICB9LAoKICBjb21wdXRlZDoge30sCiAgbWV0aG9kczogewogICAgYXN5bmMgZ2V0RHV0eURldGFpbCgpIHsKICAgICAgY29uc3QgcmVzID0gYXdhaXQgdGhpcy4kYXBpLnNpbmNlTWFuYWdlbWVudC5nZW5lcmF0ZUR1dHlEZXRhaWwoewogICAgICAgIHVzZXJJZDogdGhpcy5yb3dJZCwKICAgICAgICB5ZWFyOiB0aGlzLmN1cnJZZWFyIHx8IHRoaXMuJGZvcm1hdCgnJywgJ1lZWVknKQogICAgICB9KTsKICAgICAgdmFyIHsKICAgICAgICBkYXRhCiAgICAgIH0gPSByZXM7CiAgICAgIHRoaXMuZGVzdGFpbHMgPSBkYXRhOwogICAgfQoKICB9Cn07"}, {"version": 3, "mappings": "AA6EA;AACA;EACAA,mBADA;EAEAC,mBAFA;;EAGAC;IACA;MACAC,cADA;MAEAC,YAFA;MAGAC;IAHA;EAKA,CATA;;EAUAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,YADA;MAEAC;IAFA;EALA,CAVA;EAoBAE;IACAN;MACA;IACA;;EAHA,CApBA;;EAyBAO;IACA;IACA;EACA,CA5BA;;EA6BAC,YA7BA;EA+BAC;IACA;MACA;QAAAC;QAAAL;MAAA;MACA;QAAAR;MAAA;MACA;IACA;;EALA;AA/BA", "names": ["mixins", "name", "data", "headerlist", "destails", "currYear", "props", "rowId", "type", "default", "year", "watch", "created", "computed", "methods", "userId"], "sourceRoot": "src/views/wisdomWarehouse/SinceSituation", "sources": ["SinceDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"sincedetail\">\r\n    <div class=\"sincedetail_head\"> 青岛政协委员履职表 </div>\r\n\r\n    <div class=\"sinceTable\">\r\n      <div class=\"userName\">\r\n        <div class=\"userName_item\">\r\n          <div class=\"userName_label\"> 用户名 </div>\r\n          <div class=\"userName_value\">{{destails.userName }}</div>\r\n        </div>\r\n        <div class=\"userName_item\">\r\n          <div class=\"userName_label\">年份</div>\r\n          <div class=\"userName_value\">\r\n            <el-date-picker\r\n              v-model=\"currYear\"\r\n              type=\"year\"\r\n              value-format=\"yyyy\"\r\n              placeholder=\"选择年份\"\r\n            >\r\n            </el-date-picker>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"sinceClounm \">\r\n        <div class=\"sinceItem\"> 履职项 </div>\r\n        <div class=\"sinceNum\"> 次数 </div>\r\n        <div class=\"sinceList\"> 详情 </div>\r\n      </div>\r\n\r\n      <div\r\n        class=\"sinTable\"\r\n        v-for=\"(item , index) in ListHead\"\r\n        :key=\"index\"\r\n        v-show=\"item.fieldName != 'userName'\"\r\n      >\r\n        <div\r\n          class=\"TableItem\"\r\n          v-if=\"item.children.length \"\r\n        >\r\n          <div class=\"itemLabelHas \"> {{item.label}} </div>\r\n          <div class=\"childItemBox\">\r\n            <div\r\n              class=\"childItem\"\r\n              v-for=\"(child , ind) in item.children\"\r\n              :key=\"ind\"\r\n            >\r\n              <div class=\"childItemLable\"> {{child.label}} </div>\r\n              <div class=\"childItemNum\">{{( destails[child.detailListName] || []).length }} </div>\r\n              <div class=\"childItemList\">\r\n                <div\r\n                  v-for=\" (news , i) in destails[child.detailListName] \"\r\n                  :key=\"i\"\r\n                > {{i +1 }} . {{news.name}} </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div\r\n          v-else\r\n          class=\"TableItem\"\r\n        >\r\n          <div class=\"itemLabel\"> {{item.label}} </div>\r\n          <div class=\"itemNum\"> {{ (destails[item.detailListName]|| []).length  }}</div>\r\n          <div class=\"itemList\">\r\n            <div\r\n              v-for=\" (news , i) in destails[item.detailListName] \"\r\n              :key=\"i\"\r\n            > {{i +1 }} . {{news.name}} </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport sinceData from './sinceDataT'\r\nexport default {\r\n  mixins: [sinceData],\r\n  name: 'SinceDetail',\r\n  data () {\r\n    return {\r\n      headerlist: [],\r\n      destails: {},\r\n      currYear: ''\r\n    }\r\n  },\r\n  props: {\r\n    rowId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    year: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  watch: {\r\n    currYear () {\r\n      this.getDutyDetail()\r\n    }\r\n  },\r\n  created () {\r\n    this.currYear = this.year || this.$format('', 'YYYY')\r\n    this.getDutyDetail()\r\n  },\r\n  computed: {\r\n  },\r\n  methods: {\r\n    async getDutyDetail () {\r\n      const res = await this.$api.sinceManagement.generateDutyDetail({ userId: this.rowId, year: this.currYear || this.$format('', 'YYYY') })\r\n      var { data } = res\r\n      this.destails = data\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" >\r\n.sincedetail {\r\n    width: 1000px;\r\n    height: 100%;\r\n    padding: 24px;\r\n    .sincedetail_head {\r\n        text-align: center;\r\n        font-weight: 700;\r\n        font-size: 24px;\r\n        margin-bottom: 50px;\r\n    }\r\n    .sinceTable {\r\n        border: 1px solid #e6e6e6;\r\n    }\r\n    .userName {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        line-height: 45px;\r\n        .userName_item {\r\n            display: flex;\r\n            flex: 1;\r\n            .userName_label {\r\n                text-align: center;\r\n                width: 120px;\r\n                background: #f8faff;\r\n                border-right: 1px solid #e6e6e6;\r\n            }\r\n            .userName_value {\r\n                flex: 1;\r\n                padding-left: 20px;\r\n                input {\r\n                    border: none;\r\n                }\r\n            }\r\n        }\r\n        .userName_item + .userName_item {\r\n            border-left: 1px solid #e6e6e6;\r\n        }\r\n    }\r\n    .sinceClounm {\r\n        display: flex;\r\n        min-height: 45px;\r\n        line-height: 40px;\r\n        text-align: center;\r\n        border-top: 1px solid #e6e6e6;\r\n        background: #f8faff;\r\n\r\n        .sinceItem {\r\n            width: 340px;\r\n        }\r\n        .sinceNum {\r\n            width: 100px;\r\n            border-left: 1px solid #e6e6e6;\r\n            border-right: 1px solid #e6e6e6;\r\n            box-sizing: border-box;\r\n        }\r\n        .sinceList {\r\n            flex: 1;\r\n        }\r\n    }\r\n\r\n    .sinTable {\r\n        background: #f8faff;\r\n        .TableItem {\r\n            display: flex;\r\n            line-height: 45px;\r\n            min-height: 45px;\r\n            text-align: center;\r\n            align-items: center;\r\n            border-top: 1px solid #e6e6e6;\r\n            width: 100%;\r\n            .itemLabel {\r\n                width: 340px;\r\n            }\r\n            .itemNum {\r\n                width: 100px;\r\n                border-left: 1px solid #e6e6e6;\r\n                border-right: 1px solid #e6e6e6;\r\n                box-sizing: border-box;\r\n                background: #fff;\r\n            }\r\n            .itemList {\r\n                flex: 1;\r\n                text-align: left;\r\n                padding-left: 20px;\r\n                background: #fff;\r\n                min-height: 45px;\r\n            }\r\n            .itemLabelHas {\r\n                width: 170px;\r\n            }\r\n            .childItemBox {\r\n                border-left: 1px solid #e6e6e6;\r\n                flex: 1;\r\n\r\n                .childItem {\r\n                    display: flex;\r\n\r\n                    .childItemLable {\r\n                        width: 170px;\r\n                        display: flex;\r\n                        align-items: center;\r\n                        justify-content: center;\r\n                        padding: 0 10px;\r\n                        box-sizing: border-box;\r\n                    }\r\n                    .childItemNum {\r\n                        width: 100px;\r\n                        border-left: 1px solid #e6e6e6;\r\n                        border-right: 1px solid #e6e6e6;\r\n                        box-sizing: border-box;\r\n                        display: flex;\r\n                        align-items: center;\r\n                        justify-content: center;\r\n                        background: #fff;\r\n                    }\r\n                    .childItemList {\r\n                        flex: 1;\r\n                        text-align: left;\r\n                        padding-left: 20px;\r\n                        background: #fff;\r\n                    }\r\n                }\r\n                .childItem + .childItem {\r\n                    border-top: 1px solid #e6e6e6;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}