{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\InnovationNew.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\InnovationNew.vue", "mtime": 1752541693828}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["InnovationNew.vue"], "names": [], "mappings": ";AAyFA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "InnovationNew.vue", "sourceRoot": "src/views/AssessmentOrgan/InnovationExcellence", "sourcesContent": ["<template>\r\n  <div class=\"InnovationNew\">\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"demo-form\">\r\n      <el-form-item label=\"标题\"\r\n                    prop=\"title\">\r\n        <el-input v-model=\"form.title\"\r\n                  clearable>\r\n\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"发布时间\"\r\n                    prop=\"publishTime\">\r\n        <el-date-picker type=\"date\"\r\n                        placeholder=\"选择日期\"\r\n                        value-format=\"timestamp\"\r\n                        v-model=\"form.publishTime\"\r\n                        style=\"width: 100%;\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"部门\"\r\n                    prop=\"officeId\">\r\n        <zy-select v-model=\"form.officeId\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\"\r\n                   @select=\"select\"\r\n                   placeholder=\"请选择部门\">\r\n        </zy-select>\r\n        <!-- <button @click=\"demo\">11</button> -->\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"时效选择\"\r\n                    prop=\"endTime\">\r\n        <el-form-item prop=\"endTime\">\r\n          <el-date-picker type=\"date\"\r\n                          placeholder=\"选择日期\"\r\n                          value-format=\"timestamp\"\r\n                          v-model=\"form.endTime\"\r\n                          style=\"width: 100%;\"></el-date-picker>\r\n        </el-form-item>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"分值\"\r\n                    class=\"form-title\">\r\n        <el-input-number v-model=\"form.score\"\r\n                         controls-position=\"right\"\r\n                         @change=\"handleChange\"\r\n                         :min=\"0\"\r\n                         :max=\"100\"\r\n                         class=\"form-content\">\r\n        </el-input-number>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"是否重点工作\"\r\n                    prop=\"isMainwork\">\r\n        <el-radio-group v-model=\"form.isMainwork\">\r\n          <el-radio label=\"1\">是</el-radio>\r\n          <el-radio label=\"0\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"类型\"\r\n                    prop=\"classify\">\r\n        <el-select v-model=\"form.classify\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择类型\">\r\n          <el-option v-for=\"item in classifyData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'InnovationNew',\r\n  data () {\r\n    return {\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())), // 获取当前登录人信息\r\n\r\n      officeData: [],\r\n      classifyData: [],\r\n      form: {\r\n        id: '',\r\n        title: '',\r\n        officeId: '',\r\n        officeName: '',\r\n        endTime: '',\r\n        score: '',\r\n        classify: '',\r\n        isMainwork: '',\r\n        publishTime: '',\r\n        auditStatus: ''\r\n\r\n      },\r\n      rules: {\r\n\r\n        title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n        officeId: [\r\n          { required: true, message: '请选择部门', trigger: 'blur' }\r\n        ],\r\n        publishTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ],\r\n        endTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ],\r\n        isMainwork: [\r\n          { required: true, message: '请选择', trigger: 'blur' }\r\n        ]\r\n      },\r\n      circlesStatus: []\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  mounted () {\r\n    this.treeList()\r\n    this.form.publishTime = new Date().getTime() // 获取当前时间 需要默认登陆时显示当前时间时 解开\r\n    // this.form.endTime = this.$utils.tmp(false) //获取当前时间 需要默认登陆时显示当前时间时 解开\r\n    this.form.officeName = this.user.officeName\r\n    this.form.officeId = this.user.officeId\r\n\r\n    this.dictionaryPubkvs()\r\n    if (this.id) {\r\n      this.getInnovationDetails()\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n    *机构树\r\n   */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    select (row) {\r\n      this.form.officeName = row ? row.label : ''\r\n    },\r\n    /**\r\n *字典\r\n*/\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_functional_innovate'\r\n      })\r\n      var { data } = res\r\n      this.classifyData = data.evaluation_functional_innovate\r\n    },\r\n    // 获取目标详情 (作用:编辑界面内容填充)\r\n    async getInnovationDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqInnovationDetails(this.id)\r\n      var { data } = res\r\n      this.form.id = data.id\r\n      this.form.title = data.title\r\n      this.form.officeName = data.officeName\r\n      this.form.officeId = data.officeId\r\n      this.form.publishTime = data.publishTime\r\n      this.form.endTime = data.endTime\r\n      this.form.score = data.score\r\n      this.form.auditStatus = data.auditStatus\r\n      this.form.isMainwork = data.isMainwork\r\n      this.form.classify = data.classify\r\n    },\r\n    async historycirclesInfo () {\r\n      const res = await this.$api.memberInformation.historycirclesInfo(this.id)\r\n      var { data } = res\r\n      this.form.title = data.title\r\n      this.form.boutYear = data.boutYear\r\n      this.form.circlesStatus = data.circlesStatus\r\n    },\r\n    handleChange (value) {\r\n      console.log(value)\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        // this.form.publishTime = this.$format()\r\n        // console.log(this.form.publishTime)\r\n        if (valid) {\r\n          var url = '/functional/innovate/add?'\r\n          if (this.id) {\r\n            url = '/functional/innovate/edit?'\r\n          }\r\n          this.$api.AssessmentOrgan.reqAddInnovationExcellence(url, {\r\n            id: this.id,\r\n            title: this.form.title,\r\n            officeId: this.form.officeId,\r\n            officeName: this.form.officeName,\r\n            endTime: this.form.endTime,\r\n            score: this.form.score,\r\n            classify: this.form.classify,\r\n            isMainwork: this.form.isMainwork,\r\n            publishTime: this.form.publishTime,\r\n            auditStatus: this.form.auditStatus\r\n\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('newCallback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('newCallback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.InnovationNew {\r\n  width: 692px;\r\n  height: 100%;\r\n  padding: 24px 40px;\r\n  // overflow-y: scroll;\r\n  overflow-x: hidden;\r\n  overflow-y: auto;\r\n\r\n  .el-form-item__label {\r\n    text-align: right;\r\n    vertical-align: middle;\r\n    float: left;\r\n    font-size: 13px;\r\n    color: #606266;\r\n    line-height: 40px;\r\n    padding: 0 12px 0 0;\r\n    box-sizing: border-box;\r\n  }\r\n}\r\n</style>\r\n"]}]}