{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-export\\zy-export.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-export\\zy-export.vue", "mtime": 1752541693526}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAmCA;AACA;EACAA,gBADA;;EAEAC;IACA;MACAD,QADA;MAEAE,aAFA;MAGAC,sBAHA;MAIAC;QACAC;MADA,CAJA;MAOAC;QACAD,QACA;UAAAE;UAAAC;UAAAC;QAAA,CADA;MADA,CAPA;MAYAJ,SAZA;MAaAJ,QAbA;MAcAS,WAdA;MAeAC,eAfA;MAgBAC;IAhBA;EAkBA,CArBA;;EAsBAC;IACAC,qCADA;IAEAC;MACAD,YADA;MAEAE;QACA;MACA;IAJA,CAFA;IAQAC;MACAH,YADA;MAEAE;IAFA;EARA,CAtBA;EAmCAE,qBAnCA;;EAoCAC;IACA;IACA;EACA,CAvCA;;EAwCAC;IACAnB;MACA;QACA;MACA;IACA;;EALA,CAxCA;EA+CAoB;IACA;MACA;QAAAC;MAAA;MACAC;QACAC;;QACA;UACA;QACA;MACA,CALA;MAMA;MACA;IACA,CAXA;;IAYA;MACA;;MACA;QACAT;MACA,CAFA,MAEA;QACAA;MACA;;MACAA;MACA;MACA;MACA;IACA,CAvBA;;IAwBAU;MACA;QACA;UACA;UACA;YACA;cACAC;YACA,CAFA,MAEA;cACA;YACA;UACA,CANA,EAMA,GANA;;UAOA;YACAC;cACA;YACA,CAFA,EAEA,GAFA;UAGA,CAJA,MAIA;YACA;UACA;QACA,CAhBA,MAgBA;UACA;YACAnB,oBADA;YAEAM;UAFA;UAIA;QACA;MACA,CAxBA;IAyBA,CAlDA;;IAmDAc;MACA;MACA;MACA;QACAvB;MACA,CAFA;MAGA;MACAqB;MACAC;QACA;QACA;QACA;MACA,CAJA,EAIA,IAJA;IAKA,CAhEA;;IAiEAE;MACA;MACA;QACA;UACAC;QACA;MACA,CAJA;MAKA;IACA,CAzEA;;IA0EAC;MACA;IACA;;EA5EA;AA/CA", "names": ["name", "data", "percentage", "customColor", "form", "field", "rules", "required", "message", "trigger", "show", "dataShow", "exporttypes", "props", "type", "params", "default", "excelId", "mixins", "created", "watch", "methods", "exportType", "res", "item", "submitForm", "clearInterval", "setTimeout", "exportExcelMethods", "fieldMethods", "arr", "resetForm"], "sourceRoot": "src/components/zy-export", "sources": ["zy-export.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-export scrollBar\">\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-position=\"top\"\r\n             class=\"newForm\">\r\n      <el-form-item label=\"导出字段：\"\r\n                    prop=\"field\"\r\n                    class=\"form-title\">\r\n        <el-checkbox-group v-model=\"form.field\">\r\n          <el-checkbox v-for=\"item in field\"\r\n                       :label=\"item.id\"\r\n                       :key=\"item.id\">{{item.key}}</el-checkbox>\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <div class=\"form-button\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n\r\n    <div class=\"progress-box\"\r\n         v-if=\"show\"\r\n         @click.stop>\r\n      <div class=\"progress\">\r\n        <el-progress :percentage=\"percentage\"\r\n                     :color=\"customColor\"></el-progress>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport exportExcel from '@mixins/exportExcel'\r\nexport default {\r\n  name: 'zyExport',\r\n  data () {\r\n    return {\r\n      name: '',\r\n      percentage: 0,\r\n      customColor: '#94070A',\r\n      form: {\r\n        field: []\r\n      },\r\n      rules: {\r\n        field: [\r\n          { required: true, message: '请选择导出Excel的字段', trigger: 'blur' }\r\n        ]\r\n      },\r\n      field: [],\r\n      data: [],\r\n      show: false,\r\n      dataShow: false,\r\n      exporttypes: false\r\n    }\r\n  },\r\n  props: {\r\n    type: [String, Number, Array, Object],\r\n    params: {\r\n      type: Object,\r\n      default: () => {\r\n        return {}\r\n      }\r\n    },\r\n    excelId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  mixins: [exportExcel],\r\n  created () {\r\n    this.exportFields()\r\n    this.exportDatas()\r\n  },\r\n  watch: {\r\n    data (val) {\r\n      if (val.length && this.dataShow) {\r\n        this.exportExcelMethods()\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    async exportFields () {\r\n      const res = await this.$api.general.exportFields({ exportType: this.type })\r\n      res.data.fields.forEach((item, index) => {\r\n        item.id = index\r\n        if (item.checked) {\r\n          this.form.field.push(index)\r\n        }\r\n      })\r\n      this.name = res.data.name\r\n      this.field = res.data.fields\r\n    },\r\n    async exportDatas () {\r\n      var params = {}\r\n      if (!this.excelId) {\r\n        params = this.params\r\n      } else {\r\n        params.ids = this.excelId\r\n      }\r\n      params.exportType = this.type\r\n      const res = await this.$api.general.exportDatas(params)\r\n      this.exporttypes = true\r\n      this.data = res.data\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          this.show = true\r\n          this.setInterval = setInterval(() => {\r\n            if (this.percentage >= 90) {\r\n              clearInterval(this.setInterval)\r\n            } else {\r\n              this.percentage = this.percentage + 9\r\n            }\r\n          }, 200)\r\n          if (this.data.length || this.exporttypes) {\r\n            setTimeout(() => {\r\n              this.exportExcelMethods()\r\n            }, 400)\r\n          } else {\r\n            this.dataShow = true\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: '导出字段不能为空！',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    exportExcelMethods () {\r\n      this.percentage = 100\r\n      var field = []\r\n      this.form.field.forEach(item => {\r\n        field.push(this.fieldMethods(item))\r\n      })\r\n      this.exportExcel(this.name, field, this.data)\r\n      clearInterval(this.setInterval)\r\n      setTimeout(() => {\r\n        this.show = false\r\n        this.exporttypes = false\r\n        this.percentage = 0\r\n      }, 1000)\r\n    },\r\n    fieldMethods (id) {\r\n      var arr = {}\r\n      this.field.forEach(item => {\r\n        if (item.id === id) {\r\n          arr = item\r\n        }\r\n      })\r\n      return arr\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('callback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-export.scss\";\r\n</style>\r\n"]}]}