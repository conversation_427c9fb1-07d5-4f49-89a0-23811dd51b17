{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\ProgressBar.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\ProgressBar.vue", "mtime": 1756287011198}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdQcm9ncmVzc0JhcicsCiAgcHJvcHM6IHsKICAgIHByb2dyZXNzRGF0YTogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRQcm9ncmVzc0dyYWRpZW50KGNvbG9yKSB7CiAgICAgIC8vIOagueaNruminOiJsueUn+aIkOWvueW6lOeahOa4kOWPmOiJsgogICAgICBpZiAoY29sb3IgPT09ICcjMDBkNGZmJykgewogICAgICAgIC8vIOiTneiJsua4kOWPmAogICAgICAgIHJldHVybiAnbGluZWFyLWdyYWRpZW50KDkwZGVnLCByZ2JhKDMxLCAxOTgsIDI1NSwgMCkgMCUsICMxRkM2RkYgMTAwJSknOwogICAgICB9IGVsc2UgaWYgKGNvbG9yID09PSAnI2ZmZDcwMCcpIHsKICAgICAgICAvLyDpu4ToibLmuJDlj5gKICAgICAgICByZXR1cm4gJ2xpbmVhci1ncmFkaWVudCg5MGRlZywgcmdiYSgyNTUsIDIxNSwgMCwgMCkgMCUsICNGRkQ3MDAgMTAwJSknOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOm7mOiupOa4kOWPmO+8jOS9v+eUqOS8oOWFpeeahOminOiJsgogICAgICAgIHJldHVybiBgbGluZWFyLWdyYWRpZW50KDkwZGVnLCAke2NvbG9yfTAwIDAlLCAke2NvbG9yfSAxMDAlKWA7CiAgICAgIH0KICAgIH0KCiAgfQp9Ow=="}, {"version": 3, "mappings": "AAmBA;EACAA,mBADA;EAEAC;IACAC;MACAC,WADA;MAEAC,cAFA;MAGAC;IAHA;EADA,CAFA;EASAC;IACAC;MACA;MACA;QACA;QACA;MACA,CAHA,MAGA;QACA;QACA;MACA,CAHA,MAGA;QACA;QACA;MACA;IACA;;EAbA;AATA", "names": ["name", "props", "progressData", "type", "required", "default", "methods", "getProgressGradient"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["ProgressBar.vue"], "sourcesContent": ["<template>\n  <div class=\"progress-bar-container\">\n    <div class=\"progress-item\" v-for=\"(item, index) in progressData\" :key=\"index\">\n      <div class=\"progress-label\">\n        <!-- <span class=\"label-text\">{{ item.label }}</span> -->\n        <span class=\"label-value\">{{ item.value }}件</span>\n        <span class=\"label-percent\">占答复总件数{{ item.percent }}%</span>\n      </div>\n      <div class=\"progress-track\">\n        <div class=\"progress-fill\" :style=\"{\n          width: item.percent + '%',\n          background: getProgressGradient(item.color)\n        }\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ProgressBar',\n  props: {\n    progressData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  methods: {\n    getProgressGradient (color) {\n      // 根据颜色生成对应的渐变色\n      if (color === '#00d4ff') {\n        // 蓝色渐变\n        return 'linear-gradient(90deg, rgba(31, 198, 255, 0) 0%, #1FC6FF 100%)'\n      } else if (color === '#ffd700') {\n        // 黄色渐变\n        return 'linear-gradient(90deg, rgba(255, 215, 0, 0) 0%, #FFD700 100%)'\n      } else {\n        // 默认渐变，使用传入的颜色\n        return `linear-gradient(90deg, ${color}00 0%, ${color} 100%)`\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.progress-bar-container {\n  width: 100%;\n  padding: 20px;\n\n  .progress-item {\n    margin-bottom: 25px;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .progress-label {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 8px;\n      font-size: 14px;\n      color: #fff;\n\n      .label-text {\n        font-weight: 500;\n      }\n\n      .label-value {\n        font-size: 12px;\n        color: #FFFFFF;\n      }\n\n      .label-percent {\n        font-size: 12px;\n        color: #FFFFFF;\n      }\n    }\n\n    .progress-track {\n      width: 100%;\n      height: 8px;\n      background: rgba(255, 255, 255, 0.1);\n      border-radius: 4px;\n      overflow: hidden;\n      position: relative;\n\n      .progress-fill {\n        height: 100%;\n        border-radius: 4px;\n        transition: width 0.8s ease-in-out;\n        position: relative;\n      }\n    }\n  }\n}\n</style>\n"]}]}