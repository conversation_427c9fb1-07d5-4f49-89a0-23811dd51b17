{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\ProgressBar.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\ProgressBar.vue", "mtime": 1756286988585}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdQcm9ncmVzc0JhcicsCiAgcHJvcHM6IHsKICAgIHByb2dyZXNzRGF0YTogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdCiAgICB9CiAgfQp9Ow=="}, {"version": 3, "mappings": "AAmBA;EACAA,mBADA;EAEAC;IACAC;MACAC,WADA;MAEAC,cAFA;MAGAC;IAHA;EADA;AAFA", "names": ["name", "props", "progressData", "type", "required", "default"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["ProgressBar.vue"], "sourcesContent": ["<template>\n  <div class=\"progress-bar-container\">\n    <div class=\"progress-item\" v-for=\"(item, index) in progressData\" :key=\"index\">\n      <div class=\"progress-label\">\n        <!-- <span class=\"label-text\">{{ item.label }}</span> -->\n        <span class=\"label-value\">{{ item.value }}件</span>\n        <span class=\"label-percent\">占答复总件数{{ item.percent }}%</span>\n      </div>\n      <div class=\"progress-track\">\n        <div class=\"progress-fill\" :style=\"{\n          width: item.percent + '%',\n          background: getProgressGradient(item.color)\n        }\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ProgressBar',\n  props: {\n    progressData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.progress-bar-container {\n  width: 100%;\n  padding: 20px;\n\n  .progress-item {\n    margin-bottom: 25px;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .progress-label {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 8px;\n      font-size: 14px;\n      color: #fff;\n\n      .label-text {\n        font-weight: 500;\n      }\n\n      .label-value {\n        font-size: 12px;\n        color: #FFFFFF;\n      }\n\n      .label-percent {\n        font-size: 12px;\n        color: #FFFFFF;\n      }\n    }\n\n    .progress-track {\n      width: 100%;\n      height: 8px;\n      background: rgba(255, 255, 255, 0.1);\n      border-radius: 4px;\n      overflow: hidden;\n      position: relative;\n\n      .progress-fill {\n        height: 100%;\n        border-radius: 4px;\n        transition: width 0.8s ease-in-out;\n        position: relative;\n      }\n    }\n  }\n}\n</style>\n"]}]}