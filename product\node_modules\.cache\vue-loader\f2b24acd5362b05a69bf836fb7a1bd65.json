{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\user-management\\memberDetailsQd.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\user-management\\memberDetailsQd.vue", "mtime": 1752541697713}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdtZW1iZXJEZXRhaWxzUWQtd3cnLA0KICBkYXRhICgpIHsNCiAgICByZXR1cm4gew0KICAgICAgaWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkLA0KICAgICAgbmF0aW9uYWw6IFtdLA0KICAgICAgZGV0YWlsczoge30NCiAgICB9DQogIH0sDQogIG1vdW50ZWQgKCkgew0KICAgIHRoaXMubWVtYmVyRGV0YWlscygpDQogICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLmNvbnRyaWJ1dGVMYWJlbCcpLnN0eWxlLmhlaWdodCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJy5jb250cmlidXRlQ29udGVudCcpLm9mZnNldEhlaWdodCArICdweCcNCiAgICAgIC8vIGNvbnNvbGUubG9nKGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJy5jb250cmlidXRlQ29udGVudCcpLm9mZnNldEhlaWdodCkNCiAgICB9KQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgYXN5bmMgbWVtYmVyRGV0YWlscyAoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCB0aGlzLiRhcGkud2lzZG9tV2FyZWhvdXNlLmdldFVzZXJEZXRhaWwodGhpcy5pZCkNCiAgICAgIHZhciB7IGRhdGEgfSA9IHJlcw0KICAgICAgLy8gdmFyIHBvcyA9IGRhdGEuY2lyY2xlc0JvdXROYW1lLmluZGV4T2YoJ+WxiicpDQogICAgICAvLyBkYXRhLmNpcmNsZSA9IGRhdGEuY2lyY2xlc0JvdXROYW1lLnN1YnN0cigwLCBwb3MpDQogICAgICB0aGlzLmRldGFpbHMgPSBkYXRhDQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLmNvbnRyaWJ1dGVMYWJlbCcpLnN0eWxlLmhlaWdodCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJy5jb250cmlidXRlQ29udGVudCcpLm9mZnNldEhlaWdodCArICdweCcNCiAgICAgICAgLy8gY29uc29sZS5sb2coJ2gnLCBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcuY29udHJpYnV0ZUNvbnRlbnQnKS5vZmZzZXRIZWlnaHQpDQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["memberDetailsQd.vue"], "names": [], "mappings": ";AAkFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "memberDetailsQd.vue", "sourceRoot": "src/views/wisdomWarehouse/user-management", "sourcesContent": ["<template>\r\n  <div class=\"memberDetailsQd\">\r\n    <div class=\"memberDetailsQdTitle\">青岛市委员会智库专家信息</div>\r\n    <div class=\"memberDetailsQdForm\">\r\n      <div class=\"memberDetailsQdPortrait\">\r\n        <div class=\"memberUploader-img\"\r\n             v-if=\"details.headImg\"><img :src=\"details.headImg\"></div>\r\n        <div v-else\r\n             class=\"memberUploader-icon\">暂无</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem\">\r\n        <div class=\"memberDetailsQdLabel\">姓名</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.userName}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem\">\r\n        <div class=\"memberDetailsQdLabel\">性别</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.sex}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem\">\r\n        <div class=\"memberDetailsQdLabel\">账号</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.account}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem\">\r\n        <div class=\"memberDetailsQdLabel\">民族</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.nation}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem\">\r\n        <div class=\"memberDetailsQdLabel\">手机号</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.mobile}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem\">\r\n        <div class=\"memberDetailsQdLabel\">出生年月</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.birthday|datefmt('YYYY/MM/DD')}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem\">\r\n        <div class=\"memberDetailsQdLabel\">工作领域</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.workTerritory}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem points\">\r\n        <div class=\"memberDetailsQdLabel\">籍贯</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.nativePlace}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem\">\r\n        <div class=\"memberDetailsQdLabel\">单位职务</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.position}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem points\">\r\n        <div class=\"memberDetailsQdLabel\">毕业院校</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.graduate}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem\">\r\n        <div class=\"memberDetailsQdLabel\">邮政编码</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.postcode}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem points\">\r\n        <div class=\"memberDetailsQdLabel\">办公电话</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.officePhone}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem\">\r\n        <div class=\"memberDetailsQdLabel\">通讯地址</div>\r\n        <div class=\"memberDetailsQdcontent all\">{{details.callAddress}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem\">\r\n        <div class=\"contributeLabel\">主要介绍</div>\r\n        <div class=\"contributeContent all\">{{details.contribute}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem\">\r\n        <div class=\"memberDetailsQdLabel\">是否启用</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.isUsing}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem points\">\r\n        <div class=\"memberDetailsQdLabel\">APP安装情况</div>\r\n        <div class=\"memberDetailsQdcontent\">{{details.isJoinApp}}</div>\r\n      </div>\r\n      <div class=\"memberDetailsQdFormItem\">\r\n        <div class=\"memberDetailsQdLabel\">接受系统短信</div>\r\n        <div class=\"memberDetailsQdcontent all\">{{details.isReceiveMsg}}</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'memberDetailsQd-ww',\r\n  data () {\r\n    return {\r\n      id: this.$route.query.id,\r\n      national: [],\r\n      details: {}\r\n    }\r\n  },\r\n  mounted () {\r\n    this.memberDetails()\r\n    this.$nextTick(() => {\r\n      document.querySelector('.contributeLabel').style.height = document.querySelector('.contributeContent').offsetHeight + 'px'\r\n      // console.log(document.querySelector('.contributeContent').offsetHeight)\r\n    })\r\n  },\r\n  methods: {\r\n    async memberDetails () {\r\n      const res = await this.$api.wisdomWarehouse.getUserDetail(this.id)\r\n      var { data } = res\r\n      // var pos = data.circlesBoutName.indexOf('届')\r\n      // data.circle = data.circlesBoutName.substr(0, pos)\r\n      this.details = data\r\n      setTimeout(() => {\r\n        document.querySelector('.contributeLabel').style.height = document.querySelector('.contributeContent').offsetHeight + 'px'\r\n        // console.log('h', document.querySelector('.contributeContent').offsetHeight)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.memberDetailsQd {\r\n  width: 894px;\r\n  background: #ffffff;\r\n  border-radius: 10px;\r\n  margin: auto;\r\n  padding: 0 92px;\r\n  .memberDetailsQdTitle {\r\n    font-size: 36px;\r\n    font-family: SimSun;\r\n    font-weight: bold;\r\n    color: #ff0000;\r\n    line-height: 118px;\r\n    border-bottom: 4px solid #f00;\r\n    text-align: center;\r\n    margin-bottom: 58px;\r\n    box-sizing: border-box;\r\n    padding-top: 12px;\r\n  }\r\n  .memberDetailsQdForm {\r\n    box-shadow: 1px 0px 0px 0px #ebebeb, -1px 0px 0px 0px #ebebeb,\r\n      0px 0px 0px 0px #ebebeb, 0px -1px 0px 0px #ebebeb;\r\n    .memberDetailsQdPortrait {\r\n      width: 130px;\r\n      height: 160px;\r\n      background: #ffffff;\r\n      transform: translateY(0.1px);\r\n      float: right;\r\n      box-shadow: 0px 0px 0px 0px #ebebeb, -1px 0px 0px 0px #ebebeb,\r\n        0px 1px 0px 0px #ebebeb, 0px 0px 0px 0px #ebebeb;\r\n\r\n      .memberUploader-icon {\r\n        color: #8c939d;\r\n        width: 130px;\r\n        height: 160px;\r\n        background: url('../../../assets/img/userqd.png') no-repeat;\r\n        background-size: 84px 90px;\r\n        background-position: center;\r\n        font-size: $textSize16;\r\n        font-family: PingFang SC;\r\n        font-weight: 500;\r\n        color: #999999;\r\n        line-height: 160px;\r\n        text-align: center;\r\n      }\r\n\r\n      .memberUploader-img {\r\n        width: 130px;\r\n        height: 160px;\r\n        display: flex;\r\n        justify-content: center;\r\n        overflow: hidden;\r\n        img {\r\n          height: 100%;\r\n          display: inline-block;\r\n        }\r\n      }\r\n    }\r\n    .memberDetailsQdFormItem {\r\n      display: inline-block;\r\n      box-shadow: 0px 1px 0px 0px #ebebeb;\r\n      overflow: hidden;\r\n      .contributeLabel {\r\n        width: 120px;\r\n        height: 52px;\r\n        line-height: 52px;\r\n        text-align: center;\r\n        float: left;\r\n        background: #f5f7fb;\r\n        color: #333333;\r\n        font-family: PingFang SC;\r\n        font-weight: 500;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n      }\r\n      .contributeContent {\r\n        width: 170px;\r\n        height: auto;\r\n        margin-left: 120px;\r\n        background: #ffffff;\r\n        padding: 10px 12px;\r\n        line-height: 32px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n      .memberDetailsQdLabel {\r\n        width: 120px;\r\n        height: 52px;\r\n        line-height: 52px;\r\n        text-align: center;\r\n        float: left;\r\n        background: #f5f7fb;\r\n        color: #333333;\r\n        font-family: PingFang SC;\r\n        font-weight: 500;\r\n      }\r\n      .memberDetailsQdcontent {\r\n        width: 170px;\r\n        height: 52px;\r\n        margin-left: 120px;\r\n        background: #ffffff;\r\n        padding: 10px 12px;\r\n        line-height: 32px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n      .fill {\r\n        width: 300px;\r\n      }\r\n      .all {\r\n        width: 590px;\r\n      }\r\n    }\r\n    .points {\r\n      .memberDetailsQdLabel {\r\n        width: 160px;\r\n      }\r\n      .memberDetailsQdcontent {\r\n        width: 260px;\r\n        margin-left: 160px;\r\n      }\r\n    }\r\n    .special {\r\n      .memberDetailsQdLabel {\r\n        width: 280px;\r\n      }\r\n      .memberDetailsQdcontent {\r\n        width: 140px;\r\n        margin-left: 280px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}