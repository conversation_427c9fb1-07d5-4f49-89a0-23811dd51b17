{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\QuarterlyReview\\QuarterlyReview.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\QuarterlyReview\\QuarterlyReview.vue", "mtime": 1752541693862}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAuIA,0C,CAEA;;AACA;EACAA,uBADA;;EAEAC;IACA;MACAC,MADA;MAEAC,QAFA;MAGAC;QACAC,UADA;QAEAC;MAFA,GAGA;QACAD,UADA;QAEAC;MAFA,CAHA,EAMA;QACAD,UADA;QAEAC;MAFA,CANA,EASA;QACAD,UADA;QAEAC;MAFA,CATA,CAHA;MAgBAC,cAhBA;MAgBA;MACAC;QACAC,WADA;QAEAC,WAFA;QAGAC,YAHA;QAIAC;MAJA,CAjBA;MAwBAC,SAxBA;MAyBAC,YAzBA;MA0BAC,SA1BA;MA4BA;MACAC,aA7BA;MA8BAC,gBA9BA;MA+BAC,iBA/BA;MAgCAC,UAhCA,CAiCA;MACA;MACA;MACA;;IApCA;EAuCA,CA1CA;;EA2CAC;IACA;IACA;IACA,gBAHA,CAIA;EACA,CAhDA;;EAiDAC,sBAjDA;EAkDAC,mBAlDA;EAoDAC;IAEA;AACA;AACA;IACA;MACA;QACAC;MADA;MAGA;QAAAvB;MAAA;MACA;MACA;IACA,CAZA;;IAcA;AACA;AACA;IACA;MACA;MACA;QAAAA;MAAA;MACA;IACA,CArBA;;IAuBAwB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CA5CA;;IA6CA;IACA;MACA;MACA;QACAC;UAAAxB;UAAAgB;QAAA;MACA,CAFA;MAGA;QACAS,8BADA;QAEAjB;MAFA;MAIA;QAAAkB;QAAAC;MAAA;;MACA;QACA;QACA;UACAC,eADA;UAEAC;QAFA;MAIA;IACA,CA/DA;;IAiEA;IACA;MACA;QACAlB,mBADA;QAEAC,uBAFA;QAGAL,kCAHA;QAIAC,kCAJA;QAKA;QACA;QACAC,oCAPA;QAOA;QACAqB,gDARA,CAQA;;MARA;MAUA;QAAA/B;QAAAc;MAAA;MACA;MACA;IACA,CAhFA;;IAiFA;IACAkB;MACA;IACA,CApFA;;IAqFAC;MACA;IACA,CAvFA;;IAwFA;IACAC;MACA;MACA;IACA,CA5FA;;IA6FA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;;EApGA;AApDA", "names": ["name", "data", "id", "radio", "timeArr", "value", "label", "officeData", "searchParams", "keyword", "quarter", "officeId", "auditStatusParams", "pageNo", "pageSize", "total", "tableData", "currentRow", "evaluateLevel", "status", "mounted", "inject", "mixins", "methods", "types", "passClick", "arr", "auditJson", "<PERSON><PERSON><PERSON>", "errmsg", "message", "type", "auditStatus", "handleSizeChange", "handleCurrentChange", "search", "reset"], "sourceRoot": "src/views/AssessmentOrgan/QuarterlyReview", "sources": ["QuarterlyReview.vue"], "sourcesContent": ["<template>\r\n  <!--  季度评议  -->\r\n  <div class=\"QuarterlyReview\">\r\n    <!-- 搜索栏search-box -->\r\n    <search-box @search-click=\"search\"\r\n                @reset-click=\"reset\"\r\n                title=\"季度评议筛选\">\r\n      <!--关键字查找框  -->\r\n      <zy-widget label=\"关键字\">\r\n        <el-input placeholder=\"请输入关键词\"\r\n                  v-model=\"searchParams.keyword\"\r\n                  clearable\r\n                  @keyup.enter.native=\"search\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\"></div>\r\n        </el-input>\r\n      </zy-widget>\r\n      <!--季度查询  -->\r\n      <zy-widget label=\"季度查询\">\r\n        <el-select v-model=\"searchParams.quarter\"\r\n                   clearable\r\n                   @keyup.enter.native=\"search\"\r\n                   placeholder=\"请选择季度\">\r\n          <el-option v-for=\"item in timeArr\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.label\"\r\n                     :value=\"item.value\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n\r\n      <!-- 部门查询(用到机构树)  -->\r\n      <zy-widget label=\"部门查询\"\r\n                 v-permissions=\"'auth:QuarterlyReview:department'\">\r\n        <zy-select v-model=\"searchParams.officeId\"\r\n                   clearable\r\n                   @keyup.enter.native=\"search\"\r\n                   placeholder=\"请选择部门\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\">\r\n          </div>\r\n        </zy-select>\r\n      </zy-widget>\r\n\r\n      <!-- 审核状态查询 -->\r\n      <!-- <zy-widget label=\"审核状态查询\">\r\n        <el-select v-model=\"searchParams.auditStatusParams\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择审核状态\">\r\n          <el-option v-for=\"item in auditStatusData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget> -->\r\n\r\n    </search-box>\r\n\r\n    <div class=\"qd-list-wrap \">\r\n      <div class=\"qd-btn-box rightMove\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"passClick()\">保存</el-button>\r\n      </div>\r\n      <!-- *** -->\r\n      <div class=\"tableData\">\r\n        <zy-table>\r\n          <el-table ref=\"singleTable\"\r\n                    slot=\"zytable\"\r\n                    @select=\"selected\"\r\n                    @select-all=\"selectedAll\"\r\n                    row-key=\"id\"\r\n                    :data=\"tableData\"\r\n                    highlight-current-row\r\n                    style=\"width: 100%\">\r\n            <el-table-column label=\"序号\"\r\n                             type=\"index\"\r\n                             fixed=\"left\"\r\n                             width=\"120\">\r\n            </el-table-column>\r\n            <el-table-column property=\"userName\"\r\n                             label=\"考核人\"\r\n                             width=\"250\">\r\n            </el-table-column>\r\n            <el-table-column property=\"officeName\"\r\n                             label=\"所属部门\"\r\n                             width=\"250\">\r\n            </el-table-column>\r\n            <el-table-column property=\"quarter\"\r\n                             label=\"季度\"\r\n                             min-width=\"250\">\r\n            </el-table-column>\r\n\r\n            <!-- TODO:evaluateLevel 评级值，匹配字典evaluation_audit_status -->\r\n            <el-table-column label=\"岗位履职状况\"\r\n                             width=\"350\"\r\n                             fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-radio-group v-model=\"scope.row.evaluateLevel\"\r\n                                v-for=\"(item, index) in status\"\r\n                                :key=\"index\">\r\n                  <el-radio :label=\"item.id\"\r\n                            class=\"mright\">{{item.value}}\r\n                  </el-radio>\r\n                </el-radio-group>\r\n              </template>\r\n\r\n            </el-table-column>\r\n\r\n          </el-table>\r\n        </zy-table>\r\n      </div>\r\n      <!-- 底部页签功能 -->\r\n      <div class=\"paging_box\">\r\n        <el-pagination @size-change=\"handleSizeChange\"\r\n                       @current-change=\"handleCurrentChange\"\r\n                       :current-page.sync=\"pageNo\"\r\n                       :page-sizes=\"[10, 20, 30, 40]\"\r\n                       :page-size.sync=\"pageSize\"\r\n                       background\r\n                       layout=\"total, prev, pager, next, sizes, jumper\"\r\n                       :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport tableData from '@mixins/tableData'\r\n\r\n// import func from 'vue-editor-bridge'\r\nexport default {\r\n  name: 'QuarterlyReview',\r\n  data () {\r\n    return {\r\n      id: '',\r\n      radio: 1,\r\n      timeArr: [{\r\n        value: '1',\r\n        label: '第一季度'\r\n      }, {\r\n        value: '2',\r\n        label: '第二季度'\r\n      }, {\r\n        value: '3',\r\n        label: '第三季度'\r\n      }, {\r\n        value: '4',\r\n        label: '第四季度'\r\n      }],\r\n      officeData: [], // 机构树,\r\n      searchParams: {\r\n        keyword: '',\r\n        quarter: '',\r\n        officeId: '',\r\n        auditStatusParams: ''\r\n\r\n      },\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      total: 10,\r\n\r\n      // *********\r\n      tableData: [],\r\n      currentRow: null,\r\n      evaluateLevel: '',\r\n      status: []\r\n      // choose: [],\r\n      // selectObj: [],\r\n      // selectData: []\r\n      // auditJson: []\r\n\r\n    }\r\n  },\r\n  mounted () {\r\n    this.dictionaryPubkvs()\r\n    this.getQuarterList()\r\n    this.treeList()\r\n    // console.log(document.getElementsByName('performanceStatus'))\r\n  },\r\n  inject: ['tabDelJump'],\r\n  mixins: [tableData],\r\n\r\n  methods: {\r\n\r\n    /**\r\n     *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_audit_status,evaluate_audit_status'\r\n      })\r\n      var { data } = res\r\n      this.status = data.evaluation_audit_status\r\n      this.auditStatusData = data.evaluate_audit_status\r\n    },\r\n\r\n    /**\r\n      *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n\r\n    passClick () {\r\n      // if (this.choose.length) {\r\n      //   this.$confirm('此操作将选择的资讯的状态改为??, 是否继续?', '提示', {\r\n      //     confirmButtonText: '确定',\r\n      //     cancelButtonText: '取消',\r\n      //     type: 'warning'\r\n      //   }).then(() => {\r\n      //     // console.log(this.tableData)\r\n      //   }).catch(() => {\r\n      //     this.$message({\r\n      //       type: 'info',\r\n      //       message: '已取消操作'\r\n      //     })\r\n      //   })\r\n      // } else {\r\n      //   this.$message({\r\n      //     message: '请至少选择一条数据',\r\n      //     type: 'warning'\r\n      //   })\r\n      // }\r\n      this.saveInfo()\r\n    },\r\n    // 保存\r\n    async saveInfo () {\r\n      var arr = []\r\n      this.tableData.forEach(item => {\r\n        arr.push({ id: item.id, evaluateLevel: item.evaluateLevel })\r\n      })\r\n      const res = await this.$api.AssessmentOrgan.reqSaveQuarter({\r\n        auditJson: JSON.stringify(arr),\r\n        quarter: this.searchParams.quarter\r\n      })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getQuarterList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 请求后台数据 获取列表信息\r\n    async getQuarterList () {\r\n      const res = await this.$api.AssessmentOrgan.reqQuarterList({\r\n        pageNo: this.pageNo,\r\n        pageSize: this.pageSize,\r\n        keyword: this.searchParams.keyword,\r\n        quarter: this.searchParams.quarter,\r\n        // // sedateId: this.sedateId\r\n        // sedateId: this.selectedYear\r\n        officeId: this.searchParams.officeId, // 部门查询\r\n        auditStatus: this.searchParams.auditStatusParams // 审核状态\r\n      })\r\n      var { data, total } = res\r\n      this.tableData = data\r\n      this.total = total\r\n    },\r\n    // paging_box 里面用到的方法\r\n    handleSizeChange () {\r\n      this.getQuarterList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getQuarterList()\r\n    },\r\n    // 一整个搜索框内 查询按钮 的逻辑search\r\n    search () {\r\n      this.pageNo = 1\r\n      this.getQuarterList()\r\n    },\r\n    // 一整个搜索框内 重置按钮 逻辑\r\n    reset () {\r\n      this.searchParams.keyword = ''\r\n      this.searchParams.quarter = ''\r\n      this.searchParams.officeId = ''\r\n      this.searchParams.auditStatusParams = ''\r\n      this.getQuarterList()\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.QuarterlyReview {\r\n  height: 100%;\r\n  width: 100%;\r\n\r\n  .mright {\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .tableData {\r\n    .el-scrollbar__wrap .el-scrollbar__view th {\r\n      background: #f5f7fb;\r\n    }\r\n    height: calc(100% - 106px);\r\n  }\r\n  .button-box {\r\n    background: #fff;\r\n    margin-top: 20px;\r\n  }\r\n  .button-box .el-button {\r\n    background: #fff;\r\n  }\r\n  .el-button--danger.is-plain:hover,\r\n  .el-button--danger.is-plain:focus {\r\n    // color: #f56c6c;\r\n    background: #f56c6c;\r\n    color: #fff;\r\n  }\r\n  .el-button--success.is-plain:hover,\r\n  .el-button--success.is-plain:focus {\r\n    background: #67c23a;\r\n    color: #fff;\r\n  }\r\n  .delBtn {\r\n    color: #f56c6c;\r\n  }\r\n  .paging_box {\r\n    background: #fff;\r\n  }\r\n  .scoreEdit {\r\n    width: 700px;\r\n    height: 100%;\r\n    padding: 24px;\r\n    .form-button {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n  .search-box {\r\n    //title文字样式\r\n    .search-title {\r\n      width: 115px;\r\n      height: 16px;\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-left: 32px;\r\n    }\r\n  }\r\n  // 利用新特性弹性布局flex,设置justify-content:flex-end; 项目位于容器的结尾\r\n  // 优点:按钮会固定位置随着页面缩放,不跑偏\r\n  .rightMove {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    height: 50px;\r\n    margin-top: -8px;\r\n    margin-bottom: 2px;\r\n  }\r\n}\r\n</style>\r\n"]}]}