{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\element-ui\\lib\\autocomplete.js", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\element-ui\\lib\\autocomplete.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["module", "exports", "modules", "installedModules", "__webpack_require__", "moduleId", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "getDefault", "getModuleExports", "object", "property", "prototype", "hasOwnProperty", "p", "s", "__webpack_exports__", "normalizeComponent", "scriptExports", "render", "staticRenderFns", "functionalTemplate", "injectStyles", "scopeId", "moduleIdentifier", "shadowMode", "options", "_compiled", "functional", "_scopeId", "hook", "context", "$vnode", "ssrContext", "parent", "__VUE_SSR_CONTEXT__", "_registeredComponents", "add", "_ssrRegister", "$root", "$options", "shadowRoot", "_injectStyles", "originalRender", "renderWithStyleInjection", "h", "existing", "beforeCreate", "concat", "require", "_vm", "_h", "$createElement", "_c", "_self", "directives", "rawName", "close", "expression", "staticClass", "attrs", "role", "suggestionVisible", "id", "_b", "ref", "on", "input", "handleInput", "change", "handleChange", "focus", "handleFocus", "blur", "handleBlur", "clear", "handleClear", "nativeOn", "keydown", "$event", "_k", "keyCode", "preventDefault", "highlight", "highlightedIndex", "handleKeyEnter", "$props", "$attrs", "$slots", "prepend", "slot", "_t", "_e", "append", "prefix", "suffix", "class", "popperClass", "popperOptions", "popperAppendToBody", "placement", "_l", "suggestions", "item", "index", "highlighted", "click", "select", "_v", "_s", "valueKey", "_withStripped", "debounce_", "debounce_default", "input_", "input_default", "clickoutside_", "clickoutside_default", "autocomplete_suggestionsvue_type_template_id_cd10dcf0_render", "do<PERSON><PERSON>roy", "showPopper", "hideLoading", "loading", "style", "width", "dropdownWidth", "tag", "autocomplete_suggestionsvue_type_template_id_cd10dcf0_staticRenderFns", "vue_popper_", "vue_popper_default", "emitter_", "emitter_default", "scrollbar_", "scrollbar_default", "autocomplete_suggestionsvue_type_script_lang_js_", "components", "ElScrollbar", "a", "mixins", "componentName", "data", "$parent", "props", "default", "_default", "gpuAcceleration", "String", "methods", "dispatch", "updated", "_this", "$nextTick", "_", "popperJS", "updatePopper", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "$el", "referenceElm", "$refs", "textarea", "referenceList", "querySelector", "setAttribute", "created", "_this2", "$on", "val", "inputWidth", "src_autocomplete_suggestionsvue_type_script_lang_js_", "componentNormalizer", "component", "api", "__file", "autocomplete_suggestions", "migrating_", "migrating_default", "util_", "focus_", "focus_default", "autocompletevue_type_script_lang_js_", "inheritAttrs", "ElInput", "ElAutocompleteSuggestions", "Clickoutside", "type", "placeholder", "clearable", "Boolean", "disabled", "size", "maxlength", "Number", "minlength", "autofocus", "fetchSuggestions", "Function", "triggerOnFocus", "customItem", "selectWhenUnmatched", "prefixIcon", "suffixIcon", "label", "debounce", "highlightFirstItem", "activated", "suggestionDisabled", "computed", "isValidData", "Array", "isArray", "length", "watch", "$input", "getInput", "broadcast", "offsetWidth", "getMigratingConfig", "getData", "queryString", "console", "error", "$emit", "debouncedGetData", "event", "e", "_this3", "suggestion", "suggestionList", "querySelectorAll", "highlightItem", "scrollTop", "offsetTop", "scrollHeight", "clientHeight", "_this4", "<PERSON><PERSON><PERSON><PERSON>", "$destroy", "src_autocompletevue_type_script_lang_js_", "autocomplete_component", "autocomplete_api", "autocomplete", "install", "<PERSON><PERSON>", "packages_autocomplete"], "sources": ["D:/zy/xm/pc/qdzx/product/node_modules/element-ui/lib/autocomplete.js"], "sourcesContent": ["module.exports =\n/******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__webpack_require__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__webpack_require__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __webpack_require__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__webpack_require__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"/dist/\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 64);\n/******/ })\n/************************************************************************/\n/******/ ({\n\n/***/ 0:\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"a\", function() { return normalizeComponent; });\n/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nfunction normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode /* vue-cli only */\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () { injectStyles.call(this, this.$root.$options.shadowRoot) }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functioal component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n\n\n/***/ }),\n\n/***/ 10:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/input\");\n\n/***/ }),\n\n/***/ 11:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/mixins/migrating\");\n\n/***/ }),\n\n/***/ 12:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/clickoutside\");\n\n/***/ }),\n\n/***/ 15:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/scrollbar\");\n\n/***/ }),\n\n/***/ 19:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"throttle-debounce/debounce\");\n\n/***/ }),\n\n/***/ 22:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/mixins/focus\");\n\n/***/ }),\n\n/***/ 3:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/util\");\n\n/***/ }),\n\n/***/ 4:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/mixins/emitter\");\n\n/***/ }),\n\n/***/ 5:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/vue-popper\");\n\n/***/ }),\n\n/***/ 64:\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n\n// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./packages/autocomplete/src/autocomplete.vue?vue&type=template&id=152f2ee6&\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    {\n      directives: [\n        {\n          name: \"clickoutside\",\n          rawName: \"v-clickoutside\",\n          value: _vm.close,\n          expression: \"close\"\n        }\n      ],\n      staticClass: \"el-autocomplete\",\n      attrs: {\n        \"aria-haspopup\": \"listbox\",\n        role: \"combobox\",\n        \"aria-expanded\": _vm.suggestionVisible,\n        \"aria-owns\": _vm.id\n      }\n    },\n    [\n      _c(\n        \"el-input\",\n        _vm._b(\n          {\n            ref: \"input\",\n            on: {\n              input: _vm.handleInput,\n              change: _vm.handleChange,\n              focus: _vm.handleFocus,\n              blur: _vm.handleBlur,\n              clear: _vm.handleClear\n            },\n            nativeOn: {\n              keydown: [\n                function($event) {\n                  if (\n                    !(\"button\" in $event) &&\n                    _vm._k($event.keyCode, \"up\", 38, $event.key, [\n                      \"Up\",\n                      \"ArrowUp\"\n                    ])\n                  ) {\n                    return null\n                  }\n                  $event.preventDefault()\n                  _vm.highlight(_vm.highlightedIndex - 1)\n                },\n                function($event) {\n                  if (\n                    !(\"button\" in $event) &&\n                    _vm._k($event.keyCode, \"down\", 40, $event.key, [\n                      \"Down\",\n                      \"ArrowDown\"\n                    ])\n                  ) {\n                    return null\n                  }\n                  $event.preventDefault()\n                  _vm.highlight(_vm.highlightedIndex + 1)\n                },\n                function($event) {\n                  if (\n                    !(\"button\" in $event) &&\n                    _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")\n                  ) {\n                    return null\n                  }\n                  return _vm.handleKeyEnter($event)\n                },\n                function($event) {\n                  if (\n                    !(\"button\" in $event) &&\n                    _vm._k($event.keyCode, \"tab\", 9, $event.key, \"Tab\")\n                  ) {\n                    return null\n                  }\n                  return _vm.close($event)\n                }\n              ]\n            }\n          },\n          \"el-input\",\n          [_vm.$props, _vm.$attrs],\n          false\n        ),\n        [\n          _vm.$slots.prepend\n            ? _c(\"template\", { slot: \"prepend\" }, [_vm._t(\"prepend\")], 2)\n            : _vm._e(),\n          _vm.$slots.append\n            ? _c(\"template\", { slot: \"append\" }, [_vm._t(\"append\")], 2)\n            : _vm._e(),\n          _vm.$slots.prefix\n            ? _c(\"template\", { slot: \"prefix\" }, [_vm._t(\"prefix\")], 2)\n            : _vm._e(),\n          _vm.$slots.suffix\n            ? _c(\"template\", { slot: \"suffix\" }, [_vm._t(\"suffix\")], 2)\n            : _vm._e()\n        ],\n        2\n      ),\n      _c(\n        \"el-autocomplete-suggestions\",\n        {\n          ref: \"suggestions\",\n          class: [_vm.popperClass ? _vm.popperClass : \"\"],\n          attrs: {\n            \"visible-arrow\": \"\",\n            \"popper-options\": _vm.popperOptions,\n            \"append-to-body\": _vm.popperAppendToBody,\n            placement: _vm.placement,\n            id: _vm.id\n          }\n        },\n        _vm._l(_vm.suggestions, function(item, index) {\n          return _c(\n            \"li\",\n            {\n              key: index,\n              class: { highlighted: _vm.highlightedIndex === index },\n              attrs: {\n                id: _vm.id + \"-item-\" + index,\n                role: \"option\",\n                \"aria-selected\": _vm.highlightedIndex === index\n              },\n              on: {\n                click: function($event) {\n                  _vm.select(item)\n                }\n              }\n            },\n            [\n              _vm._t(\n                \"default\",\n                [\n                  _vm._v(\"\\n        \" + _vm._s(item[_vm.valueKey]) + \"\\n      \")\n                ],\n                { item: item }\n              )\n            ],\n            2\n          )\n        }),\n        0\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\n\n// CONCATENATED MODULE: ./packages/autocomplete/src/autocomplete.vue?vue&type=template&id=152f2ee6&\n\n// EXTERNAL MODULE: external \"throttle-debounce/debounce\"\nvar debounce_ = __webpack_require__(19);\nvar debounce_default = /*#__PURE__*/__webpack_require__.n(debounce_);\n\n// EXTERNAL MODULE: external \"element-ui/lib/input\"\nvar input_ = __webpack_require__(10);\nvar input_default = /*#__PURE__*/__webpack_require__.n(input_);\n\n// EXTERNAL MODULE: external \"element-ui/lib/utils/clickoutside\"\nvar clickoutside_ = __webpack_require__(12);\nvar clickoutside_default = /*#__PURE__*/__webpack_require__.n(clickoutside_);\n\n// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./packages/autocomplete/src/autocomplete-suggestions.vue?vue&type=template&id=cd10dcf0&\nvar autocomplete_suggestionsvue_type_template_id_cd10dcf0_render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"transition\",\n    { attrs: { name: \"el-zoom-in-top\" }, on: { \"after-leave\": _vm.doDestroy } },\n    [\n      _c(\n        \"div\",\n        {\n          directives: [\n            {\n              name: \"show\",\n              rawName: \"v-show\",\n              value: _vm.showPopper,\n              expression: \"showPopper\"\n            }\n          ],\n          staticClass: \"el-autocomplete-suggestion el-popper\",\n          class: {\n            \"is-loading\": !_vm.parent.hideLoading && _vm.parent.loading\n          },\n          style: { width: _vm.dropdownWidth },\n          attrs: { role: \"region\" }\n        },\n        [\n          _c(\n            \"el-scrollbar\",\n            {\n              attrs: {\n                tag: \"ul\",\n                \"wrap-class\": \"el-autocomplete-suggestion__wrap\",\n                \"view-class\": \"el-autocomplete-suggestion__list\"\n              }\n            },\n            [\n              !_vm.parent.hideLoading && _vm.parent.loading\n                ? _c(\"li\", [_c(\"i\", { staticClass: \"el-icon-loading\" })])\n                : _vm._t(\"default\")\n            ],\n            2\n          )\n        ],\n        1\n      )\n    ]\n  )\n}\nvar autocomplete_suggestionsvue_type_template_id_cd10dcf0_staticRenderFns = []\nautocomplete_suggestionsvue_type_template_id_cd10dcf0_render._withStripped = true\n\n\n// CONCATENATED MODULE: ./packages/autocomplete/src/autocomplete-suggestions.vue?vue&type=template&id=cd10dcf0&\n\n// EXTERNAL MODULE: external \"element-ui/lib/utils/vue-popper\"\nvar vue_popper_ = __webpack_require__(5);\nvar vue_popper_default = /*#__PURE__*/__webpack_require__.n(vue_popper_);\n\n// EXTERNAL MODULE: external \"element-ui/lib/mixins/emitter\"\nvar emitter_ = __webpack_require__(4);\nvar emitter_default = /*#__PURE__*/__webpack_require__.n(emitter_);\n\n// EXTERNAL MODULE: external \"element-ui/lib/scrollbar\"\nvar scrollbar_ = __webpack_require__(15);\nvar scrollbar_default = /*#__PURE__*/__webpack_require__.n(scrollbar_);\n\n// CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/autocomplete/src/autocomplete-suggestions.vue?vue&type=script&lang=js&\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\n\n\n\n/* harmony default export */ var autocomplete_suggestionsvue_type_script_lang_js_ = ({\n  components: { ElScrollbar: scrollbar_default.a },\n  mixins: [vue_popper_default.a, emitter_default.a],\n\n  componentName: 'ElAutocompleteSuggestions',\n\n  data: function data() {\n    return {\n      parent: this.$parent,\n      dropdownWidth: ''\n    };\n  },\n\n\n  props: {\n    options: {\n      default: function _default() {\n        return {\n          gpuAcceleration: false\n        };\n      }\n    },\n    id: String\n  },\n\n  methods: {\n    select: function select(item) {\n      this.dispatch('ElAutocomplete', 'item-click', item);\n    }\n  },\n\n  updated: function updated() {\n    var _this = this;\n\n    this.$nextTick(function (_) {\n      _this.popperJS && _this.updatePopper();\n    });\n  },\n  mounted: function mounted() {\n    this.$parent.popperElm = this.popperElm = this.$el;\n    this.referenceElm = this.$parent.$refs.input.$refs.input || this.$parent.$refs.input.$refs.textarea;\n    this.referenceList = this.$el.querySelector('.el-autocomplete-suggestion__list');\n    this.referenceList.setAttribute('role', 'listbox');\n    this.referenceList.setAttribute('id', this.id);\n  },\n  created: function created() {\n    var _this2 = this;\n\n    this.$on('visible', function (val, inputWidth) {\n      _this2.dropdownWidth = inputWidth + 'px';\n      _this2.showPopper = val;\n    });\n  }\n});\n// CONCATENATED MODULE: ./packages/autocomplete/src/autocomplete-suggestions.vue?vue&type=script&lang=js&\n /* harmony default export */ var src_autocomplete_suggestionsvue_type_script_lang_js_ = (autocomplete_suggestionsvue_type_script_lang_js_); \n// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js\nvar componentNormalizer = __webpack_require__(0);\n\n// CONCATENATED MODULE: ./packages/autocomplete/src/autocomplete-suggestions.vue\n\n\n\n\n\n/* normalize component */\n\nvar component = Object(componentNormalizer[\"a\" /* default */])(\n  src_autocomplete_suggestionsvue_type_script_lang_js_,\n  autocomplete_suggestionsvue_type_template_id_cd10dcf0_render,\n  autocomplete_suggestionsvue_type_template_id_cd10dcf0_staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (false) { var api; }\ncomponent.options.__file = \"packages/autocomplete/src/autocomplete-suggestions.vue\"\n/* harmony default export */ var autocomplete_suggestions = (component.exports);\n// EXTERNAL MODULE: external \"element-ui/lib/mixins/migrating\"\nvar migrating_ = __webpack_require__(11);\nvar migrating_default = /*#__PURE__*/__webpack_require__.n(migrating_);\n\n// EXTERNAL MODULE: external \"element-ui/lib/utils/util\"\nvar util_ = __webpack_require__(3);\n\n// EXTERNAL MODULE: external \"element-ui/lib/mixins/focus\"\nvar focus_ = __webpack_require__(22);\nvar focus_default = /*#__PURE__*/__webpack_require__.n(focus_);\n\n// CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/autocomplete/src/autocomplete.vue?vue&type=script&lang=js&\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\n\n\n\n\n\n\n\n\n/* harmony default export */ var autocompletevue_type_script_lang_js_ = ({\n  name: 'ElAutocomplete',\n\n  mixins: [emitter_default.a, focus_default()('input'), migrating_default.a],\n\n  inheritAttrs: false,\n\n  componentName: 'ElAutocomplete',\n\n  components: {\n    ElInput: input_default.a,\n    ElAutocompleteSuggestions: autocomplete_suggestions\n  },\n\n  directives: { Clickoutside: clickoutside_default.a },\n\n  props: {\n    valueKey: {\n      type: String,\n      default: 'value'\n    },\n    popperClass: String,\n    popperOptions: Object,\n    placeholder: String,\n    clearable: {\n      type: Boolean,\n      default: false\n    },\n    disabled: Boolean,\n    name: String,\n    size: String,\n    value: String,\n    maxlength: Number,\n    minlength: Number,\n    autofocus: Boolean,\n    fetchSuggestions: Function,\n    triggerOnFocus: {\n      type: Boolean,\n      default: true\n    },\n    customItem: String,\n    selectWhenUnmatched: {\n      type: Boolean,\n      default: false\n    },\n    prefixIcon: String,\n    suffixIcon: String,\n    label: String,\n    debounce: {\n      type: Number,\n      default: 300\n    },\n    placement: {\n      type: String,\n      default: 'bottom-start'\n    },\n    hideLoading: Boolean,\n    popperAppendToBody: {\n      type: Boolean,\n      default: true\n    },\n    highlightFirstItem: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      activated: false,\n      suggestions: [],\n      loading: false,\n      highlightedIndex: -1,\n      suggestionDisabled: false\n    };\n  },\n\n  computed: {\n    suggestionVisible: function suggestionVisible() {\n      var suggestions = this.suggestions;\n      var isValidData = Array.isArray(suggestions) && suggestions.length > 0;\n      return (isValidData || this.loading) && this.activated;\n    },\n    id: function id() {\n      return 'el-autocomplete-' + Object(util_[\"generateId\"])();\n    }\n  },\n  watch: {\n    suggestionVisible: function suggestionVisible(val) {\n      var $input = this.getInput();\n      if ($input) {\n        this.broadcast('ElAutocompleteSuggestions', 'visible', [val, $input.offsetWidth]);\n      }\n    }\n  },\n  methods: {\n    getMigratingConfig: function getMigratingConfig() {\n      return {\n        props: {\n          'custom-item': 'custom-item is removed, use scoped slot instead.',\n          'props': 'props is removed, use value-key instead.'\n        }\n      };\n    },\n    getData: function getData(queryString) {\n      var _this = this;\n\n      if (this.suggestionDisabled) {\n        return;\n      }\n      this.loading = true;\n      this.fetchSuggestions(queryString, function (suggestions) {\n        _this.loading = false;\n        if (_this.suggestionDisabled) {\n          return;\n        }\n        if (Array.isArray(suggestions)) {\n          _this.suggestions = suggestions;\n          _this.highlightedIndex = _this.highlightFirstItem ? 0 : -1;\n        } else {\n          console.error('[Element Error][Autocomplete]autocomplete suggestions must be an array');\n        }\n      });\n    },\n    handleInput: function handleInput(value) {\n      this.$emit('input', value);\n      this.suggestionDisabled = false;\n      if (!this.triggerOnFocus && !value) {\n        this.suggestionDisabled = true;\n        this.suggestions = [];\n        return;\n      }\n      this.debouncedGetData(value);\n    },\n    handleChange: function handleChange(value) {\n      this.$emit('change', value);\n    },\n    handleFocus: function handleFocus(event) {\n      this.activated = true;\n      this.$emit('focus', event);\n      if (this.triggerOnFocus) {\n        this.debouncedGetData(this.value);\n      }\n    },\n    handleBlur: function handleBlur(event) {\n      this.$emit('blur', event);\n    },\n    handleClear: function handleClear() {\n      this.activated = false;\n      this.$emit('clear');\n    },\n    close: function close(e) {\n      this.activated = false;\n    },\n    handleKeyEnter: function handleKeyEnter(e) {\n      var _this2 = this;\n\n      if (this.suggestionVisible && this.highlightedIndex >= 0 && this.highlightedIndex < this.suggestions.length) {\n        e.preventDefault();\n        this.select(this.suggestions[this.highlightedIndex]);\n      } else if (this.selectWhenUnmatched) {\n        this.$emit('select', { value: this.value });\n        this.$nextTick(function (_) {\n          _this2.suggestions = [];\n          _this2.highlightedIndex = -1;\n        });\n      }\n    },\n    select: function select(item) {\n      var _this3 = this;\n\n      this.$emit('input', item[this.valueKey]);\n      this.$emit('select', item);\n      this.$nextTick(function (_) {\n        _this3.suggestions = [];\n        _this3.highlightedIndex = -1;\n      });\n    },\n    highlight: function highlight(index) {\n      if (!this.suggestionVisible || this.loading) {\n        return;\n      }\n      if (index < 0) {\n        this.highlightedIndex = -1;\n        return;\n      }\n      if (index >= this.suggestions.length) {\n        index = this.suggestions.length - 1;\n      }\n      var suggestion = this.$refs.suggestions.$el.querySelector('.el-autocomplete-suggestion__wrap');\n      var suggestionList = suggestion.querySelectorAll('.el-autocomplete-suggestion__list li');\n\n      var highlightItem = suggestionList[index];\n      var scrollTop = suggestion.scrollTop;\n      var offsetTop = highlightItem.offsetTop;\n\n      if (offsetTop + highlightItem.scrollHeight > scrollTop + suggestion.clientHeight) {\n        suggestion.scrollTop += highlightItem.scrollHeight;\n      }\n      if (offsetTop < scrollTop) {\n        suggestion.scrollTop -= highlightItem.scrollHeight;\n      }\n      this.highlightedIndex = index;\n      var $input = this.getInput();\n      $input.setAttribute('aria-activedescendant', this.id + '-item-' + this.highlightedIndex);\n    },\n    getInput: function getInput() {\n      return this.$refs.input.getInput();\n    }\n  },\n  mounted: function mounted() {\n    var _this4 = this;\n\n    this.debouncedGetData = debounce_default()(this.debounce, this.getData);\n    this.$on('item-click', function (item) {\n      _this4.select(item);\n    });\n    var $input = this.getInput();\n    $input.setAttribute('role', 'textbox');\n    $input.setAttribute('aria-autocomplete', 'list');\n    $input.setAttribute('aria-controls', 'id');\n    $input.setAttribute('aria-activedescendant', this.id + '-item-' + this.highlightedIndex);\n  },\n  beforeDestroy: function beforeDestroy() {\n    this.$refs.suggestions.$destroy();\n  }\n});\n// CONCATENATED MODULE: ./packages/autocomplete/src/autocomplete.vue?vue&type=script&lang=js&\n /* harmony default export */ var src_autocompletevue_type_script_lang_js_ = (autocompletevue_type_script_lang_js_); \n// CONCATENATED MODULE: ./packages/autocomplete/src/autocomplete.vue\n\n\n\n\n\n/* normalize component */\n\nvar autocomplete_component = Object(componentNormalizer[\"a\" /* default */])(\n  src_autocompletevue_type_script_lang_js_,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (false) { var autocomplete_api; }\nautocomplete_component.options.__file = \"packages/autocomplete/src/autocomplete.vue\"\n/* harmony default export */ var autocomplete = (autocomplete_component.exports);\n// CONCATENATED MODULE: ./packages/autocomplete/index.js\n\n\n/* istanbul ignore next */\nautocomplete.install = function (Vue) {\n  Vue.component(autocomplete.name, autocomplete);\n};\n\n/* harmony default export */ var packages_autocomplete = __webpack_exports__[\"default\"] = (autocomplete);\n\n/***/ })\n\n/******/ });"], "mappings": "AAAAA,MAAM,CAACC,OAAP;AACA;AAAU,UAASC,OAAT,EAAkB;EAAE;;EAC9B;EAAU;;EACV;EAAU,IAAIC,gBAAgB,GAAG,EAAvB;EACV;;EACA;EAAU;;EACV;;EAAU,SAASC,mBAAT,CAA6BC,QAA7B,EAAuC;IACjD;;IACA;IAAW;;IACX;IAAW,IAAGF,gBAAgB,CAACE,QAAD,CAAnB,EAA+B;MAC1C;MAAY,OAAOF,gBAAgB,CAACE,QAAD,CAAhB,CAA2BJ,OAAlC;MACZ;IAAY;IACZ;IAAW;;IACX;;;IAAW,IAAID,MAAM,GAAGG,gBAAgB,CAACE,QAAD,CAAhB,GAA6B;MACrD;MAAYC,CAAC,EAAED,QADsC;;MAErD;MAAYE,CAAC,EAAE,KAFsC;;MAGrD;MAAYN,OAAO,EAAE;MACrB;;IAJqD,CAA1C;IAKX;;IACA;IAAW;;IACX;;IAAWC,OAAO,CAACG,QAAD,CAAP,CAAkBG,IAAlB,CAAuBR,MAAM,CAACC,OAA9B,EAAuCD,MAAvC,EAA+CA,MAAM,CAACC,OAAtD,EAA+DG,mBAA/D;IACX;;IACA;IAAW;;IACX;;IAAWJ,MAAM,CAACO,CAAP,GAAW,IAAX;IACX;;IACA;IAAW;;IACX;;IAAW,OAAOP,MAAM,CAACC,OAAd;IACX;EAAW;EACX;;EACA;;EACA;EAAU;;EACV;;;EAAUG,mBAAmB,CAACK,CAApB,GAAwBP,OAAxB;EACV;;EACA;EAAU;;EACV;;EAAUE,mBAAmB,CAACM,CAApB,GAAwBP,gBAAxB;EACV;;EACA;EAAU;;EACV;;EAAUC,mBAAmB,CAACO,CAApB,GAAwB,UAASV,OAAT,EAAkBW,IAAlB,EAAwBC,MAAxB,EAAgC;IAClE;IAAW,IAAG,CAACT,mBAAmB,CAACU,CAApB,CAAsBb,OAAtB,EAA+BW,IAA/B,CAAJ,EAA0C;MACrD;MAAYG,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+BW,IAA/B,EAAqC;QAAEK,UAAU,EAAE,IAAd;QAAoBC,GAAG,EAAEL;MAAzB,CAArC;MACZ;IAAY;IACZ;;EAAW,CAJD;EAKV;;EACA;EAAU;;EACV;;;EAAUT,mBAAmB,CAACe,CAApB,GAAwB,UAASlB,OAAT,EAAkB;IACpD;IAAW,IAAG,OAAOmB,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACC,WAA3C,EAAwD;MACnE;MAAYN,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+BmB,MAAM,CAACC,WAAtC,EAAmD;QAAEC,KAAK,EAAE;MAAT,CAAnD;MACZ;IAAY;IACZ;;;IAAWP,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+B,YAA/B,EAA6C;MAAEqB,KAAK,EAAE;IAAT,CAA7C;IACX;EAAW,CALD;EAMV;;EACA;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;;;EAAUlB,mBAAmB,CAACmB,CAApB,GAAwB,UAASD,KAAT,EAAgBE,IAAhB,EAAsB;IACxD;IAAW,IAAGA,IAAI,GAAG,CAAV,EAAaF,KAAK,GAAGlB,mBAAmB,CAACkB,KAAD,CAA3B;IACxB;;IAAW,IAAGE,IAAI,GAAG,CAAV,EAAa,OAAOF,KAAP;IACxB;;IAAW,IAAIE,IAAI,GAAG,CAAR,IAAc,OAAOF,KAAP,KAAiB,QAA/B,IAA2CA,KAA3C,IAAoDA,KAAK,CAACG,UAA7D,EAAyE,OAAOH,KAAP;IACpF;;IAAW,IAAII,EAAE,GAAGX,MAAM,CAACY,MAAP,CAAc,IAAd,CAAT;IACX;;IAAWvB,mBAAmB,CAACe,CAApB,CAAsBO,EAAtB;IACX;;;IAAWX,MAAM,CAACC,cAAP,CAAsBU,EAAtB,EAA0B,SAA1B,EAAqC;MAAET,UAAU,EAAE,IAAd;MAAoBK,KAAK,EAAEA;IAA3B,CAArC;IACX;;IAAW,IAAGE,IAAI,GAAG,CAAP,IAAY,OAAOF,KAAP,IAAgB,QAA/B,EAAyC,KAAI,IAAIM,GAAR,IAAeN,KAAf,EAAsBlB,mBAAmB,CAACO,CAApB,CAAsBe,EAAtB,EAA0BE,GAA1B,EAA+B,UAASA,GAAT,EAAc;MAAE,OAAON,KAAK,CAACM,GAAD,CAAZ;IAAoB,CAApC,CAAqCC,IAArC,CAA0C,IAA1C,EAAgDD,GAAhD,CAA/B;IAC1E;;IAAW,OAAOF,EAAP;IACX;EAAW,CATD;EAUV;;EACA;EAAU;;EACV;;;EAAUtB,mBAAmB,CAAC0B,CAApB,GAAwB,UAAS9B,MAAT,EAAiB;IACnD;IAAW,IAAIa,MAAM,GAAGb,MAAM,IAAIA,MAAM,CAACyB,UAAjB;IACxB;IAAY,SAASM,UAAT,GAAsB;MAAE,OAAO/B,MAAM,CAAC,SAAD,CAAb;IAA2B,CADvC;IAExB;IAAY,SAASgC,gBAAT,GAA4B;MAAE,OAAOhC,MAAP;IAAgB,CAF/C;IAGX;;IAAWI,mBAAmB,CAACO,CAApB,CAAsBE,MAAtB,EAA8B,GAA9B,EAAmCA,MAAnC;IACX;;;IAAW,OAAOA,MAAP;IACX;EAAW,CAND;EAOV;;EACA;EAAU;;EACV;;;EAAUT,mBAAmB,CAACU,CAApB,GAAwB,UAASmB,MAAT,EAAiBC,QAAjB,EAA2B;IAAE,OAAOnB,MAAM,CAACoB,SAAP,CAAiBC,cAAjB,CAAgC5B,IAAhC,CAAqCyB,MAArC,EAA6CC,QAA7C,CAAP;EAAgE,CAArH;EACV;;EACA;EAAU;;EACV;;;EAAU9B,mBAAmB,CAACiC,CAApB,GAAwB,QAAxB;EACV;;EACA;;EACA;EAAU;;EACV;;EAAU,OAAOjC,mBAAmB,CAACA,mBAAmB,CAACkC,CAApB,GAAwB,EAAzB,CAA1B;EACV;AAAU;AACV;;AACA;AAtFS,CAsFC;EAEV;EAAM;EACN;EAAO,UAAStC,MAAT,EAAiBuC,mBAAjB,EAAsCnC,mBAAtC,EAA2D;IAElE;IACA;;IAA+BA,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;MAAE,OAAOC,kBAAP;IAA4B,CAAzF;IAC/B;IAEA;IACA;IACA;;;IAEA,SAASA,kBAAT,CACEC,aADF,EAEEC,MAFF,EAGEC,eAHF,EAIEC,kBAJF,EAKEC,YALF,EAMEC,OANF,EAOEC,gBAPF;IAOoB;IAClBC;IAAW;IARb,EASE;MACA;MACA,IAAIC,OAAO,GAAG,OAAOR,aAAP,KAAyB,UAAzB,GACVA,aAAa,CAACQ,OADJ,GAEVR,aAFJ,CAFA,CAMA;;MACA,IAAIC,MAAJ,EAAY;QACVO,OAAO,CAACP,MAAR,GAAiBA,MAAjB;QACAO,OAAO,CAACN,eAAR,GAA0BA,eAA1B;QACAM,OAAO,CAACC,SAAR,GAAoB,IAApB;MACD,CAXD,CAaA;;;MACA,IAAIN,kBAAJ,EAAwB;QACtBK,OAAO,CAACE,UAAR,GAAqB,IAArB;MACD,CAhBD,CAkBA;;;MACA,IAAIL,OAAJ,EAAa;QACXG,OAAO,CAACG,QAAR,GAAmB,YAAYN,OAA/B;MACD;;MAED,IAAIO,IAAJ;;MACA,IAAIN,gBAAJ,EAAsB;QAAE;QACtBM,IAAI,GAAG,UAAUC,OAAV,EAAmB;UACxB;UACAA,OAAO,GACLA,OAAO,IAAI;UACV,KAAKC,MAAL,IAAe,KAAKA,MAAL,CAAYC,UAD5B,IAC2C;UAC1C,KAAKC,MAAL,IAAe,KAAKA,MAAL,CAAYF,MAA3B,IAAqC,KAAKE,MAAL,CAAYF,MAAZ,CAAmBC,UAH3D,CAFwB,CAK+C;UACvE;;UACA,IAAI,CAACF,OAAD,IAAY,OAAOI,mBAAP,KAA+B,WAA/C,EAA4D;YAC1DJ,OAAO,GAAGI,mBAAV;UACD,CATuB,CAUxB;;;UACA,IAAIb,YAAJ,EAAkB;YAChBA,YAAY,CAACrC,IAAb,CAAkB,IAAlB,EAAwB8C,OAAxB;UACD,CAbuB,CAcxB;;;UACA,IAAIA,OAAO,IAAIA,OAAO,CAACK,qBAAvB,EAA8C;YAC5CL,OAAO,CAACK,qBAAR,CAA8BC,GAA9B,CAAkCb,gBAAlC;UACD;QACF,CAlBD,CADoB,CAoBpB;QACA;;;QACAE,OAAO,CAACY,YAAR,GAAuBR,IAAvB;MACD,CAvBD,MAuBO,IAAIR,YAAJ,EAAkB;QACvBQ,IAAI,GAAGL,UAAU,GACb,YAAY;UAAEH,YAAY,CAACrC,IAAb,CAAkB,IAAlB,EAAwB,KAAKsD,KAAL,CAAWC,QAAX,CAAoBC,UAA5C;QAAyD,CAD1D,GAEbnB,YAFJ;MAGD;;MAED,IAAIQ,IAAJ,EAAU;QACR,IAAIJ,OAAO,CAACE,UAAZ,EAAwB;UACtB;UACA;UACAF,OAAO,CAACgB,aAAR,GAAwBZ,IAAxB,CAHsB,CAItB;;UACA,IAAIa,cAAc,GAAGjB,OAAO,CAACP,MAA7B;;UACAO,OAAO,CAACP,MAAR,GAAiB,SAASyB,wBAAT,CAAmCC,CAAnC,EAAsCd,OAAtC,EAA+C;YAC9DD,IAAI,CAAC7C,IAAL,CAAU8C,OAAV;YACA,OAAOY,cAAc,CAACE,CAAD,EAAId,OAAJ,CAArB;UACD,CAHD;QAID,CAVD,MAUO;UACL;UACA,IAAIe,QAAQ,GAAGpB,OAAO,CAACqB,YAAvB;UACArB,OAAO,CAACqB,YAAR,GAAuBD,QAAQ,GAC3B,GAAGE,MAAH,CAAUF,QAAV,EAAoBhB,IAApB,CAD2B,GAE3B,CAACA,IAAD,CAFJ;QAGD;MACF;;MAED,OAAO;QACLpD,OAAO,EAAEwC,aADJ;QAELQ,OAAO,EAAEA;MAFJ,CAAP;IAID;IAGD;;EAAO,CAtGG;;EAwGV;EAAM;EACN;EAAO,UAASjD,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,sBAAD,CAAxB;IAEA;EAAO,CA7GG;;EA+GV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,iCAAD,CAAxB;IAEA;EAAO,CApHG;;EAsHV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,mCAAD,CAAxB;IAEA;EAAO,CA3HG;;EA6HV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,0BAAD,CAAxB;IAEA;EAAO,CAlIG;;EAoIV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,4BAAD,CAAxB;IAEA;EAAO,CAzIG;;EA2IV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,6BAAD,CAAxB;IAEA;EAAO,CAhJG;;EAkJV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,2BAAD,CAAxB;IAEA;EAAO,CAvJG;;EAyJV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,+BAAD,CAAxB;IAEA;EAAO,CA9JG;;EAgKV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,iCAAD,CAAxB;IAEA;EAAO,CArKG;;EAuKV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBuC,mBAAjB,EAAsCnC,mBAAtC,EAA2D;IAElE;;IACAA,mBAAmB,CAACe,CAApB,CAAsBoB,mBAAtB,EAHkE,CAKlE;;;IACA,IAAIG,MAAM,GAAG,YAAW;MACtB,IAAI+B,GAAG,GAAG,IAAV;;MACA,IAAIC,EAAE,GAAGD,GAAG,CAACE,cAAb;;MACA,IAAIC,EAAE,GAAGH,GAAG,CAACI,KAAJ,CAAUD,EAAV,IAAgBF,EAAzB;;MACA,OAAOE,EAAE,CACP,KADO,EAEP;QACEE,UAAU,EAAE,CACV;UACElE,IAAI,EAAE,cADR;UAEEmE,OAAO,EAAE,gBAFX;UAGEzD,KAAK,EAAEmD,GAAG,CAACO,KAHb;UAIEC,UAAU,EAAE;QAJd,CADU,CADd;QASEC,WAAW,EAAE,iBATf;QAUEC,KAAK,EAAE;UACL,iBAAiB,SADZ;UAELC,IAAI,EAAE,UAFD;UAGL,iBAAiBX,GAAG,CAACY,iBAHhB;UAIL,aAAaZ,GAAG,CAACa;QAJZ;MAVT,CAFO,EAmBP,CACEV,EAAE,CACA,UADA,EAEAH,GAAG,CAACc,EAAJ,CACE;QACEC,GAAG,EAAE,OADP;QAEEC,EAAE,EAAE;UACFC,KAAK,EAAEjB,GAAG,CAACkB,WADT;UAEFC,MAAM,EAAEnB,GAAG,CAACoB,YAFV;UAGFC,KAAK,EAAErB,GAAG,CAACsB,WAHT;UAIFC,IAAI,EAAEvB,GAAG,CAACwB,UAJR;UAKFC,KAAK,EAAEzB,GAAG,CAAC0B;QALT,CAFN;QASEC,QAAQ,EAAE;UACRC,OAAO,EAAE,CACP,UAASC,MAAT,EAAiB;YACf,IACE,EAAE,YAAYA,MAAd,KACA7B,GAAG,CAAC8B,EAAJ,CAAOD,MAAM,CAACE,OAAd,EAAuB,IAAvB,EAA6B,EAA7B,EAAiCF,MAAM,CAAC1E,GAAxC,EAA6C,CAC3C,IAD2C,EAE3C,SAF2C,CAA7C,CAFF,EAME;cACA,OAAO,IAAP;YACD;;YACD0E,MAAM,CAACG,cAAP;;YACAhC,GAAG,CAACiC,SAAJ,CAAcjC,GAAG,CAACkC,gBAAJ,GAAuB,CAArC;UACD,CAbM,EAcP,UAASL,MAAT,EAAiB;YACf,IACE,EAAE,YAAYA,MAAd,KACA7B,GAAG,CAAC8B,EAAJ,CAAOD,MAAM,CAACE,OAAd,EAAuB,MAAvB,EAA+B,EAA/B,EAAmCF,MAAM,CAAC1E,GAA1C,EAA+C,CAC7C,MAD6C,EAE7C,WAF6C,CAA/C,CAFF,EAME;cACA,OAAO,IAAP;YACD;;YACD0E,MAAM,CAACG,cAAP;;YACAhC,GAAG,CAACiC,SAAJ,CAAcjC,GAAG,CAACkC,gBAAJ,GAAuB,CAArC;UACD,CA1BM,EA2BP,UAASL,MAAT,EAAiB;YACf,IACE,EAAE,YAAYA,MAAd,KACA7B,GAAG,CAAC8B,EAAJ,CAAOD,MAAM,CAACE,OAAd,EAAuB,OAAvB,EAAgC,EAAhC,EAAoCF,MAAM,CAAC1E,GAA3C,EAAgD,OAAhD,CAFF,EAGE;cACA,OAAO,IAAP;YACD;;YACD,OAAO6C,GAAG,CAACmC,cAAJ,CAAmBN,MAAnB,CAAP;UACD,CAnCM,EAoCP,UAASA,MAAT,EAAiB;YACf,IACE,EAAE,YAAYA,MAAd,KACA7B,GAAG,CAAC8B,EAAJ,CAAOD,MAAM,CAACE,OAAd,EAAuB,KAAvB,EAA8B,CAA9B,EAAiCF,MAAM,CAAC1E,GAAxC,EAA6C,KAA7C,CAFF,EAGE;cACA,OAAO,IAAP;YACD;;YACD,OAAO6C,GAAG,CAACO,KAAJ,CAAUsB,MAAV,CAAP;UACD,CA5CM;QADD;MATZ,CADF,EA2DE,UA3DF,EA4DE,CAAC7B,GAAG,CAACoC,MAAL,EAAapC,GAAG,CAACqC,MAAjB,CA5DF,EA6DE,KA7DF,CAFA,EAiEA,CACErC,GAAG,CAACsC,MAAJ,CAAWC,OAAX,GACIpC,EAAE,CAAC,UAAD,EAAa;QAAEqC,IAAI,EAAE;MAAR,CAAb,EAAkC,CAACxC,GAAG,CAACyC,EAAJ,CAAO,SAAP,CAAD,CAAlC,EAAuD,CAAvD,CADN,GAEIzC,GAAG,CAAC0C,EAAJ,EAHN,EAIE1C,GAAG,CAACsC,MAAJ,CAAWK,MAAX,GACIxC,EAAE,CAAC,UAAD,EAAa;QAAEqC,IAAI,EAAE;MAAR,CAAb,EAAiC,CAACxC,GAAG,CAACyC,EAAJ,CAAO,QAAP,CAAD,CAAjC,EAAqD,CAArD,CADN,GAEIzC,GAAG,CAAC0C,EAAJ,EANN,EAOE1C,GAAG,CAACsC,MAAJ,CAAWM,MAAX,GACIzC,EAAE,CAAC,UAAD,EAAa;QAAEqC,IAAI,EAAE;MAAR,CAAb,EAAiC,CAACxC,GAAG,CAACyC,EAAJ,CAAO,QAAP,CAAD,CAAjC,EAAqD,CAArD,CADN,GAEIzC,GAAG,CAAC0C,EAAJ,EATN,EAUE1C,GAAG,CAACsC,MAAJ,CAAWO,MAAX,GACI1C,EAAE,CAAC,UAAD,EAAa;QAAEqC,IAAI,EAAE;MAAR,CAAb,EAAiC,CAACxC,GAAG,CAACyC,EAAJ,CAAO,QAAP,CAAD,CAAjC,EAAqD,CAArD,CADN,GAEIzC,GAAG,CAAC0C,EAAJ,EAZN,CAjEA,EA+EA,CA/EA,CADJ,EAkFEvC,EAAE,CACA,6BADA,EAEA;QACEY,GAAG,EAAE,aADP;QAEE+B,KAAK,EAAE,CAAC9C,GAAG,CAAC+C,WAAJ,GAAkB/C,GAAG,CAAC+C,WAAtB,GAAoC,EAArC,CAFT;QAGErC,KAAK,EAAE;UACL,iBAAiB,EADZ;UAEL,kBAAkBV,GAAG,CAACgD,aAFjB;UAGL,kBAAkBhD,GAAG,CAACiD,kBAHjB;UAILC,SAAS,EAAElD,GAAG,CAACkD,SAJV;UAKLrC,EAAE,EAAEb,GAAG,CAACa;QALH;MAHT,CAFA,EAaAb,GAAG,CAACmD,EAAJ,CAAOnD,GAAG,CAACoD,WAAX,EAAwB,UAASC,IAAT,EAAeC,KAAf,EAAsB;QAC5C,OAAOnD,EAAE,CACP,IADO,EAEP;UACEhD,GAAG,EAAEmG,KADP;UAEER,KAAK,EAAE;YAAES,WAAW,EAAEvD,GAAG,CAACkC,gBAAJ,KAAyBoB;UAAxC,CAFT;UAGE5C,KAAK,EAAE;YACLG,EAAE,EAAEb,GAAG,CAACa,EAAJ,GAAS,QAAT,GAAoByC,KADnB;YAEL3C,IAAI,EAAE,QAFD;YAGL,iBAAiBX,GAAG,CAACkC,gBAAJ,KAAyBoB;UAHrC,CAHT;UAQEtC,EAAE,EAAE;YACFwC,KAAK,EAAE,UAAS3B,MAAT,EAAiB;cACtB7B,GAAG,CAACyD,MAAJ,CAAWJ,IAAX;YACD;UAHC;QARN,CAFO,EAgBP,CACErD,GAAG,CAACyC,EAAJ,CACE,SADF,EAEE,CACEzC,GAAG,CAAC0D,EAAJ,CAAO,eAAe1D,GAAG,CAAC2D,EAAJ,CAAON,IAAI,CAACrD,GAAG,CAAC4D,QAAL,CAAX,CAAf,GAA4C,UAAnD,CADF,CAFF,EAKE;UAAEP,IAAI,EAAEA;QAAR,CALF,CADF,CAhBO,EAyBP,CAzBO,CAAT;MA2BD,CA5BD,CAbA,EA0CA,CA1CA,CAlFJ,CAnBO,EAkJP,CAlJO,CAAT;IAoJD,CAxJD;;IAyJA,IAAInF,eAAe,GAAG,EAAtB;IACAD,MAAM,CAAC4F,aAAP,GAAuB,IAAvB,CAhKkE,CAmKlE;IAEA;;IACA,IAAIC,SAAS,GAAGnI,mBAAmB,CAAC,EAAD,CAAnC;;IACA,IAAIoI,gBAAgB,GAAG,aAAapI,mBAAmB,CAAC0B,CAApB,CAAsByG,SAAtB,CAApC,CAvKkE,CAyKlE;;;IACA,IAAIE,MAAM,GAAGrI,mBAAmB,CAAC,EAAD,CAAhC;;IACA,IAAIsI,aAAa,GAAG,aAAatI,mBAAmB,CAAC0B,CAApB,CAAsB2G,MAAtB,CAAjC,CA3KkE,CA6KlE;;;IACA,IAAIE,aAAa,GAAGvI,mBAAmB,CAAC,EAAD,CAAvC;;IACA,IAAIwI,oBAAoB,GAAG,aAAaxI,mBAAmB,CAAC0B,CAApB,CAAsB6G,aAAtB,CAAxC,CA/KkE,CAiLlE;;;IACA,IAAIE,4DAA4D,GAAG,YAAW;MAC5E,IAAIpE,GAAG,GAAG,IAAV;;MACA,IAAIC,EAAE,GAAGD,GAAG,CAACE,cAAb;;MACA,IAAIC,EAAE,GAAGH,GAAG,CAACI,KAAJ,CAAUD,EAAV,IAAgBF,EAAzB;;MACA,OAAOE,EAAE,CACP,YADO,EAEP;QAAEO,KAAK,EAAE;UAAEvE,IAAI,EAAE;QAAR,CAAT;QAAqC6E,EAAE,EAAE;UAAE,eAAehB,GAAG,CAACqE;QAArB;MAAzC,CAFO,EAGP,CACElE,EAAE,CACA,KADA,EAEA;QACEE,UAAU,EAAE,CACV;UACElE,IAAI,EAAE,MADR;UAEEmE,OAAO,EAAE,QAFX;UAGEzD,KAAK,EAAEmD,GAAG,CAACsE,UAHb;UAIE9D,UAAU,EAAE;QAJd,CADU,CADd;QASEC,WAAW,EAAE,sCATf;QAUEqC,KAAK,EAAE;UACL,cAAc,CAAC9C,GAAG,CAAChB,MAAJ,CAAWuF,WAAZ,IAA2BvE,GAAG,CAAChB,MAAJ,CAAWwF;QAD/C,CAVT;QAaEC,KAAK,EAAE;UAAEC,KAAK,EAAE1E,GAAG,CAAC2E;QAAb,CAbT;QAcEjE,KAAK,EAAE;UAAEC,IAAI,EAAE;QAAR;MAdT,CAFA,EAkBA,CACER,EAAE,CACA,cADA,EAEA;QACEO,KAAK,EAAE;UACLkE,GAAG,EAAE,IADA;UAEL,cAAc,kCAFT;UAGL,cAAc;QAHT;MADT,CAFA,EASA,CACE,CAAC5E,GAAG,CAAChB,MAAJ,CAAWuF,WAAZ,IAA2BvE,GAAG,CAAChB,MAAJ,CAAWwF,OAAtC,GACIrE,EAAE,CAAC,IAAD,EAAO,CAACA,EAAE,CAAC,GAAD,EAAM;QAAEM,WAAW,EAAE;MAAf,CAAN,CAAH,CAAP,CADN,GAEIT,GAAG,CAACyC,EAAJ,CAAO,SAAP,CAHN,CATA,EAcA,CAdA,CADJ,CAlBA,EAoCA,CApCA,CADJ,CAHO,CAAT;IA4CD,CAhDD;;IAiDA,IAAIoC,qEAAqE,GAAG,EAA5E;IACAT,4DAA4D,CAACP,aAA7D,GAA6E,IAA7E,CApOkE,CAuOlE;IAEA;;IACA,IAAIiB,WAAW,GAAGnJ,mBAAmB,CAAC,CAAD,CAArC;;IACA,IAAIoJ,kBAAkB,GAAG,aAAapJ,mBAAmB,CAAC0B,CAApB,CAAsByH,WAAtB,CAAtC,CA3OkE,CA6OlE;;;IACA,IAAIE,QAAQ,GAAGrJ,mBAAmB,CAAC,CAAD,CAAlC;;IACA,IAAIsJ,eAAe,GAAG,aAAatJ,mBAAmB,CAAC0B,CAApB,CAAsB2H,QAAtB,CAAnC,CA/OkE,CAiPlE;;;IACA,IAAIE,UAAU,GAAGvJ,mBAAmB,CAAC,EAAD,CAApC;;IACA,IAAIwJ,iBAAiB,GAAG,aAAaxJ,mBAAmB,CAAC0B,CAApB,CAAsB6H,UAAtB,CAArC,CAnPkE,CAqPlE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAMA;;;IAA6B,IAAIE,gDAAgD,GAAI;MACnFC,UAAU,EAAE;QAAEC,WAAW,EAAEH,iBAAiB,CAACI;MAAjC,CADuE;MAEnFC,MAAM,EAAE,CAACT,kBAAkB,CAACQ,CAApB,EAAuBN,eAAe,CAACM,CAAvC,CAF2E;MAInFE,aAAa,EAAE,2BAJoE;MAMnFC,IAAI,EAAE,SAASA,IAAT,GAAgB;QACpB,OAAO;UACL1G,MAAM,EAAE,KAAK2G,OADR;UAELhB,aAAa,EAAE;QAFV,CAAP;MAID,CAXkF;MAcnFiB,KAAK,EAAE;QACLpH,OAAO,EAAE;UACPqH,OAAO,EAAE,SAASC,QAAT,GAAoB;YAC3B,OAAO;cACLC,eAAe,EAAE;YADZ,CAAP;UAGD;QALM,CADJ;QAQLlF,EAAE,EAAEmF;MARC,CAd4E;MAyBnFC,OAAO,EAAE;QACPxC,MAAM,EAAE,SAASA,MAAT,CAAgBJ,IAAhB,EAAsB;UAC5B,KAAK6C,QAAL,CAAc,gBAAd,EAAgC,YAAhC,EAA8C7C,IAA9C;QACD;MAHM,CAzB0E;MA+BnF8C,OAAO,EAAE,SAASA,OAAT,GAAmB;QAC1B,IAAIC,KAAK,GAAG,IAAZ;;QAEA,KAAKC,SAAL,CAAe,UAAUC,CAAV,EAAa;UAC1BF,KAAK,CAACG,QAAN,IAAkBH,KAAK,CAACI,YAAN,EAAlB;QACD,CAFD;MAGD,CArCkF;MAsCnFC,OAAO,EAAE,SAASA,OAAT,GAAmB;QAC1B,KAAKd,OAAL,CAAae,SAAb,GAAyB,KAAKA,SAAL,GAAiB,KAAKC,GAA/C;QACA,KAAKC,YAAL,GAAoB,KAAKjB,OAAL,CAAakB,KAAb,CAAmB5F,KAAnB,CAAyB4F,KAAzB,CAA+B5F,KAA/B,IAAwC,KAAK0E,OAAL,CAAakB,KAAb,CAAmB5F,KAAnB,CAAyB4F,KAAzB,CAA+BC,QAA3F;QACA,KAAKC,aAAL,GAAqB,KAAKJ,GAAL,CAASK,aAAT,CAAuB,mCAAvB,CAArB;QACA,KAAKD,aAAL,CAAmBE,YAAnB,CAAgC,MAAhC,EAAwC,SAAxC;QACA,KAAKF,aAAL,CAAmBE,YAAnB,CAAgC,IAAhC,EAAsC,KAAKpG,EAA3C;MACD,CA5CkF;MA6CnFqG,OAAO,EAAE,SAASA,OAAT,GAAmB;QAC1B,IAAIC,MAAM,GAAG,IAAb;;QAEA,KAAKC,GAAL,CAAS,SAAT,EAAoB,UAAUC,GAAV,EAAeC,UAAf,EAA2B;UAC7CH,MAAM,CAACxC,aAAP,GAAuB2C,UAAU,GAAG,IAApC;UACAH,MAAM,CAAC7C,UAAP,GAAoB+C,GAApB;QACD,CAHD;MAID;IApDkF,CAAxD,CA9QqC,CAoUlE;;IACC;;IAA6B,IAAIE,oDAAoD,GAAInC,gDAA5D,CArUoC,CAsUlE;;IACA,IAAIoC,mBAAmB,GAAG7L,mBAAmB,CAAC,CAAD,CAA7C,CAvUkE,CAyUlE;;IAMA;;;IAEA,IAAI8L,SAAS,GAAGnL,MAAM,CAACkL,mBAAmB,CAAC;IAAI;IAAL,CAApB,CAAN,CACdD,oDADc,EAEdnD,4DAFc,EAGdS,qEAHc,EAId,KAJc,EAKd,IALc,EAMd,IANc,EAOd,IAPc,CAAhB;IAWA;;IACA,IAAI,KAAJ,EAAW;MAAE,IAAI6C,GAAJ;IAAU;;IACvBD,SAAS,CAACjJ,OAAV,CAAkBmJ,MAAlB,GAA2B,wDAA3B;IACA;;IAA6B,IAAIC,wBAAwB,GAAIH,SAAS,CAACjM,OAA1C,CA/VqC,CAgWlE;;IACA,IAAIqM,UAAU,GAAGlM,mBAAmB,CAAC,EAAD,CAApC;;IACA,IAAImM,iBAAiB,GAAG,aAAanM,mBAAmB,CAAC0B,CAApB,CAAsBwK,UAAtB,CAArC,CAlWkE,CAoWlE;;;IACA,IAAIE,KAAK,GAAGpM,mBAAmB,CAAC,CAAD,CAA/B,CArWkE,CAuWlE;;;IACA,IAAIqM,MAAM,GAAGrM,mBAAmB,CAAC,EAAD,CAAhC;;IACA,IAAIsM,aAAa,GAAG,aAAatM,mBAAmB,CAAC0B,CAApB,CAAsB2K,MAAtB,CAAjC,CAzWkE,CA2WlE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAWA;;;IAA6B,IAAIE,oCAAoC,GAAI;MACvE/L,IAAI,EAAE,gBADiE;MAGvEqJ,MAAM,EAAE,CAACP,eAAe,CAACM,CAAjB,EAAoB0C,aAAa,GAAG,OAAH,CAAjC,EAA8CH,iBAAiB,CAACvC,CAAhE,CAH+D;MAKvE4C,YAAY,EAAE,KALyD;MAOvE1C,aAAa,EAAE,gBAPwD;MASvEJ,UAAU,EAAE;QACV+C,OAAO,EAAEnE,aAAa,CAACsB,CADb;QAEV8C,yBAAyB,EAAET;MAFjB,CAT2D;MAcvEvH,UAAU,EAAE;QAAEiI,YAAY,EAAEnE,oBAAoB,CAACoB;MAArC,CAd2D;MAgBvEK,KAAK,EAAE;QACLhC,QAAQ,EAAE;UACR2E,IAAI,EAAEvC,MADE;UAERH,OAAO,EAAE;QAFD,CADL;QAKL9C,WAAW,EAAEiD,MALR;QAMLhD,aAAa,EAAE1G,MANV;QAOLkM,WAAW,EAAExC,MAPR;QAQLyC,SAAS,EAAE;UACTF,IAAI,EAAEG,OADG;UAET7C,OAAO,EAAE;QAFA,CARN;QAYL8C,QAAQ,EAAED,OAZL;QAaLvM,IAAI,EAAE6J,MAbD;QAcL4C,IAAI,EAAE5C,MAdD;QAeLnJ,KAAK,EAAEmJ,MAfF;QAgBL6C,SAAS,EAAEC,MAhBN;QAiBLC,SAAS,EAAED,MAjBN;QAkBLE,SAAS,EAAEN,OAlBN;QAmBLO,gBAAgB,EAAEC,QAnBb;QAoBLC,cAAc,EAAE;UACdZ,IAAI,EAAEG,OADQ;UAEd7C,OAAO,EAAE;QAFK,CApBX;QAwBLuD,UAAU,EAAEpD,MAxBP;QAyBLqD,mBAAmB,EAAE;UACnBd,IAAI,EAAEG,OADa;UAEnB7C,OAAO,EAAE;QAFU,CAzBhB;QA6BLyD,UAAU,EAAEtD,MA7BP;QA8BLuD,UAAU,EAAEvD,MA9BP;QA+BLwD,KAAK,EAAExD,MA/BF;QAgCLyD,QAAQ,EAAE;UACRlB,IAAI,EAAEO,MADE;UAERjD,OAAO,EAAE;QAFD,CAhCL;QAoCL3C,SAAS,EAAE;UACTqF,IAAI,EAAEvC,MADG;UAETH,OAAO,EAAE;QAFA,CApCN;QAwCLtB,WAAW,EAAEmE,OAxCR;QAyCLzF,kBAAkB,EAAE;UAClBsF,IAAI,EAAEG,OADY;UAElB7C,OAAO,EAAE;QAFS,CAzCf;QA6CL6D,kBAAkB,EAAE;UAClBnB,IAAI,EAAEG,OADY;UAElB7C,OAAO,EAAE;QAFS;MA7Cf,CAhBgE;MAkEvEH,IAAI,EAAE,SAASA,IAAT,GAAgB;QACpB,OAAO;UACLiE,SAAS,EAAE,KADN;UAELvG,WAAW,EAAE,EAFR;UAGLoB,OAAO,EAAE,KAHJ;UAILtC,gBAAgB,EAAE,CAAC,CAJd;UAKL0H,kBAAkB,EAAE;QALf,CAAP;MAOD,CA1EsE;MA4EvEC,QAAQ,EAAE;QACRjJ,iBAAiB,EAAE,SAASA,iBAAT,GAA6B;UAC9C,IAAIwC,WAAW,GAAG,KAAKA,WAAvB;UACA,IAAI0G,WAAW,GAAGC,KAAK,CAACC,OAAN,CAAc5G,WAAd,KAA8BA,WAAW,CAAC6G,MAAZ,GAAqB,CAArE;UACA,OAAO,CAACH,WAAW,IAAI,KAAKtF,OAArB,KAAiC,KAAKmF,SAA7C;QACD,CALO;QAMR9I,EAAE,EAAE,SAASA,EAAT,GAAc;UAChB,OAAO,qBAAqBvE,MAAM,CAACyL,KAAK,CAAC,YAAD,CAAN,CAAN,EAA5B;QACD;MARO,CA5E6D;MAsFvEmC,KAAK,EAAE;QACLtJ,iBAAiB,EAAE,SAASA,iBAAT,CAA2ByG,GAA3B,EAAgC;UACjD,IAAI8C,MAAM,GAAG,KAAKC,QAAL,EAAb;;UACA,IAAID,MAAJ,EAAY;YACV,KAAKE,SAAL,CAAe,2BAAf,EAA4C,SAA5C,EAAuD,CAAChD,GAAD,EAAM8C,MAAM,CAACG,WAAb,CAAvD;UACD;QACF;MANI,CAtFgE;MA8FvErE,OAAO,EAAE;QACPsE,kBAAkB,EAAE,SAASA,kBAAT,GAA8B;UAChD,OAAO;YACL3E,KAAK,EAAE;cACL,eAAe,kDADV;cAEL,SAAS;YAFJ;UADF,CAAP;QAMD,CARM;QASP4E,OAAO,EAAE,SAASA,OAAT,CAAiBC,WAAjB,EAA8B;UACrC,IAAIrE,KAAK,GAAG,IAAZ;;UAEA,IAAI,KAAKwD,kBAAT,EAA6B;YAC3B;UACD;;UACD,KAAKpF,OAAL,GAAe,IAAf;UACA,KAAKyE,gBAAL,CAAsBwB,WAAtB,EAAmC,UAAUrH,WAAV,EAAuB;YACxDgD,KAAK,CAAC5B,OAAN,GAAgB,KAAhB;;YACA,IAAI4B,KAAK,CAACwD,kBAAV,EAA8B;cAC5B;YACD;;YACD,IAAIG,KAAK,CAACC,OAAN,CAAc5G,WAAd,CAAJ,EAAgC;cAC9BgD,KAAK,CAAChD,WAAN,GAAoBA,WAApB;cACAgD,KAAK,CAAClE,gBAAN,GAAyBkE,KAAK,CAACsD,kBAAN,GAA2B,CAA3B,GAA+B,CAAC,CAAzD;YACD,CAHD,MAGO;cACLgB,OAAO,CAACC,KAAR,CAAc,wEAAd;YACD;UACF,CAXD;QAYD,CA5BM;QA6BPzJ,WAAW,EAAE,SAASA,WAAT,CAAqBrE,KAArB,EAA4B;UACvC,KAAK+N,KAAL,CAAW,OAAX,EAAoB/N,KAApB;UACA,KAAK+M,kBAAL,GAA0B,KAA1B;;UACA,IAAI,CAAC,KAAKT,cAAN,IAAwB,CAACtM,KAA7B,EAAoC;YAClC,KAAK+M,kBAAL,GAA0B,IAA1B;YACA,KAAKxG,WAAL,GAAmB,EAAnB;YACA;UACD;;UACD,KAAKyH,gBAAL,CAAsBhO,KAAtB;QACD,CAtCM;QAuCPuE,YAAY,EAAE,SAASA,YAAT,CAAsBvE,KAAtB,EAA6B;UACzC,KAAK+N,KAAL,CAAW,QAAX,EAAqB/N,KAArB;QACD,CAzCM;QA0CPyE,WAAW,EAAE,SAASA,WAAT,CAAqBwJ,KAArB,EAA4B;UACvC,KAAKnB,SAAL,GAAiB,IAAjB;UACA,KAAKiB,KAAL,CAAW,OAAX,EAAoBE,KAApB;;UACA,IAAI,KAAK3B,cAAT,EAAyB;YACvB,KAAK0B,gBAAL,CAAsB,KAAKhO,KAA3B;UACD;QACF,CAhDM;QAiDP2E,UAAU,EAAE,SAASA,UAAT,CAAoBsJ,KAApB,EAA2B;UACrC,KAAKF,KAAL,CAAW,MAAX,EAAmBE,KAAnB;QACD,CAnDM;QAoDPpJ,WAAW,EAAE,SAASA,WAAT,GAAuB;UAClC,KAAKiI,SAAL,GAAiB,KAAjB;UACA,KAAKiB,KAAL,CAAW,OAAX;QACD,CAvDM;QAwDPrK,KAAK,EAAE,SAASA,KAAT,CAAewK,CAAf,EAAkB;UACvB,KAAKpB,SAAL,GAAiB,KAAjB;QACD,CA1DM;QA2DPxH,cAAc,EAAE,SAASA,cAAT,CAAwB4I,CAAxB,EAA2B;UACzC,IAAI5D,MAAM,GAAG,IAAb;;UAEA,IAAI,KAAKvG,iBAAL,IAA0B,KAAKsB,gBAAL,IAAyB,CAAnD,IAAwD,KAAKA,gBAAL,GAAwB,KAAKkB,WAAL,CAAiB6G,MAArG,EAA6G;YAC3Gc,CAAC,CAAC/I,cAAF;YACA,KAAKyB,MAAL,CAAY,KAAKL,WAAL,CAAiB,KAAKlB,gBAAtB,CAAZ;UACD,CAHD,MAGO,IAAI,KAAKmH,mBAAT,EAA8B;YACnC,KAAKuB,KAAL,CAAW,QAAX,EAAqB;cAAE/N,KAAK,EAAE,KAAKA;YAAd,CAArB;YACA,KAAKwJ,SAAL,CAAe,UAAUC,CAAV,EAAa;cAC1Ba,MAAM,CAAC/D,WAAP,GAAqB,EAArB;cACA+D,MAAM,CAACjF,gBAAP,GAA0B,CAAC,CAA3B;YACD,CAHD;UAID;QACF,CAxEM;QAyEPuB,MAAM,EAAE,SAASA,MAAT,CAAgBJ,IAAhB,EAAsB;UAC5B,IAAI2H,MAAM,GAAG,IAAb;;UAEA,KAAKJ,KAAL,CAAW,OAAX,EAAoBvH,IAAI,CAAC,KAAKO,QAAN,CAAxB;UACA,KAAKgH,KAAL,CAAW,QAAX,EAAqBvH,IAArB;UACA,KAAKgD,SAAL,CAAe,UAAUC,CAAV,EAAa;YAC1B0E,MAAM,CAAC5H,WAAP,GAAqB,EAArB;YACA4H,MAAM,CAAC9I,gBAAP,GAA0B,CAAC,CAA3B;UACD,CAHD;QAID,CAlFM;QAmFPD,SAAS,EAAE,SAASA,SAAT,CAAmBqB,KAAnB,EAA0B;UACnC,IAAI,CAAC,KAAK1C,iBAAN,IAA2B,KAAK4D,OAApC,EAA6C;YAC3C;UACD;;UACD,IAAIlB,KAAK,GAAG,CAAZ,EAAe;YACb,KAAKpB,gBAAL,GAAwB,CAAC,CAAzB;YACA;UACD;;UACD,IAAIoB,KAAK,IAAI,KAAKF,WAAL,CAAiB6G,MAA9B,EAAsC;YACpC3G,KAAK,GAAG,KAAKF,WAAL,CAAiB6G,MAAjB,GAA0B,CAAlC;UACD;;UACD,IAAIgB,UAAU,GAAG,KAAKpE,KAAL,CAAWzD,WAAX,CAAuBuD,GAAvB,CAA2BK,aAA3B,CAAyC,mCAAzC,CAAjB;UACA,IAAIkE,cAAc,GAAGD,UAAU,CAACE,gBAAX,CAA4B,sCAA5B,CAArB;UAEA,IAAIC,aAAa,GAAGF,cAAc,CAAC5H,KAAD,CAAlC;UACA,IAAI+H,SAAS,GAAGJ,UAAU,CAACI,SAA3B;UACA,IAAIC,SAAS,GAAGF,aAAa,CAACE,SAA9B;;UAEA,IAAIA,SAAS,GAAGF,aAAa,CAACG,YAA1B,GAAyCF,SAAS,GAAGJ,UAAU,CAACO,YAApE,EAAkF;YAChFP,UAAU,CAACI,SAAX,IAAwBD,aAAa,CAACG,YAAtC;UACD;;UACD,IAAID,SAAS,GAAGD,SAAhB,EAA2B;YACzBJ,UAAU,CAACI,SAAX,IAAwBD,aAAa,CAACG,YAAtC;UACD;;UACD,KAAKrJ,gBAAL,GAAwBoB,KAAxB;UACA,IAAI6G,MAAM,GAAG,KAAKC,QAAL,EAAb;UACAD,MAAM,CAAClD,YAAP,CAAoB,uBAApB,EAA6C,KAAKpG,EAAL,GAAU,QAAV,GAAqB,KAAKqB,gBAAvE;QACD,CA9GM;QA+GPkI,QAAQ,EAAE,SAASA,QAAT,GAAoB;UAC5B,OAAO,KAAKvD,KAAL,CAAW5F,KAAX,CAAiBmJ,QAAjB,EAAP;QACD;MAjHM,CA9F8D;MAiNvE3D,OAAO,EAAE,SAASA,OAAT,GAAmB;QAC1B,IAAIgF,MAAM,GAAG,IAAb;;QAEA,KAAKZ,gBAAL,GAAwB9G,gBAAgB,GAAG,KAAK0F,QAAR,EAAkB,KAAKe,OAAvB,CAAxC;QACA,KAAKpD,GAAL,CAAS,YAAT,EAAuB,UAAU/D,IAAV,EAAgB;UACrCoI,MAAM,CAAChI,MAAP,CAAcJ,IAAd;QACD,CAFD;QAGA,IAAI8G,MAAM,GAAG,KAAKC,QAAL,EAAb;QACAD,MAAM,CAAClD,YAAP,CAAoB,MAApB,EAA4B,SAA5B;QACAkD,MAAM,CAAClD,YAAP,CAAoB,mBAApB,EAAyC,MAAzC;QACAkD,MAAM,CAAClD,YAAP,CAAoB,eAApB,EAAqC,IAArC;QACAkD,MAAM,CAAClD,YAAP,CAAoB,uBAApB,EAA6C,KAAKpG,EAAL,GAAU,QAAV,GAAqB,KAAKqB,gBAAvE;MACD,CA7NsE;MA8NvEwJ,aAAa,EAAE,SAASA,aAAT,GAAyB;QACtC,KAAK7E,KAAL,CAAWzD,WAAX,CAAuBuI,QAAvB;MACD;IAhOsE,CAA5C,CAjbqC,CAmpBlE;;IACC;;IAA6B,IAAIC,wCAAwC,GAAI1D,oCAAhD,CAppBoC,CAqpBlE;;IAMA;;IAEA,IAAI2D,sBAAsB,GAAGvP,MAAM,CAACkL,mBAAmB,CAAC;IAAI;IAAL,CAApB,CAAN,CAC3BoE,wCAD2B,EAE3B3N,MAF2B,EAG3BC,eAH2B,EAI3B,KAJ2B,EAK3B,IAL2B,EAM3B,IAN2B,EAO3B,IAP2B,CAA7B;IAWA;;IACA,IAAI,KAAJ,EAAW;MAAE,IAAI4N,gBAAJ;IAAuB;;IACpCD,sBAAsB,CAACrN,OAAvB,CAA+BmJ,MAA/B,GAAwC,4CAAxC;IACA;;IAA6B,IAAIoE,YAAY,GAAIF,sBAAsB,CAACrQ,OAA3C,CA3qBqC,CA4qBlE;;IAGA;;IACAuQ,YAAY,CAACC,OAAb,GAAuB,UAAUC,GAAV,EAAe;MACpCA,GAAG,CAACxE,SAAJ,CAAcsE,YAAY,CAAC5P,IAA3B,EAAiC4P,YAAjC;IACD,CAFD;IAIA;;;IAA6B,IAAIG,qBAAqB,GAAGpO,mBAAmB,CAAC,SAAD,CAAnB,GAAkCiO,YAA9D;IAE7B;EAAO;EAEP;;AAh2BU,CAtFD,CADT"}]}