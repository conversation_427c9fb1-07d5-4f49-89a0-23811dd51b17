{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\general\\general.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\general\\general.vue", "mtime": 1752541695848}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA2GA;AACA;AACA;AACA;AACA;AACA,uE,CACA;;AACA;EACAA;IAAAC;EAAA,CADA;EAEAC,eAFA;;EAGAC;IACA;MACAC,+DADA;MAEAC,4EAFA;MAGAH,gDAHA;MAIAI,cAJA;MAKAC,aALA;MAMAC,iBANA;MAOAC,cAPA;MAQAC,gCARA;MASAC;IATA;EAWA,CAfA;;EAgBAC;IACA;IACA;MACAC;IACA,CAFA;EAGA,CArBA;;EAsBAC;IACAN;MACA;QACA;QACAO;MACA;IACA;;EANA,CAtBA;;EA8BAC;IACAH;EACA,CAhCA;;EAiCAI;IACA;MACAC,mBADA;MACA;MACAC,2BAFA;MAEA;MACAC,uBAHA;MAGA;MACAC,iCAJA;MAIA;MACAC,mCALA;MAMAC,6BANA;MAOAC,+BAPA;MAQAC,2BARA;MASAC;IATA;EAYA,CA9CA;;EA+CAC,wCA/CA;EAgDAC;IACAC;MACA;MACA;IACA,CAJA;;IAKAJ;MACA;MACA;QACAK;UACA;UACAC;UACA,wCAHA,CAGA;;UACA;UACAD;YACA;UACA,CAFA,EAEA,IAFA;QAGA,CARA,EAQA,IARA;MASA,CAVA;IAWA,CAlBA;;IAmBAJ;MACA;MACA;QACAI;UACA;UACAE;UACAF;YACA;;YACA;cACAG;YACA;UACA,CALA,EAKA,IALA;QAMA,CATA,EASA,IATA;MAUA,CAXA;IAYA,CAjCA;;IAkCA,sCAlCA;;IAmCAX;MACA;IACA,CArCA;;IAsCAC;MACA;IACA,CAxCA;;IAyCAW;MACA;;MACA;QACA;QACA;MACA;IACA,CA/CA;;IAgDAC;MACA;MACAC;MACAA;;MACA;QACAA;MACA,CAFA,MAEA;QACAA;MACA;;MACAC;MACAD;MACAC,iCAXA,CAYA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;;EAtEA;AAhDA", "names": ["components", "zyMenu", "name", "data", "user", "moduleName", "loading", "text", "officeShow", "pdfshow", "webOfficeTplPdf", "fontSize", "mounted", "window", "watch", "console", "destroyed", "provide", "newTab", "tabDelJump", "jumpMenu", "tabNameDelete", "loadingprovide", "loadingtext", "matchingMenu", "openoffice", "openPdf", "mixins", "methods", "fontSizeClick", "setTimeout", "OfficeDiv", "pdfDiv", "addins", "scrollMenu", "do<PERSON><PERSON><PERSON>", "elink", "document"], "sourceRoot": "src/views/general", "sources": ["general.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"general\"\r\n                v-loading=\"loading\"\r\n                :element-loading-text=\"text\">\r\n    <el-header class=\"general-header\"\r\n               height=\"100px\">\r\n      <div class=\"general-header-log-box\">\r\n        <div class=\"general-header-log\"></div>\r\n        <div class=\"general-header-text-box\">\r\n          <!-- <div class=\"general-header-name\">{{moduleName}}</div> -->\r\n          <div class=\"general-header-name\"></div>\r\n          <div class=\"general-header-module-name\">{{name}}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"general-header-operation\">\r\n        <el-popover placement=\"bottom\"\r\n                    width=\"152\"\r\n                    trigger=\"click\">\r\n          <div class=\"sizeSwitch\"\r\n               slot=\"reference\">\r\n            <template v-if=\"fontSize == 1\">超大号字</template>\r\n            <template v-if=\"fontSize == 2\">大号字</template>\r\n            <template v-if=\"fontSize == 3\">标准字号</template>\r\n            <i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n          </div>\r\n          <div class=\"sizeSwitchItem\"\r\n               @click=\"fontSizeClick(1)\"\r\n               :class=\"{sizeSwitchItemA:fontSize == 1}\">超大号字</div>\r\n          <div class=\"sizeSwitchItem\"\r\n               @click=\"fontSizeClick(2)\"\r\n               :class=\"{sizeSwitchItemA:fontSize == 2}\">大号字</div>\r\n          <div class=\"sizeSwitchItem\"\r\n               @click=\"fontSizeClick(3)\"\r\n               :class=\"{sizeSwitchItemA:fontSize == 3}\">标准字号</div>\r\n        </el-popover>\r\n\r\n        <div class=\"general-header-home\"\r\n             @click=\"returnClick\"></div>\r\n        <div class=\"general-header-help\"\r\n             v-if=\"helpShow.length\"\r\n             @click=\"help\"></div>\r\n        <div class=\"general-header-set\"\r\n             v-if=\"systemShow.length\"\r\n             @click=\"system\"></div>\r\n        <div class=\"general-header-user\"\r\n             v-if=\"user\">\r\n          <!-- <div class=\"general-header-user-name\">{{user.userName}}</div> -->\r\n          <div class=\"general-header-user-img\">\r\n            <img :src=\"user.headImg\"\r\n                 alt=\"\">\r\n          </div>\r\n        </div>\r\n        <div class=\"general-exit\"\r\n             @click=\"exit()\">退出</div>\r\n      </div>\r\n    </el-header>\r\n    <el-container class=\"general-container\">\r\n      <el-aside width=\"248px\"\r\n                class=\"general-aside\">\r\n        <zy-menu v-model=\"menuId\"\r\n                 :menu=\"menuData\"\r\n                 @select=\"menuSelect\"\r\n                 :props=\"{ children: 'children', label: 'name', id: 'menuId' ,to: 'to',icon:'iconUrl', isShow: 'isShow', showValue: true}\"></zy-menu>\r\n      </el-aside>\r\n      <el-main class=\"general-main\">\r\n        <div class=\"general-main-breadcrumb\">\r\n          <el-breadcrumb separator=\"/\">\r\n            <el-breadcrumb-item v-for=\"(item,index) in crumbsData\"\r\n                                @click.native=\"crumbsClcik(item,index)\"\r\n                                :to=\"{ path: item.to, query: item.params }\"\r\n                                :key=\"item.id\">{{item.name}}</el-breadcrumb-item>\r\n          </el-breadcrumb>\r\n        </div>\r\n        <div class=\"general-main-content scrollBar\">\r\n\r\n          <zy-pop-up v-model=\"officeShow\"\r\n                     title=\"文件预览\">\r\n            <div class=\"btnbox\">\r\n              <el-button type=\"danger\"\r\n                         @click=\"dowload('word')\">无法打开，请点击按钮</el-button>\r\n            </div>\r\n            <div id=\"OfficeDiv\"\r\n                 ref=\"OfficeDiv\"\r\n                 class=\"OfficeDiv\"\r\n                 style=\"width:1160px; height: 700px;\">\r\n            </div>\r\n          </zy-pop-up>\r\n          <zy-pop-up v-model=\"pdfshow\"\r\n                     title=\"文件预览\">\r\n            <div class=\"btnbox\">\r\n              <el-button type=\"danger\"\r\n                         @click=\"dowload('pdf')\">无法打开，请点击按钮</el-button>\r\n            </div>\r\n            <div id=\"pdfDiv\"\r\n                 ref=\"pdfDiv\"\r\n                 style=\"width:1160px; height: 700px;\">\r\n            </div>\r\n          </zy-pop-up>\r\n          <keep-alive :include=\"includes\">\r\n            <router-view :key=\"$route.fullPath\" />\r\n          </keep-alive>\r\n        </div>\r\n      </el-main>\r\n    </el-container>\r\n  </el-container>\r\n</template>\r\n<script>\r\nimport mixins from '../../mixins'\r\nimport mixinsGeneral from '../../mixins/general'\r\nimport { mapActions } from 'vuex'\r\nimport zyMenu from '../../components/zy-menu/zy-menu.vue'\r\nimport woffice from './office.js'\r\nimport webOfficeTplPdf from '../../../public/static/js/iWebPDF2018.js'\r\n// import { WebOpenUrlPdf } from '../../../public/static/js/PDF2018.js'\r\nexport default {\r\n  components: { zyMenu },\r\n  name: 'general',\r\n  data () {\r\n    return {\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())),\r\n      moduleName: JSON.parse(sessionStorage.getItem('generalName' + this.$logo())),\r\n      name: JSON.parse(sessionStorage.getItem('name')),\r\n      loading: false,\r\n      text: '正在加载中',\r\n      officeShow: false,\r\n      pdfshow: false,\r\n      webOfficeTplPdf: webOfficeTplPdf,\r\n      fontSize: 3\r\n    }\r\n  },\r\n  mounted () {\r\n    this.fontSize = JSON.parse(localStorage.getItem('fontSize')) || 3\r\n    this.$nextTick(() => {\r\n      window.addEventListener('scroll', this.scrollMenu, true)\r\n    })\r\n  },\r\n  watch: {\r\n    officeShow () {\r\n      if (!this.officeShow) {\r\n        this.webOfficeObj.WebClose()\r\n        console.log(this.webOfficeObj)\r\n      }\r\n    }\r\n  },\r\n  destroyed () {\r\n    window.removeEventListener('scroll', this.scrollMenu)\r\n  },\r\n  provide () {\r\n    return {\r\n      newTab: this.newTab, // 打开新一级面包屑方法，以前使用newTab打开tab页的页面不用改\r\n      tabDelJump: this.tabDelJump, // 关闭当前面包屑跳转到上一级面包屑方法，以前调用tabDelJump关闭tab页的不用改  （注意：不用传参）\r\n      jumpMenu: this.jumpMenu, // 没啥用，为了不然页面报错\r\n      tabNameDelete: this.tabNameDelete, // 也是关闭当前面包屑跳转到上一级面包屑方法，为了兼容以前页面调用的方法 （注意：不用传参）\r\n      loadingprovide: this.loadingprovide,\r\n      loadingtext: this.loadingtext,\r\n      matchingMenu: this.matchingMenu,\r\n      openoffice: this.openoffice,\r\n      openPdf: this.openPdf\r\n\r\n    }\r\n  },\r\n  mixins: [mixins, mixinsGeneral, woffice],\r\n  methods: {\r\n    fontSizeClick (type) {\r\n      this.fontSize = type\r\n      this.$fontSize(type)\r\n    },\r\n    openoffice (openUrl) {\r\n      this.officeShow = true\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          const OfficeDiv = this.$refs.OfficeDiv\r\n          OfficeDiv.innerHTML = this.webOfficeTpl\r\n          this.webOfficeObj = new WebOffice2015() // eslint-disable-line \r\n          this.webOfficeObj.setObj(document.getElementById('WebOffice2015'))\r\n          setTimeout(() => {\r\n            this.initWebOfficeObject(openUrl)\r\n          }, 1000)\r\n        }, 1000)\r\n      })\r\n    },\r\n    openPdf (openUrl) {\r\n      this.pdfshow = true\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          const pdfDiv = this.$refs.pdfDiv\r\n          pdfDiv.innerHTML = this.webOfficeTplPdf\r\n          setTimeout(() => {\r\n            const addins = document.getElementById('iWebPDF2018').iWebPDFFun\r\n            if (addins != null) {\r\n              addins.WebOpenUrlFile(openUrl)\r\n            }\r\n          }, 1000)\r\n        }, 1000)\r\n      })\r\n    },\r\n    ...mapActions('position', ['GET_top']),\r\n    loadingprovide (loading) {\r\n      this.loading = loading\r\n    },\r\n    loadingtext (text) {\r\n      this.text = text\r\n    },\r\n    scrollMenu (e) {\r\n      var arr = ['/activityNew']\r\n      if (arr.includes(this.$route.path)) {\r\n        var top = this.$refs.scrollMenuRef.scrollTop\r\n        this.GET_top(top)\r\n      }\r\n    },\r\n    dowload (type) {\r\n      const elink = document.createElement('a')\r\n      elink.style.display = 'none'\r\n      elink.href = ''\r\n      if (type === 'word') {\r\n        elink.href = './static/office2015/iWebOffice2015.msi'\r\n      } else {\r\n        elink.href = './static/pdf2018/iWebPDF2018.exe'\r\n      }\r\n      document.body.appendChild(elink)\r\n      elink.click()\r\n      document.body.removeChild(elink)\r\n      // const elink = document.createElement('a')\r\n      // elink.style.display = 'none'\r\n      // elink.download = '../../../public/static/office2015/msiexec.exe'\r\n      // if (type === 'word') {\r\n      // } else {\r\n      //   elink.download = '../../../public/static/pdf2018/iWebPDF2018.exe'\r\n      // }\r\n      // document.body.appendChild(elink)\r\n      // elink.click()\r\n      // document.body.removeChild(elink)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"./general.scss\";\r\n.btnbox {\r\n  text-align: right;\r\n  margin: 10px;\r\n  margin-right: 20px;\r\n  // .el-button {\r\n  //     font-size: $textSize16;\r\n  //     color: #fff;\r\n  //     background: #f56c6c;\r\n  // }\r\n}\r\n</style>\r\n"]}]}