{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tabs\\zy-tabs.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tabs\\zy-tabs.vue", "mtime": 1752541693597}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAsBA;EACAA,cADA;;EAEAC;IACA;MACAC,YADA;MAEAC,WAFA;MAGAC,SAHA;MAIAC,UAJA;MAKAC,sCALA;MAMAC;IANA;EAQA,CAXA;;EAYAC;IACAC,SADA;IAEAC;MACAC,WADA;MAEAC;IAFA,CAFA;IAMAC;MACAF,YADA;MAEAC;IAFA,CANA;IAUAJ;MACAG,YADA;MAEAC;IAFA;EAVA,CAZA;EA2BAE;IACAC,aADA;IAEAC;EAFA,CA3BA;;EA+BAC;IACA;EACA,CAjCA;;EAkCAC;IACA,oBADA,CAEA;;IACA;MACA;IACA,CAFA;;IAGAC;MACA;QACAA;QACA;MACA,CAHA;IAIA,CALA;EAMA,CA9CA;;EA+CAC;IACAX;MACA;QACA;UACA;QACA;MACA,CAJA;IAKA,CAPA;;IAQAC;MACA;IACA,CAVA;;IAWAR;MACA;QACA;UACA;YACA;UACA;QACA,CAJA;MAKA;IACA,CAnBA;;IAoBAI;MACA;QACA;QACA;QACA;QACAe;UACA;QACA,CAFA,EAEA,GAFA;MAGA;IACA;;EA7BA,CA/CA;EA8EAC;IACAC;MACA;MACA;MACAC;QACAC;;QACA;UACA;UACAA;QACA;MACA,CANA;MAOAJ;QACA;MACA,CAFA,EAEA,GAFA;IAGA,CAdA;;IAeAK;MACA;MACA;QACA;UAAA;UACA;YACAF;UACA;QACA,CAJA,MAIA;UACA;YACAA;UACA;QACA;MACA,CAVA;MAWA;IACA,CA7BA;;IA8BAG;MACA;MACA;;MACA;QACA;QACA;MACA,CAHA,MAGA;QACA;MACA;IACA,CAvCA;;IAwCAC;MACA;MACA;;MACA;QACAxB;MACA;;MACAyB;MACA;IACA,CAhDA;;IAiDAC;MACA;MACA;;MACA;QACA1B;MACA;;MACAyB;MACA;IACA,CAzDA;;IA0DAE;MACA;MACA;MACA;;MACA;QACAF;QACAA;;QACA;UACA;UACAA;QACA,CAHA,MAGA;UACA;UACAA;QACA,CAHA,MAGA;UACA;YACA;UACA,CAFA,MAEA;YACA;UACA;;UACAA;QACA;MACA;IACA,CAhFA;;IAiFAG;MACA;MACA;IACA,CApFA;;IAqFAC;MACAC;QACA;UAAA;UACAT;QACA;;QACA;UAAA;UACAA;QACA;;QACA;UAAA;UACAA;QACA;;QACA;UAAA;UACAA;QACA;;QACA;UACAA;QACA;MACA,CAhBA;IAiBA,CAvGA;;IAwGAU;MACA;MACA;MACA;MACA;;MACA;QACAC;MACA,CAFA,MAEA;QACAA;MACA,CAFA,MAEA;QACA;MACA;;MACA;QACA;UACAA;QACA;;QACA;MACA,CALA,MAKA;QACA;UACAA;QACA;;QACA;MACA;IACA,CA/HA;;IAgIAzB;MACA;MACA;QACA,6BADA;QAEA,2BAFA;QAGA,2BAHA;QAIA,+BAJA;QAKA,yBALA;QAMA,uBANA;QAOA,2BAPA;QAQA,iCARA;QASA,uBATA;QAUA;MAVA;MAYA;IACA;;EA/IA,CA9EA;;EA+NA0B;IACAlB;EACA;;AAjOA", "names": ["name", "data", "tabsData", "show", "offset", "biggest", "screenWidth", "timer", "props", "value", "tabsList", "type", "default", "shift", "model", "prop", "event", "created", "mounted", "window", "watch", "setTimeout", "methods", "selected", "arr", "item", "selectedClick", "biggestClick", "tabsLeft", "itemBox", "tabsRight", "tabsBox", "tabsCopyData", "initData", "items", "deepCopy", "o", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "src/components/zy-tabs", "sources": ["zy-tabs.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-tabs\">\r\n    <div class=\"zy-tabs-left\"\r\n         v-if=\"show&&offset>0||offset!=0\"\r\n         @click.stop=\"tabsLeft\"><i class=\"el-icon-d-arrow-left\"></i></div>\r\n    <div class=\"zy-tabs-right\"\r\n         v-if=\"show&&offset<biggest\"\r\n         @click.stop=\"tabsRight\"><i class=\"el-icon-d-arrow-right\"></i></div>\r\n    <div class=\"zy-tabs-box\">\r\n      <div class=\"zy-tabs-item-list\">\r\n        <div :class=\"['zy-tabs-item',item.class?'zy-tabs-item-active':'']\"\r\n             v-for=\"(item, index) in tabsData\"\r\n             :key=\"index\"\r\n             @click=\"selected(item)\">\r\n          <div class=\"zy-tabs-item-number\">{{item.number}}</div>\r\n          <div class=\"zy-tabs-item-text\">{{item.name}}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyTabs',\r\n  data () {\r\n    return {\r\n      tabsData: [],\r\n      show: false,\r\n      offset: 0,\r\n      biggest: 0,\r\n      screenWidth: document.body.clientWidth,\r\n      timer: false\r\n    }\r\n  },\r\n  props: {\r\n    value: {},\r\n    tabsList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    shift: {\r\n      type: Number,\r\n      default: 168\r\n    },\r\n    props: {\r\n      type: Object,\r\n      default: () => { }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  created () {\r\n    this.tabsCopyData(this.deepCopy(this.tabsList))\r\n  },\r\n  mounted () {\r\n    this.biggestClick()\r\n    // 绑定onresize事件 监听屏幕变化设置宽\r\n    this.$nextTick(() => {\r\n      this.screenWidth = document.body.clientWidth\r\n    })\r\n    window.onresize = () => {\r\n      return (() => {\r\n        window.screenWidth = document.body.clientWidth\r\n        this.screenWidth = window.screenWidth\r\n      })()\r\n    }\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      this.tabsData.forEach(item => {\r\n        if (item.id === val) {\r\n          this.selected(item)\r\n        }\r\n      })\r\n    },\r\n    tabsList (val) {\r\n      this.tabsCopyData(this.deepCopy(this.tabsList))\r\n    },\r\n    tabsData (val) {\r\n      if (val.length) {\r\n        this.tabsData.forEach(item => {\r\n          if (item.id === this.value) {\r\n            this.selected(item)\r\n          }\r\n        })\r\n      }\r\n    },\r\n    screenWidth (val) {\r\n      if (!this.timer) {\r\n        this.screenWidth = val\r\n        this.timer = true\r\n        this.biggestClick()\r\n        setTimeout(() => {\r\n          this.timer = false\r\n        }, 400)\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    selected (data) {\r\n      this.selectedClick(data)\r\n      var arr = this.tabsData\r\n      arr.forEach(item => {\r\n        item.class = false\r\n        if (item.id === data.id) {\r\n          this.$emit('id', item.id)\r\n          item.class = true\r\n        }\r\n      })\r\n      setTimeout(() => {\r\n        this.tabsBox()\r\n      }, 300)\r\n    },\r\n    selectedClick (data) {\r\n      var arr = []\r\n      this.tabsList.forEach((item, index) => {\r\n        if (!JSON.stringify(this.props) == '{}') {// eslint-disable-line\r\n          if (data.id === item[this.props.id]) {\r\n            arr = item\r\n          }\r\n        } else {\r\n          if (data.id === item.id) {\r\n            arr = item\r\n          }\r\n        }\r\n      })\r\n      this.$emit('tabs-click', arr)\r\n    },\r\n    biggestClick () {\r\n      var tabBox = document.querySelector('.zy-tabs-box')\r\n      var itemBox = document.querySelector('.zy-tabs-item-list')\r\n      if (itemBox.offsetWidth > tabBox.offsetWidth) {\r\n        this.show = true\r\n        this.biggest = itemBox.offsetWidth - tabBox.offsetWidth\r\n      } else {\r\n        this.show = false\r\n      }\r\n    },\r\n    tabsLeft () {\r\n      var itemBox = document.querySelector('.zy-tabs-item-list')\r\n      var offset = this.offset - this.shift\r\n      if (this.offset - this.shift <= 0) {\r\n        offset = 0\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      this.offset = offset\r\n    },\r\n    tabsRight () {\r\n      var itemBox = document.querySelector('.zy-tabs-item-list')\r\n      var offset = this.offset + this.shift\r\n      if (this.biggest < this.offset + this.shift) {\r\n        offset = this.biggest\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      this.offset = offset\r\n    },\r\n    tabsBox () {\r\n      var tabBox = document.querySelector('.zy-tabs-box')\r\n      var itemBox = document.querySelector('.zy-tabs-item-list')\r\n      var item = document.querySelector('.zy-tabs-item-active')\r\n      if (tabBox.offsetWidth < itemBox.offsetWidth) {\r\n        itemBox.style.transform = 'translateX(0px)'\r\n        itemBox.style.transitionDuration = '.4s'\r\n        if (itemBox.offsetWidth === item.offsetLeft + item.offsetWidth) {\r\n          this.offset = itemBox.offsetWidth - tabBox.offsetWidth\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`\r\n        } else if (tabBox.offsetWidth / 2 > itemBox.offsetWidth - item.offsetLeft) {\r\n          this.offset = itemBox.offsetWidth - tabBox.offsetWidth\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`\r\n        } else {\r\n          if (item.offsetLeft - tabBox.offsetWidth / 2 < 0) {\r\n            this.offset = 0\r\n          } else {\r\n            this.offset = item.offsetLeft - tabBox.offsetWidth / 2\r\n          }\r\n          itemBox.style.transform = `translateX(-${item.offsetLeft - tabBox.offsetWidth / 2}px)`\r\n        }\r\n      }\r\n    },\r\n    tabsCopyData (data) {\r\n      this.initData(data)\r\n      this.tabsData = data\r\n    },\r\n    initData (items) {\r\n      items.forEach((item, index) => {\r\n        if ((typeof item.id) === 'undefined') { // eslint-disable-line\r\n          item.id = item[this.props.id]\r\n        }\r\n        if ((typeof item.name) === 'undefined') { // eslint-disable-line\r\n          item.name = item[this.props.name]\r\n        }\r\n        if ((typeof item.number) === 'undefined') { // eslint-disable-line\r\n          item.number = item[this.props.number]\r\n        }\r\n        if ((typeof item.class) === 'undefined') { // eslint-disable-line\r\n          item.class = false\r\n        }\r\n        if (this.tabId === item.id) {\r\n          item.class = true\r\n        }\r\n      })\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    }\r\n  },\r\n  beforeDestroy () {\r\n    window.onresize = null\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-tabs.scss\";\r\n</style>\r\n"]}]}