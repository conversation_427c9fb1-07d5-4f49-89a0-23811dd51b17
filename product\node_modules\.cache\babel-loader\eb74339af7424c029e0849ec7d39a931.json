{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu\\zy-menu-children.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu\\zy-menu-children.vue", "mtime": 1752541693535}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICd6eU1lbnVDaGlsZHJlbicsCiAgcHJvcHM6IHsKICAgIHZhbHVlOiBbU3RyaW5nLCBOdW1iZXIsIEFycmF5LCBPYmplY3RdLAogICAgbWVudTogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0sCiAgICBsZXZlbDogewogICAgICB0eXBlOiBOdW1iZXIsCiAgICAgIGRlZmF1bHQ6IDEKICAgIH0sCiAgICAvLyDmoJHnu5PmnoTphY3nva4KICAgIHByb3BzOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogKCkgPT4gewogICAgICAgIHJldHVybiB7CiAgICAgICAgICBjaGlsZHJlbjogJ2NoaWxkcmVuJywKICAgICAgICAgIGxhYmVsOiAnbGFiZWwnLAogICAgICAgICAgaWQ6ICdpZCcsCiAgICAgICAgICB0bzogJ3RvJywKICAgICAgICAgIGlzU2hvdzogJ2lzU2hvdycsCiAgICAgICAgICBzaG93VmFsdWU6IHRydWUKICAgICAgICB9OwogICAgICB9CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBpc1Nob3dDaGlsZHJlbihtZW51KSB7CiAgICAgIGxldCBpc1Nob3cgPSBmYWxzZTsKCiAgICAgIGlmIChtZW51W3RoaXMucHJvcHMuY2hpbGRyZW5dLmxlbmd0aCkgewogICAgICAgIGlzU2hvdyA9IG1lbnVbdGhpcy5wcm9wcy5jaGlsZHJlbl0uc29tZShpdGVtID0+IGl0ZW1bdGhpcy5wcm9wcy5pc1Nob3ddID09PSB0aGlzLnByb3BzLnNob3dWYWx1ZSk7CiAgICAgIH0KCiAgICAgIHJldHVybiBpc1Nob3c7CiAgICB9CgogIH0KfTs="}, {"version": 3, "mappings": "AAiCA;EACAA,sBADA;EAEAC;IACAC,sCADA;IAEAC;MACAC,WADA;MAEAC;IAFA,CAFA;IAMAC;MACAF,YADA;MAEAC;IAFA,CANA;IAUA;IACAJ;MACAG,YADA;MAEAC;QACA;UACAE,oBADA;UAEAC,cAFA;UAGAC,QAHA;UAIAC,QAJA;UAKAC,gBALA;UAMAC;QANA;MAQA;IAXA;EAXA,CAFA;EA2BAC;IACAC;MACA;;MACA;QACAH;MACA;;MACA;IACA;;EAPA;AA3BA", "names": ["name", "props", "value", "menu", "type", "default", "level", "children", "label", "id", "to", "isShow", "showValue", "methods", "isShowChildren"], "sourceRoot": "src/components/zy-menu", "sources": ["zy-menu-children.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div v-for=\"(item) in menu\"\r\n         :key=\"item[props.id]\">\r\n      <el-submenu :index=\"item[props.id]\"\r\n                  v-if=\"isShowChildren(item)\">\r\n        <template #title>\r\n          <div class=\"zy-menu-icon\"\r\n               v-if=\"level==1\">\r\n            <img :src=\"item[props.icon]\"\r\n                 alt=\"\">\r\n          </div>\r\n          <span :class=\"[level==1?'menu-color':'']\">{{item[props.label]}}</span>\r\n        </template>\r\n        <zy-menu-children :menu=\"item[props.children]\"\r\n                          :level=\"level+1\"\r\n                          :value=\"value\"\r\n                          :props=\"props\"></zy-menu-children>\r\n      </el-submenu>\r\n      <el-menu-item :index=\"item[props.id]\"\r\n                    v-else>\r\n        <div class=\"zy-menu-icon\"\r\n             v-if=\"level==1\">\r\n          <img :src=\"item[props.icon]\"\r\n               alt=\"\">\r\n        </div>\r\n        <span slot=\"title\"\r\n              :class=\"[level==1?'menu-color':'']\">{{item[props.label]}}</span>\r\n      </el-menu-item>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyMenuChildren',\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    menu: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    level: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label',\r\n          id: 'id',\r\n          to: 'to',\r\n          isShow: 'isShow',\r\n          showValue: true\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    isShowChildren (menu) {\r\n      let isShow = false\r\n      if (menu[this.props.children].length) {\r\n        isShow = menu[this.props.children].some(item => item[this.props.isShow] === this.props.showValue)\r\n      }\r\n      return isShow\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}