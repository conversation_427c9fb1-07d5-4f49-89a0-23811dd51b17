{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\element-ui\\lib\\popover.js", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\element-ui\\lib\\popover.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["module", "exports", "modules", "installedModules", "__webpack_require__", "moduleId", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "getDefault", "getModuleExports", "object", "property", "prototype", "hasOwnProperty", "p", "s", "__webpack_exports__", "normalizeComponent", "scriptExports", "render", "staticRenderFns", "functionalTemplate", "injectStyles", "scopeId", "moduleIdentifier", "shadowMode", "options", "_compiled", "functional", "_scopeId", "hook", "context", "$vnode", "ssrContext", "parent", "__VUE_SSR_CONTEXT__", "_registeredComponents", "add", "_ssrRegister", "$root", "$options", "shadowRoot", "_injectStyles", "originalRender", "renderWithStyleInjection", "h", "existing", "beforeCreate", "concat", "require", "_vm", "_h", "$createElement", "_c", "_self", "attrs", "transition", "on", "handleAfterEnter", "handleAfterLeave", "directives", "rawName", "disabled", "showPopper", "expression", "ref", "staticClass", "class", "popperClass", "content", "style", "width", "role", "id", "tooltipId", "title", "domProps", "textContent", "_s", "_e", "_t", "_v", "_withStripped", "vue_popper_", "vue_popper_default", "dom_", "util_", "mainvue_type_script_lang_js_", "mixins", "a", "props", "trigger", "type", "String", "default", "validator", "indexOf", "openDelay", "Number", "close<PERSON><PERSON><PERSON>", "Boolean", "reference", "visibleArrow", "arrowOffset", "tabindex", "computed", "watch", "val", "$emit", "mounted", "_this", "referenceElm", "$refs", "popper", "wrapper", "children", "setAttribute", "handleFocus", "instance", "__vue__", "focus", "handleBlur", "handleKeydown", "handleClick", "doToggle", "document", "handleDocumentClick", "handleMouseEnter", "handleMouseLeave", "console", "warn", "querySelector", "doShow", "doClose", "<PERSON><PERSON><PERSON><PERSON>", "cleanup", "deactivated", "methods", "_this2", "clearTimeout", "_timer", "setTimeout", "ev", "keyCode", "_this3", "e", "$el", "contains", "target", "do<PERSON><PERSON>roy", "destroyed", "src_mainvue_type_script_lang_js_", "componentNormalizer", "component", "api", "__file", "main", "getReference", "el", "binding", "vnode", "_ref", "arg", "Array", "isArray", "directive", "inserted", "external_vue_", "external_vue_default", "install", "<PERSON><PERSON>", "popover"], "sources": ["D:/zy/xm/pc/qdzx/product/node_modules/element-ui/lib/popover.js"], "sourcesContent": ["module.exports =\n/******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__webpack_require__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__webpack_require__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __webpack_require__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__webpack_require__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"/dist/\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 77);\n/******/ })\n/************************************************************************/\n/******/ ({\n\n/***/ 0:\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"a\", function() { return normalizeComponent; });\n/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nfunction normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode /* vue-cli only */\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () { injectStyles.call(this, this.$root.$options.shadowRoot) }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functioal component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n\n\n/***/ }),\n\n/***/ 2:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/dom\");\n\n/***/ }),\n\n/***/ 3:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/util\");\n\n/***/ }),\n\n/***/ 5:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/vue-popper\");\n\n/***/ }),\n\n/***/ 7:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"vue\");\n\n/***/ }),\n\n/***/ 77:\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n\n// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./packages/popover/src/main.vue?vue&type=template&id=52060272&\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"span\",\n    [\n      _c(\n        \"transition\",\n        {\n          attrs: { name: _vm.transition },\n          on: {\n            \"after-enter\": _vm.handleAfterEnter,\n            \"after-leave\": _vm.handleAfterLeave\n          }\n        },\n        [\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: !_vm.disabled && _vm.showPopper,\n                  expression: \"!disabled && showPopper\"\n                }\n              ],\n              ref: \"popper\",\n              staticClass: \"el-popover el-popper\",\n              class: [_vm.popperClass, _vm.content && \"el-popover--plain\"],\n              style: { width: _vm.width + \"px\" },\n              attrs: {\n                role: \"tooltip\",\n                id: _vm.tooltipId,\n                \"aria-hidden\":\n                  _vm.disabled || !_vm.showPopper ? \"true\" : \"false\"\n              }\n            },\n            [\n              _vm.title\n                ? _c(\"div\", {\n                    staticClass: \"el-popover__title\",\n                    domProps: { textContent: _vm._s(_vm.title) }\n                  })\n                : _vm._e(),\n              _vm._t(\"default\", [_vm._v(_vm._s(_vm.content))])\n            ],\n            2\n          )\n        ]\n      ),\n      _c(\n        \"span\",\n        { ref: \"wrapper\", staticClass: \"el-popover__reference-wrapper\" },\n        [_vm._t(\"reference\")],\n        2\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\n\n// CONCATENATED MODULE: ./packages/popover/src/main.vue?vue&type=template&id=52060272&\n\n// EXTERNAL MODULE: external \"element-ui/lib/utils/vue-popper\"\nvar vue_popper_ = __webpack_require__(5);\nvar vue_popper_default = /*#__PURE__*/__webpack_require__.n(vue_popper_);\n\n// EXTERNAL MODULE: external \"element-ui/lib/utils/dom\"\nvar dom_ = __webpack_require__(2);\n\n// EXTERNAL MODULE: external \"element-ui/lib/utils/util\"\nvar util_ = __webpack_require__(3);\n\n// CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/popover/src/main.vue?vue&type=script&lang=js&\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\n\n\n\n\n/* harmony default export */ var mainvue_type_script_lang_js_ = ({\n  name: 'ElPopover',\n\n  mixins: [vue_popper_default.a],\n\n  props: {\n    trigger: {\n      type: String,\n      default: 'click',\n      validator: function validator(value) {\n        return ['click', 'focus', 'hover', 'manual'].indexOf(value) > -1;\n      }\n    },\n    openDelay: {\n      type: Number,\n      default: 0\n    },\n    closeDelay: {\n      type: Number,\n      default: 200\n    },\n    title: String,\n    disabled: Boolean,\n    content: String,\n    reference: {},\n    popperClass: String,\n    width: {},\n    visibleArrow: {\n      default: true\n    },\n    arrowOffset: {\n      type: Number,\n      default: 0\n    },\n    transition: {\n      type: String,\n      default: 'fade-in-linear'\n    },\n    tabindex: {\n      type: Number,\n      default: 0\n    }\n  },\n\n  computed: {\n    tooltipId: function tooltipId() {\n      return 'el-popover-' + Object(util_[\"generateId\"])();\n    }\n  },\n  watch: {\n    showPopper: function showPopper(val) {\n      if (this.disabled) {\n        return;\n      }\n      val ? this.$emit('show') : this.$emit('hide');\n    }\n  },\n\n  mounted: function mounted() {\n    var _this = this;\n\n    var reference = this.referenceElm = this.reference || this.$refs.reference;\n    var popper = this.popper || this.$refs.popper;\n\n    if (!reference && this.$refs.wrapper.children) {\n      reference = this.referenceElm = this.$refs.wrapper.children[0];\n    }\n    // 可访问性\n    if (reference) {\n      Object(dom_[\"addClass\"])(reference, 'el-popover__reference');\n      reference.setAttribute('aria-describedby', this.tooltipId);\n      reference.setAttribute('tabindex', this.tabindex); // tab序列\n      popper.setAttribute('tabindex', 0);\n\n      if (this.trigger !== 'click') {\n        Object(dom_[\"on\"])(reference, 'focusin', function () {\n          _this.handleFocus();\n          var instance = reference.__vue__;\n          if (instance && typeof instance.focus === 'function') {\n            instance.focus();\n          }\n        });\n        Object(dom_[\"on\"])(popper, 'focusin', this.handleFocus);\n        Object(dom_[\"on\"])(reference, 'focusout', this.handleBlur);\n        Object(dom_[\"on\"])(popper, 'focusout', this.handleBlur);\n      }\n      Object(dom_[\"on\"])(reference, 'keydown', this.handleKeydown);\n      Object(dom_[\"on\"])(reference, 'click', this.handleClick);\n    }\n    if (this.trigger === 'click') {\n      Object(dom_[\"on\"])(reference, 'click', this.doToggle);\n      Object(dom_[\"on\"])(document, 'click', this.handleDocumentClick);\n    } else if (this.trigger === 'hover') {\n      Object(dom_[\"on\"])(reference, 'mouseenter', this.handleMouseEnter);\n      Object(dom_[\"on\"])(popper, 'mouseenter', this.handleMouseEnter);\n      Object(dom_[\"on\"])(reference, 'mouseleave', this.handleMouseLeave);\n      Object(dom_[\"on\"])(popper, 'mouseleave', this.handleMouseLeave);\n    } else if (this.trigger === 'focus') {\n      if (this.tabindex < 0) {\n        console.warn('[Element Warn][Popover]a negative taindex means that the element cannot be focused by tab key');\n      }\n      if (reference.querySelector('input, textarea')) {\n        Object(dom_[\"on\"])(reference, 'focusin', this.doShow);\n        Object(dom_[\"on\"])(reference, 'focusout', this.doClose);\n      } else {\n        Object(dom_[\"on\"])(reference, 'mousedown', this.doShow);\n        Object(dom_[\"on\"])(reference, 'mouseup', this.doClose);\n      }\n    }\n  },\n  beforeDestroy: function beforeDestroy() {\n    this.cleanup();\n  },\n  deactivated: function deactivated() {\n    this.cleanup();\n  },\n\n\n  methods: {\n    doToggle: function doToggle() {\n      this.showPopper = !this.showPopper;\n    },\n    doShow: function doShow() {\n      this.showPopper = true;\n    },\n    doClose: function doClose() {\n      this.showPopper = false;\n    },\n    handleFocus: function handleFocus() {\n      Object(dom_[\"addClass\"])(this.referenceElm, 'focusing');\n      if (this.trigger === 'click' || this.trigger === 'focus') this.showPopper = true;\n    },\n    handleClick: function handleClick() {\n      Object(dom_[\"removeClass\"])(this.referenceElm, 'focusing');\n    },\n    handleBlur: function handleBlur() {\n      Object(dom_[\"removeClass\"])(this.referenceElm, 'focusing');\n      if (this.trigger === 'click' || this.trigger === 'focus') this.showPopper = false;\n    },\n    handleMouseEnter: function handleMouseEnter() {\n      var _this2 = this;\n\n      clearTimeout(this._timer);\n      if (this.openDelay) {\n        this._timer = setTimeout(function () {\n          _this2.showPopper = true;\n        }, this.openDelay);\n      } else {\n        this.showPopper = true;\n      }\n    },\n    handleKeydown: function handleKeydown(ev) {\n      if (ev.keyCode === 27 && this.trigger !== 'manual') {\n        // esc\n        this.doClose();\n      }\n    },\n    handleMouseLeave: function handleMouseLeave() {\n      var _this3 = this;\n\n      clearTimeout(this._timer);\n      if (this.closeDelay) {\n        this._timer = setTimeout(function () {\n          _this3.showPopper = false;\n        }, this.closeDelay);\n      } else {\n        this.showPopper = false;\n      }\n    },\n    handleDocumentClick: function handleDocumentClick(e) {\n      var reference = this.reference || this.$refs.reference;\n      var popper = this.popper || this.$refs.popper;\n\n      if (!reference && this.$refs.wrapper.children) {\n        reference = this.referenceElm = this.$refs.wrapper.children[0];\n      }\n      if (!this.$el || !reference || this.$el.contains(e.target) || reference.contains(e.target) || !popper || popper.contains(e.target)) return;\n      this.showPopper = false;\n    },\n    handleAfterEnter: function handleAfterEnter() {\n      this.$emit('after-enter');\n    },\n    handleAfterLeave: function handleAfterLeave() {\n      this.$emit('after-leave');\n      this.doDestroy();\n    },\n    cleanup: function cleanup() {\n      if (this.openDelay || this.closeDelay) {\n        clearTimeout(this._timer);\n      }\n    }\n  },\n\n  destroyed: function destroyed() {\n    var reference = this.reference;\n\n    Object(dom_[\"off\"])(reference, 'click', this.doToggle);\n    Object(dom_[\"off\"])(reference, 'mouseup', this.doClose);\n    Object(dom_[\"off\"])(reference, 'mousedown', this.doShow);\n    Object(dom_[\"off\"])(reference, 'focusin', this.doShow);\n    Object(dom_[\"off\"])(reference, 'focusout', this.doClose);\n    Object(dom_[\"off\"])(reference, 'mousedown', this.doShow);\n    Object(dom_[\"off\"])(reference, 'mouseup', this.doClose);\n    Object(dom_[\"off\"])(reference, 'mouseleave', this.handleMouseLeave);\n    Object(dom_[\"off\"])(reference, 'mouseenter', this.handleMouseEnter);\n    Object(dom_[\"off\"])(document, 'click', this.handleDocumentClick);\n  }\n});\n// CONCATENATED MODULE: ./packages/popover/src/main.vue?vue&type=script&lang=js&\n /* harmony default export */ var src_mainvue_type_script_lang_js_ = (mainvue_type_script_lang_js_); \n// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js\nvar componentNormalizer = __webpack_require__(0);\n\n// CONCATENATED MODULE: ./packages/popover/src/main.vue\n\n\n\n\n\n/* normalize component */\n\nvar component = Object(componentNormalizer[\"a\" /* default */])(\n  src_mainvue_type_script_lang_js_,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (false) { var api; }\ncomponent.options.__file = \"packages/popover/src/main.vue\"\n/* harmony default export */ var main = (component.exports);\n// CONCATENATED MODULE: ./packages/popover/src/directive.js\nvar getReference = function getReference(el, binding, vnode) {\n  var _ref = binding.expression ? binding.value : binding.arg;\n  var popper = vnode.context.$refs[_ref];\n  if (popper) {\n    if (Array.isArray(popper)) {\n      popper[0].$refs.reference = el;\n    } else {\n      popper.$refs.reference = el;\n    }\n  }\n};\n\n/* harmony default export */ var directive = ({\n  bind: function bind(el, binding, vnode) {\n    getReference(el, binding, vnode);\n  },\n  inserted: function inserted(el, binding, vnode) {\n    getReference(el, binding, vnode);\n  }\n});\n// EXTERNAL MODULE: external \"vue\"\nvar external_vue_ = __webpack_require__(7);\nvar external_vue_default = /*#__PURE__*/__webpack_require__.n(external_vue_);\n\n// CONCATENATED MODULE: ./packages/popover/index.js\n\n\n\n\nexternal_vue_default.a.directive('popover', directive);\n\n/* istanbul ignore next */\nmain.install = function (Vue) {\n  Vue.directive('popover', directive);\n  Vue.component(main.name, main);\n};\nmain.directive = directive;\n\n/* harmony default export */ var popover = __webpack_exports__[\"default\"] = (main);\n\n/***/ })\n\n/******/ });"], "mappings": "AAAAA,MAAM,CAACC,OAAP;AACA;AAAU,UAASC,OAAT,EAAkB;EAAE;;EAC9B;EAAU;;EACV;EAAU,IAAIC,gBAAgB,GAAG,EAAvB;EACV;;EACA;EAAU;;EACV;;EAAU,SAASC,mBAAT,CAA6BC,QAA7B,EAAuC;IACjD;;IACA;IAAW;;IACX;IAAW,IAAGF,gBAAgB,CAACE,QAAD,CAAnB,EAA+B;MAC1C;MAAY,OAAOF,gBAAgB,CAACE,QAAD,CAAhB,CAA2BJ,OAAlC;MACZ;IAAY;IACZ;IAAW;;IACX;;;IAAW,IAAID,MAAM,GAAGG,gBAAgB,CAACE,QAAD,CAAhB,GAA6B;MACrD;MAAYC,CAAC,EAAED,QADsC;;MAErD;MAAYE,CAAC,EAAE,KAFsC;;MAGrD;MAAYN,OAAO,EAAE;MACrB;;IAJqD,CAA1C;IAKX;;IACA;IAAW;;IACX;;IAAWC,OAAO,CAACG,QAAD,CAAP,CAAkBG,IAAlB,CAAuBR,MAAM,CAACC,OAA9B,EAAuCD,MAAvC,EAA+CA,MAAM,CAACC,OAAtD,EAA+DG,mBAA/D;IACX;;IACA;IAAW;;IACX;;IAAWJ,MAAM,CAACO,CAAP,GAAW,IAAX;IACX;;IACA;IAAW;;IACX;;IAAW,OAAOP,MAAM,CAACC,OAAd;IACX;EAAW;EACX;;EACA;;EACA;EAAU;;EACV;;;EAAUG,mBAAmB,CAACK,CAApB,GAAwBP,OAAxB;EACV;;EACA;EAAU;;EACV;;EAAUE,mBAAmB,CAACM,CAApB,GAAwBP,gBAAxB;EACV;;EACA;EAAU;;EACV;;EAAUC,mBAAmB,CAACO,CAApB,GAAwB,UAASV,OAAT,EAAkBW,IAAlB,EAAwBC,MAAxB,EAAgC;IAClE;IAAW,IAAG,CAACT,mBAAmB,CAACU,CAApB,CAAsBb,OAAtB,EAA+BW,IAA/B,CAAJ,EAA0C;MACrD;MAAYG,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+BW,IAA/B,EAAqC;QAAEK,UAAU,EAAE,IAAd;QAAoBC,GAAG,EAAEL;MAAzB,CAArC;MACZ;IAAY;IACZ;;EAAW,CAJD;EAKV;;EACA;EAAU;;EACV;;;EAAUT,mBAAmB,CAACe,CAApB,GAAwB,UAASlB,OAAT,EAAkB;IACpD;IAAW,IAAG,OAAOmB,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACC,WAA3C,EAAwD;MACnE;MAAYN,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+BmB,MAAM,CAACC,WAAtC,EAAmD;QAAEC,KAAK,EAAE;MAAT,CAAnD;MACZ;IAAY;IACZ;;;IAAWP,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+B,YAA/B,EAA6C;MAAEqB,KAAK,EAAE;IAAT,CAA7C;IACX;EAAW,CALD;EAMV;;EACA;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;;;EAAUlB,mBAAmB,CAACmB,CAApB,GAAwB,UAASD,KAAT,EAAgBE,IAAhB,EAAsB;IACxD;IAAW,IAAGA,IAAI,GAAG,CAAV,EAAaF,KAAK,GAAGlB,mBAAmB,CAACkB,KAAD,CAA3B;IACxB;;IAAW,IAAGE,IAAI,GAAG,CAAV,EAAa,OAAOF,KAAP;IACxB;;IAAW,IAAIE,IAAI,GAAG,CAAR,IAAc,OAAOF,KAAP,KAAiB,QAA/B,IAA2CA,KAA3C,IAAoDA,KAAK,CAACG,UAA7D,EAAyE,OAAOH,KAAP;IACpF;;IAAW,IAAII,EAAE,GAAGX,MAAM,CAACY,MAAP,CAAc,IAAd,CAAT;IACX;;IAAWvB,mBAAmB,CAACe,CAApB,CAAsBO,EAAtB;IACX;;;IAAWX,MAAM,CAACC,cAAP,CAAsBU,EAAtB,EAA0B,SAA1B,EAAqC;MAAET,UAAU,EAAE,IAAd;MAAoBK,KAAK,EAAEA;IAA3B,CAArC;IACX;;IAAW,IAAGE,IAAI,GAAG,CAAP,IAAY,OAAOF,KAAP,IAAgB,QAA/B,EAAyC,KAAI,IAAIM,GAAR,IAAeN,KAAf,EAAsBlB,mBAAmB,CAACO,CAApB,CAAsBe,EAAtB,EAA0BE,GAA1B,EAA+B,UAASA,GAAT,EAAc;MAAE,OAAON,KAAK,CAACM,GAAD,CAAZ;IAAoB,CAApC,CAAqCC,IAArC,CAA0C,IAA1C,EAAgDD,GAAhD,CAA/B;IAC1E;;IAAW,OAAOF,EAAP;IACX;EAAW,CATD;EAUV;;EACA;EAAU;;EACV;;;EAAUtB,mBAAmB,CAAC0B,CAApB,GAAwB,UAAS9B,MAAT,EAAiB;IACnD;IAAW,IAAIa,MAAM,GAAGb,MAAM,IAAIA,MAAM,CAACyB,UAAjB;IACxB;IAAY,SAASM,UAAT,GAAsB;MAAE,OAAO/B,MAAM,CAAC,SAAD,CAAb;IAA2B,CADvC;IAExB;IAAY,SAASgC,gBAAT,GAA4B;MAAE,OAAOhC,MAAP;IAAgB,CAF/C;IAGX;;IAAWI,mBAAmB,CAACO,CAApB,CAAsBE,MAAtB,EAA8B,GAA9B,EAAmCA,MAAnC;IACX;;;IAAW,OAAOA,MAAP;IACX;EAAW,CAND;EAOV;;EACA;EAAU;;EACV;;;EAAUT,mBAAmB,CAACU,CAApB,GAAwB,UAASmB,MAAT,EAAiBC,QAAjB,EAA2B;IAAE,OAAOnB,MAAM,CAACoB,SAAP,CAAiBC,cAAjB,CAAgC5B,IAAhC,CAAqCyB,MAArC,EAA6CC,QAA7C,CAAP;EAAgE,CAArH;EACV;;EACA;EAAU;;EACV;;;EAAU9B,mBAAmB,CAACiC,CAApB,GAAwB,QAAxB;EACV;;EACA;;EACA;EAAU;;EACV;;EAAU,OAAOjC,mBAAmB,CAACA,mBAAmB,CAACkC,CAApB,GAAwB,EAAzB,CAA1B;EACV;AAAU;AACV;;AACA;AAtFS,CAsFC;EAEV;EAAM;EACN;EAAO,UAAStC,MAAT,EAAiBuC,mBAAjB,EAAsCnC,mBAAtC,EAA2D;IAElE;IACA;;IAA+BA,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;MAAE,OAAOC,kBAAP;IAA4B,CAAzF;IAC/B;IAEA;IACA;IACA;;;IAEA,SAASA,kBAAT,CACEC,aADF,EAEEC,MAFF,EAGEC,eAHF,EAIEC,kBAJF,EAKEC,YALF,EAMEC,OANF,EAOEC,gBAPF;IAOoB;IAClBC;IAAW;IARb,EASE;MACA;MACA,IAAIC,OAAO,GAAG,OAAOR,aAAP,KAAyB,UAAzB,GACVA,aAAa,CAACQ,OADJ,GAEVR,aAFJ,CAFA,CAMA;;MACA,IAAIC,MAAJ,EAAY;QACVO,OAAO,CAACP,MAAR,GAAiBA,MAAjB;QACAO,OAAO,CAACN,eAAR,GAA0BA,eAA1B;QACAM,OAAO,CAACC,SAAR,GAAoB,IAApB;MACD,CAXD,CAaA;;;MACA,IAAIN,kBAAJ,EAAwB;QACtBK,OAAO,CAACE,UAAR,GAAqB,IAArB;MACD,CAhBD,CAkBA;;;MACA,IAAIL,OAAJ,EAAa;QACXG,OAAO,CAACG,QAAR,GAAmB,YAAYN,OAA/B;MACD;;MAED,IAAIO,IAAJ;;MACA,IAAIN,gBAAJ,EAAsB;QAAE;QACtBM,IAAI,GAAG,UAAUC,OAAV,EAAmB;UACxB;UACAA,OAAO,GACLA,OAAO,IAAI;UACV,KAAKC,MAAL,IAAe,KAAKA,MAAL,CAAYC,UAD5B,IAC2C;UAC1C,KAAKC,MAAL,IAAe,KAAKA,MAAL,CAAYF,MAA3B,IAAqC,KAAKE,MAAL,CAAYF,MAAZ,CAAmBC,UAH3D,CAFwB,CAK+C;UACvE;;UACA,IAAI,CAACF,OAAD,IAAY,OAAOI,mBAAP,KAA+B,WAA/C,EAA4D;YAC1DJ,OAAO,GAAGI,mBAAV;UACD,CATuB,CAUxB;;;UACA,IAAIb,YAAJ,EAAkB;YAChBA,YAAY,CAACrC,IAAb,CAAkB,IAAlB,EAAwB8C,OAAxB;UACD,CAbuB,CAcxB;;;UACA,IAAIA,OAAO,IAAIA,OAAO,CAACK,qBAAvB,EAA8C;YAC5CL,OAAO,CAACK,qBAAR,CAA8BC,GAA9B,CAAkCb,gBAAlC;UACD;QACF,CAlBD,CADoB,CAoBpB;QACA;;;QACAE,OAAO,CAACY,YAAR,GAAuBR,IAAvB;MACD,CAvBD,MAuBO,IAAIR,YAAJ,EAAkB;QACvBQ,IAAI,GAAGL,UAAU,GACb,YAAY;UAAEH,YAAY,CAACrC,IAAb,CAAkB,IAAlB,EAAwB,KAAKsD,KAAL,CAAWC,QAAX,CAAoBC,UAA5C;QAAyD,CAD1D,GAEbnB,YAFJ;MAGD;;MAED,IAAIQ,IAAJ,EAAU;QACR,IAAIJ,OAAO,CAACE,UAAZ,EAAwB;UACtB;UACA;UACAF,OAAO,CAACgB,aAAR,GAAwBZ,IAAxB,CAHsB,CAItB;;UACA,IAAIa,cAAc,GAAGjB,OAAO,CAACP,MAA7B;;UACAO,OAAO,CAACP,MAAR,GAAiB,SAASyB,wBAAT,CAAmCC,CAAnC,EAAsCd,OAAtC,EAA+C;YAC9DD,IAAI,CAAC7C,IAAL,CAAU8C,OAAV;YACA,OAAOY,cAAc,CAACE,CAAD,EAAId,OAAJ,CAArB;UACD,CAHD;QAID,CAVD,MAUO;UACL;UACA,IAAIe,QAAQ,GAAGpB,OAAO,CAACqB,YAAvB;UACArB,OAAO,CAACqB,YAAR,GAAuBD,QAAQ,GAC3B,GAAGE,MAAH,CAAUF,QAAV,EAAoBhB,IAApB,CAD2B,GAE3B,CAACA,IAAD,CAFJ;QAGD;MACF;;MAED,OAAO;QACLpD,OAAO,EAAEwC,aADJ;QAELQ,OAAO,EAAEA;MAFJ,CAAP;IAID;IAGD;;EAAO,CAtGG;;EAwGV;EAAM;EACN;EAAO,UAASjD,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,0BAAD,CAAxB;IAEA;EAAO,CA7GG;;EA+GV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,2BAAD,CAAxB;IAEA;EAAO,CApHG;;EAsHV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,iCAAD,CAAxB;IAEA;EAAO,CA3HG;;EA6HV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,KAAD,CAAxB;IAEA;EAAO,CAlIG;;EAoIV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBuC,mBAAjB,EAAsCnC,mBAAtC,EAA2D;IAElE;;IACAA,mBAAmB,CAACe,CAApB,CAAsBoB,mBAAtB,EAHkE,CAKlE;;;IACA,IAAIG,MAAM,GAAG,YAAW;MACtB,IAAI+B,GAAG,GAAG,IAAV;;MACA,IAAIC,EAAE,GAAGD,GAAG,CAACE,cAAb;;MACA,IAAIC,EAAE,GAAGH,GAAG,CAACI,KAAJ,CAAUD,EAAV,IAAgBF,EAAzB;;MACA,OAAOE,EAAE,CACP,MADO,EAEP,CACEA,EAAE,CACA,YADA,EAEA;QACEE,KAAK,EAAE;UAAElE,IAAI,EAAE6D,GAAG,CAACM;QAAZ,CADT;QAEEC,EAAE,EAAE;UACF,eAAeP,GAAG,CAACQ,gBADjB;UAEF,eAAeR,GAAG,CAACS;QAFjB;MAFN,CAFA,EASA,CACEN,EAAE,CACA,KADA,EAEA;QACEO,UAAU,EAAE,CACV;UACEvE,IAAI,EAAE,MADR;UAEEwE,OAAO,EAAE,QAFX;UAGE9D,KAAK,EAAE,CAACmD,GAAG,CAACY,QAAL,IAAiBZ,GAAG,CAACa,UAH9B;UAIEC,UAAU,EAAE;QAJd,CADU,CADd;QASEC,GAAG,EAAE,QATP;QAUEC,WAAW,EAAE,sBAVf;QAWEC,KAAK,EAAE,CAACjB,GAAG,CAACkB,WAAL,EAAkBlB,GAAG,CAACmB,OAAJ,IAAe,mBAAjC,CAXT;QAYEC,KAAK,EAAE;UAAEC,KAAK,EAAErB,GAAG,CAACqB,KAAJ,GAAY;QAArB,CAZT;QAaEhB,KAAK,EAAE;UACLiB,IAAI,EAAE,SADD;UAELC,EAAE,EAAEvB,GAAG,CAACwB,SAFH;UAGL,eACExB,GAAG,CAACY,QAAJ,IAAgB,CAACZ,GAAG,CAACa,UAArB,GAAkC,MAAlC,GAA2C;QAJxC;MAbT,CAFA,EAsBA,CACEb,GAAG,CAACyB,KAAJ,GACItB,EAAE,CAAC,KAAD,EAAQ;QACRa,WAAW,EAAE,mBADL;QAERU,QAAQ,EAAE;UAAEC,WAAW,EAAE3B,GAAG,CAAC4B,EAAJ,CAAO5B,GAAG,CAACyB,KAAX;QAAf;MAFF,CAAR,CADN,GAKIzB,GAAG,CAAC6B,EAAJ,EANN,EAOE7B,GAAG,CAAC8B,EAAJ,CAAO,SAAP,EAAkB,CAAC9B,GAAG,CAAC+B,EAAJ,CAAO/B,GAAG,CAAC4B,EAAJ,CAAO5B,GAAG,CAACmB,OAAX,CAAP,CAAD,CAAlB,CAPF,CAtBA,EA+BA,CA/BA,CADJ,CATA,CADJ,EA8CEhB,EAAE,CACA,MADA,EAEA;QAAEY,GAAG,EAAE,SAAP;QAAkBC,WAAW,EAAE;MAA/B,CAFA,EAGA,CAAChB,GAAG,CAAC8B,EAAJ,CAAO,WAAP,CAAD,CAHA,EAIA,CAJA,CA9CJ,CAFO,EAuDP,CAvDO,CAAT;IAyDD,CA7DD;;IA8DA,IAAI5D,eAAe,GAAG,EAAtB;IACAD,MAAM,CAAC+D,aAAP,GAAuB,IAAvB,CArEkE,CAwElE;IAEA;;IACA,IAAIC,WAAW,GAAGtG,mBAAmB,CAAC,CAAD,CAArC;;IACA,IAAIuG,kBAAkB,GAAG,aAAavG,mBAAmB,CAAC0B,CAApB,CAAsB4E,WAAtB,CAAtC,CA5EkE,CA8ElE;;;IACA,IAAIE,IAAI,GAAGxG,mBAAmB,CAAC,CAAD,CAA9B,CA/EkE,CAiFlE;;;IACA,IAAIyG,KAAK,GAAGzG,mBAAmB,CAAC,CAAD,CAA/B,CAlFkE,CAoFlE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAOA;;;IAA6B,IAAI0G,4BAA4B,GAAI;MAC/DlG,IAAI,EAAE,WADyD;MAG/DmG,MAAM,EAAE,CAACJ,kBAAkB,CAACK,CAApB,CAHuD;MAK/DC,KAAK,EAAE;QACLC,OAAO,EAAE;UACPC,IAAI,EAAEC,MADC;UAEPC,OAAO,EAAE,OAFF;UAGPC,SAAS,EAAE,SAASA,SAAT,CAAmBhG,KAAnB,EAA0B;YACnC,OAAO,CAAC,OAAD,EAAU,OAAV,EAAmB,OAAnB,EAA4B,QAA5B,EAAsCiG,OAAtC,CAA8CjG,KAA9C,IAAuD,CAAC,CAA/D;UACD;QALM,CADJ;QAQLkG,SAAS,EAAE;UACTL,IAAI,EAAEM,MADG;UAETJ,OAAO,EAAE;QAFA,CARN;QAYLK,UAAU,EAAE;UACVP,IAAI,EAAEM,MADI;UAEVJ,OAAO,EAAE;QAFC,CAZP;QAgBLnB,KAAK,EAAEkB,MAhBF;QAiBL/B,QAAQ,EAAEsC,OAjBL;QAkBL/B,OAAO,EAAEwB,MAlBJ;QAmBLQ,SAAS,EAAE,EAnBN;QAoBLjC,WAAW,EAAEyB,MApBR;QAqBLtB,KAAK,EAAE,EArBF;QAsBL+B,YAAY,EAAE;UACZR,OAAO,EAAE;QADG,CAtBT;QAyBLS,WAAW,EAAE;UACXX,IAAI,EAAEM,MADK;UAEXJ,OAAO,EAAE;QAFE,CAzBR;QA6BLtC,UAAU,EAAE;UACVoC,IAAI,EAAEC,MADI;UAEVC,OAAO,EAAE;QAFC,CA7BP;QAiCLU,QAAQ,EAAE;UACRZ,IAAI,EAAEM,MADE;UAERJ,OAAO,EAAE;QAFD;MAjCL,CALwD;MA4C/DW,QAAQ,EAAE;QACR/B,SAAS,EAAE,SAASA,SAAT,GAAqB;UAC9B,OAAO,gBAAgBlF,MAAM,CAAC8F,KAAK,CAAC,YAAD,CAAN,CAAN,EAAvB;QACD;MAHO,CA5CqD;MAiD/DoB,KAAK,EAAE;QACL3C,UAAU,EAAE,SAASA,UAAT,CAAoB4C,GAApB,EAAyB;UACnC,IAAI,KAAK7C,QAAT,EAAmB;YACjB;UACD;;UACD6C,GAAG,GAAG,KAAKC,KAAL,CAAW,MAAX,CAAH,GAAwB,KAAKA,KAAL,CAAW,MAAX,CAA3B;QACD;MANI,CAjDwD;MA0D/DC,OAAO,EAAE,SAASA,OAAT,GAAmB;QAC1B,IAAIC,KAAK,GAAG,IAAZ;;QAEA,IAAIT,SAAS,GAAG,KAAKU,YAAL,GAAoB,KAAKV,SAAL,IAAkB,KAAKW,KAAL,CAAWX,SAAjE;QACA,IAAIY,MAAM,GAAG,KAAKA,MAAL,IAAe,KAAKD,KAAL,CAAWC,MAAvC;;QAEA,IAAI,CAACZ,SAAD,IAAc,KAAKW,KAAL,CAAWE,OAAX,CAAmBC,QAArC,EAA+C;UAC7Cd,SAAS,GAAG,KAAKU,YAAL,GAAoB,KAAKC,KAAL,CAAWE,OAAX,CAAmBC,QAAnB,CAA4B,CAA5B,CAAhC;QACD,CARyB,CAS1B;;;QACA,IAAId,SAAJ,EAAe;UACb7G,MAAM,CAAC6F,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBgB,SAAzB,EAAoC,uBAApC;UACAA,SAAS,CAACe,YAAV,CAAuB,kBAAvB,EAA2C,KAAK1C,SAAhD;UACA2B,SAAS,CAACe,YAAV,CAAuB,UAAvB,EAAmC,KAAKZ,QAAxC,EAHa,CAGsC;;UACnDS,MAAM,CAACG,YAAP,CAAoB,UAApB,EAAgC,CAAhC;;UAEA,IAAI,KAAKzB,OAAL,KAAiB,OAArB,EAA8B;YAC5BnG,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmBgB,SAAnB,EAA8B,SAA9B,EAAyC,YAAY;cACnDS,KAAK,CAACO,WAAN;;cACA,IAAIC,QAAQ,GAAGjB,SAAS,CAACkB,OAAzB;;cACA,IAAID,QAAQ,IAAI,OAAOA,QAAQ,CAACE,KAAhB,KAA0B,UAA1C,EAAsD;gBACpDF,QAAQ,CAACE,KAAT;cACD;YACF,CAND;YAOAhI,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmB4B,MAAnB,EAA2B,SAA3B,EAAsC,KAAKI,WAA3C;YACA7H,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmBgB,SAAnB,EAA8B,UAA9B,EAA0C,KAAKoB,UAA/C;YACAjI,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmB4B,MAAnB,EAA2B,UAA3B,EAAuC,KAAKQ,UAA5C;UACD;;UACDjI,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmBgB,SAAnB,EAA8B,SAA9B,EAAyC,KAAKqB,aAA9C;UACAlI,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmBgB,SAAnB,EAA8B,OAA9B,EAAuC,KAAKsB,WAA5C;QACD;;QACD,IAAI,KAAKhC,OAAL,KAAiB,OAArB,EAA8B;UAC5BnG,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmBgB,SAAnB,EAA8B,OAA9B,EAAuC,KAAKuB,QAA5C;UACApI,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmBwC,QAAnB,EAA6B,OAA7B,EAAsC,KAAKC,mBAA3C;QACD,CAHD,MAGO,IAAI,KAAKnC,OAAL,KAAiB,OAArB,EAA8B;UACnCnG,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmBgB,SAAnB,EAA8B,YAA9B,EAA4C,KAAK0B,gBAAjD;UACAvI,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmB4B,MAAnB,EAA2B,YAA3B,EAAyC,KAAKc,gBAA9C;UACAvI,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmBgB,SAAnB,EAA8B,YAA9B,EAA4C,KAAK2B,gBAAjD;UACAxI,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmB4B,MAAnB,EAA2B,YAA3B,EAAyC,KAAKe,gBAA9C;QACD,CALM,MAKA,IAAI,KAAKrC,OAAL,KAAiB,OAArB,EAA8B;UACnC,IAAI,KAAKa,QAAL,GAAgB,CAApB,EAAuB;YACrByB,OAAO,CAACC,IAAR,CAAa,+FAAb;UACD;;UACD,IAAI7B,SAAS,CAAC8B,aAAV,CAAwB,iBAAxB,CAAJ,EAAgD;YAC9C3I,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmBgB,SAAnB,EAA8B,SAA9B,EAAyC,KAAK+B,MAA9C;YACA5I,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmBgB,SAAnB,EAA8B,UAA9B,EAA0C,KAAKgC,OAA/C;UACD,CAHD,MAGO;YACL7I,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmBgB,SAAnB,EAA8B,WAA9B,EAA2C,KAAK+B,MAAhD;YACA5I,MAAM,CAAC6F,IAAI,CAAC,IAAD,CAAL,CAAN,CAAmBgB,SAAnB,EAA8B,SAA9B,EAAyC,KAAKgC,OAA9C;UACD;QACF;MACF,CA7G8D;MA8G/DC,aAAa,EAAE,SAASA,aAAT,GAAyB;QACtC,KAAKC,OAAL;MACD,CAhH8D;MAiH/DC,WAAW,EAAE,SAASA,WAAT,GAAuB;QAClC,KAAKD,OAAL;MACD,CAnH8D;MAsH/DE,OAAO,EAAE;QACPb,QAAQ,EAAE,SAASA,QAAT,GAAoB;UAC5B,KAAK7D,UAAL,GAAkB,CAAC,KAAKA,UAAxB;QACD,CAHM;QAIPqE,MAAM,EAAE,SAASA,MAAT,GAAkB;UACxB,KAAKrE,UAAL,GAAkB,IAAlB;QACD,CANM;QAOPsE,OAAO,EAAE,SAASA,OAAT,GAAmB;UAC1B,KAAKtE,UAAL,GAAkB,KAAlB;QACD,CATM;QAUPsD,WAAW,EAAE,SAASA,WAAT,GAAuB;UAClC7H,MAAM,CAAC6F,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyB,KAAK0B,YAA9B,EAA4C,UAA5C;UACA,IAAI,KAAKpB,OAAL,KAAiB,OAAjB,IAA4B,KAAKA,OAAL,KAAiB,OAAjD,EAA0D,KAAK5B,UAAL,GAAkB,IAAlB;QAC3D,CAbM;QAcP4D,WAAW,EAAE,SAASA,WAAT,GAAuB;UAClCnI,MAAM,CAAC6F,IAAI,CAAC,aAAD,CAAL,CAAN,CAA4B,KAAK0B,YAAjC,EAA+C,UAA/C;QACD,CAhBM;QAiBPU,UAAU,EAAE,SAASA,UAAT,GAAsB;UAChCjI,MAAM,CAAC6F,IAAI,CAAC,aAAD,CAAL,CAAN,CAA4B,KAAK0B,YAAjC,EAA+C,UAA/C;UACA,IAAI,KAAKpB,OAAL,KAAiB,OAAjB,IAA4B,KAAKA,OAAL,KAAiB,OAAjD,EAA0D,KAAK5B,UAAL,GAAkB,KAAlB;QAC3D,CApBM;QAqBPgE,gBAAgB,EAAE,SAASA,gBAAT,GAA4B;UAC5C,IAAIW,MAAM,GAAG,IAAb;;UAEAC,YAAY,CAAC,KAAKC,MAAN,CAAZ;;UACA,IAAI,KAAK3C,SAAT,EAAoB;YAClB,KAAK2C,MAAL,GAAcC,UAAU,CAAC,YAAY;cACnCH,MAAM,CAAC3E,UAAP,GAAoB,IAApB;YACD,CAFuB,EAErB,KAAKkC,SAFgB,CAAxB;UAGD,CAJD,MAIO;YACL,KAAKlC,UAAL,GAAkB,IAAlB;UACD;QACF,CAhCM;QAiCP2D,aAAa,EAAE,SAASA,aAAT,CAAuBoB,EAAvB,EAA2B;UACxC,IAAIA,EAAE,CAACC,OAAH,KAAe,EAAf,IAAqB,KAAKpD,OAAL,KAAiB,QAA1C,EAAoD;YAClD;YACA,KAAK0C,OAAL;UACD;QACF,CAtCM;QAuCPL,gBAAgB,EAAE,SAASA,gBAAT,GAA4B;UAC5C,IAAIgB,MAAM,GAAG,IAAb;;UAEAL,YAAY,CAAC,KAAKC,MAAN,CAAZ;;UACA,IAAI,KAAKzC,UAAT,EAAqB;YACnB,KAAKyC,MAAL,GAAcC,UAAU,CAAC,YAAY;cACnCG,MAAM,CAACjF,UAAP,GAAoB,KAApB;YACD,CAFuB,EAErB,KAAKoC,UAFgB,CAAxB;UAGD,CAJD,MAIO;YACL,KAAKpC,UAAL,GAAkB,KAAlB;UACD;QACF,CAlDM;QAmDP+D,mBAAmB,EAAE,SAASA,mBAAT,CAA6BmB,CAA7B,EAAgC;UACnD,IAAI5C,SAAS,GAAG,KAAKA,SAAL,IAAkB,KAAKW,KAAL,CAAWX,SAA7C;UACA,IAAIY,MAAM,GAAG,KAAKA,MAAL,IAAe,KAAKD,KAAL,CAAWC,MAAvC;;UAEA,IAAI,CAACZ,SAAD,IAAc,KAAKW,KAAL,CAAWE,OAAX,CAAmBC,QAArC,EAA+C;YAC7Cd,SAAS,GAAG,KAAKU,YAAL,GAAoB,KAAKC,KAAL,CAAWE,OAAX,CAAmBC,QAAnB,CAA4B,CAA5B,CAAhC;UACD;;UACD,IAAI,CAAC,KAAK+B,GAAN,IAAa,CAAC7C,SAAd,IAA2B,KAAK6C,GAAL,CAASC,QAAT,CAAkBF,CAAC,CAACG,MAApB,CAA3B,IAA0D/C,SAAS,CAAC8C,QAAV,CAAmBF,CAAC,CAACG,MAArB,CAA1D,IAA0F,CAACnC,MAA3F,IAAqGA,MAAM,CAACkC,QAAP,CAAgBF,CAAC,CAACG,MAAlB,CAAzG,EAAoI;UACpI,KAAKrF,UAAL,GAAkB,KAAlB;QACD,CA5DM;QA6DPL,gBAAgB,EAAE,SAASA,gBAAT,GAA4B;UAC5C,KAAKkD,KAAL,CAAW,aAAX;QACD,CA/DM;QAgEPjD,gBAAgB,EAAE,SAASA,gBAAT,GAA4B;UAC5C,KAAKiD,KAAL,CAAW,aAAX;UACA,KAAKyC,SAAL;QACD,CAnEM;QAoEPd,OAAO,EAAE,SAASA,OAAT,GAAmB;UAC1B,IAAI,KAAKtC,SAAL,IAAkB,KAAKE,UAA3B,EAAuC;YACrCwC,YAAY,CAAC,KAAKC,MAAN,CAAZ;UACD;QACF;MAxEM,CAtHsD;MAiM/DU,SAAS,EAAE,SAASA,SAAT,GAAqB;QAC9B,IAAIjD,SAAS,GAAG,KAAKA,SAArB;QAEA7G,MAAM,CAAC6F,IAAI,CAAC,KAAD,CAAL,CAAN,CAAoBgB,SAApB,EAA+B,OAA/B,EAAwC,KAAKuB,QAA7C;QACApI,MAAM,CAAC6F,IAAI,CAAC,KAAD,CAAL,CAAN,CAAoBgB,SAApB,EAA+B,SAA/B,EAA0C,KAAKgC,OAA/C;QACA7I,MAAM,CAAC6F,IAAI,CAAC,KAAD,CAAL,CAAN,CAAoBgB,SAApB,EAA+B,WAA/B,EAA4C,KAAK+B,MAAjD;QACA5I,MAAM,CAAC6F,IAAI,CAAC,KAAD,CAAL,CAAN,CAAoBgB,SAApB,EAA+B,SAA/B,EAA0C,KAAK+B,MAA/C;QACA5I,MAAM,CAAC6F,IAAI,CAAC,KAAD,CAAL,CAAN,CAAoBgB,SAApB,EAA+B,UAA/B,EAA2C,KAAKgC,OAAhD;QACA7I,MAAM,CAAC6F,IAAI,CAAC,KAAD,CAAL,CAAN,CAAoBgB,SAApB,EAA+B,WAA/B,EAA4C,KAAK+B,MAAjD;QACA5I,MAAM,CAAC6F,IAAI,CAAC,KAAD,CAAL,CAAN,CAAoBgB,SAApB,EAA+B,SAA/B,EAA0C,KAAKgC,OAA/C;QACA7I,MAAM,CAAC6F,IAAI,CAAC,KAAD,CAAL,CAAN,CAAoBgB,SAApB,EAA+B,YAA/B,EAA6C,KAAK2B,gBAAlD;QACAxI,MAAM,CAAC6F,IAAI,CAAC,KAAD,CAAL,CAAN,CAAoBgB,SAApB,EAA+B,YAA/B,EAA6C,KAAK0B,gBAAlD;QACAvI,MAAM,CAAC6F,IAAI,CAAC,KAAD,CAAL,CAAN,CAAoBwC,QAApB,EAA8B,OAA9B,EAAuC,KAAKC,mBAA5C;MACD;IA9M8D,CAApC,CApHqC,CAoUlE;;IACC;;IAA6B,IAAIyB,gCAAgC,GAAIhE,4BAAxC,CArUoC,CAsUlE;;IACA,IAAIiE,mBAAmB,GAAG3K,mBAAmB,CAAC,CAAD,CAA7C,CAvUkE,CAyUlE;;IAMA;;;IAEA,IAAI4K,SAAS,GAAGjK,MAAM,CAACgK,mBAAmB,CAAC;IAAI;IAAL,CAApB,CAAN,CACdD,gCADc,EAEdpI,MAFc,EAGdC,eAHc,EAId,KAJc,EAKd,IALc,EAMd,IANc,EAOd,IAPc,CAAhB;IAWA;;IACA,IAAI,KAAJ,EAAW;MAAE,IAAIsI,GAAJ;IAAU;;IACvBD,SAAS,CAAC/H,OAAV,CAAkBiI,MAAlB,GAA2B,+BAA3B;IACA;;IAA6B,IAAIC,IAAI,GAAIH,SAAS,CAAC/K,OAAtB,CA/VqC,CAgWlE;;IACA,IAAImL,YAAY,GAAG,SAASA,YAAT,CAAsBC,EAAtB,EAA0BC,OAA1B,EAAmCC,KAAnC,EAA0C;MAC3D,IAAIC,IAAI,GAAGF,OAAO,CAAC/F,UAAR,GAAqB+F,OAAO,CAAChK,KAA7B,GAAqCgK,OAAO,CAACG,GAAxD;;MACA,IAAIjD,MAAM,GAAG+C,KAAK,CAACjI,OAAN,CAAciF,KAAd,CAAoBiD,IAApB,CAAb;;MACA,IAAIhD,MAAJ,EAAY;QACV,IAAIkD,KAAK,CAACC,OAAN,CAAcnD,MAAd,CAAJ,EAA2B;UACzBA,MAAM,CAAC,CAAD,CAAN,CAAUD,KAAV,CAAgBX,SAAhB,GAA4ByD,EAA5B;QACD,CAFD,MAEO;UACL7C,MAAM,CAACD,KAAP,CAAaX,SAAb,GAAyByD,EAAzB;QACD;MACF;IACF,CAVD;IAYA;;;IAA6B,IAAIO,SAAS,GAAI;MAC5C/J,IAAI,EAAE,SAASA,IAAT,CAAcwJ,EAAd,EAAkBC,OAAlB,EAA2BC,KAA3B,EAAkC;QACtCH,YAAY,CAACC,EAAD,EAAKC,OAAL,EAAcC,KAAd,CAAZ;MACD,CAH2C;MAI5CM,QAAQ,EAAE,SAASA,QAAT,CAAkBR,EAAlB,EAAsBC,OAAtB,EAA+BC,KAA/B,EAAsC;QAC9CH,YAAY,CAACC,EAAD,EAAKC,OAAL,EAAcC,KAAd,CAAZ;MACD;IAN2C,CAAjB,CA7WqC,CAqXlE;;IACA,IAAIO,aAAa,GAAG1L,mBAAmB,CAAC,CAAD,CAAvC;;IACA,IAAI2L,oBAAoB,GAAG,aAAa3L,mBAAmB,CAAC0B,CAApB,CAAsBgK,aAAtB,CAAxC,CAvXkE,CAyXlE;;;IAKAC,oBAAoB,CAAC/E,CAArB,CAAuB4E,SAAvB,CAAiC,SAAjC,EAA4CA,SAA5C;IAEA;;IACAT,IAAI,CAACa,OAAL,GAAe,UAAUC,GAAV,EAAe;MAC5BA,GAAG,CAACL,SAAJ,CAAc,SAAd,EAAyBA,SAAzB;MACAK,GAAG,CAACjB,SAAJ,CAAcG,IAAI,CAACvK,IAAnB,EAAyBuK,IAAzB;IACD,CAHD;;IAIAA,IAAI,CAACS,SAAL,GAAiBA,SAAjB;IAEA;;IAA6B,IAAIM,OAAO,GAAG3J,mBAAmB,CAAC,SAAD,CAAnB,GAAkC4I,IAAhD;IAE7B;EAAO;EAEP;;AAhhBU,CAtFD,CADT"}]}