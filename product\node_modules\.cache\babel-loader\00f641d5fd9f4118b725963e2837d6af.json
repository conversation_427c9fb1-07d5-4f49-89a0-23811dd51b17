{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-cascader\\zy-cascader.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-cascader\\zy-cascader.vue", "mtime": 1752541693518}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAmCA;EACAA,kBADA;;EAEAC;IACA;MACAC,cADA;MAEAC,SAFA;MAGAC,cAHA;MAIAC,mBAJA;MAKAC,aALA;MAMAC;IANA;EAQA,CAXA;;EAYAC;IACAC,sCADA;IAEA;IACAR;MACAS,WADA;MAEAC;IAFA,CAHA;IAOA;IACAH;MACAE,YADA;MAEAC;QACA;UACAC,oBADA;UAEAC;QAFA;MAIA;IAPA,CARA;IAiBA;IACAC;MACAJ,YADA;MAEAC;IAFA,CAlBA;IAsBAI;MACAL,YADA;MAEAC;IAFA,CAtBA;IA0BA;IACAK;MACAN,aADA;MAEAC;IAFA,CA3BA;IA+BA;IACAM;MACAP,YADA;MAEAC;IAFA,CAhCA;IAoCAO;MACAR,YADA;MAEAC;IAFA,CApCA;IAwCAQ;MACAT,aADA;MAEAC;IAFA;EAxCA,CAZA;EAyDAS;IACAC,aADA;IAEAC;EAFA,CAzDA;;EA6DAC;IACA;EACA,CA/DA;;EAgEAC;IACAf;MACA;QACA;QACA;MACA,CAHA,MAGA;QACA;QACA;QACA;MACA;IACA,CAVA;;IAWAP;MACA;IACA,CAbA;;IAcAE;MACA;IACA,CAhBA;;IAiBAC;MACA;QACA;QACA;QACA;MACA;IACA;;EAvBA,CAhEA;EAyFAoB;IACAC;MACA;QACA;QACA;QACA;MACA;IACA,CAPA;;IAQAC;MACA;;MACA;QACA;QACA;MACA;IACA,CAdA;;IAeA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACA;MACA,CAFA,EAEA,EAFA;IAGA,CA9BA;;IA+BAC;MACA;MACA;MACA;MACA;MACAD;QACA;MACA,CAFA,EAEA,EAFA;IAGA;;EAvCA;AAzFA", "names": ["name", "data", "id", "input", "selectData", "options_show", "inputText", "determine", "props", "value", "type", "default", "children", "label", "trigger", "placeholder", "disabled", "width", "nodeKey", "child", "model", "prop", "event", "created", "watch", "methods", "blur", "focus", "selectedClick", "setTimeout", "remove"], "sourceRoot": "src/components/zy-cascader", "sources": ["zy-cascader.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-cascader\"\r\n       :style=\"{ width: width + 'px' }\">\r\n    <el-popover popper-class=\"zy-cascader-popover\"\r\n                :trigger=\"trigger\"\r\n                :disabled=\"disabled\"\r\n                :width=\"width\"\r\n                v-model=\"options_show\">\r\n      <el-input slot=\"reference\"\r\n                clearable\r\n                @blur=\"blur\"\r\n                @focus=\"focus\"\r\n                ref=\"zy-cascader\"\r\n                v-model=\"input\"\r\n                @clear=\"remove\"\r\n                :disabled=\"disabled\"\r\n                :placeholder=\"inputText\">\r\n        <i slot=\"suffix\"\r\n           v-if=\"input == ''\"\r\n           :class=\"['zy-cascader-icon', 'el-icon-arrow-down', options_show ? 'zy-cascader-icon-a' : '']\"></i>\r\n      </el-input>\r\n      <el-scrollbar class=\"zy-cascader-box\">\r\n        <zy-tree-components :tree=\"data\"\r\n                            v-model=\"id\"\r\n                            :child=\"child\"\r\n                            :props=\"props\"\r\n                            :keyword=\"input\"\r\n                            :node-key=\"nodeKey\"\r\n                            :determine=\"determine\"\r\n                            @on-tree-click=\"selectedClick\"></zy-tree-components>\r\n      </el-scrollbar>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyCascader',\r\n  data () {\r\n    return {\r\n      id: this.value,\r\n      input: '',\r\n      selectData: {},\r\n      options_show: false,\r\n      inputText: '',\r\n      determine: false\r\n    }\r\n  },\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    // 数据\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    // 触发方式 click/focus/hover/manual\r\n    trigger: {\r\n      type: String,\r\n      default: 'click'\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '请选择内容'\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 宽度\r\n    width: {\r\n      type: String,\r\n      default: '296'\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    child: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  created () {\r\n    this.inputText = this.placeholder\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.id = val\r\n        this.determine = true\r\n      } else {\r\n        this.id = ''\r\n        this.input = ''\r\n        this.inputText = this.placeholder\r\n      }\r\n    },\r\n    id (val) {\r\n      this.$emit('id', val)\r\n    },\r\n    selectData (val) {\r\n      this.$emit('select', val)\r\n    },\r\n    options_show () {\r\n      if (this.input === '' && this.id && !this.options_show) {\r\n        this.determine = true\r\n        this.input = this.selectedText\r\n        this.inputText = this.selectedText\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    blur () {\r\n      if (this.input === '' && this.id && !this.options_show) {\r\n        this.determine = true\r\n        this.input = this.selectedText\r\n        this.inputText = this.selectedText\r\n      }\r\n    },\r\n    focus () {\r\n      this.determine = false\r\n      if (this.input && this.id) {\r\n        this.input = ''\r\n        this.inputText = this.selectedText\r\n      }\r\n    },\r\n    // 下拉框选中事件\r\n    selectedClick (data) {\r\n      // if (this.selectedText === data[this.props.label]) {\r\n      //   return\r\n      // }\r\n      this.input = data[this.props.label]\r\n      this.selectData = data\r\n      this.options_show = false\r\n      this.inputText = data[this.props.label]\r\n      this.selectedText = data[this.props.label]\r\n      this.determine = true\r\n      this.$refs['zy-cascader'].focus()\r\n      setTimeout(() => {\r\n        this.$refs['zy-cascader'].blur()\r\n      }, 22)\r\n    },\r\n    remove () {\r\n      this.id = ''\r\n      this.input = ''\r\n      this.inputText = this.placeholder\r\n      this.selectData = {}\r\n      setTimeout(() => {\r\n        this.options_show = false\r\n      }, 22)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-cascader.scss\";\r\n</style>\r\n"]}]}