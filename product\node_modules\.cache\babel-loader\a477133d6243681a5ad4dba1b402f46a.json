{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\sinceManagement-zx\\SinceReport\\SinceReportAdd.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\sinceManagement-zx\\SinceReport\\SinceReportAdd.vue", "mtime": 1752541697057}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA0FA;EACAA;IACA;MACAC,+DADA;MAEAC;QACAC,MADA;QAEAC,YAFA;QAGAC,UAHA;QAIAC,YAJA;QAKAC,QALA;QAMAC;MANA,CAFA;MAUAC;QACAL,WACA;UAAAM;UAAAC;UAAAC;QAAA,CADA,CADA;QAIAL,OACA;UAAAG;UAAAC;UAAAC;QAAA,CADA,CAJA;QAOAJ,UACA;UAAAE;UAAAC;UAAAC;QAAA,CADA;MAPA,CAVA;MAqBAC,QArBA;MAsBAC,eAtBA;MAuBAC;IAvBA;EA0BA,CA5BA;;EA6BAC,qBA7BA;;EA8BAC;IACA;MACA;IACA,CAFA,MAEA;MACA;QACA;UAAAC;UAAAC;UAAAf;UAAAgB;UAAAC;QAAA;QACA;MACA;IACA;EACA,CAvCA;;EAwCAC;IACAP;MACA;QACA;MACA,CAFA,MAEA;QACA;QACA;QACA;QACA;MACA;IACA;;EAVA,CAxCA;EAoDAQ;IACA;MACA;QACAL;MADA;MAGA;QAAAlB;MAAA;MACA;MACA;MACA;MACA;IACA,CAVA;;IAWA;MACA;MACA;QAAAA;MAAA;MACA;QAAAkB;QAAAC;QAAAf;QAAAgB;QAAAC;MAAA;;MACA;QACArB;UACAwB;UACAA;QACA,CAHA;QAIA;MACA;;MACA;MACA;IACA,CAxBA;;IAyBAC;MACA;QACA;MACA;;MACA;IACA,CA9BA;;IA+BAC;MACA;QACA;MACA;;MACA;IACA,CApCA;;IAqCA;IACAC;MACA;QACA;MACA;;MACA;MACA;IACA,CA5CA;;IA6CAC,gCACA,CA9CA;;IA+CA;AACA;AACA;IACAC;MACA;MACA;MACAC;MACAA;MACAA;MACA;QACA;UAAA9B;QAAA;QACAA;QACA;MACA,CAJA;IAKA,CA7DA;;IA8DA;AACA;AACA;IACA+B;MACA;MACA;IACA,CApEA;;IAqEAC;MACA;QACA;UACA;;UACA;YACAC;UACA,CAFA,MAEA;YACAA;UACA;;UACA;YACAA;UACA;;UACA;UACA;YACAC;UACA,CAFA;UAGA;YACA/B,WADA;YAEAe,oBAFA;YAGAd,4BAHA;YAIAC,wBAJA;YAKAC,4BALA;YAMAC,oBANA;YAOA2B,sCAPA;YAQA1B;UARA,GASA2B,IATA,CASAC;YACA;cAAAC;cAAAC;YAAA;;YACA;cACA;gBACA3B,eADA;gBAEA4B;cAFA;cAIA;YACA;UACA,CAlBA;QAmBA,CAjCA,MAiCA;UACA;YACA5B,iBADA;YAEA4B;UAFA;UAIA;QACA;MACA,CAzCA;IA0CA,CAhHA;;IAiHA;AACA;AACA;IACAC;MACA;IACA;;EAtHA;AApDA", "names": ["data", "user", "form", "id", "userName", "deleId", "memberNo", "year", "content", "rules", "required", "message", "trigger", "file", "userShow", "userData", "props", "mounted", "userId", "name", "mobile", "position", "watch", "methods", "item", "userClick", "userCallback", "remove", "handleImg_data", "imgUpload_data", "param", "beforeRemove", "submitForm", "url", "attachmentIds", "then", "res", "<PERSON><PERSON><PERSON>", "errmsg", "type", "cancel"], "sourceRoot": "src/views/sinceManagement-zx/SinceReport", "sources": ["SinceReportAdd.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SinceReportAdd\">\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-position=\"top\"\r\n             class=\"newForm\">\r\n      <el-form-item label=\"委员姓名\"\r\n                    prop=\"userName\"\r\n                    class=\"form-title\">\r\n        <div class=\"form-user-box\"\r\n             @click=\"userClick\">\r\n          <div v-if=\"!userData.length\"\r\n               class=\"form-user-box-text\">请选择委员</div>\r\n          <el-tag v-for=\"tag in userData\"\r\n                  :key=\"tag.userId\"\r\n                  size=\"medium\"\r\n                  :closable=\"type\"\r\n                  :disable-transitions=\"false\"\r\n                  @close.stop=\"remove(tag)\">\r\n            {{tag.name}}\r\n          </el-tag>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item class=\"form-input\"\r\n                    label=\"界别\">\r\n        <el-input placeholder=\"请输入界别\"\r\n                  type=\"text\"\r\n                  v-model=\"form.deleId\"\r\n                  disabled\r\n                  clearable>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"委员证号\"\r\n                    class=\"form-input\">\r\n        <el-input placeholder=\"请输入委员证号\"\r\n                  v-model=\"form.memberNo\"\r\n                  disabled\r\n                  clearable>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"年份\"\r\n                    prop=\"year\"\r\n                    class=\"form-input\">\r\n        <el-date-picker v-model=\"form.year\"\r\n                        type=\"year\"\r\n                        value-format='yyyy'\r\n                        :disabled=\"type\"\r\n                        placeholder=\"请选择年份\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"上传附件\"\r\n                    class=\"form-upload\">\r\n        <el-upload class=\"form-upload-demo\"\r\n                   drag\r\n                   action=\"/\"\r\n                   :before-remove=\"beforeRemove\"\r\n                   :before-upload=\"handleImg_data\"\r\n                   :http-request=\"imgUpload_data\"\r\n                   :file-list=\"file\"\r\n                   multiple>\r\n          <div class=\"el-upload__text\">将附件拖拽至此区域，或<em>点击上传</em></div>\r\n          <div class=\"el-upload__tip\">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>\r\n        </el-upload>\r\n      </el-form-item>\r\n      <el-form-item label=\"内容\"\r\n                    prop=\"content\"\r\n                    class=\"form-ue\">\r\n        <!-- <UEditor v-model=\"form.content\"></UEditor> -->\r\n\r\n        <wang-editor v-model='form.content'></wang-editor>\r\n      </el-form-item>\r\n\r\n      <div class=\"form-button\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"cancel\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n    <zy-pop-up v-model=\"userShow\"\r\n               title=\"选择代表\">\r\n      <candidates-user point=\"point_15\"\r\n                       :data=\"userData\"\r\n                       :max=\"1\"\r\n                       @userCallback=\"userCallback\"></candidates-user>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data () {\r\n    return {\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())),\r\n      form: {\r\n        id: '',\r\n        userName: '',\r\n        deleId: '',\r\n        memberNo: '',\r\n        year: '',\r\n        content: ''\r\n      },\r\n      rules: {\r\n        userName: [\r\n          { required: true, message: '请选择代表', trigger: 'blur' }\r\n        ],\r\n        year: [\r\n          { required: true, message: '请选择年份', trigger: 'blur' }\r\n        ],\r\n        content: [\r\n          { required: true, message: '请输入内容', trigger: 'blur' }\r\n        ]\r\n      },\r\n      file: [],\r\n      userShow: false,\r\n      userData: []\r\n\r\n    }\r\n  },\r\n  props: ['id', 'type'],\r\n  mounted () {\r\n    if (this.id) {\r\n      this.dutyreportInfo()\r\n    } else {\r\n      if (this.type) {\r\n        this.userData.push({ userId: this.user.id, name: this.user.userName, userName: this.user.userName + this.user.mobile, mobile: this.user.mobile, position: this.user.position })\r\n        this.form.year = this.$format('', 'YYYY')\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    userData (val) {\r\n      if (this.userData.length) {\r\n        this.getMyInfo(val[0].userId)\r\n      } else {\r\n        this.form.id = ''\r\n        this.form.userName = ''\r\n        this.form.deleId = ''\r\n        this.form.memberNo = ''\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    async getMyInfo (userId) {\r\n      const res = await this.$api.sinceManagement.dutyreportGetMyInfo({\r\n        userId: userId\r\n      })\r\n      var { data } = res\r\n      this.form.memberNo = data.memberNo\r\n      this.form.deleId = data.deleId\r\n      this.form.id = data.userId\r\n      this.form.userName = data.userName\r\n    },\r\n    async dutyreportInfo () {\r\n      const res = await this.$api.sinceManagement.dutyreportInfo(this.id)\r\n      var { data } = res\r\n      this.userData.push({ userId: data.userId, name: data.userName, userName: data.userName, mobile: '', position: '' })\r\n      if (data.attachmentList) {\r\n        data.attachmentList.forEach((item, index) => {\r\n          item.uid = item.id\r\n          item.name = item.fileName\r\n        })\r\n        this.file = data.attachmentList\r\n      }\r\n      this.form.content = data.content\r\n      this.form.year = data.year\r\n    },\r\n    userClick () {\r\n      if (this.type) {\r\n        return false\r\n      }\r\n      this.userShow = !this.userShow\r\n    },\r\n    userCallback (data, type) {\r\n      if (type) {\r\n        this.userData = data\r\n      }\r\n      this.userShow = !this.userShow\r\n    },\r\n    // 移除tag\r\n    remove (data) {\r\n      if (this.type) {\r\n        return false\r\n      }\r\n      var userData = this.userData\r\n      this.userData = userData.filter(item => item.userId !== data.userId)\r\n    },\r\n    handleImg_data (file, fileList) {\r\n    },\r\n    /**\r\n* 上传附件请求方法\r\n*/\r\n    imgUpload_data (files) {\r\n      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''\r\n      const param = new FormData()\r\n      param.append('module', 'materiainfoFile')\r\n      param.append('siteId', JSON.parse(areaId))\r\n      param.append('attachment', files.file)\r\n      this.$api.proposal.proposalfile(param).then(res => {\r\n        var { data } = res\r\n        data[0].name = data[0].fileName\r\n        this.file.push(data[0])\r\n      })\r\n    },\r\n    /**\r\n  * 删除附件\r\n */\r\n    beforeRemove (file, fileList) {\r\n      var fileData = this.file\r\n      this.fileData = fileData.filter(item => item.id !== file.id)\r\n    },\r\n    submitForm (formName, type) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var url = ''\r\n          if (this.type) {\r\n            url = '/dutyreport/addMyDutyReport'\r\n          } else {\r\n            url = '/dutyreport/add'\r\n          }\r\n          if (this.id) {\r\n            url = '/dutyreport/edit?'\r\n          }\r\n          var attachmentIds = []\r\n          this.file.forEach(item => {\r\n            attachmentIds.push(item.id)\r\n          })\r\n          this.$api.general.generalAdd(url, {\r\n            id: this.id,\r\n            userId: this.form.id,\r\n            userName: this.form.userName,\r\n            deleId: this.form.deleId,\r\n            memberNo: this.form.memberNo,\r\n            year: this.form.year,\r\n            attachmentIds: attachmentIds.join(','),\r\n            content: this.form.content\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('callback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 取消按钮\r\n    */\r\n    cancel () {\r\n      this.$emit('callback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SinceReportAdd {\r\n  width: 1000px;\r\n  height: 100%;\r\n  padding: 24px;\r\n\r\n  .form-user-box {\r\n    width: 100%;\r\n    min-height: 40px;\r\n    background-color: #fff;\r\n    border: 1px solid #dcdfe6;\r\n    border-radius: 4px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    padding: 0 16px;\r\n    padding-right: 40px;\r\n    // overflow: hidden;\r\n    padding-top: 6px;\r\n\r\n    .form-user-box-text {\r\n      color: #999;\r\n      font-size: $textSize14;\r\n      padding-bottom: 6px;\r\n      line-height: 28px;\r\n    }\r\n\r\n    .el-tag {\r\n      margin-bottom: 6px;\r\n      margin-right: 12px;\r\n    }\r\n\r\n    &:hover {\r\n      border-color: #199bc5;\r\n    }\r\n\r\n    &:focus {\r\n      border-color: #199bc5;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}