{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\search-box\\search-box.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\search-box\\search-box.vue", "mtime": 1752541693483}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["search-box.vue"], "names": [], "mappings": ";AA0BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "search-box.vue", "sourceRoot": "src/components/search-box", "sourcesContent": ["<template>\r\n  <div class=\"search-box\"\r\n       ref=\"searchBox\">\r\n    <span class=\"search-title\"\r\n          v-if=\"title\">{{ title }}</span>\r\n    <div class=\"search-box-slot\"\r\n         ref=\"searchBoxSlot\">\r\n      <slot></slot>\r\n    </div>\r\n    <div class=\"search-box-btn\"\r\n         :style=\"{ width: (isMore ? 220 : 184) + 'px' }\">\r\n      <el-button type=\"text\"\r\n                 v-if=\"isMore\"\r\n                 @click=\"moreClick\">\r\n        <i :class=\"['el-icon-arrow-down', isShow ? '' : 'el-icon-arrow-down-a']\"></i>{{ isShow ? '展开' : '收起' }}\r\n      </el-button>\r\n      <el-button type=\"primary\"\r\n                 @click=\"search\"\r\n                 v-if=\"isSearch\">查询</el-button>\r\n      <el-button @click=\"reset\"\r\n                 v-if=\"isReset\">重置</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport _ from 'lodash'\r\nexport default {\r\n  name: 'search-box',\r\n  props: {\r\n    isSearch: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    isReset: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    title: String\r\n  },\r\n  mounted () {\r\n    this.$nextTick(() => {\r\n      this.collapse()\r\n    })\r\n    this.resize = window.onresize = _.debounce(() => {\r\n      this.collapse()\r\n    }, 500)\r\n  },\r\n  data () {\r\n    return {\r\n      isMore: false,\r\n      isShow: true,\r\n      resize: null\r\n    }\r\n  },\r\n  destroyed () {\r\n    this.resize = null\r\n  },\r\n  methods: {\r\n    search () {\r\n      this.$emit('search-click')\r\n    },\r\n    reset () {\r\n      this.$emit('reset-click')\r\n    },\r\n    collapse () {\r\n      var searchBoxSlot = this.$refs.searchBoxSlot\r\n      if (searchBoxSlot) {\r\n        var width = 0\r\n        for (let index = 0; index < searchBoxSlot.childNodes.length; index++) {\r\n          if (searchBoxSlot.childNodes[index].offsetWidth !== undefined) {\r\n            width += searchBoxSlot.childNodes[index].offsetWidth + 24\r\n          }\r\n        }\r\n        if (searchBoxSlot.offsetWidth < width) {\r\n          this.isMore = true\r\n        } else {\r\n          this.isMore = false\r\n        }\r\n      }\r\n    },\r\n    moreClick () {\r\n      var searchBoxSlot = this.$refs.searchBoxSlot\r\n      if (this.isShow) {\r\n        searchBoxSlot.style.height = 'auto'\r\n      } else {\r\n        searchBoxSlot.style.height = '52px'\r\n      }\r\n      this.isShow = !this.isShow\r\n      this.$emit('more-click', searchBoxSlot.offsetHeight, this.isShow)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\"\r\n       scoped>\r\n      .search-box {\r\n        box-sizing: border-box;\r\n        border-radius: 10px;\r\n        background-color: #fff;\r\n        box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        padding: 12px 10px 0;\r\n\r\n        .search-title {\r\n          min-width: 120px;\r\n          flex-shrink: 0;\r\n          color: #444;\r\n          line-height: 40px;\r\n          height: 40px;\r\n        }\r\n\r\n        .el-input {\r\n          width: 222px;\r\n          // margin-left: 24px;\r\n          margin-bottom: 12px;\r\n        }\r\n\r\n        .el-select {\r\n          // margin-left: 24px;\r\n          margin-bottom: 12px;\r\n\r\n          .el-input {\r\n            margin-left: 0;\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n\r\n        .zy-select {\r\n          // margin-left: 24px;\r\n          margin-bottom: 12px;\r\n\r\n          .el-input {\r\n            margin-left: 0;\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n\r\n        .el-date-editor {\r\n          // margin-left: 24px;\r\n          margin-bottom: 12px;\r\n        }\r\n\r\n        .search-box-slot {\r\n          width: calc(100% - 246px);\r\n          display: flex;\r\n          justify-content: flex-end;\r\n          flex-wrap: wrap;\r\n          overflow: hidden;\r\n          height: 52px;\r\n        }\r\n\r\n        .search-box-btn {\r\n          flex-shrink: 0;\r\n\r\n          .el-button {\r\n            height: 40px;\r\n            margin-left: 24px;\r\n            padding: 0 16px;\r\n          }\r\n\r\n          .el-button+.el-button {\r\n            margin-left: 16px;\r\n          }\r\n\r\n          .el-button--text {\r\n            margin-left: 9px;\r\n            font-size: $textSize14;\r\n            padding: 0;\r\n\r\n            .el-icon-arrow-down {\r\n              transition-duration: 0.4s;\r\n              transform: rotate(0);\r\n            }\r\n\r\n            .el-icon-arrow-down-a {\r\n              transition-duration: 0.4s;\r\n              transform: rotate(-180deg);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    </style>\r\n"]}]}