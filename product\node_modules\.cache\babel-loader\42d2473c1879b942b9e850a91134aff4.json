{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-table\\zy-table.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-table\\zy-table.vue", "mtime": 1752541693589}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAWA;AACA;AACA;EACAA,eADA;;EAEAC;IACA;MACAC,QADA;MAEAC,SAFA;MAGAC,MAHA;MAIAC;IAJA;EAMA,CATA;;EAUA;EACA;EACA;EACAC;IACA;IACA;EACA,CAhBA;;EAiBAC;IACA;MACA;QACA;MACA;;MACA;MACA;MACA;;MACA;QACA;QACAC;UACAC;YACAA;YACAA;YACA;YACA;UACA,CALA;QAMA,CAPA;MAQA;IACA,CAlBA,EADA,CAoBA;EACA,CAtCA;;EAuCAC;IACAC;MACA;QACA;QACA;;QACA;UACAA;QACA;MACA,CANA;IAOA,CATA;;IAUAC;MACA;QACA;QACA;QACA;QACA;QACA;QACAC;QACA;;QACA;UACA;UACA;UACA;UACAC;UACAC;QACA;;QACA;;QACA;UACA;UACA;UACA;UACA;UACAC;UACAD;QACA;MACA;IACA,CApCA;;IAqCAE;MACA;QACA;;QACA;UACA;UACAD;QACA;MACA;IACA,CA7CA;;IA8CAE;MACA;QACA;UACA;UACA;UACA;;UACA;YACAC;UACA,CAFA,MAEA;YACAA;UACA;QACA;MACA,CAXA;IAYA;;EA3DA,CAvCA;;EAoGAC;IACAZ;EACA;;AAtGA", "names": ["name", "data", "width", "height", "top", "left", "activated", "mounted", "erd", "that", "methods", "loading", "handleScroll", "box", "fixed", "fixedHeader", "fixedright", "sliding", "scrollshow", "horizontal", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "src/components/zy-table", "sources": ["zy-table.vue"], "sourcesContent": ["\r\n<template>\r\n  <div class=\"zy-table\"\r\n       ref=\"zy-table\">\r\n    <el-scrollbar class=\"my-scroll-bar\"\r\n                  :style=\"{width:width+'px',height:height+'px',}\">\r\n      <slot name=\"zytable\"></slot>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nexport default {\r\n  name: 'zyTable',\r\n  data () {\r\n    return {\r\n      width: 0,\r\n      height: 0,\r\n      top: 0,\r\n      left: 0\r\n    }\r\n  },\r\n  // created () {\r\n  //   this.scrollshow()\r\n  // },\r\n  activated () {\r\n    this.scrollshow()\r\n    this.handleScroll()\r\n  },\r\n  mounted () {\r\n    this.$nextTick(() => {\r\n      if (this.$refs['zy-table']) {\r\n        this.$refs['zy-table'].querySelector('.el-scrollbar__wrap').addEventListener('scroll', this.handleScroll)\r\n      }\r\n      this.handleScroll()\r\n      this.scrollshow()\r\n      this.sliding()\r\n      if (this.$refs['zy-table']) {\r\n        const that = this\r\n        erd.listenTo(this.$refs['zy-table'], (element) => {\r\n          that.$nextTick(() => {\r\n            that.width = element.offsetWidth\r\n            that.height = element.offsetHeight\r\n            this.scrollshow()\r\n            this.handleScroll()\r\n          })\r\n        })\r\n      }\r\n    })\r\n    // this.loading()\r\n  },\r\n  methods: {\r\n    loading () {\r\n      this.$nextTick(() => {\r\n        var box = this.$refs['zy-table']\r\n        var loading = box.querySelector('.el-loading-mask')\r\n        if (loading) {\r\n          loading.style.height = `${box.clientHeight}px`\r\n        }\r\n      })\r\n    },\r\n    handleScroll (event) {\r\n      if (this.$refs['zy-table']) {\r\n        var box = this.$refs['zy-table'].querySelector('.el-table__header-wrapper')\r\n        this.top = this.$refs['zy-table'].getBoundingClientRect().top\r\n        this.left = this.$refs['zy-table'].getBoundingClientRect().left\r\n        var boxTop = this.$refs['zy-table'].querySelector('.el-table').getBoundingClientRect().top\r\n        var boxss = this.top - 1 - boxTop\r\n        box.style.top = boxss + 'px'\r\n        var fixed = this.$refs['zy-table'].querySelector('.el-table__fixed')\r\n        if (fixed) {\r\n          const fixedHeader = fixed.querySelector('.el-table__fixed-header-wrapper')\r\n          const boxTop = this.$refs['zy-table'].querySelector('.el-table').getBoundingClientRect().left\r\n          const distance = this.left - 1 - boxTop\r\n          fixed.style.left = distance + 'px'\r\n          fixedHeader.style.top = boxss + 'px'\r\n        }\r\n        var fixedright = this.$refs['zy-table'].querySelector('.el-table__fixed-right')\r\n        if (fixedright) {\r\n          const fixedHeader = fixedright.querySelector('.el-table__fixed-header-wrapper')\r\n          const aa = this.$refs['zy-table'].offsetWidth\r\n          const boxTop = this.$refs['zy-table'].querySelector('.el-table').getBoundingClientRect().left\r\n          const distance = this.left - 1 - boxTop\r\n          fixedright.style.left = (distance + aa - fixedright.offsetWidth) + 'px'\r\n          fixedHeader.style.top = boxss + 'px'\r\n        }\r\n      }\r\n    },\r\n    sliding () {\r\n      if (this.$refs['zy-table']) {\r\n        var fixedright = this.$refs['zy-table'].querySelector('.el-table__fixed-right')\r\n        if (fixedright) {\r\n          const aa = this.$refs['zy-table'].offsetWidth\r\n          fixedright.style.left = (aa - fixedright.offsetWidth) + 'px'\r\n        }\r\n      }\r\n    },\r\n    scrollshow () {\r\n      this.$nextTick(() => {\r\n        if (this.$refs['zy-table']) {\r\n          var arrayWidth = this.$refs['zy-table'].clientWidth\r\n          var arrWidth = this.$refs['zy-table'].querySelector('.el-table__body-wrapper').querySelector('tbody').clientWidth\r\n          var horizontal = this.$refs['zy-table'].querySelector('.is-horizontal')\r\n          if (arrayWidth < arrWidth) {\r\n            horizontal.style.backgroundColor = '#EEF1F4'\r\n          } else {\r\n            horizontal.style.backgroundColor = 'transparent'\r\n          }\r\n        }\r\n      })\r\n    }\r\n  },\r\n  beforeDestroy () {\r\n    erd.uninstall(this.$refs['zy-table'])\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-table.scss\";\r\n</style>\r\n"]}]}