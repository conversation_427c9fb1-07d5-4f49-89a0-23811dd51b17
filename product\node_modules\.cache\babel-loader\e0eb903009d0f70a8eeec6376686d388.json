{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\candidates-box\\candidates-box.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\candidates-box\\candidates-box.vue", "mtime": 1752541693438}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdDYW5kaWRhdGVzQm94JywKCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHVzZXJTaG93OiBmYWxzZSwKICAgICAgdXNlckRhdGE6IHRoaXMuZGF0YQogICAgfTsKICB9LAoKICBwcm9wczogewogICAgcG9pbnQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfSwKICAgIHBsYWNlaG9sZGVyOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJ+ivt+mAieaLqeeUqOaItycKICAgIH0sCiAgICBtYXg6IHsKICAgICAgdHlwZTogTnVtYmVyLAogICAgICBkZWZhdWx0OiAxMDAwMAogICAgfSwKICAgIGJlZm9yZUNsb3NlOiBGdW5jdGlvbiwKICAgIGRhdGE6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdCiAgICB9LAogICAgZGlzYWJsZWQ6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdCiAgICB9CiAgfSwKICB3YXRjaDogewogICAgZGF0YSgpIHsKICAgICAgdGhpcy51c2VyRGF0YSA9IHRoaXMuZGF0YTsKICAgIH0KCiAgfSwKICBtZXRob2RzOiB7CiAgICB1c2VyQ2xpY2soKSB7CiAgICAgIGlmICh0eXBlb2YgdGhpcy5iZWZvcmVDbG9zZSA9PT0gJ2Z1bmN0aW9uJykgewogICAgICAgIHRoaXMuYmVmb3JlQ2xvc2UodGhpcy5TaHV0KTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLlNodXQoKTsKICAgICAgfQogICAgfSwKCiAgICBTaHV0KCkgewogICAgICB0aGlzLnVzZXJTaG93ID0gIXRoaXMudXNlclNob3c7CiAgICB9LAoKICAgIC8vIOenu+mZpHRhZwogICAgcmVtb3ZlKGRhdGEpIHsKICAgICAgdmFyIHVzZXJEYXRhID0gdGhpcy51c2VyRGF0YTsKICAgICAgdGhpcy51c2VyRGF0YSA9IHVzZXJEYXRhLmZpbHRlcihpdGVtID0+IGl0ZW0udXNlcklkICE9PSBkYXRhLnVzZXJJZCk7CiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpkYXRhJywgdGhpcy51c2VyRGF0YSk7CiAgICB9LAoKICAgIC8qKg0KICAgICAqIOmAieaLqeeUqOaIt+eahOWbnuiwgw0KICAgICovCiAgICB1c2VyQ2FsbGJhY2soZGF0YSwgdHlwZSkgewogICAgICBpZiAodHlwZSkgewogICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpkYXRhJywgZGF0YSk7CiAgICAgIH0KCiAgICAgIHRoaXMudXNlclNob3cgPSAhdGhpcy51c2VyU2hvdzsKICAgIH0KCiAgfQp9Ow=="}, {"version": 3, "mappings": "AAyBA;EACAA,qBADA;;EAEAC;IACA;MACAC,eADA;MAEAC;IAFA;EAIA,CAPA;;EAQAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,YADA;MAEAC;IAFA,CALA;IASAE;MACAH,YADA;MAEAC;IAFA,CATA;IAaAG,qBAbA;IAcAT;MACAK,WADA;MAEAC;IAFA,CAdA;IAkBAI;MACAL,WADA;MAEAC;IAFA;EAlBA,CARA;EA+BAK;IACAX;MACA;IACA;;EAHA,CA/BA;EAoCAY;IACAC;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA;IACA,CAPA;;IAQAC;MACA;IACA,CAVA;;IAWA;IACAC;MACA;MACA;MACA;IACA,CAhBA;;IAiBA;AACA;AACA;IACAC;MACA;QACA;MACA;;MACA;IACA;;EAzBA;AApCA", "names": ["name", "data", "userShow", "userData", "props", "point", "type", "default", "placeholder", "max", "beforeClose", "disabled", "watch", "methods", "userClick", "Shut", "remove", "userCallback"], "sourceRoot": "src/components/candidates-box", "sources": ["candidates-box.vue"], "sourcesContent": ["<template>\r\n  <div class=\"candidates-box\"\r\n       @click=\"userClick\">\r\n    <el-scrollbar class=\"candidates--user-box\">\r\n      <div v-if=\"!userData.length\"\r\n           class=\"form-user-box-text\">{{placeholder}}</div>\r\n      <el-tag v-for=\"tag in userData\"\r\n              :key=\"tag.userId\"\r\n              size=\"medium\"\r\n              closable\r\n              :disable-transitions=\"false\"\r\n              @close.stop=\"remove(tag)\">\r\n        {{tag.name}}\r\n      </el-tag>\r\n    </el-scrollbar>\r\n    <zy-pop-up v-model=\"userShow\"\r\n               :title=\"placeholder\">\r\n      <candidates-user :point=\"point\"\r\n                       :disabled=\"disabled\"\r\n                       :data=\"userData\"\r\n                       @userCallback=\"userCallback\"></candidates-user>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'CandidatesBox',\r\n  data () {\r\n    return {\r\n      userShow: false,\r\n      userData: this.data\r\n    }\r\n  },\r\n  props: {\r\n    point: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '请选择用户'\r\n    },\r\n    max: {\r\n      type: Number,\r\n      default: 10000\r\n    },\r\n    beforeClose: Function,\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    disabled: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  watch: {\r\n    data () {\r\n      this.userData = this.data\r\n    }\r\n  },\r\n  methods: {\r\n    userClick () {\r\n      if (typeof this.beforeClose === 'function') {\r\n        this.beforeClose(this.Shut)\r\n      } else {\r\n        this.Shut()\r\n      }\r\n    },\r\n    Shut () {\r\n      this.userShow = !this.userShow\r\n    },\r\n    // 移除tag\r\n    remove (data) {\r\n      var userData = this.userData\r\n      this.userData = userData.filter(item => item.userId !== data.userId)\r\n      this.$emit('update:data', this.userData)\r\n    },\r\n    /**\r\n     * 选择用户的回调\r\n    */\r\n    userCallback (data, type) {\r\n      if (type) {\r\n        this.$emit('update:data', data)\r\n      }\r\n      this.userShow = !this.userShow\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./candidates-box.scss\";\r\n</style>\r\n"]}]}