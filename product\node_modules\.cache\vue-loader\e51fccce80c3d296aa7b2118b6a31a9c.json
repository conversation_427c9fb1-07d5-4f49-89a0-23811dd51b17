{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tab\\zy-tab.vue?vue&type=style&index=0&id=151980a2&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tab\\zy-tab.vue", "mtime": 1752541693583}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3p5LXRhYi5zY3NzIjsNCg=="}, {"version": 3, "sources": ["zy-tab.vue"], "names": [], "mappings": ";AA+RA", "file": "zy-tab.vue", "sourceRoot": "src/components/zy-tab", "sourcesContent": ["<template>\r\n  <div class=\"zy-tab\"\r\n       ref=\"zy-tab\">\r\n    <div class=\"zy-tab-left\"\r\n         v-if=\"show&&offset>0||offset!=0\"\r\n         @click.stop=\"tabsLeft\"><i class=\"el-icon-d-arrow-left\"></i></div>\r\n    <div class=\"zy-tab-right\"\r\n         v-if=\"show&&offset<biggest\"\r\n         @click.stop=\"tabsRight\"><i class=\"el-icon-d-arrow-right\"></i></div>\r\n    <div class=\"zy-tab-box\"\r\n         ref=\"zy-tab-box\">\r\n      <div class=\"zy-tab-item-list\"\r\n           ref=\"zy-tab-item-list\">\r\n        <div v-for=\"(item, index) in tabData\"\r\n             :key=\"index\"\r\n             @click.prevent=\"selectedMethods(item)\"\r\n             :ref=\"item.class?'zy-tab-item-active':'zy-tab-item'\"\r\n             :class=\"['zy-tab-item',item.class?'zy-tab-item-active':'']\">\r\n          <div class=\"zy-tab-item-label\">{{item[props.label]}}</div>\r\n          <span class=\"zy-tab-item-del-box\"\r\n                v-if=\"item.class\">\r\n            <span class=\"zy-tab-item-refresh\"\r\n                  @click.stop=\"refreshclick(item,index)\"><i class=\"el-icon-refresh\"></i></span>\r\n          </span>\r\n          <span class=\"zy-tab-item-del-box\"\r\n                v-if=\"tabData.length!=1\">\r\n            <span class=\"zy-tab-item-del\"\r\n                  @click.stop=\"deleteclick(item,index)\"><i class=\"el-icon-close\"></i></span>\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nexport default {\r\n  name: 'zyTab',\r\n  data () {\r\n    return {\r\n      tabId: this.value,\r\n      tabData: [],\r\n      show: false,\r\n      offset: 0,\r\n      biggest: 0\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String\r\n    },\r\n    tabList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    shift: {\r\n      type: Number,\r\n      default: 168\r\n    },\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          id: 'id',\r\n          label: 'label'\r\n        }\r\n      }\r\n    }\r\n  },\r\n  created () {\r\n    this.tabCopyData(this.deepCopy(this.tabList))\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      this.tabId = val\r\n      this.selected()\r\n    },\r\n    tabId (val) {\r\n      this.$emit('input', val)\r\n    },\r\n    tabList (val) {\r\n      this.tabCopyData(this.deepCopy(this.tabList))\r\n      this.$nextTick(() => {\r\n        this.biggestClick()\r\n      })\r\n    }\r\n  },\r\n  mounted () {\r\n    this.$nextTick(() => {\r\n      if (this.tabId) {\r\n        this.tabBox()\r\n      }\r\n      const that = this\r\n      erd.listenTo(this.$refs['zy-tab'], (element) => {\r\n        that.$nextTick(() => {\r\n          this.biggestClick()\r\n        })\r\n      })\r\n    })\r\n  },\r\n  methods: {\r\n    selectedMethods (item) {\r\n      this.tabId = item[this.props.id]\r\n      this.selected()\r\n      if (item.type) {\r\n        setTimeout(() => {\r\n          this.$router.push({\r\n            path: item.to,\r\n            query: item.params\r\n          })\r\n        }, 200)\r\n      }\r\n    },\r\n    selected (id) {\r\n      var arr = this.tabData\r\n      arr.forEach(item => {\r\n        item.class = false\r\n        if (item[this.props.id] === this.tabId) {\r\n          item.class = true\r\n        }\r\n      })\r\n      this.tabData = arr\r\n      this.$nextTick(() => {\r\n        this.tabBox()\r\n      })\r\n    },\r\n    refreshclick (data) {\r\n      this.$emit('tab-refresh', data)\r\n    },\r\n    deleteclick (data, index) {\r\n      if (this.tabId === data[this.props.id]) {\r\n        this.Before(index)\r\n      }\r\n      this.$emit('tab-click', data)\r\n    },\r\n    Before (i) {\r\n      this.tabList.forEach((item, index) => {\r\n        if (i === 0) {\r\n          if (index === i + 1) {\r\n            this.tabId = item[this.props.id]\r\n            if (item.type) {\r\n              setTimeout(() => {\r\n                this.$router.push({\r\n                  path: item.to,\r\n                  query: item.params\r\n                })\r\n              }, 200)\r\n            }\r\n          }\r\n        } else {\r\n          if (index === i - 1) {\r\n            this.tabId = item[this.props.id]\r\n            if (item.type) {\r\n              setTimeout(() => {\r\n                this.$router.push({\r\n                  path: item.to,\r\n                  query: item.params\r\n                })\r\n              }, 200)\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    biggestClick () {\r\n      var tabBox = this.$refs['zy-tab-box']\r\n      var itemBox = this.$refs['zy-tab-item-list']\r\n      if (itemBox.offsetWidth > tabBox.offsetWidth) {\r\n        this.show = true\r\n        this.biggest = itemBox.offsetWidth - tabBox.offsetWidth\r\n      } else {\r\n        this.show = false\r\n      }\r\n    },\r\n    tabsLeft () {\r\n      var itemBox = this.$refs['zy-tab-item-list']\r\n      var offset = this.offset - this.shift\r\n      if (this.offset - this.shift <= 0) {\r\n        offset = 0\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      itemBox.style.transitionDuration = '.4s'\r\n      this.offset = offset\r\n    },\r\n    tabsRight () {\r\n      var itemBox = this.$refs['zy-tab-item-list']\r\n      var offset = this.offset + this.shift\r\n      if (this.biggest < this.offset + this.shift) {\r\n        offset = this.biggest\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      itemBox.style.transitionDuration = '.4s'\r\n      this.offset = offset\r\n    },\r\n    tabBox () {\r\n      var tabBox = this.$refs['zy-tab-box']\r\n      var itemBox = this.$refs['zy-tab-item-list']\r\n      var itemActive = itemBox.querySelector('.zy-tab-item-active')\r\n      if (tabBox.offsetWidth < itemBox.offsetWidth) {\r\n        itemBox.style.transform = 'translateX(0px)'\r\n        itemBox.style.transitionDuration = '.4s'\r\n        if (itemBox.offsetWidth === itemActive.offsetLeft + itemActive.offsetWidth) {\r\n          this.offset = itemBox.offsetWidth - tabBox.offsetWidth\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`\r\n        } else if (tabBox.offsetWidth / 2 > itemBox.offsetWidth - itemActive.offsetLeft) {\r\n          this.offset = itemBox.offsetWidth - tabBox.offsetWidth\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`\r\n        } else {\r\n          if (itemActive.offsetLeft - tabBox.offsetWidth / 2 < 0) {\r\n            this.offset = 0\r\n          } else {\r\n            this.offset = itemActive.offsetLeft - tabBox.offsetWidth / 2\r\n          }\r\n          itemBox.style.transform = `translateX(-${itemActive.offsetLeft - tabBox.offsetWidth / 2}px)`\r\n        }\r\n      }\r\n    },\r\n    tabCopyData (data) {\r\n      this.tabData = this.initData(data)\r\n    },\r\n    initData (items) {\r\n      items.forEach((item, index) => {\r\n        if ((typeof item.class) === 'undefined') { // eslint-disable-line\r\n          item.class = false\r\n        }\r\n        if (this.tabId === item[this.props.id]) {\r\n          item.class = true\r\n          if (item.type) {\r\n            setTimeout(() => {\r\n              this.$router.push({\r\n                path: item.to,\r\n                query: item.params\r\n              })\r\n            }, 200)\r\n          }\r\n        }\r\n      })\r\n      return items\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    }\r\n  },\r\n  beforeDestroy () {\r\n    erd.uninstall(this.$refs['zy-tab'])\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-tab.scss\";\r\n</style>\r\n"]}]}