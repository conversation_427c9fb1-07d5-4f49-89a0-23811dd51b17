<template>
  <div class="big-screen" ref="bigScreen">
    <div class="screen-header">
      <div class="header-left">
        <span class="date-time">{{ currentTime }}</span>
        <span class="weather">晴 24℃ 东南风</span>
      </div>
      <div class="header-center">
        <img src="../../../assets/largeScreen/top_header_txt.png" alt="" style="height: 50px;">
      </div>
      <div class="header-right">
        <div class="header-buttons">
          <div class="header-btn current-module-btn">
            <span>提案统计</span>
          </div>
          <div class="header-btn home-btn" @click="goHome">
            <span>返回首页</span>
          </div>
        </div>
      </div>
    </div>
    <div class="screen-content">
      <div class="left-panel">
        <!-- 类别分布 -->
        <div class="category_distribution">
          <div class="header_box">
            <span class="header_text_left">类别分布</span>
          </div>
          <div class="category_distribution_content">
            <PieChart id="category_distribution" :chart-data="categoryChartData" :name="categoryChartName" />
          </div>
        </div>
        <!-- 热词分析 -->
        <div class="hot_word_analysis">
          <div class="header_box">
            <span class="header_text_left">热词分析</span>
          </div>
          <div class="hot_word_analysis_content">
            <WordCloud chart-id="hotWordChart" :words="hotWordsData" @word-click="onWordClick" />
          </div>
        </div>
      </div>
      <div class="center-panel">
        <!-- 提案整体情况 -->
        <div class="proposal_overall_situation">
          <div class="header_box">
            <span class="header_text_left">提案整体情况</span>
          </div>
          <div class="proposal_overall_situation_content">
            <!-- 左侧数据卡片 -->
            <div class="left-section">
              <div class="data-card total-proposals">
                <div class="card-number">{{ proposalOverallData.totalProposals }}</div>
                <div class="card-label">提案总件数</div>
              </div>
              <div class="data-card approved-proposals">
                <div class="card-number">{{ proposalOverallData.approvedProposals }}</div>
                <div class="card-label">立案总件数</div>
              </div>
              <div class="data-card replied-proposals">
                <div class="card-number">{{ proposalOverallData.repliedProposals }}</div>
                <div class="card-label">答复总件数</div>
              </div>
            </div>
            <!-- 右侧图表区域 -->
            <div class="right-section">
              <div class="top-charts">
                <div class="chart-item approval-rate">
                  <CircularProgress id="approval-rate-chart" :percentage="proposalOverallData.approvalRate" label="立案率"
                    color="#00d4ff" />
                </div>
                <div class="chart-item reply-rate">
                  <CircularProgress id="reply-rate-chart" :percentage="proposalOverallData.replyRate" label="答复率"
                    color="#ffd700" />
                </div>
              </div>
              <div class="bottom-section">
                <div class="reply-pie-chart">
                  <PieChart id="reply-type-pie" :chart-data="replyTypeChartData" name="答复类型" />
                </div>
                <div class="reply-progress">
                  <ProgressBar :progress-data="replyTypeProgressData" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 各专委会提案数 -->
        <div class="committee_proposal_number">
          <div class="header_box">
            <span class="header_text_left">各专委会提案数</span>
          </div>
          <div class="committee_proposal_content">
            <BarChart id="committee_proposal" :chart-data="committeeProposalData" />
          </div>
        </div>
      </div>
      <div class="right-panel">
        <!-- 提交情况 -->
        <div class="submission_status">
          <div class="header_box">
            <span class="header_text_left">提交情况</span>
          </div>
          <div class="submission_status_content">
            <div class="submission_item" v-for="item in submissionStatusData" :key="item.name">
              <div class="submission_icon">
                <img :src="require(`../../../assets/largeScreen/${item.icon}`)" alt="">
                <div class="submission_name">{{ item.name }}</div>
              </div>
              <div class="submission_value">{{ item.value }}</div>
            </div>
          </div>
        </div>
        <!-- 办理单位统计前十 -->
        <div class="hand_unit">
          <div class="header_box">
            <span class="header_text_left">办理单位统计（前十）</span>
          </div>
          <div class="hand_unit_content">
            <RankingBarChart id="handling_unit_chart" :chart-data="handlingUnitData" :show-values="true"
              :max-value="100" />
          </div>
        </div>
        <!-- 重点提案 -->
        <div class="key_proposals">
          <div class="header_box">
            <span class="header_text_left">重点提案</span>
            <span class="header_text_right"></span>
          </div>
          <div class="key_proposals_list">
            <div v-for="(item, index) in keyProposalsData" :key="item.id" class="key_proposals_item"
              :class="{ 'with-bg-image': index % 2 === 0, 'with-bg-color': index % 2 === 1 }">
              <div class="key_proposals_content">
                <div class="key_proposals_title">{{ item.title }}</div>
                <div class="key_proposals_name">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useIndex } from '../screen.js'
import PieChart from '../components/PieChart.vue'
import WordCloud from '../components/WordCloud.vue'
import CircularProgress from '../components/CircularProgress.vue'
import ProgressBar from '../components/ProgressBar.vue'
import BarChart from '../components/BarChart.vue'
import RankingBarChart from '../components/RankingBarChart.vue'

export default {
  name: 'BigScreen',
  components: {
    PieChart,
    WordCloud,
    CircularProgress,
    ProgressBar,
    BarChart,
    RankingBarChart
  },
  data () {
    return {
      currentTime: '',
      categoryChartName: '类别分析',
      categoryChartData: [
        { name: '政府制约', value: 22.52 },
        { name: '县区市政', value: 18.33 },
        { name: '司法法治', value: 15.73 },
        { name: '区市政府', value: 11.34 },
        { name: '科技工商', value: 9.56 },
        { name: '教育文化', value: 8.09 },
        { name: '派出机构', value: 4.21 },
        { name: '社会事业', value: 3.71 },
        { name: '企事业', value: 3.65 },
        { name: '农村卫生', value: 3.21 },
        { name: '其他机构', value: 1.86 },
        { name: '各群体他', value: 1.02 }
      ],
      hotWordsData: [
        { name: '经济建设', value: 10 },
        { name: '人才培养', value: 9 },
        { name: 'AI技术', value: 6 },
        { name: '改革创新', value: 7 },
        { name: '教育', value: 5 },
        { name: '车辆交通', value: 6 },
        { name: '旅游', value: 5 },
        { name: '公共安全', value: 7 },
        { name: '智能化', value: 6 },
        { name: '电梯故障', value: 4 },
        { name: '社会保障', value: 6 },
        { name: '环境保护', value: 5 },
        { name: '医疗卫生', value: 7 },
        { name: '文化建设', value: 4 },
        { name: '科技创新', value: 8 }
      ],
      // 提案整体情况数据
      proposalOverallData: {
        totalProposals: 1500,
        approvedProposals: 600,
        repliedProposals: 600,
        approvalRate: 69,
        replyRate: 69
      },
      // 答复类型统一数据源
      replyTypeData: [
        {
          name: '面复',
          value: 360,
          color: '#00d4ff'
        },
        {
          name: '函复',
          value: 240,
          color: '#ffd700'
        }
      ],
      // 各专委会提案数数据
      committeeProposalData: [
        { name: '提案委', value: 43 },
        { name: '经济委', value: 67 },
        { name: '农业农村委', value: 84 },
        { name: '人口资源环境委', value: 52 },
        { name: '教科卫体委', value: 36 },
        { name: '社会和法制委', value: 66 },
        { name: '民族宗教委', value: 26 },
        { name: '港澳台侨外事委', value: 60 },
        { name: '文化文史和学习委', value: 46 }
      ],
      // 提交情况数据
      submissionStatusData: [
        {
          name: '委员提案',
          value: 270,
          icon: 'icon_committee_proposal_situation.png'
        },
        {
          name: '界别提案',
          value: 290,
          icon: 'icon_sector_proposal_situation.png'
        },
        {
          name: '组织提案',
          value: 170,
          icon: 'icon_organizational_proposal_situation.png'
        }
      ],
      // 办理单位统计前十数据
      handlingUnitData: [
        { name: '政府', value: 89 },
        { name: '政协办公厅', value: 75 },
        { name: '发展改革委', value: 70 },
        { name: '教育体育局', value: 63 },
        { name: '民政局', value: 60 },
        { name: '财政局', value: 50 },
        { name: '人社局', value: 46 },
        { name: '自然资源局', value: 46 },
        { name: '生态环境局', value: 44 },
        { name: '文化旅游局', value: 41 }
      ],
      // 重点提案数据
      keyProposalsData: [
        {
          id: 1,
          title: '市政协社会和法制工作办公室围绕"居家适老化改造"开展专题调研',
          name: '赵国胜'
        },
        {
          id: 2,
          title: '"与民同行 共创共赢"新格局下民营企业转型发展座谈会召开',
          name: '李颖之'
        },
        {
          id: 3,
          title: '"惠民生·基层行"义诊活动温暖人心',
          name: '王红妮'
        },
        {
          id: 4,
          title: '市科技局面复市政协科技界别提案',
          name: '张万强'
        },
        {
          id: 5,
          title: '市政协召开"推进数字化转型"专题协商会',
          name: '张万强'
        },
        {
          id: 6,
          title: '政协委员深入基层开展"三服务"活动',
          name: '张万强'
        }
      ]
    }
  },
  computed: {
    // 为饼图组件提供数据格式
    replyTypeChartData () {
      return this.replyTypeData.map(item => ({
        name: item.name,
        value: item.value,
        color: item.color
      }))
    },

    // 为进度条组件提供数据格式
    replyTypeProgressData () {
      const total = this.replyTypeData.reduce((sum, item) => sum + item.value, 0)
      return this.replyTypeData.map(item => ({
        label: item.name,
        value: item.value,
        percent: Math.round((item.value / total) * 100),
        color: item.color
      }))
    }
  },
  mounted () {
    this.initScreen()
    this.updateTime()
    this.timeInterval = setInterval(this.updateTime, 1000)
  },
  beforeDestroy () {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    initScreen () {
      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)
      calcRate()
      windowDraw()
    },
    updateTime () {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    // 返回首页
    goHome () {
      this.$router.push({ path: '/homeBox' })
    },
    // 词云点击事件
    onWordClick (word) {
      console.log('词汇点击:', word)
    }
  }
}
</script>

<style lang="scss" scoped>
.big-screen {
  width: 1920px;
  height: 1080px;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transform-origin: left top;
  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;
  background-size: cover;
  background-position: center;

  .screen-header {
    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;
    background-size: 100% 100%;
    background-position: center;
    height: 65px;
    display: flex;
    align-items: center;
    padding: 0 40px;

    .header-left {
      display: flex;
      gap: 20px;
      font-size: 14px;
      color: #8cc8ff;
      flex: 1;
    }

    .header-center {
      width: 60%;
      text-align: center;
    }

    .header-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .header-buttons {
        display: flex;
        gap: 15px;

        .header-btn {
          height: 42px;
          line-height: 42px;
          padding: 0 16px;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover {
            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);
            border-color: rgba(0, 181, 254, 1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);

            &::before {
              left: 100%;
            }
          }

          &.current-module-btn {
            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
            font-weight: bold;
            font-size: 16px;
            color: #FFFFFF;
          }

          &.home-btn {
            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
            font-weight: 400;
            font-size: 16px;
            color: #1FC6FF;
          }

          &.area-select-btn {
            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);
            border: 1px solid rgba(0, 181, 254, 0.5);
            border-radius: 6px;
            font-weight: 500;
            font-size: 16px;
            color: #1FC6FF;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-width: 120px;

            .dropdown-icon {
              margin-left: 8px;
              font-size: 12px;
              transition: transform 0.3s ease;
              color: #1FC6FF;

              &.active {
                transform: rotate(180deg);
              }
            }

            &:hover {
              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);
              border-color: rgba(0, 181, 254, 0.8);
            }
          }
        }
      }
    }
  }

  .screen-content {
    height: calc(100% - 65px);
    display: flex;
    padding: 35px 20px 0 20px;
    gap: 30px;

    .header_box {
      position: absolute;
      top: 15px;
      left: 24px;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header_text_left {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        cursor: pointer;
      }

      .header_text_right {
        font-size: 15px;
        color: #FFD600;
      }

      .header_text_center {
        font-size: 15px;
        color: #FFFFFF;
        display: flex;
        align-items: center;

        span {
          font-weight: 500;
          font-size: 24px;
          color: #02FBFB;
          margin: 0 10px 0 6px;
        }
      }
    }

    .left-panel,
    .right-panel {
      width: 470px;
      display: flex;
      flex-direction: column;
      gap: 30px 30px;
    }

    .left-panel {
      .category_distribution {
        background: url('../../../assets/largeScreen/category_distribution_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 585px;
        width: 100%;

        .category_distribution_content {
          height: calc(100% - 70px);
          margin-top: 70px;
        }
      }

      .hot_word_analysis {
        background: url('../../../assets/largeScreen/hot_word_analysis_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 320px;
        width: 100%;

        .hot_word_analysis_content {
          height: calc(100% - 92px);
          margin-top: 72px;
          margin-bottom: 20px;
          position: relative;
        }
      }
    }

    .right-panel {
      .submission_status {
        background: url('../../../assets/largeScreen/submission_status_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 245px;
        width: 100%;

        .submission_status_content {
          margin-top: 60px;
          margin-left: 20px;
          margin-right: 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: calc(100% - 80px);

          .submission_item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            height: 120px;
            margin: 0 10px;

            .submission_icon {
              width: 100px;
              height: 100px;
              margin-bottom: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              position: relative;
              z-index: 2;

              img {
                width: 100%;
                height: 100%;
                object-fit: contain;
              }

              // 文字覆盖在图片上
              .submission_name {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 14px;
                color: #FFFFFF;
                white-space: nowrap;
              }
            }

            .submission_value {
              font-weight: 500;
              font-size: 20px;
              color: #02FBFB;
              font-family: 'Arial', sans-serif;
            }
          }
        }
      }

      .hand_unit {
        background: url('../../../assets/largeScreen/hand_unit_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 315px;
        width: 100%;

        .hand_unit_content {
          margin-top: 60px;
          margin-left: 20px;
          margin-right: 20px;
          height: calc(100% - 80px);
        }
      }

      .key_proposals {
        background: url('../../../assets/largeScreen/key_proposals_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 315px;
        width: 100%;

        .key_proposals_list {
          margin-top: 60px;
          margin-left: 14px;
          margin-right: 14px;
          height: calc(100% - 70px);
          overflow-y: auto;

          &::-webkit-scrollbar {
            width: 4px;
          }

          &::-webkit-scrollbar-track {
            background: rgba(0, 30, 60, 0.3);
            border-radius: 2px;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(0, 212, 255, 0.4);
            border-radius: 2px;

            &:hover {
              background: rgba(0, 212, 255, 0.6);
            }
          }

          .key_proposals_item {
            margin-bottom: 12px;
            overflow: hidden;
            position: relative;

            &:last-child {
              margin-bottom: 0;
            }

            // 奇数项 - 背景图片样式
            &.with-bg-image {
              background: url('../../../assets/largeScreen/table_bg.png') no-repeat;
              background-size: 100% 100%;
              background-position: center;
            }

            // 偶数项 - 背景颜色样式
            &.with-bg-color {
              background: rgba(6, 79, 219, 0.05);
            }

            .key_proposals_content {
              padding: 12px 15px;
              position: relative;
              z-index: 2;
              display: flex;
              justify-content: space-between;
              align-items: center;

              .key_proposals_title {
                flex: 1;
                color: #fff;
                font-size: 16px;
                margin-right: 16px;
                // 文本溢出处理
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .key_proposals_name {
                flex-shrink: 0;
                font-size: 16px;
                color: #FFFFFF;
              }
            }
          }
        }
      }
    }

    .center-panel {
      flex: 1;
      gap: 30px;
      display: flex;
      flex-direction: column;

      .proposal_overall_situation {
        background: url('../../../assets/largeScreen/overall_situation_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        height: 505px;
        width: 100%;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .proposal_overall_situation_content {
          width: 100%;
          height: calc(100% - 70px);
          margin-top: 70px;
          margin-left: 40px;
          margin-right: 40px;
          display: flex;
          gap: 50px;

          .left-section {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            margin: 10px 0;

            .data-card {
              width: 133px;
              height: 98px;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;

              &.total-proposals {
                background: url('../../../assets/largeScreen/icon_proposal_total.png') no-repeat;
                background-size: 100% 100%;
                background-position: center;
              }

              &.approved-proposals {
                background: url('../../../assets/largeScreen/icon_proposal_total.png') no-repeat;
                background-size: 100% 100%;
                background-position: center;
              }

              &.replied-proposals {
                background: url('../../../assets/largeScreen/icon_reply_total.png') no-repeat;
                background-size: 100% 100%;
                background-position: center;
              }

              .card-number {
                font-weight: 500;
                font-size: 32px;
                margin-bottom: 8px;
              }

              &.total-proposals .card-number {
                color: #FFFFFF;
              }

              &.approved-proposals .card-number {
                color: #1FC6FF;
              }

              &.replied-proposals .card-number {
                color: #F5E74F;
              }

              .card-label {
                font-size: 16px;
                color: #FFFFFF;
                opacity: 0.9;
              }
            }
          }

          .right-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            // gap: 100px;

            .top-charts {
              display: flex;
              justify-content: space-evenly;

              .chart-item {
                width: 199px;
                height: 199px;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;

                &.approval-rate {
                  background: url('../../../assets/largeScreen/icon_case_filing.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                  z-index: 1; // 背景图最低优先级
                }

                &.reply-rate {
                  background: url('../../../assets/largeScreen/icon_reply_rate.png') no-repeat;
                  background-size: 100% 100%;
                  background-position: center;
                  z-index: 1; // 背景图最低优先级
                }

                // 确保圆形进度图在背景图中央，但在文字下方
                .circular-progress {
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  width: 140px;
                  height: 140px;
                  z-index: 50; // 图表中等优先级
                }
              }
            }

            .bottom-section {
              display: flex;
              justify-content: space-evenly;
              width: 100%;
              height: 230px;

              .reply-pie-chart {
                width: 55%;
                height: 100%;
              }

              .reply-progress {
                width: 45%;
                height: 100%;
                display: flex;
                align-items: center;
              }
            }
          }
        }
      }

      .committee_proposal_number {
        background: url('../../../assets/largeScreen/committee_proposal_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 400px;
        width: 100%;

        .committee_proposal_content {
          height: 100%;
          margin-top: 65px;
          margin-left: 16px;
          margin-right: 16px;
        }
      }
    }
  }
}
</style>
