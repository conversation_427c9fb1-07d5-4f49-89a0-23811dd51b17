{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\general-custom-topic\\custom-topic-add\\custom-topic-add.vue?vue&type=style&index=0&id=32c6301f&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\general-custom-topic\\custom-topic-add\\custom-topic-add.vue", "mtime": 1752541697687}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICcuL2NvbW1pdHRlZS1kYXRhLWFkZC5zY3NzJzsNCg=="}, {"version": 3, "sources": ["custom-topic-add.vue"], "names": [], "mappings": ";AAmLA", "file": "custom-topic-add.vue", "sourceRoot": "src/views/wisdomWarehouse/general-custom-topic/custom-topic-add", "sourcesContent": ["<template>\r\n  <div class=\"committee-data-add\">\r\n    <el-form\r\n      :model=\"form\"\r\n      :rules=\"rules\"\r\n      inline\r\n      ref=\"form\"\r\n      label-position=\"top\"\r\n      class=\"newForm\"\r\n    >\r\n      <el-form-item\r\n        label=\"标题\"\r\n        prop=\"name\"\r\n        class=\"form-title\"\r\n      >\r\n        <el-input\r\n          placeholder=\"请输入标题\"\r\n          v-model=\"form.name\"\r\n          clearable\r\n        >\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item\r\n        label=\"发布时间\"\r\n        prop=\"pubDate\"\r\n        class=\"form-input\"\r\n      >\r\n        <el-date-picker\r\n          v-model=\"form.pubDate\"\r\n          type=\"datetime\"\r\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n          placeholder=\"选择发布时间\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"排序\"\r\n                    class=\"form-input\">\r\n        <el-input-number style=\"width:100%;\"\r\n                         :min=\"1\"\r\n                         placeholder=\"请输入排序\"\r\n                         v-model=\"form.sort\"\r\n                         clearable>\r\n        </el-input-number>\r\n      </el-form-item>\r\n      <el-form-item\r\n        label=\"上传附件\"\r\n        class=\"form-upload\"\r\n      >\r\n        <zy-upload-file\r\n          ref=\"upload\"\r\n          :data=\"file\"\r\n          placeholder=\"仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式\"\r\n        ></zy-upload-file>\r\n      </el-form-item>\r\n      <!-- <el-form-item\r\n        label=\"外部链接\"\r\n        class=\"form-title\"\r\n      >\r\n        <el-input\r\n          placeholder=\"请输入外部链接\"\r\n          v-model=\"form.externalLinks\"\r\n          clearable\r\n        >\r\n        </el-input>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item\r\n        label=\"内容\"\r\n        class=\"form-ue\"\r\n      >\r\n        <wang-editor v-model='form.content'></wang-editor>\r\n      </el-form-item> -->\r\n      <div class=\"form-button\">\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"submitForm('form')\"\r\n        >提交</el-button>\r\n        <el-button @click=\"cancel\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'committeeDataAdd',\r\n  data () {\r\n    return {\r\n      form: {\r\n        name: '',\r\n        sort: '',\r\n        pubDate: '',\r\n        externalLinks: '',\r\n        content: ''\r\n      },\r\n      rules: {\r\n        name: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n        pubDate: [\r\n          { required: true, message: '选择发布时间', trigger: 'blur' }\r\n        ]\r\n      },\r\n      file: []\r\n    }\r\n  },\r\n  props: ['id', 'columnId'],\r\n  mounted () {\r\n    if (this.id) {\r\n      this.informationListInfo()\r\n    } else {\r\n      this.form.pubDate = this.$format(new Date(), 'YYYY-MM-DD HH:mm:ss')\r\n    }\r\n  },\r\n  methods: {\r\n    async informationListInfo () {\r\n      const res = await this.$api.wisdomWarehouse.assemblyInfo(this.id)\r\n      var { data } = res\r\n      this.form.name = data.name\r\n      this.form.sort = data.sort\r\n      this.form.pubDate = data.pubDate\r\n      this.form.externalLinks = data.externalLinks\r\n      this.form.content = data.content\r\n      if (data.attachmentList) {\r\n        data.attachmentList.forEach((item, index) => {\r\n          item.uid = item.id\r\n          item.name = item.fileName\r\n        })\r\n        this.file = data.attachmentList\r\n      }\r\n    },\r\n    /**\r\n   * 提交提案\r\n  */\r\n    submitForm (formName, type) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var attach = []\r\n          attach = this.$refs.upload.obtainId().join(',')\r\n          var url = '/assembly/add'\r\n          if (this.id) {\r\n            url = '/assembly/edit'\r\n          }\r\n          this.$api.wisdomWarehouse.addAssembly(url, {\r\n            id: this.id,\r\n            columnId: this.columnId,\r\n            name: this.form.name,\r\n            sort: this.form.sort,\r\n            pubDate: this.form.pubDate,\r\n            attachmentIds: attach,\r\n            externalLinks: this.form.externalLinks,\r\n            content: this.form.content\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('callback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    /**\r\n   * 取消按钮\r\n  */\r\n    cancel () {\r\n      this.$emit('callback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './committee-data-add.scss';\r\n</style>\r\n"]}]}