{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-select-checkbox\\zy-select-checkbox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-select-checkbox\\zy-select-checkbox.vue", "mtime": 1752541693550}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAkDA;EACAA,wBADA;;EAEAC;IACA;MACAC,WADA;MAEAC,OAFA;MAGAC,cAHA;MAIAC;IAJA;EAMA,CATA;;EAUAC;IACAC,cADA;IAEA;IACAN;MACAO,WADA;MAEAC;IAFA,CAHA;IAOA;IACAH;MACAE,YADA;MAEAC;QACA;UACAC,oBADA;UAEAC;QAFA;MAIA;IAPA,CARA;IAiBA;IACAC;MACAJ,YADA;MAEAC;IAFA,CAlBA;IAsBAI;MACAL,YADA;MAEAC;IAFA,CAtBA;IA0BAK;MACAN,YADA;MAEAC;IAFA,CA1BA;IA8BA;IACAM;MACAP,aADA;MAEAC;IAFA,CA/BA;IAmCA;IACAO;MACAR,aADA;MAEAC;IAFA,CApCA;IAwCA;IACAQ;MACAT,YADA;MAEAC;IAFA,CAzCA;IA6CAS;MACAV,YADA;MAEAC;IAFA,CA7CA;IAiDAU;MACAX;IADA;EAjDA,CAVA;EA+DAY;IACAC,aADA;IAEAC;EAFA,CA/DA;;EAmEAC;IACA;EACA,CArEA;;EAsEAC;IACAjB;MACA;IACA,CAHA;;IAIAN;MACA;IACA,CANA;;IAOAC;MACA;IACA;;EATA,CAtEA;EAiFAuB;IACAC;MACA;MACA;IACA,CAJA;;IAKAC;MACA;MACA;MACA;QACA;MACA,CAFA;IAGA,CAXA;;IAYA;IACAC;MACA;MACA;MACA;;MACA;QACA;UACAC;UACAC;QACA;MACA;;MACA;IACA,CAxBA;;IAyBA;IACAC;MACA;MACA9B;QACA+B;MACA,CAFA;MAGA;MACA;IACA,CAjCA;;IAkCA;IACAC;MACA;MACAhC;QACA;UACAiC;QACA;;QACA;UACAA;QACA;MACA,CAPA;MAQA;IACA,CA9CA;;IA+CA;IACAC;MACA;QACA;UACA;YACA;cACA;YACA,CAFA;YAGA;YACA;YACA;cACArB,0CADA;cAEAN;YAFA;YAIA;UACA;QACA;;QACA;QACA;MACA,CAjBA,MAiBA;QACA;QACA;QACA;QACA;MACA;;MACA;MACA;IACA,CA1EA;;IA2EA;IACA4B;MACA;QACA;MACA;;MACA;MACA;MACA;MACA;MACA;QACA;MACA,CAFA;MAGA;MACA;IACA;;EAzFA;AAjFA", "names": ["name", "data", "keyword", "ids", "selectData", "options_show", "props", "value", "type", "default", "children", "label", "trigger", "placeholder", "message", "disabled", "child", "width", "nodeKey", "max", "model", "prop", "event", "mounted", "watch", "methods", "filterNode", "dataMethods", "getArray", "hash", "result", "selected", "arr", "selectedMethods", "obj", "selectedClick", "remove"], "sourceRoot": "src/components/zy-select-checkbox", "sources": ["zy-select-checkbox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-select-checkbox\"\r\n       :style=\"{ width: width + 'px' }\">\r\n    <el-popover popper-class=\"zy-select-checkbox-popover\"\r\n                :trigger=\"trigger\"\r\n                :disabled=\"disabled\"\r\n                :width=\"width\"\r\n                v-model=\"options_show\">\r\n      <div slot=\"reference\"\r\n           :class=\"['zy-select-checkbox-input',disabled?'zy-select-checkbox-input-disabled':'']\">\r\n        <div :class=\"['zy-select-checkbox-input-icon',options_show?'zy-select-checkbox-input-icon-a':'']\">\r\n          <i class=\"el-icon-arrow-down\"></i>\r\n        </div>\r\n        <div v-if=\"!selectData.length\"\r\n             class=\"zy-select-checkbox-input-text\">{{placeholder}}</div>\r\n        <el-tag v-for=\"tag in selectData\"\r\n                :key=\"tag[nodeKey]\"\r\n                size=\"medium\"\r\n                closable\r\n                :disable-transitions=\"false\"\r\n                @close=\"remove(tag)\">\r\n          {{tag[props.label]}}\r\n        </el-tag>\r\n      </div>\r\n\r\n      <el-scrollbar class=\"zy-select-checkbox-box-box\">\r\n        <div>\r\n          <el-input placeholder=\"请输入关键字查询\"\r\n                    v-model=\"keyword\"\r\n                    clearable>\r\n          </el-input>\r\n        </div>\r\n        <el-scrollbar class=\"zy-select-checkbox-box\">\r\n          <el-tree :class=\"['zy-select-checkbox-tree',child? 'zy-select-checkbox-tree-a':'']\"\r\n                   ref=\"tree\"\r\n                   :data=\"data\"\r\n                   show-checkbox\r\n                   :props=\"props\"\r\n                   check-strictly\r\n                   highlight-current\r\n                   :node-key=\"nodeKey\"\r\n                   :filter-node-method=\"filterNode\"\r\n                   @check-change=\"selectedClick\">\r\n          </el-tree>\r\n        </el-scrollbar>\r\n      </el-scrollbar>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zySelectCheckbox',\r\n  data () {\r\n    return {\r\n      keyword: '',\r\n      ids: [],\r\n      selectData: [],\r\n      options_show: false\r\n    }\r\n  },\r\n  props: {\r\n    value: [Array],\r\n    // 数据\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    // 触发方式 click/focus/hover/manual\r\n    trigger: {\r\n      type: String,\r\n      default: 'click'\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '请选择内容'\r\n    },\r\n    message: {\r\n      type: String,\r\n      default: '选择的内容'\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 是否禁用\r\n    child: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 宽度\r\n    width: {\r\n      type: String,\r\n      default: '296'\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    max: {\r\n      type: Number\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  mounted () {\r\n    this.dataMethods()\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      this.dataMethods()\r\n    },\r\n    data (val) {\r\n      this.dataMethods()\r\n    },\r\n    keyword (val) {\r\n      this.$refs.tree.filter(val)\r\n    }\r\n  },\r\n  methods: {\r\n    filterNode (value, data) {\r\n      if (!value) return true\r\n      return data[this.props.label].indexOf(value) !== -1\r\n    },\r\n    dataMethods () {\r\n      this.ids = this.getArray(this.value)\r\n      this.selected(this.ids)\r\n      this.$nextTick(function () {\r\n        this.$refs.tree.setCheckedKeys(this.ids)\r\n      })\r\n    },\r\n    // 数组去重\r\n    getArray (a) {\r\n      var hash = {}\r\n      var len = a.length\r\n      var result = []\r\n      for (var i = 0; i < len; i++) {\r\n        if (!hash[a[i]]) {\r\n          hash[a[i]] = true\r\n          result.push(a[i])\r\n        }\r\n      }\r\n      return result\r\n    },\r\n    // 首次进来传入的数据\r\n    selected (data) {\r\n      var arr = []\r\n      data.forEach(item => {\r\n        arr = arr.concat(this.selectedMethods(this.data, item))\r\n      })\r\n      this.selectData = arr\r\n      this.$emit('choose-click', this.selectData)\r\n    },\r\n    // 首次进来默认选中\r\n    selectedMethods (data, id) {\r\n      var obj = []\r\n      data.forEach(item => {\r\n        if (item[this.nodeKey] === id) {\r\n          obj.push(item)\r\n        }\r\n        if (item.children && item.children.length > 0) {\r\n          obj = obj.concat(this.selectedMethods(item.children, id))\r\n        }\r\n      })\r\n      return obj\r\n    },\r\n    // 下拉框选中事件\r\n    selectedClick (data, type) {\r\n      if (type) {\r\n        if (this.max) {\r\n          if (this.ids.length === this.max) {\r\n            this.$nextTick(function () {\r\n              this.$refs.tree.setCheckedKeys(this.ids)\r\n            })\r\n            this.$emit('id', this.ids)\r\n            this.$emit('choose-click', this.selectData)\r\n            this.$message({\r\n              message: `${this.message}不能超过${this.max}个`,\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n        }\r\n        this.ids.push(data[this.nodeKey])\r\n        this.selectData.push(data)\r\n      } else {\r\n        var ids = this.ids\r\n        var selectData = this.selectData\r\n        this.ids = ids.filter(item => item !== data[this.nodeKey])\r\n        this.selectData = selectData.filter(item => item[this.nodeKey] !== data[this.nodeKey])\r\n      }\r\n      this.$emit('id', this.ids)\r\n      this.$emit('choose-click', this.selectData)\r\n    },\r\n    // 移除tag\r\n    remove (data) {\r\n      if (this.disabled) {\r\n        return\r\n      }\r\n      var ids = this.ids\r\n      var selectData = this.selectData\r\n      this.ids = ids.filter(item => item !== data[this.nodeKey])\r\n      this.selectData = selectData.filter(item => item[this.nodeKey] !== data[this.nodeKey])\r\n      this.$nextTick(function () {\r\n        this.$refs.tree.setCheckedKeys(this.ids)\r\n      })\r\n      this.$emit('id', this.ids)\r\n      this.$emit('choose-click', this.selectData)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-select-checkbox.scss\";\r\n</style>\r\n"]}]}