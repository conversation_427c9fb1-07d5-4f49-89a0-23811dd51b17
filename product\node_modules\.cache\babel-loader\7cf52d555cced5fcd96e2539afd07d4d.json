{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\element-ui\\lib\\table.js", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\element-ui\\lib\\table.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["module", "exports", "modules", "installedModules", "__webpack_require__", "moduleId", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "getDefault", "getModuleExports", "object", "property", "prototype", "hasOwnProperty", "p", "s", "__webpack_exports__", "normalizeComponent", "scriptExports", "render", "staticRenderFns", "functionalTemplate", "injectStyles", "scopeId", "moduleIdentifier", "shadowMode", "options", "_compiled", "functional", "_scopeId", "hook", "context", "$vnode", "ssrContext", "parent", "__VUE_SSR_CONTEXT__", "_registeredComponents", "add", "_ssrRegister", "$root", "$options", "shadowRoot", "_injectStyles", "originalRender", "renderWithStyleInjection", "h", "existing", "beforeCreate", "concat", "require", "getCell", "orderBy", "getColumnById", "getColumnByKey", "getColumnByCell", "getRowIdentity", "getKeysMap", "mergeOptions", "parse<PERSON>idth", "parse<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseHeight", "compose", "toggleRowStatus", "walkTreeNode", "element_ui_src_utils_util__WEBPACK_IMPORTED_MODULE_0__", "element_ui_src_utils_util__WEBPACK_IMPORTED_MODULE_0___default", "_typeof", "iterator", "obj", "constructor", "event", "cell", "target", "tagName", "toUpperCase", "parentNode", "isObject", "array", "sortKey", "reverse", "sortMethod", "sortBy", "Array", "isArray", "length", "<PERSON><PERSON><PERSON>", "index", "map", "by", "$value", "compare", "a", "b", "len", "sort", "order", "item", "table", "columnId", "column", "columns", "for<PERSON>ach", "id", "column<PERSON>ey", "matches", "className", "match", "row", "<PERSON><PERSON><PERSON>", "Error", "indexOf", "split", "current", "arrayMap", "hasOwn", "defaults", "config", "width", "undefined", "parseInt", "isNaN", "min<PERSON><PERSON><PERSON>", "height", "test", "_len", "arguments", "funcs", "_key", "arg", "reduce", "apply", "statusArr", "newVal", "changed", "included", "addRow", "push", "removeRow", "splice", "root", "cb", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isNil", "_walker", "children", "level", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "class", "fit", "stripe", "border", "isGroup", "isHidden", "maxHeight", "layout", "scrollX", "scrollY", "store", "states", "isComplex", "data", "tableSize", "on", "mouseleave", "$event", "handleMouseLeave", "ref", "_t", "showHeader", "directives", "rawName", "handleHeaderFooterMousewheel", "expression", "style", "bodyWidth", "attrs", "defaultSort", "_e", "scrollPosition", "bodyHeight", "rowClassName", "rowStyle", "highlight", "highlightCurrentRow", "emptyBlockStyle", "_v", "_s", "emptyText", "$slots", "append", "showSummary", "sumText", "summaryMethod", "fixedColumns", "handleFixedMousewheel", "fixedWidth", "fixedHeight", "fixed", "top", "headerHeight", "fixedBodyHeight", "appendHeight", "rightFixedColumns", "rightFixedWidth", "right", "gutterWidth", "resizeProxyVisible", "_withStripped", "checkbox_", "checkbox_default", "external_throttle_debounce_", "resize_event_", "external_normalize_wheel_", "external_normalize_wheel_default", "isFirefox", "navigator", "userAgent", "toLowerCase", "mousewheel_mousewheel", "mousewheel", "element", "callback", "addEventListener", "normalized", "directives_mousewheel", "el", "binding", "locale_", "locale_default", "migrating_", "migrating_default", "external_vue_", "external_vue_default", "merge_", "merge_default", "util", "expand", "defaultExpandAll", "expandRows", "methods", "updateExpandRows", "_states", "_states$data", "slice", "expandRowsMap", "prev", "rowId", "rowInfo", "toggleRowExpansion", "expanded", "$emit", "scheduleLayout", "setExpandRowKeys", "row<PERSON>eys", "assertRowKey", "_states2", "keysMap", "cur", "info", "isRowExpanded", "_states3", "_states3$expandRows", "expandMap", "util_", "_currentRowKey", "currentRow", "setCurrentRowKey", "setCurrentRowByKey", "restoreCurrentRowKey", "updateCurrentRow", "oldCurrentRow", "updateCurrentRowData", "currentRowKey", "_extends", "assign", "source", "tree", "expandRowKeys", "treeData", "indent", "lazy", "lazyTreeNodeMap", "lazyColumnIdentifier", "childrenColumnName", "computed", "normalizedData", "normalize", "normalizedLazyNode", "keys", "res", "watch", "parentId", "updateTreeData", "nested", "newTreeData", "oldTreeData", "rootLazyRowKeys", "getExpanded", "oldValue", "newValue", "_ref", "_ref$loaded", "loaded", "_ref$loading", "loading", "lazy<PERSON>eys", "lazy<PERSON><PERSON><PERSON><PERSON><PERSON>n", "_ref2", "_ref2$loaded", "_ref2$loading", "updateTableScrollY", "updateTreeExpandKeys", "toggleTreeExpansion", "_states4", "oldExpanded", "loadOrToggle", "_states5", "loadData", "treeNode", "_this", "load", "rawTreeData", "_states6", "$set", "watcher_sortData", "sortData", "sortingColumn", "sortable", "sortProp", "sortOrder", "doFlattenColumns", "result", "watcher", "extend", "_columns", "originColumns", "leafColumns", "fixedLeafColumns", "rightFixedLeafColumns", "leafColumns<PERSON>ength", "fixedLeafColumnsLength", "rightFixedLeafColumnsLength", "isAllSelected", "selection", "reserveSelection", "selectOnIndeterminate", "selectable", "filters", "filteredData", "hoverRow", "mixins", "updateColumns", "filter", "type", "unshift", "notFixedColumns", "needUpdateColumns", "debouncedUpdateLayout", "isSelected", "_states$selection", "clearSelection", "oldSelection", "cleanSelection", "deleted", "selectedMap", "dataMap", "newSelection", "toggleRowSelection", "selected", "emitChange", "_toggleAllSelection", "selectionChanged", "updateSelectionByRowKey", "updateAllSelected", "selectedCount", "j", "isRowSelectable", "updateFilters", "values", "col", "updateSort", "prop", "execFilter", "_data", "filterMethod", "some", "execSort", "execQ<PERSON>y", "ignore", "clearFilter", "columnKeys", "_table$$refs", "$refs", "tableHeader", "fixedTableHeader", "rightFixedTableHeader", "panels", "filterPanels", "find", "filteredValue", "commit", "silent", "multi", "clearSort", "setExpandRowKeysAdapter", "val", "toggleRowExpansionAdapter", "hasExpandColumn", "mutations", "setData", "dataInstanceChanged", "insertColumn", "$ready", "removeColumn", "init", "changeSortCondition", "ingore", "filterChange", "newFilters", "toggleAllSelection", "rowSelectedChanged", "setHoverRow", "setCurrentRow", "args", "nextTick", "updateScrollY", "src_store", "debounce_", "debounce_default", "createStore", "initialState", "mapStates", "mapper", "fn", "console", "error", "scrollbar_width_", "scrollbar_width_default", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "table_layout_TableLayout", "TableLayout", "observers", "tableHeight", "footerHeight", "viewportHeight", "bodyWrapper", "$el", "body", "querySelector", "prevScrollY", "offsetHeight", "setHeight", "$isServer", "updateElsHeight", "setMaxHeight", "getFlattenColumns", "flattenColumns", "isColumnGroup", "_this2", "headerWrapper", "appendWrapper", "footerWrapper", "headerTrElm", "noneHeader", "headerDisplayNone", "offsetWidth", "clientHeight", "noData", "notifyObservers", "elm", "headerChild", "getComputedStyle", "display", "parentElement", "updateColumnsWidth", "clientWidth", "body<PERSON><PERSON><PERSON><PERSON><PERSON>", "flexColumns", "realWidth", "scrollYWidth", "totalFlexWidth", "allColumnsWidth", "flexWidthPerPixel", "none<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flexWidth", "Math", "floor", "max", "resizeState", "addObserver", "observer", "removeObserver", "_this3", "onColumnsChange", "onScrollableChange", "table_layout", "dom_", "tooltip_", "tooltip_default", "layout_observer", "created", "tableLayout", "destroyed", "mounted", "updated", "__updated__", "cols", "querySelectorAll", "columnsMap", "getAttribute", "setAttribute", "ths", "_i", "_j", "th", "table_row_extends", "table_row", "props", "components", "ElCheckbox", "$index", "firstDefaultColumnIndex", "treeRowData", "treeIndent", "_columnsHidden", "columnsHidden", "isExpanded", "cellIndex", "_getSpan", "getSpan", "rowspan", "colspan", "columnData", "getColspanRealWidth", "noLazyChildren", "getCellStyle", "getCellClass", "mouseenter", "handleCellMouseEnter", "handleCellMouseLeave", "renderCell", "_renderProxy", "table_body_extends", "table_body", "ElTooltip", "TableRow", "required", "Boolean", "String", "Function", "cellspacing", "cellpadding", "acc", "wrappedRowRender", "effect", "tooltipEffect", "placement", "content", "tooltipContent", "$parent", "leftFixedLeafCount", "rightFixedLeafCount", "columnsCount", "leftFixedCount", "rightFixedCount", "isColumnHidden", "storeStatesHoverRow", "oldVal", "raf", "window", "requestAnimationFrame", "setTimeout", "rows", "oldRow", "newRow", "activateTooltip", "tooltip", "handleShowPopper", "getKeyOfRow", "rowIndex", "columnIndex", "spanMethod", "getRowStyle", "getRowClass", "classes", "cellStyle", "align", "cellClassName", "join", "widthArr", "_ref3", "hoverState", "cellChild", "childNodes", "range", "document", "createRange", "setStart", "setEnd", "rangeWidth", "getBoundingClientRect", "padding", "scrollWidth", "innerText", "textContent", "referenceElm", "popper", "do<PERSON><PERSON>roy", "setExpectedState", "handleClosePopper", "oldHoverState", "handleMouseEnter", "handleContextMenu", "handleEvent", "handleDoubleClick", "handleClick", "rowRender", "_this4", "rowClasses", "displayStyle", "nativeOn", "dblclick", "click", "contextmenu", "_", "_this5", "_store$states", "renderExpanded", "tr", "tmp", "traverse", "node", "innerTreeRowData", "<PERSON><PERSON><PERSON>", "_nodes", "nodes", "filter_panelvue_type_template_id_7f2c919f_render", "multiple", "handleOutsideClick", "showPopper", "model", "$$v", "_l", "label", "text", "disabled", "handleConfirm", "handleReset", "filterValue", "handleSelect", "isActive", "filter_panelvue_type_template_id_7f2c919f_staticRenderFns", "vue_popper_", "vue_popper_default", "popup_", "clickoutside_", "clickoutside_default", "dropdowns", "dropdown", "contains", "open", "close", "checkbox_group_", "checkbox_group_default", "scrollbar_", "scrollbar_default", "filter_panelvue_type_script_lang_js_", "Clickoutside", "ElCheckboxGroup", "ElScrollbar", "default", "confirmFilter", "set", "filterMultiple", "<PERSON><PERSON><PERSON><PERSON>", "updatePopper", "$watch", "filterOpened", "popperJS", "_popper", "zIndex", "nextZIndex", "src_filter_panelvue_type_script_lang_js_", "componentNormalizer", "component", "api", "__file", "filter_panel", "table_header_extends", "getAllColumns", "convertToRows", "maxLevel", "colSpan", "subColumn", "allColumns", "rowSpan", "table_header", "columnRows", "<PERSON><PERSON><PERSON>", "getHeaderRowStyle", "getHeaderRowClass", "mousemove", "handleMouseMove", "handleMouseOut", "mousedown", "handleMouseDown", "handleHeaderClick", "handleHeaderContextMenu", "getHeaderCellStyle", "getHeaderCellClass", "labelClassName", "renderHeader", "handleSortClick", "filterable", "handleFilterClick", "_default", "$nextTick", "_defaultSort", "<PERSON><PERSON><PERSON><PERSON>", "$destroy", "isCellHidden", "start", "after", "headerRowStyle", "headerRowClassName", "headerCellStyle", "headerAlign", "headerCellClassName", "stopPropagation", "filterPanel", "filterPlacement", "$mount", "createElement", "draggingColumn", "dragging", "tableEl", "tableLeft", "left", "columnEl", "columnRect", "minLeft", "dragState", "startMouseLeft", "clientX", "startLeft", "startColumnLeft", "resizeProxy", "onselectstart", "ondragstart", "deltaLeft", "proxyLeft", "handleMouseUp", "_dragState", "finalLeft", "columnWidth", "cursor", "removeEventListener", "resizable", "rect", "bodyStyle", "pageX", "toggleOrder", "sortOrders", "givenOrder", "table_footer_extends", "table_footer", "sums", "Number", "precisions", "notNumber", "decimal", "precision", "curr", "parseFloat", "toFixed", "min", "getRowClasses", "before", "tablevue_type_script_lang_js_extends", "tableIdSeed", "tablevue_type_script_lang_js_", "Mousewheel", "size", "treeProps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TableHeader", "TableFooter", "TableBody", "getMigratingConfig", "events", "abs", "spinY", "currentScrollTop", "scrollTop", "pixelY", "preventDefault", "scrollHeight", "ceil", "scrollLeft", "pixelX", "syncPostion", "_bodyWrapper", "_$refs", "fixedBodyWrapper", "rightFixedBodyWrapper", "maxScrollLeftPosition", "throttleSyncPostion", "onScroll", "evt", "bindEvents", "passive", "resizeListener", "unbindEvents", "shouldUpdateLayout", "_resizeState", "oldWidth", "oldHeight", "shouldUpdateHeight", "doLayout", "$ELEMENT", "_layout", "_layout2", "_layout2$headerHeight", "_layout2$footerHeight", "bottom", "tableData", "immediate", "handler", "tableId", "_treeProps", "_treeProps$hasChildre", "_treeProps$children", "src_tablevue_type_script_lang_js_", "table_component", "table_api", "src_table", "install", "<PERSON><PERSON>", "packages_table"], "sources": ["D:/zy/xm/pc/qdzx/product/node_modules/element-ui/lib/table.js"], "sourcesContent": ["module.exports =\n/******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__webpack_require__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__webpack_require__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __webpack_require__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__webpack_require__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"/dist/\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 57);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"a\", function() { return normalizeComponent; });\n/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nfunction normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode /* vue-cli only */\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () { injectStyles.call(this, this.$root.$options.shadowRoot) }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functioal component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n\n\n/***/ }),\n/* 1 */,\n/* 2 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/dom\");\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/util\");\n\n/***/ }),\n/* 4 */,\n/* 5 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/vue-popper\");\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/mixins/locale\");\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"vue\");\n\n/***/ }),\n/* 8 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"b\", function() { return getCell; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"i\", function() { return orderBy; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"d\", function() { return getColumnById; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"e\", function() { return getColumnByKey; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"c\", function() { return getColumnByCell; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"g\", function() { return getRowIdentity; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"f\", function() { return getKeysMap; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"h\", function() { return mergeOptions; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"l\", function() { return parseWidth; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"k\", function() { return parseMinWidth; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"j\", function() { return parseHeight; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"a\", function() { return compose; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"m\", function() { return toggleRowStatus; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"n\", function() { return walkTreeNode; });\n/* harmony import */ var element_ui_src_utils_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);\n/* harmony import */ var element_ui_src_utils_util__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(element_ui_src_utils_util__WEBPACK_IMPORTED_MODULE_0__);\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\n\n\nvar getCell = function getCell(event) {\n  var cell = event.target;\n\n  while (cell && cell.tagName.toUpperCase() !== 'HTML') {\n    if (cell.tagName.toUpperCase() === 'TD') {\n      return cell;\n    }\n    cell = cell.parentNode;\n  }\n\n  return null;\n};\n\nvar isObject = function isObject(obj) {\n  return obj !== null && (typeof obj === 'undefined' ? 'undefined' : _typeof(obj)) === 'object';\n};\n\nvar orderBy = function orderBy(array, sortKey, reverse, sortMethod, sortBy) {\n  if (!sortKey && !sortMethod && (!sortBy || Array.isArray(sortBy) && !sortBy.length)) {\n    return array;\n  }\n  if (typeof reverse === 'string') {\n    reverse = reverse === 'descending' ? -1 : 1;\n  } else {\n    reverse = reverse && reverse < 0 ? -1 : 1;\n  }\n  var getKey = sortMethod ? null : function (value, index) {\n    if (sortBy) {\n      if (!Array.isArray(sortBy)) {\n        sortBy = [sortBy];\n      }\n      return sortBy.map(function (by) {\n        if (typeof by === 'string') {\n          return Object(element_ui_src_utils_util__WEBPACK_IMPORTED_MODULE_0__[\"getValueByPath\"])(value, by);\n        } else {\n          return by(value, index, array);\n        }\n      });\n    }\n    if (sortKey !== '$key') {\n      if (isObject(value) && '$value' in value) value = value.$value;\n    }\n    return [isObject(value) ? Object(element_ui_src_utils_util__WEBPACK_IMPORTED_MODULE_0__[\"getValueByPath\"])(value, sortKey) : value];\n  };\n  var compare = function compare(a, b) {\n    if (sortMethod) {\n      return sortMethod(a.value, b.value);\n    }\n    for (var i = 0, len = a.key.length; i < len; i++) {\n      if (a.key[i] < b.key[i]) {\n        return -1;\n      }\n      if (a.key[i] > b.key[i]) {\n        return 1;\n      }\n    }\n    return 0;\n  };\n  return array.map(function (value, index) {\n    return {\n      value: value,\n      index: index,\n      key: getKey ? getKey(value, index) : null\n    };\n  }).sort(function (a, b) {\n    var order = compare(a, b);\n    if (!order) {\n      // make stable https://en.wikipedia.org/wiki/Sorting_algorithm#Stability\n      order = a.index - b.index;\n    }\n    return order * reverse;\n  }).map(function (item) {\n    return item.value;\n  });\n};\n\nvar getColumnById = function getColumnById(table, columnId) {\n  var column = null;\n  table.columns.forEach(function (item) {\n    if (item.id === columnId) {\n      column = item;\n    }\n  });\n  return column;\n};\n\nvar getColumnByKey = function getColumnByKey(table, columnKey) {\n  var column = null;\n  for (var i = 0; i < table.columns.length; i++) {\n    var item = table.columns[i];\n    if (item.columnKey === columnKey) {\n      column = item;\n      break;\n    }\n  }\n  return column;\n};\n\nvar getColumnByCell = function getColumnByCell(table, cell) {\n  var matches = (cell.className || '').match(/el-table_[^\\s]+/gm);\n  if (matches) {\n    return getColumnById(table, matches[0]);\n  }\n  return null;\n};\n\nvar getRowIdentity = function getRowIdentity(row, rowKey) {\n  if (!row) throw new Error('row is required when get row identity');\n  if (typeof rowKey === 'string') {\n    if (rowKey.indexOf('.') < 0) {\n      return row[rowKey];\n    }\n    var key = rowKey.split('.');\n    var current = row;\n    for (var i = 0; i < key.length; i++) {\n      current = current[key[i]];\n    }\n    return current;\n  } else if (typeof rowKey === 'function') {\n    return rowKey.call(null, row);\n  }\n};\n\nvar getKeysMap = function getKeysMap(array, rowKey) {\n  var arrayMap = {};\n  (array || []).forEach(function (row, index) {\n    arrayMap[getRowIdentity(row, rowKey)] = { row: row, index: index };\n  });\n  return arrayMap;\n};\n\nfunction hasOwn(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nfunction mergeOptions(defaults, config) {\n  var options = {};\n  var key = void 0;\n  for (key in defaults) {\n    options[key] = defaults[key];\n  }\n  for (key in config) {\n    if (hasOwn(config, key)) {\n      var value = config[key];\n      if (typeof value !== 'undefined') {\n        options[key] = value;\n      }\n    }\n  }\n  return options;\n}\n\nfunction parseWidth(width) {\n  if (width !== undefined) {\n    width = parseInt(width, 10);\n    if (isNaN(width)) {\n      width = null;\n    }\n  }\n  return width;\n}\n\nfunction parseMinWidth(minWidth) {\n  if (typeof minWidth !== 'undefined') {\n    minWidth = parseWidth(minWidth);\n    if (isNaN(minWidth)) {\n      minWidth = 80;\n    }\n  }\n  return minWidth;\n};\n\nfunction parseHeight(height) {\n  if (typeof height === 'number') {\n    return height;\n  }\n  if (typeof height === 'string') {\n    if (/^\\d+(?:px)?$/.test(height)) {\n      return parseInt(height, 10);\n    } else {\n      return height;\n    }\n  }\n  return null;\n}\n\n// https://github.com/reduxjs/redux/blob/master/src/compose.js\nfunction compose() {\n  for (var _len = arguments.length, funcs = Array(_len), _key = 0; _key < _len; _key++) {\n    funcs[_key] = arguments[_key];\n  }\n\n  if (funcs.length === 0) {\n    return function (arg) {\n      return arg;\n    };\n  }\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n  return funcs.reduce(function (a, b) {\n    return function () {\n      return a(b.apply(undefined, arguments));\n    };\n  });\n}\n\nfunction toggleRowStatus(statusArr, row, newVal) {\n  var changed = false;\n  var index = statusArr.indexOf(row);\n  var included = index !== -1;\n\n  var addRow = function addRow() {\n    statusArr.push(row);\n    changed = true;\n  };\n  var removeRow = function removeRow() {\n    statusArr.splice(index, 1);\n    changed = true;\n  };\n\n  if (typeof newVal === 'boolean') {\n    if (newVal && !included) {\n      addRow();\n    } else if (!newVal && included) {\n      removeRow();\n    }\n  } else {\n    if (included) {\n      removeRow();\n    } else {\n      addRow();\n    }\n  }\n  return changed;\n}\n\nfunction walkTreeNode(root, cb) {\n  var childrenKey = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'children';\n  var lazyKey = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'hasChildren';\n\n  var isNil = function isNil(array) {\n    return !(Array.isArray(array) && array.length);\n  };\n\n  function _walker(parent, children, level) {\n    cb(parent, children, level);\n    children.forEach(function (item) {\n      if (item[lazyKey]) {\n        cb(item, null, level + 1);\n        return;\n      }\n      var children = item[childrenKey];\n      if (!isNil(children)) {\n        _walker(item, children, level + 1);\n      }\n    });\n  }\n\n  root.forEach(function (item) {\n    if (item[lazyKey]) {\n      cb(item, null, 0);\n      return;\n    }\n    var children = item[childrenKey];\n    if (!isNil(children)) {\n      _walker(item, children, 0);\n    }\n  });\n}\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/merge\");\n\n/***/ }),\n/* 10 */,\n/* 11 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/mixins/migrating\");\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/clickoutside\");\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/popup\");\n\n/***/ }),\n/* 14 */,\n/* 15 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/scrollbar\");\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/resize-event\");\n\n/***/ }),\n/* 17 */,\n/* 18 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/checkbox\");\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"throttle-debounce/debounce\");\n\n/***/ }),\n/* 20 */,\n/* 21 */,\n/* 22 */,\n/* 23 */,\n/* 24 */,\n/* 25 */,\n/* 26 */,\n/* 27 */,\n/* 28 */,\n/* 29 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/tooltip\");\n\n/***/ }),\n/* 30 */,\n/* 31 */,\n/* 32 */,\n/* 33 */,\n/* 34 */,\n/* 35 */,\n/* 36 */,\n/* 37 */,\n/* 38 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/scrollbar-width\");\n\n/***/ }),\n/* 39 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/checkbox-group\");\n\n/***/ }),\n/* 40 */,\n/* 41 */,\n/* 42 */,\n/* 43 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"throttle-debounce\");\n\n/***/ }),\n/* 44 */,\n/* 45 */,\n/* 46 */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"normalize-wheel\");\n\n/***/ }),\n/* 47 */,\n/* 48 */,\n/* 49 */,\n/* 50 */,\n/* 51 */,\n/* 52 */,\n/* 53 */,\n/* 54 */,\n/* 55 */,\n/* 56 */,\n/* 57 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n\n// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./packages/table/src/table.vue?vue&type=template&id=493fe34e&\nvar render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    {\n      staticClass: \"el-table\",\n      class: [\n        {\n          \"el-table--fit\": _vm.fit,\n          \"el-table--striped\": _vm.stripe,\n          \"el-table--border\": _vm.border || _vm.isGroup,\n          \"el-table--hidden\": _vm.isHidden,\n          \"el-table--group\": _vm.isGroup,\n          \"el-table--fluid-height\": _vm.maxHeight,\n          \"el-table--scrollable-x\": _vm.layout.scrollX,\n          \"el-table--scrollable-y\": _vm.layout.scrollY,\n          \"el-table--enable-row-hover\": !_vm.store.states.isComplex,\n          \"el-table--enable-row-transition\":\n            (_vm.store.states.data || []).length !== 0 &&\n            (_vm.store.states.data || []).length < 100\n        },\n        _vm.tableSize ? \"el-table--\" + _vm.tableSize : \"\"\n      ],\n      on: {\n        mouseleave: function($event) {\n          _vm.handleMouseLeave($event)\n        }\n      }\n    },\n    [\n      _c(\n        \"div\",\n        { ref: \"hiddenColumns\", staticClass: \"hidden-columns\" },\n        [_vm._t(\"default\")],\n        2\n      ),\n      _vm.showHeader\n        ? _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"mousewheel\",\n                  rawName: \"v-mousewheel\",\n                  value: _vm.handleHeaderFooterMousewheel,\n                  expression: \"handleHeaderFooterMousewheel\"\n                }\n              ],\n              ref: \"headerWrapper\",\n              staticClass: \"el-table__header-wrapper\"\n            },\n            [\n              _c(\"table-header\", {\n                ref: \"tableHeader\",\n                style: {\n                  width: _vm.layout.bodyWidth ? _vm.layout.bodyWidth + \"px\" : \"\"\n                },\n                attrs: {\n                  store: _vm.store,\n                  border: _vm.border,\n                  \"default-sort\": _vm.defaultSort\n                }\n              })\n            ],\n            1\n          )\n        : _vm._e(),\n      _c(\n        \"div\",\n        {\n          ref: \"bodyWrapper\",\n          staticClass: \"el-table__body-wrapper\",\n          class: [\n            _vm.layout.scrollX\n              ? \"is-scrolling-\" + _vm.scrollPosition\n              : \"is-scrolling-none\"\n          ],\n          style: [_vm.bodyHeight]\n        },\n        [\n          _c(\"table-body\", {\n            style: {\n              width: _vm.bodyWidth\n            },\n            attrs: {\n              context: _vm.context,\n              store: _vm.store,\n              stripe: _vm.stripe,\n              \"row-class-name\": _vm.rowClassName,\n              \"row-style\": _vm.rowStyle,\n              highlight: _vm.highlightCurrentRow\n            }\n          }),\n          !_vm.data || _vm.data.length === 0\n            ? _c(\n                \"div\",\n                {\n                  ref: \"emptyBlock\",\n                  staticClass: \"el-table__empty-block\",\n                  style: _vm.emptyBlockStyle\n                },\n                [\n                  _c(\n                    \"span\",\n                    { staticClass: \"el-table__empty-text\" },\n                    [\n                      _vm._t(\"empty\", [\n                        _vm._v(\n                          _vm._s(_vm.emptyText || _vm.t(\"el.table.emptyText\"))\n                        )\n                      ])\n                    ],\n                    2\n                  )\n                ]\n              )\n            : _vm._e(),\n          _vm.$slots.append\n            ? _c(\n                \"div\",\n                {\n                  ref: \"appendWrapper\",\n                  staticClass: \"el-table__append-wrapper\"\n                },\n                [_vm._t(\"append\")],\n                2\n              )\n            : _vm._e()\n        ],\n        1\n      ),\n      _vm.showSummary\n        ? _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.data && _vm.data.length > 0,\n                  expression: \"data && data.length > 0\"\n                },\n                {\n                  name: \"mousewheel\",\n                  rawName: \"v-mousewheel\",\n                  value: _vm.handleHeaderFooterMousewheel,\n                  expression: \"handleHeaderFooterMousewheel\"\n                }\n              ],\n              ref: \"footerWrapper\",\n              staticClass: \"el-table__footer-wrapper\"\n            },\n            [\n              _c(\"table-footer\", {\n                style: {\n                  width: _vm.layout.bodyWidth ? _vm.layout.bodyWidth + \"px\" : \"\"\n                },\n                attrs: {\n                  store: _vm.store,\n                  border: _vm.border,\n                  \"sum-text\": _vm.sumText || _vm.t(\"el.table.sumText\"),\n                  \"summary-method\": _vm.summaryMethod,\n                  \"default-sort\": _vm.defaultSort\n                }\n              })\n            ],\n            1\n          )\n        : _vm._e(),\n      _vm.fixedColumns.length > 0\n        ? _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"mousewheel\",\n                  rawName: \"v-mousewheel\",\n                  value: _vm.handleFixedMousewheel,\n                  expression: \"handleFixedMousewheel\"\n                }\n              ],\n              ref: \"fixedWrapper\",\n              staticClass: \"el-table__fixed\",\n              style: [\n                {\n                  width: _vm.layout.fixedWidth\n                    ? _vm.layout.fixedWidth + \"px\"\n                    : \"\"\n                },\n                _vm.fixedHeight\n              ]\n            },\n            [\n              _vm.showHeader\n                ? _c(\n                    \"div\",\n                    {\n                      ref: \"fixedHeaderWrapper\",\n                      staticClass: \"el-table__fixed-header-wrapper\"\n                    },\n                    [\n                      _c(\"table-header\", {\n                        ref: \"fixedTableHeader\",\n                        style: {\n                          width: _vm.bodyWidth\n                        },\n                        attrs: {\n                          fixed: \"left\",\n                          border: _vm.border,\n                          store: _vm.store\n                        }\n                      })\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _c(\n                \"div\",\n                {\n                  ref: \"fixedBodyWrapper\",\n                  staticClass: \"el-table__fixed-body-wrapper\",\n                  style: [\n                    {\n                      top: _vm.layout.headerHeight + \"px\"\n                    },\n                    _vm.fixedBodyHeight\n                  ]\n                },\n                [\n                  _c(\"table-body\", {\n                    style: {\n                      width: _vm.bodyWidth\n                    },\n                    attrs: {\n                      fixed: \"left\",\n                      store: _vm.store,\n                      stripe: _vm.stripe,\n                      highlight: _vm.highlightCurrentRow,\n                      \"row-class-name\": _vm.rowClassName,\n                      \"row-style\": _vm.rowStyle\n                    }\n                  }),\n                  _vm.$slots.append\n                    ? _c(\"div\", {\n                        staticClass: \"el-table__append-gutter\",\n                        style: { height: _vm.layout.appendHeight + \"px\" }\n                      })\n                    : _vm._e()\n                ],\n                1\n              ),\n              _vm.showSummary\n                ? _c(\n                    \"div\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: _vm.data && _vm.data.length > 0,\n                          expression: \"data && data.length > 0\"\n                        }\n                      ],\n                      ref: \"fixedFooterWrapper\",\n                      staticClass: \"el-table__fixed-footer-wrapper\"\n                    },\n                    [\n                      _c(\"table-footer\", {\n                        style: {\n                          width: _vm.bodyWidth\n                        },\n                        attrs: {\n                          fixed: \"left\",\n                          border: _vm.border,\n                          \"sum-text\": _vm.sumText || _vm.t(\"el.table.sumText\"),\n                          \"summary-method\": _vm.summaryMethod,\n                          store: _vm.store\n                        }\n                      })\n                    ],\n                    1\n                  )\n                : _vm._e()\n            ]\n          )\n        : _vm._e(),\n      _vm.rightFixedColumns.length > 0\n        ? _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"mousewheel\",\n                  rawName: \"v-mousewheel\",\n                  value: _vm.handleFixedMousewheel,\n                  expression: \"handleFixedMousewheel\"\n                }\n              ],\n              ref: \"rightFixedWrapper\",\n              staticClass: \"el-table__fixed-right\",\n              style: [\n                {\n                  width: _vm.layout.rightFixedWidth\n                    ? _vm.layout.rightFixedWidth + \"px\"\n                    : \"\",\n                  right: _vm.layout.scrollY\n                    ? (_vm.border\n                        ? _vm.layout.gutterWidth\n                        : _vm.layout.gutterWidth || 0) + \"px\"\n                    : \"\"\n                },\n                _vm.fixedHeight\n              ]\n            },\n            [\n              _vm.showHeader\n                ? _c(\n                    \"div\",\n                    {\n                      ref: \"rightFixedHeaderWrapper\",\n                      staticClass: \"el-table__fixed-header-wrapper\"\n                    },\n                    [\n                      _c(\"table-header\", {\n                        ref: \"rightFixedTableHeader\",\n                        style: {\n                          width: _vm.bodyWidth\n                        },\n                        attrs: {\n                          fixed: \"right\",\n                          border: _vm.border,\n                          store: _vm.store\n                        }\n                      })\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _c(\n                \"div\",\n                {\n                  ref: \"rightFixedBodyWrapper\",\n                  staticClass: \"el-table__fixed-body-wrapper\",\n                  style: [\n                    {\n                      top: _vm.layout.headerHeight + \"px\"\n                    },\n                    _vm.fixedBodyHeight\n                  ]\n                },\n                [\n                  _c(\"table-body\", {\n                    style: {\n                      width: _vm.bodyWidth\n                    },\n                    attrs: {\n                      fixed: \"right\",\n                      store: _vm.store,\n                      stripe: _vm.stripe,\n                      \"row-class-name\": _vm.rowClassName,\n                      \"row-style\": _vm.rowStyle,\n                      highlight: _vm.highlightCurrentRow\n                    }\n                  }),\n                  _vm.$slots.append\n                    ? _c(\"div\", {\n                        staticClass: \"el-table__append-gutter\",\n                        style: { height: _vm.layout.appendHeight + \"px\" }\n                      })\n                    : _vm._e()\n                ],\n                1\n              ),\n              _vm.showSummary\n                ? _c(\n                    \"div\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: _vm.data && _vm.data.length > 0,\n                          expression: \"data && data.length > 0\"\n                        }\n                      ],\n                      ref: \"rightFixedFooterWrapper\",\n                      staticClass: \"el-table__fixed-footer-wrapper\"\n                    },\n                    [\n                      _c(\"table-footer\", {\n                        style: {\n                          width: _vm.bodyWidth\n                        },\n                        attrs: {\n                          fixed: \"right\",\n                          border: _vm.border,\n                          \"sum-text\": _vm.sumText || _vm.t(\"el.table.sumText\"),\n                          \"summary-method\": _vm.summaryMethod,\n                          store: _vm.store\n                        }\n                      })\n                    ],\n                    1\n                  )\n                : _vm._e()\n            ]\n          )\n        : _vm._e(),\n      _vm.rightFixedColumns.length > 0\n        ? _c(\"div\", {\n            ref: \"rightFixedPatch\",\n            staticClass: \"el-table__fixed-right-patch\",\n            style: {\n              width: _vm.layout.scrollY ? _vm.layout.gutterWidth + \"px\" : \"0\",\n              height: _vm.layout.headerHeight + \"px\"\n            }\n          })\n        : _vm._e(),\n      _c(\"div\", {\n        directives: [\n          {\n            name: \"show\",\n            rawName: \"v-show\",\n            value: _vm.resizeProxyVisible,\n            expression: \"resizeProxyVisible\"\n          }\n        ],\n        ref: \"resizeProxy\",\n        staticClass: \"el-table__column-resize-proxy\"\n      })\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\n\n// CONCATENATED MODULE: ./packages/table/src/table.vue?vue&type=template&id=493fe34e&\n\n// EXTERNAL MODULE: external \"element-ui/lib/checkbox\"\nvar checkbox_ = __webpack_require__(18);\nvar checkbox_default = /*#__PURE__*/__webpack_require__.n(checkbox_);\n\n// EXTERNAL MODULE: external \"throttle-debounce\"\nvar external_throttle_debounce_ = __webpack_require__(43);\n\n// EXTERNAL MODULE: external \"element-ui/lib/utils/resize-event\"\nvar resize_event_ = __webpack_require__(16);\n\n// EXTERNAL MODULE: external \"normalize-wheel\"\nvar external_normalize_wheel_ = __webpack_require__(46);\nvar external_normalize_wheel_default = /*#__PURE__*/__webpack_require__.n(external_normalize_wheel_);\n\n// CONCATENATED MODULE: ./src/directives/mousewheel.js\n\n\nvar isFirefox = typeof navigator !== 'undefined' && navigator.userAgent.toLowerCase().indexOf('firefox') > -1;\n\nvar mousewheel_mousewheel = function mousewheel(element, callback) {\n  if (element && element.addEventListener) {\n    element.addEventListener(isFirefox ? 'DOMMouseScroll' : 'mousewheel', function (event) {\n      var normalized = external_normalize_wheel_default()(event);\n      callback && callback.apply(this, [event, normalized]);\n    });\n  }\n};\n\n/* harmony default export */ var directives_mousewheel = ({\n  bind: function bind(el, binding) {\n    mousewheel_mousewheel(el, binding.value);\n  }\n});\n// EXTERNAL MODULE: external \"element-ui/lib/mixins/locale\"\nvar locale_ = __webpack_require__(6);\nvar locale_default = /*#__PURE__*/__webpack_require__.n(locale_);\n\n// EXTERNAL MODULE: external \"element-ui/lib/mixins/migrating\"\nvar migrating_ = __webpack_require__(11);\nvar migrating_default = /*#__PURE__*/__webpack_require__.n(migrating_);\n\n// EXTERNAL MODULE: external \"vue\"\nvar external_vue_ = __webpack_require__(7);\nvar external_vue_default = /*#__PURE__*/__webpack_require__.n(external_vue_);\n\n// EXTERNAL MODULE: external \"element-ui/lib/utils/merge\"\nvar merge_ = __webpack_require__(9);\nvar merge_default = /*#__PURE__*/__webpack_require__.n(merge_);\n\n// EXTERNAL MODULE: ./packages/table/src/util.js\nvar util = __webpack_require__(8);\n\n// CONCATENATED MODULE: ./packages/table/src/store/expand.js\n\n\n/* harmony default export */ var expand = ({\n  data: function data() {\n    return {\n      states: {\n        defaultExpandAll: false,\n        expandRows: []\n      }\n    };\n  },\n\n\n  methods: {\n    updateExpandRows: function updateExpandRows() {\n      var _states = this.states,\n          _states$data = _states.data,\n          data = _states$data === undefined ? [] : _states$data,\n          rowKey = _states.rowKey,\n          defaultExpandAll = _states.defaultExpandAll,\n          expandRows = _states.expandRows;\n\n      if (defaultExpandAll) {\n        this.states.expandRows = data.slice();\n      } else if (rowKey) {\n        // TODO：这里的代码可以优化\n        var expandRowsMap = Object(util[\"f\" /* getKeysMap */])(expandRows, rowKey);\n        this.states.expandRows = data.reduce(function (prev, row) {\n          var rowId = Object(util[\"g\" /* getRowIdentity */])(row, rowKey);\n          var rowInfo = expandRowsMap[rowId];\n          if (rowInfo) {\n            prev.push(row);\n          }\n          return prev;\n        }, []);\n      } else {\n        this.states.expandRows = [];\n      }\n    },\n    toggleRowExpansion: function toggleRowExpansion(row, expanded) {\n      var changed = Object(util[\"m\" /* toggleRowStatus */])(this.states.expandRows, row, expanded);\n      if (changed) {\n        this.table.$emit('expand-change', row, this.states.expandRows.slice());\n        this.scheduleLayout();\n      }\n    },\n    setExpandRowKeys: function setExpandRowKeys(rowKeys) {\n      this.assertRowKey();\n      // TODO：这里的代码可以优化\n      var _states2 = this.states,\n          data = _states2.data,\n          rowKey = _states2.rowKey;\n\n      var keysMap = Object(util[\"f\" /* getKeysMap */])(data, rowKey);\n      this.states.expandRows = rowKeys.reduce(function (prev, cur) {\n        var info = keysMap[cur];\n        if (info) {\n          prev.push(info.row);\n        }\n        return prev;\n      }, []);\n    },\n    isRowExpanded: function isRowExpanded(row) {\n      var _states3 = this.states,\n          _states3$expandRows = _states3.expandRows,\n          expandRows = _states3$expandRows === undefined ? [] : _states3$expandRows,\n          rowKey = _states3.rowKey;\n\n      if (rowKey) {\n        var expandMap = Object(util[\"f\" /* getKeysMap */])(expandRows, rowKey);\n        return !!expandMap[Object(util[\"g\" /* getRowIdentity */])(row, rowKey)];\n      }\n      return expandRows.indexOf(row) !== -1;\n    }\n  }\n});\n// EXTERNAL MODULE: external \"element-ui/lib/utils/util\"\nvar util_ = __webpack_require__(3);\n\n// CONCATENATED MODULE: ./packages/table/src/store/current.js\n\n\n\n/* harmony default export */ var current = ({\n  data: function data() {\n    return {\n      states: {\n        // 不可响应的，设置 currentRowKey 时，data 不一定存在，也许无法算出正确的 currentRow\n        // 把该值缓存一下，当用户点击修改 currentRow 时，把该值重置为 null\n        _currentRowKey: null,\n        currentRow: null\n      }\n    };\n  },\n\n\n  methods: {\n    setCurrentRowKey: function setCurrentRowKey(key) {\n      this.assertRowKey();\n      this.states._currentRowKey = key;\n      this.setCurrentRowByKey(key);\n    },\n    restoreCurrentRowKey: function restoreCurrentRowKey() {\n      this.states._currentRowKey = null;\n    },\n    setCurrentRowByKey: function setCurrentRowByKey(key) {\n      var states = this.states;\n      var _states$data = states.data,\n          data = _states$data === undefined ? [] : _states$data,\n          rowKey = states.rowKey;\n\n      var currentRow = null;\n      if (rowKey) {\n        currentRow = Object(util_[\"arrayFind\"])(data, function (item) {\n          return Object(util[\"g\" /* getRowIdentity */])(item, rowKey) === key;\n        });\n      }\n      states.currentRow = currentRow;\n    },\n    updateCurrentRow: function updateCurrentRow(currentRow) {\n      var states = this.states,\n          table = this.table;\n\n      var oldCurrentRow = states.currentRow;\n      if (currentRow && currentRow !== oldCurrentRow) {\n        states.currentRow = currentRow;\n        table.$emit('current-change', currentRow, oldCurrentRow);\n        return;\n      }\n      if (!currentRow && oldCurrentRow) {\n        states.currentRow = null;\n        table.$emit('current-change', null, oldCurrentRow);\n      }\n    },\n    updateCurrentRowData: function updateCurrentRowData() {\n      var states = this.states,\n          table = this.table;\n      var rowKey = states.rowKey,\n          _currentRowKey = states._currentRowKey;\n      // data 为 null 时，解构时的默认值会被忽略\n\n      var data = states.data || [];\n      var oldCurrentRow = states.currentRow;\n\n      // 当 currentRow 不在 data 中时尝试更新数据\n      if (data.indexOf(oldCurrentRow) === -1 && oldCurrentRow) {\n        if (rowKey) {\n          var currentRowKey = Object(util[\"g\" /* getRowIdentity */])(oldCurrentRow, rowKey);\n          this.setCurrentRowByKey(currentRowKey);\n        } else {\n          states.currentRow = null;\n        }\n        if (states.currentRow === null) {\n          table.$emit('current-change', null, oldCurrentRow);\n        }\n      } else if (_currentRowKey) {\n        // 把初始时下设置的 rowKey 转化成 rowData\n        this.setCurrentRowByKey(_currentRowKey);\n        this.restoreCurrentRowKey();\n      }\n    }\n  }\n});\n// CONCATENATED MODULE: ./packages/table/src/store/tree.js\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\n\n\n/* harmony default export */ var tree = ({\n  data: function data() {\n    return {\n      states: {\n        // defaultExpandAll 存在于 expand.js 中，这里不重复添加\n        // 在展开行中，expandRowKeys 会被转化成 expandRows，expandRowKeys 这个属性只是记录了 TreeTable 行的展开\n        // TODO: 拆分为独立的 TreeTable，统一用法\n        expandRowKeys: [],\n        treeData: {},\n        indent: 16,\n        lazy: false,\n        lazyTreeNodeMap: {},\n        lazyColumnIdentifier: 'hasChildren',\n        childrenColumnName: 'children'\n      }\n    };\n  },\n\n\n  computed: {\n    // 嵌入型的数据，watch 无法是检测到变化 https://github.com/ElemeFE/element/issues/14998\n    // TODO: 使用 computed 解决该问题，是否会造成性能问题？\n    // @return { id: { level, children } }\n    normalizedData: function normalizedData() {\n      if (!this.states.rowKey) return {};\n      var data = this.states.data || [];\n      return this.normalize(data);\n    },\n\n    // @return { id: { children } }\n    // 针对懒加载的情形，不处理嵌套数据\n    normalizedLazyNode: function normalizedLazyNode() {\n      var _states = this.states,\n          rowKey = _states.rowKey,\n          lazyTreeNodeMap = _states.lazyTreeNodeMap,\n          lazyColumnIdentifier = _states.lazyColumnIdentifier;\n\n      var keys = Object.keys(lazyTreeNodeMap);\n      var res = {};\n      if (!keys.length) return res;\n      keys.forEach(function (key) {\n        if (lazyTreeNodeMap[key].length) {\n          var item = { children: [] };\n          lazyTreeNodeMap[key].forEach(function (row) {\n            var currentRowKey = Object(util[\"g\" /* getRowIdentity */])(row, rowKey);\n            item.children.push(currentRowKey);\n            if (row[lazyColumnIdentifier] && !res[currentRowKey]) {\n              res[currentRowKey] = { children: [] };\n            }\n          });\n          res[key] = item;\n        }\n      });\n      return res;\n    }\n  },\n\n  watch: {\n    normalizedData: 'updateTreeData',\n    normalizedLazyNode: 'updateTreeData'\n  },\n\n  methods: {\n    normalize: function normalize(data) {\n      var _states2 = this.states,\n          childrenColumnName = _states2.childrenColumnName,\n          lazyColumnIdentifier = _states2.lazyColumnIdentifier,\n          rowKey = _states2.rowKey,\n          lazy = _states2.lazy;\n\n      var res = {};\n      Object(util[\"n\" /* walkTreeNode */])(data, function (parent, children, level) {\n        var parentId = Object(util[\"g\" /* getRowIdentity */])(parent, rowKey);\n        if (Array.isArray(children)) {\n          res[parentId] = {\n            children: children.map(function (row) {\n              return Object(util[\"g\" /* getRowIdentity */])(row, rowKey);\n            }),\n            level: level\n          };\n        } else if (lazy) {\n          // 当 children 不存在且 lazy 为 true，该节点即为懒加载的节点\n          res[parentId] = {\n            children: [],\n            lazy: true,\n            level: level\n          };\n        }\n      }, childrenColumnName, lazyColumnIdentifier);\n      return res;\n    },\n    updateTreeData: function updateTreeData() {\n      var nested = this.normalizedData;\n      var normalizedLazyNode = this.normalizedLazyNode;\n      var keys = Object.keys(nested);\n      var newTreeData = {};\n      if (keys.length) {\n        var _states3 = this.states,\n            oldTreeData = _states3.treeData,\n            defaultExpandAll = _states3.defaultExpandAll,\n            expandRowKeys = _states3.expandRowKeys,\n            lazy = _states3.lazy;\n\n        var rootLazyRowKeys = [];\n        var getExpanded = function getExpanded(oldValue, key) {\n          var included = defaultExpandAll || expandRowKeys && expandRowKeys.indexOf(key) !== -1;\n          return !!(oldValue && oldValue.expanded || included);\n        };\n        // 合并 expanded 与 display，确保数据刷新后，状态不变\n        keys.forEach(function (key) {\n          var oldValue = oldTreeData[key];\n          var newValue = _extends({}, nested[key]);\n          newValue.expanded = getExpanded(oldValue, key);\n          if (newValue.lazy) {\n            var _ref = oldValue || {},\n                _ref$loaded = _ref.loaded,\n                loaded = _ref$loaded === undefined ? false : _ref$loaded,\n                _ref$loading = _ref.loading,\n                loading = _ref$loading === undefined ? false : _ref$loading;\n\n            newValue.loaded = !!loaded;\n            newValue.loading = !!loading;\n            rootLazyRowKeys.push(key);\n          }\n          newTreeData[key] = newValue;\n        });\n        // 根据懒加载数据更新 treeData\n        var lazyKeys = Object.keys(normalizedLazyNode);\n        if (lazy && lazyKeys.length && rootLazyRowKeys.length) {\n          lazyKeys.forEach(function (key) {\n            var oldValue = oldTreeData[key];\n            var lazyNodeChildren = normalizedLazyNode[key].children;\n            if (rootLazyRowKeys.indexOf(key) !== -1) {\n              // 懒加载的 root 节点，更新一下原有的数据，原来的 children 一定是空数组\n              if (newTreeData[key].children.length !== 0) {\n                throw new Error('[ElTable]children must be an empty array.');\n              }\n              newTreeData[key].children = lazyNodeChildren;\n            } else {\n              var _ref2 = oldValue || {},\n                  _ref2$loaded = _ref2.loaded,\n                  loaded = _ref2$loaded === undefined ? false : _ref2$loaded,\n                  _ref2$loading = _ref2.loading,\n                  loading = _ref2$loading === undefined ? false : _ref2$loading;\n\n              newTreeData[key] = {\n                lazy: true,\n                loaded: !!loaded,\n                loading: !!loading,\n                expanded: getExpanded(oldValue, key),\n                children: lazyNodeChildren,\n                level: ''\n              };\n            }\n          });\n        }\n      }\n      this.states.treeData = newTreeData;\n      this.updateTableScrollY();\n    },\n    updateTreeExpandKeys: function updateTreeExpandKeys(value) {\n      this.states.expandRowKeys = value;\n      this.updateTreeData();\n    },\n    toggleTreeExpansion: function toggleTreeExpansion(row, expanded) {\n      this.assertRowKey();\n\n      var _states4 = this.states,\n          rowKey = _states4.rowKey,\n          treeData = _states4.treeData;\n\n      var id = Object(util[\"g\" /* getRowIdentity */])(row, rowKey);\n      var data = id && treeData[id];\n      if (id && data && 'expanded' in data) {\n        var oldExpanded = data.expanded;\n        expanded = typeof expanded === 'undefined' ? !data.expanded : expanded;\n        treeData[id].expanded = expanded;\n        if (oldExpanded !== expanded) {\n          this.table.$emit('expand-change', row, expanded);\n        }\n        this.updateTableScrollY();\n      }\n    },\n    loadOrToggle: function loadOrToggle(row) {\n      this.assertRowKey();\n      var _states5 = this.states,\n          lazy = _states5.lazy,\n          treeData = _states5.treeData,\n          rowKey = _states5.rowKey;\n\n      var id = Object(util[\"g\" /* getRowIdentity */])(row, rowKey);\n      var data = treeData[id];\n      if (lazy && data && 'loaded' in data && !data.loaded) {\n        this.loadData(row, id, data);\n      } else {\n        this.toggleTreeExpansion(row);\n      }\n    },\n    loadData: function loadData(row, key, treeNode) {\n      var _this = this;\n\n      var load = this.table.load;\n      var rawTreeData = this.states.treeData;\n\n      if (load && !rawTreeData[key].loaded) {\n        rawTreeData[key].loading = true;\n        load(row, treeNode, function (data) {\n          if (!Array.isArray(data)) {\n            throw new Error('[ElTable] data must be an array');\n          }\n          var _states6 = _this.states,\n              lazyTreeNodeMap = _states6.lazyTreeNodeMap,\n              treeData = _states6.treeData;\n\n          treeData[key].loading = false;\n          treeData[key].loaded = true;\n          treeData[key].expanded = true;\n          if (data.length) {\n            _this.$set(lazyTreeNodeMap, key, data);\n          }\n          _this.table.$emit('expand-change', row, true);\n        });\n      }\n    }\n  }\n});\n// CONCATENATED MODULE: ./packages/table/src/store/watcher.js\n\n\n\n\n\n\n\nvar watcher_sortData = function sortData(data, states) {\n  var sortingColumn = states.sortingColumn;\n  if (!sortingColumn || typeof sortingColumn.sortable === 'string') {\n    return data;\n  }\n  return Object(util[\"i\" /* orderBy */])(data, states.sortProp, states.sortOrder, sortingColumn.sortMethod, sortingColumn.sortBy);\n};\n\nvar doFlattenColumns = function doFlattenColumns(columns) {\n  var result = [];\n  columns.forEach(function (column) {\n    if (column.children) {\n      result.push.apply(result, doFlattenColumns(column.children));\n    } else {\n      result.push(column);\n    }\n  });\n  return result;\n};\n\n/* harmony default export */ var watcher = (external_vue_default.a.extend({\n  data: function data() {\n    return {\n      states: {\n        // 3.0 版本后要求必须设置该属性\n        rowKey: null,\n\n        // 渲染的数据来源，是对 table 中的 data 过滤排序后的结果\n        data: [],\n\n        // 是否包含固定列\n        isComplex: false,\n\n        // 列\n        _columns: [], // 不可响应的\n        originColumns: [],\n        columns: [],\n        fixedColumns: [],\n        rightFixedColumns: [],\n        leafColumns: [],\n        fixedLeafColumns: [],\n        rightFixedLeafColumns: [],\n        leafColumnsLength: 0,\n        fixedLeafColumnsLength: 0,\n        rightFixedLeafColumnsLength: 0,\n\n        // 选择\n        isAllSelected: false,\n        selection: [],\n        reserveSelection: false,\n        selectOnIndeterminate: false,\n        selectable: null,\n\n        // 过滤\n        filters: {}, // 不可响应的\n        filteredData: null,\n\n        // 排序\n        sortingColumn: null,\n        sortProp: null,\n        sortOrder: null,\n\n        hoverRow: null\n      }\n    };\n  },\n\n\n  mixins: [expand, current, tree],\n\n  methods: {\n    // 检查 rowKey 是否存在\n    assertRowKey: function assertRowKey() {\n      var rowKey = this.states.rowKey;\n      if (!rowKey) throw new Error('[ElTable] prop row-key is required');\n    },\n\n\n    // 更新列\n    updateColumns: function updateColumns() {\n      var states = this.states;\n      var _columns = states._columns || [];\n      states.fixedColumns = _columns.filter(function (column) {\n        return column.fixed === true || column.fixed === 'left';\n      });\n      states.rightFixedColumns = _columns.filter(function (column) {\n        return column.fixed === 'right';\n      });\n\n      if (states.fixedColumns.length > 0 && _columns[0] && _columns[0].type === 'selection' && !_columns[0].fixed) {\n        _columns[0].fixed = true;\n        states.fixedColumns.unshift(_columns[0]);\n      }\n\n      var notFixedColumns = _columns.filter(function (column) {\n        return !column.fixed;\n      });\n      states.originColumns = [].concat(states.fixedColumns).concat(notFixedColumns).concat(states.rightFixedColumns);\n\n      var leafColumns = doFlattenColumns(notFixedColumns);\n      var fixedLeafColumns = doFlattenColumns(states.fixedColumns);\n      var rightFixedLeafColumns = doFlattenColumns(states.rightFixedColumns);\n\n      states.leafColumnsLength = leafColumns.length;\n      states.fixedLeafColumnsLength = fixedLeafColumns.length;\n      states.rightFixedLeafColumnsLength = rightFixedLeafColumns.length;\n\n      states.columns = [].concat(fixedLeafColumns).concat(leafColumns).concat(rightFixedLeafColumns);\n      states.isComplex = states.fixedColumns.length > 0 || states.rightFixedColumns.length > 0;\n    },\n\n\n    // 更新 DOM\n    scheduleLayout: function scheduleLayout(needUpdateColumns) {\n      if (needUpdateColumns) {\n        this.updateColumns();\n      }\n      this.table.debouncedUpdateLayout();\n    },\n\n\n    // 选择\n    isSelected: function isSelected(row) {\n      var _states$selection = this.states.selection,\n          selection = _states$selection === undefined ? [] : _states$selection;\n\n      return selection.indexOf(row) > -1;\n    },\n    clearSelection: function clearSelection() {\n      var states = this.states;\n      states.isAllSelected = false;\n      var oldSelection = states.selection;\n      if (oldSelection.length) {\n        states.selection = [];\n        this.table.$emit('selection-change', []);\n      }\n    },\n    cleanSelection: function cleanSelection() {\n      var states = this.states;\n      var data = states.data,\n          rowKey = states.rowKey,\n          selection = states.selection;\n\n      var deleted = void 0;\n      if (rowKey) {\n        deleted = [];\n        var selectedMap = Object(util[\"f\" /* getKeysMap */])(selection, rowKey);\n        var dataMap = Object(util[\"f\" /* getKeysMap */])(data, rowKey);\n        for (var key in selectedMap) {\n          if (selectedMap.hasOwnProperty(key) && !dataMap[key]) {\n            deleted.push(selectedMap[key].row);\n          }\n        }\n      } else {\n        deleted = selection.filter(function (item) {\n          return data.indexOf(item) === -1;\n        });\n      }\n      if (deleted.length) {\n        var newSelection = selection.filter(function (item) {\n          return deleted.indexOf(item) === -1;\n        });\n        states.selection = newSelection;\n        this.table.$emit('selection-change', newSelection.slice());\n      }\n    },\n    toggleRowSelection: function toggleRowSelection(row, selected) {\n      var emitChange = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n\n      var changed = Object(util[\"m\" /* toggleRowStatus */])(this.states.selection, row, selected);\n      if (changed) {\n        var newSelection = (this.states.selection || []).slice();\n        // 调用 API 修改选中值，不触发 select 事件\n        if (emitChange) {\n          this.table.$emit('select', newSelection, row);\n        }\n        this.table.$emit('selection-change', newSelection);\n      }\n    },\n    _toggleAllSelection: function _toggleAllSelection() {\n      var states = this.states;\n      var _states$data = states.data,\n          data = _states$data === undefined ? [] : _states$data,\n          selection = states.selection;\n      // when only some rows are selected (but not all), select or deselect all of them\n      // depending on the value of selectOnIndeterminate\n\n      var value = states.selectOnIndeterminate ? !states.isAllSelected : !(states.isAllSelected || selection.length);\n      states.isAllSelected = value;\n\n      var selectionChanged = false;\n      data.forEach(function (row, index) {\n        if (states.selectable) {\n          if (states.selectable.call(null, row, index) && Object(util[\"m\" /* toggleRowStatus */])(selection, row, value)) {\n            selectionChanged = true;\n          }\n        } else {\n          if (Object(util[\"m\" /* toggleRowStatus */])(selection, row, value)) {\n            selectionChanged = true;\n          }\n        }\n      });\n\n      if (selectionChanged) {\n        this.table.$emit('selection-change', selection ? selection.slice() : []);\n      }\n      this.table.$emit('select-all', selection);\n    },\n    updateSelectionByRowKey: function updateSelectionByRowKey() {\n      var states = this.states;\n      var selection = states.selection,\n          rowKey = states.rowKey,\n          data = states.data;\n\n      var selectedMap = Object(util[\"f\" /* getKeysMap */])(selection, rowKey);\n      data.forEach(function (row) {\n        var rowId = Object(util[\"g\" /* getRowIdentity */])(row, rowKey);\n        var rowInfo = selectedMap[rowId];\n        if (rowInfo) {\n          selection[rowInfo.index] = row;\n        }\n      });\n    },\n    updateAllSelected: function updateAllSelected() {\n      var states = this.states;\n      var selection = states.selection,\n          rowKey = states.rowKey,\n          selectable = states.selectable;\n      // data 为 null 时，解构时的默认值会被忽略\n\n      var data = states.data || [];\n      if (data.length === 0) {\n        states.isAllSelected = false;\n        return;\n      }\n\n      var selectedMap = void 0;\n      if (rowKey) {\n        selectedMap = Object(util[\"f\" /* getKeysMap */])(selection, rowKey);\n      }\n      var isSelected = function isSelected(row) {\n        if (selectedMap) {\n          return !!selectedMap[Object(util[\"g\" /* getRowIdentity */])(row, rowKey)];\n        } else {\n          return selection.indexOf(row) !== -1;\n        }\n      };\n      var isAllSelected = true;\n      var selectedCount = 0;\n      for (var i = 0, j = data.length; i < j; i++) {\n        var item = data[i];\n        var isRowSelectable = selectable && selectable.call(null, item, i);\n        if (!isSelected(item)) {\n          if (!selectable || isRowSelectable) {\n            isAllSelected = false;\n            break;\n          }\n        } else {\n          selectedCount++;\n        }\n      }\n\n      if (selectedCount === 0) isAllSelected = false;\n      states.isAllSelected = isAllSelected;\n    },\n\n\n    // 过滤与排序\n    updateFilters: function updateFilters(columns, values) {\n      if (!Array.isArray(columns)) {\n        columns = [columns];\n      }\n      var states = this.states;\n      var filters = {};\n      columns.forEach(function (col) {\n        states.filters[col.id] = values;\n        filters[col.columnKey || col.id] = values;\n      });\n\n      return filters;\n    },\n    updateSort: function updateSort(column, prop, order) {\n      if (this.states.sortingColumn && this.states.sortingColumn !== column) {\n        this.states.sortingColumn.order = null;\n      }\n      this.states.sortingColumn = column;\n      this.states.sortProp = prop;\n      this.states.sortOrder = order;\n    },\n    execFilter: function execFilter() {\n      var _this = this;\n\n      var states = this.states;\n      var _data = states._data,\n          filters = states.filters;\n\n      var data = _data;\n\n      Object.keys(filters).forEach(function (columnId) {\n        var values = states.filters[columnId];\n        if (!values || values.length === 0) return;\n        var column = Object(util[\"d\" /* getColumnById */])(_this.states, columnId);\n        if (column && column.filterMethod) {\n          data = data.filter(function (row) {\n            return values.some(function (value) {\n              return column.filterMethod.call(null, value, row, column);\n            });\n          });\n        }\n      });\n\n      states.filteredData = data;\n    },\n    execSort: function execSort() {\n      var states = this.states;\n      states.data = watcher_sortData(states.filteredData, states);\n    },\n\n\n    // 根据 filters 与 sort 去过滤 data\n    execQuery: function execQuery(ignore) {\n      if (!(ignore && ignore.filter)) {\n        this.execFilter();\n      }\n      this.execSort();\n    },\n    clearFilter: function clearFilter(columnKeys) {\n      var states = this.states;\n      var _table$$refs = this.table.$refs,\n          tableHeader = _table$$refs.tableHeader,\n          fixedTableHeader = _table$$refs.fixedTableHeader,\n          rightFixedTableHeader = _table$$refs.rightFixedTableHeader;\n\n\n      var panels = {};\n      if (tableHeader) panels = merge_default()(panels, tableHeader.filterPanels);\n      if (fixedTableHeader) panels = merge_default()(panels, fixedTableHeader.filterPanels);\n      if (rightFixedTableHeader) panels = merge_default()(panels, rightFixedTableHeader.filterPanels);\n\n      var keys = Object.keys(panels);\n      if (!keys.length) return;\n\n      if (typeof columnKeys === 'string') {\n        columnKeys = [columnKeys];\n      }\n\n      if (Array.isArray(columnKeys)) {\n        var columns = columnKeys.map(function (key) {\n          return Object(util[\"e\" /* getColumnByKey */])(states, key);\n        });\n        keys.forEach(function (key) {\n          var column = columns.find(function (col) {\n            return col.id === key;\n          });\n          if (column) {\n            // TODO: 优化这里的代码\n            panels[key].filteredValue = [];\n          }\n        });\n        this.commit('filterChange', {\n          column: columns,\n          values: [],\n          silent: true,\n          multi: true\n        });\n      } else {\n        keys.forEach(function (key) {\n          // TODO: 优化这里的代码\n          panels[key].filteredValue = [];\n        });\n\n        states.filters = {};\n        this.commit('filterChange', {\n          column: {},\n          values: [],\n          silent: true\n        });\n      }\n    },\n    clearSort: function clearSort() {\n      var states = this.states;\n      if (!states.sortingColumn) return;\n\n      this.updateSort(null, null, null);\n      this.commit('changeSortCondition', {\n        silent: true\n      });\n    },\n\n\n    // 适配层，expand-row-keys 在 Expand 与 TreeTable 中都有使用\n    setExpandRowKeysAdapter: function setExpandRowKeysAdapter(val) {\n      // 这里会触发额外的计算，但为了兼容性，暂时这么做\n      this.setExpandRowKeys(val);\n      this.updateTreeExpandKeys(val);\n    },\n\n\n    // 展开行与 TreeTable 都要使用\n    toggleRowExpansionAdapter: function toggleRowExpansionAdapter(row, expanded) {\n      var hasExpandColumn = this.states.columns.some(function (_ref) {\n        var type = _ref.type;\n        return type === 'expand';\n      });\n      if (hasExpandColumn) {\n        this.toggleRowExpansion(row, expanded);\n      } else {\n        this.toggleTreeExpansion(row, expanded);\n      }\n    }\n  }\n}));\n// CONCATENATED MODULE: ./packages/table/src/store/index.js\n\n\n\n\nwatcher.prototype.mutations = {\n  setData: function setData(states, data) {\n    var dataInstanceChanged = states._data !== data;\n    states._data = data;\n\n    this.execQuery();\n    // 数据变化，更新部分数据。\n    // 没有使用 computed，而是手动更新部分数据 https://github.com/vuejs/vue/issues/6660#issuecomment-331417140\n    this.updateCurrentRowData();\n    this.updateExpandRows();\n    if (states.reserveSelection) {\n      this.assertRowKey();\n      this.updateSelectionByRowKey();\n    } else {\n      if (dataInstanceChanged) {\n        this.clearSelection();\n      } else {\n        this.cleanSelection();\n      }\n    }\n    this.updateAllSelected();\n\n    this.updateTableScrollY();\n  },\n  insertColumn: function insertColumn(states, column, index, parent) {\n    var array = states._columns;\n    if (parent) {\n      array = parent.children;\n      if (!array) array = parent.children = [];\n    }\n\n    if (typeof index !== 'undefined') {\n      array.splice(index, 0, column);\n    } else {\n      array.push(column);\n    }\n\n    if (column.type === 'selection') {\n      states.selectable = column.selectable;\n      states.reserveSelection = column.reserveSelection;\n    }\n\n    if (this.table.$ready) {\n      this.updateColumns(); // hack for dynamics insert column\n      this.scheduleLayout();\n    }\n  },\n  removeColumn: function removeColumn(states, column, parent) {\n    var array = states._columns;\n    if (parent) {\n      array = parent.children;\n      if (!array) array = parent.children = [];\n    }\n    if (array) {\n      array.splice(array.indexOf(column), 1);\n    }\n\n    if (this.table.$ready) {\n      this.updateColumns(); // hack for dynamics remove column\n      this.scheduleLayout();\n    }\n  },\n  sort: function sort(states, options) {\n    var prop = options.prop,\n        order = options.order,\n        init = options.init;\n\n    if (prop) {\n      var column = Object(util_[\"arrayFind\"])(states.columns, function (column) {\n        return column.property === prop;\n      });\n      if (column) {\n        column.order = order;\n        this.updateSort(column, prop, order);\n        this.commit('changeSortCondition', { init: init });\n      }\n    }\n  },\n  changeSortCondition: function changeSortCondition(states, options) {\n    // 修复 pr https://github.com/ElemeFE/element/pull/15012 导致的 bug\n    var column = states.sortingColumn,\n        prop = states.sortProp,\n        order = states.sortOrder;\n\n    if (order === null) {\n      states.sortingColumn = null;\n      states.sortProp = null;\n    }\n    var ingore = { filter: true };\n    this.execQuery(ingore);\n\n    if (!options || !(options.silent || options.init)) {\n      this.table.$emit('sort-change', {\n        column: column,\n        prop: prop,\n        order: order\n      });\n    }\n\n    this.updateTableScrollY();\n  },\n  filterChange: function filterChange(states, options) {\n    var column = options.column,\n        values = options.values,\n        silent = options.silent;\n\n    var newFilters = this.updateFilters(column, values);\n\n    this.execQuery();\n\n    if (!silent) {\n      this.table.$emit('filter-change', newFilters);\n    }\n\n    this.updateTableScrollY();\n  },\n  toggleAllSelection: function toggleAllSelection() {\n    this.toggleAllSelection();\n  },\n  rowSelectedChanged: function rowSelectedChanged(states, row) {\n    this.toggleRowSelection(row);\n    this.updateAllSelected();\n  },\n  setHoverRow: function setHoverRow(states, row) {\n    states.hoverRow = row;\n  },\n  setCurrentRow: function setCurrentRow(states, row) {\n    this.updateCurrentRow(row);\n  }\n};\n\nwatcher.prototype.commit = function (name) {\n  var mutations = this.mutations;\n  if (mutations[name]) {\n    for (var _len = arguments.length, args = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    mutations[name].apply(this, [this.states].concat(args));\n  } else {\n    throw new Error('Action not found: ' + name);\n  }\n};\n\nwatcher.prototype.updateTableScrollY = function () {\n  external_vue_default.a.nextTick(this.table.updateScrollY);\n};\n\n/* harmony default export */ var src_store = (watcher);\n// EXTERNAL MODULE: external \"throttle-debounce/debounce\"\nvar debounce_ = __webpack_require__(19);\nvar debounce_default = /*#__PURE__*/__webpack_require__.n(debounce_);\n\n// CONCATENATED MODULE: ./packages/table/src/store/helper.js\n\n\n\nfunction createStore(table) {\n  var initialState = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  if (!table) {\n    throw new Error('Table is required.');\n  }\n\n  var store = new src_store();\n  store.table = table;\n  // fix https://github.com/ElemeFE/element/issues/14075\n  // related pr https://github.com/ElemeFE/element/pull/14146\n  store.toggleAllSelection = debounce_default()(10, store._toggleAllSelection);\n  Object.keys(initialState).forEach(function (key) {\n    store.states[key] = initialState[key];\n  });\n  return store;\n}\n\nfunction mapStates(mapper) {\n  var res = {};\n  Object.keys(mapper).forEach(function (key) {\n    var value = mapper[key];\n    var fn = void 0;\n    if (typeof value === 'string') {\n      fn = function fn() {\n        return this.store.states[value];\n      };\n    } else if (typeof value === 'function') {\n      fn = function fn() {\n        return value.call(this, this.store.states);\n      };\n    } else {\n      console.error('invalid value type');\n    }\n    if (fn) {\n      res[key] = fn;\n    }\n  });\n  return res;\n};\n// EXTERNAL MODULE: external \"element-ui/lib/utils/scrollbar-width\"\nvar scrollbar_width_ = __webpack_require__(38);\nvar scrollbar_width_default = /*#__PURE__*/__webpack_require__.n(scrollbar_width_);\n\n// CONCATENATED MODULE: ./packages/table/src/table-layout.js\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n\n\n\n\nvar table_layout_TableLayout = function () {\n  function TableLayout(options) {\n    _classCallCheck(this, TableLayout);\n\n    this.observers = [];\n    this.table = null;\n    this.store = null;\n    this.columns = null;\n    this.fit = true;\n    this.showHeader = true;\n\n    this.height = null;\n    this.scrollX = false;\n    this.scrollY = false;\n    this.bodyWidth = null;\n    this.fixedWidth = null;\n    this.rightFixedWidth = null;\n    this.tableHeight = null;\n    this.headerHeight = 44; // Table Header Height\n    this.appendHeight = 0; // Append Slot Height\n    this.footerHeight = 44; // Table Footer Height\n    this.viewportHeight = null; // Table Height - Scroll Bar Height\n    this.bodyHeight = null; // Table Height - Table Header Height\n    this.fixedBodyHeight = null; // Table Height - Table Header Height - Scroll Bar Height\n    this.gutterWidth = scrollbar_width_default()();\n\n    for (var name in options) {\n      if (options.hasOwnProperty(name)) {\n        this[name] = options[name];\n      }\n    }\n\n    if (!this.table) {\n      throw new Error('table is required for Table Layout');\n    }\n    if (!this.store) {\n      throw new Error('store is required for Table Layout');\n    }\n  }\n\n  TableLayout.prototype.updateScrollY = function updateScrollY() {\n    var height = this.height;\n    if (height === null) return false;\n    var bodyWrapper = this.table.bodyWrapper;\n    if (this.table.$el && bodyWrapper) {\n      var body = bodyWrapper.querySelector('.el-table__body');\n      var prevScrollY = this.scrollY;\n      var scrollY = body.offsetHeight > this.bodyHeight;\n      this.scrollY = scrollY;\n      return prevScrollY !== scrollY;\n    }\n    return false;\n  };\n\n  TableLayout.prototype.setHeight = function setHeight(value) {\n    var _this = this;\n\n    var prop = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'height';\n\n    if (external_vue_default.a.prototype.$isServer) return;\n    var el = this.table.$el;\n    value = Object(util[\"j\" /* parseHeight */])(value);\n    this.height = value;\n\n    if (!el && (value || value === 0)) return external_vue_default.a.nextTick(function () {\n      return _this.setHeight(value, prop);\n    });\n\n    if (typeof value === 'number') {\n      el.style[prop] = value + 'px';\n      this.updateElsHeight();\n    } else if (typeof value === 'string') {\n      el.style[prop] = value;\n      this.updateElsHeight();\n    }\n  };\n\n  TableLayout.prototype.setMaxHeight = function setMaxHeight(value) {\n    this.setHeight(value, 'max-height');\n  };\n\n  TableLayout.prototype.getFlattenColumns = function getFlattenColumns() {\n    var flattenColumns = [];\n    var columns = this.table.columns;\n    columns.forEach(function (column) {\n      if (column.isColumnGroup) {\n        flattenColumns.push.apply(flattenColumns, column.columns);\n      } else {\n        flattenColumns.push(column);\n      }\n    });\n\n    return flattenColumns;\n  };\n\n  TableLayout.prototype.updateElsHeight = function updateElsHeight() {\n    var _this2 = this;\n\n    if (!this.table.$ready) return external_vue_default.a.nextTick(function () {\n      return _this2.updateElsHeight();\n    });\n    var _table$$refs = this.table.$refs,\n        headerWrapper = _table$$refs.headerWrapper,\n        appendWrapper = _table$$refs.appendWrapper,\n        footerWrapper = _table$$refs.footerWrapper;\n\n    this.appendHeight = appendWrapper ? appendWrapper.offsetHeight : 0;\n\n    if (this.showHeader && !headerWrapper) return;\n\n    // fix issue (https://github.com/ElemeFE/element/pull/16956)\n    var headerTrElm = headerWrapper ? headerWrapper.querySelector('.el-table__header tr') : null;\n    var noneHeader = this.headerDisplayNone(headerTrElm);\n\n    var headerHeight = this.headerHeight = !this.showHeader ? 0 : headerWrapper.offsetHeight;\n    if (this.showHeader && !noneHeader && headerWrapper.offsetWidth > 0 && (this.table.columns || []).length > 0 && headerHeight < 2) {\n      return external_vue_default.a.nextTick(function () {\n        return _this2.updateElsHeight();\n      });\n    }\n    var tableHeight = this.tableHeight = this.table.$el.clientHeight;\n    var footerHeight = this.footerHeight = footerWrapper ? footerWrapper.offsetHeight : 0;\n    if (this.height !== null) {\n      this.bodyHeight = tableHeight - headerHeight - footerHeight + (footerWrapper ? 1 : 0);\n    }\n    this.fixedBodyHeight = this.scrollX ? this.bodyHeight - this.gutterWidth : this.bodyHeight;\n\n    var noData = !(this.store.states.data && this.store.states.data.length);\n    this.viewportHeight = this.scrollX ? tableHeight - (noData ? 0 : this.gutterWidth) : tableHeight;\n    this.updateScrollY();\n    this.notifyObservers('scrollable');\n  };\n\n  TableLayout.prototype.headerDisplayNone = function headerDisplayNone(elm) {\n    if (!elm) return true;\n    var headerChild = elm;\n    while (headerChild.tagName !== 'DIV') {\n      if (getComputedStyle(headerChild).display === 'none') {\n        return true;\n      }\n      headerChild = headerChild.parentElement;\n    }\n    return false;\n  };\n\n  TableLayout.prototype.updateColumnsWidth = function updateColumnsWidth() {\n    if (external_vue_default.a.prototype.$isServer) return;\n    var fit = this.fit;\n    var bodyWidth = this.table.$el.clientWidth;\n    var bodyMinWidth = 0;\n\n    var flattenColumns = this.getFlattenColumns();\n    var flexColumns = flattenColumns.filter(function (column) {\n      return typeof column.width !== 'number';\n    });\n\n    flattenColumns.forEach(function (column) {\n      // Clean those columns whose width changed from flex to unflex\n      if (typeof column.width === 'number' && column.realWidth) column.realWidth = null;\n    });\n\n    if (flexColumns.length > 0 && fit) {\n      flattenColumns.forEach(function (column) {\n        bodyMinWidth += column.width || column.minWidth || 80;\n      });\n\n      var scrollYWidth = this.scrollY ? this.gutterWidth : 0;\n\n      if (bodyMinWidth <= bodyWidth - scrollYWidth) {\n        // DON'T HAVE SCROLL BAR\n        this.scrollX = false;\n\n        var totalFlexWidth = bodyWidth - scrollYWidth - bodyMinWidth;\n\n        if (flexColumns.length === 1) {\n          flexColumns[0].realWidth = (flexColumns[0].minWidth || 80) + totalFlexWidth;\n        } else {\n          var allColumnsWidth = flexColumns.reduce(function (prev, column) {\n            return prev + (column.minWidth || 80);\n          }, 0);\n          var flexWidthPerPixel = totalFlexWidth / allColumnsWidth;\n          var noneFirstWidth = 0;\n\n          flexColumns.forEach(function (column, index) {\n            if (index === 0) return;\n            var flexWidth = Math.floor((column.minWidth || 80) * flexWidthPerPixel);\n            noneFirstWidth += flexWidth;\n            column.realWidth = (column.minWidth || 80) + flexWidth;\n          });\n\n          flexColumns[0].realWidth = (flexColumns[0].minWidth || 80) + totalFlexWidth - noneFirstWidth;\n        }\n      } else {\n        // HAVE HORIZONTAL SCROLL BAR\n        this.scrollX = true;\n        flexColumns.forEach(function (column) {\n          column.realWidth = column.minWidth;\n        });\n      }\n\n      this.bodyWidth = Math.max(bodyMinWidth, bodyWidth);\n      this.table.resizeState.width = this.bodyWidth;\n    } else {\n      flattenColumns.forEach(function (column) {\n        if (!column.width && !column.minWidth) {\n          column.realWidth = 80;\n        } else {\n          column.realWidth = column.width || column.minWidth;\n        }\n\n        bodyMinWidth += column.realWidth;\n      });\n      this.scrollX = bodyMinWidth > bodyWidth;\n\n      this.bodyWidth = bodyMinWidth;\n    }\n\n    var fixedColumns = this.store.states.fixedColumns;\n\n    if (fixedColumns.length > 0) {\n      var fixedWidth = 0;\n      fixedColumns.forEach(function (column) {\n        fixedWidth += column.realWidth || column.width;\n      });\n\n      this.fixedWidth = fixedWidth;\n    }\n\n    var rightFixedColumns = this.store.states.rightFixedColumns;\n    if (rightFixedColumns.length > 0) {\n      var rightFixedWidth = 0;\n      rightFixedColumns.forEach(function (column) {\n        rightFixedWidth += column.realWidth || column.width;\n      });\n\n      this.rightFixedWidth = rightFixedWidth;\n    }\n\n    this.notifyObservers('columns');\n  };\n\n  TableLayout.prototype.addObserver = function addObserver(observer) {\n    this.observers.push(observer);\n  };\n\n  TableLayout.prototype.removeObserver = function removeObserver(observer) {\n    var index = this.observers.indexOf(observer);\n    if (index !== -1) {\n      this.observers.splice(index, 1);\n    }\n  };\n\n  TableLayout.prototype.notifyObservers = function notifyObservers(event) {\n    var _this3 = this;\n\n    var observers = this.observers;\n    observers.forEach(function (observer) {\n      switch (event) {\n        case 'columns':\n          observer.onColumnsChange(_this3);\n          break;\n        case 'scrollable':\n          observer.onScrollableChange(_this3);\n          break;\n        default:\n          throw new Error('Table Layout don\\'t have event ' + event + '.');\n      }\n    });\n  };\n\n  return TableLayout;\n}();\n\n/* harmony default export */ var table_layout = (table_layout_TableLayout);\n// EXTERNAL MODULE: external \"element-ui/lib/utils/dom\"\nvar dom_ = __webpack_require__(2);\n\n// EXTERNAL MODULE: external \"element-ui/lib/tooltip\"\nvar tooltip_ = __webpack_require__(29);\nvar tooltip_default = /*#__PURE__*/__webpack_require__.n(tooltip_);\n\n// CONCATENATED MODULE: ./packages/table/src/layout-observer.js\n/* harmony default export */ var layout_observer = ({\n  created: function created() {\n    this.tableLayout.addObserver(this);\n  },\n  destroyed: function destroyed() {\n    this.tableLayout.removeObserver(this);\n  },\n\n\n  computed: {\n    tableLayout: function tableLayout() {\n      var layout = this.layout;\n      if (!layout && this.table) {\n        layout = this.table.layout;\n      }\n      if (!layout) {\n        throw new Error('Can not find table layout.');\n      }\n      return layout;\n    }\n  },\n\n  mounted: function mounted() {\n    this.onColumnsChange(this.tableLayout);\n    this.onScrollableChange(this.tableLayout);\n  },\n  updated: function updated() {\n    if (this.__updated__) return;\n    this.onColumnsChange(this.tableLayout);\n    this.onScrollableChange(this.tableLayout);\n    this.__updated__ = true;\n  },\n\n\n  methods: {\n    onColumnsChange: function onColumnsChange(layout) {\n      var cols = this.$el.querySelectorAll('colgroup > col');\n      if (!cols.length) return;\n      var flattenColumns = layout.getFlattenColumns();\n      var columnsMap = {};\n      flattenColumns.forEach(function (column) {\n        columnsMap[column.id] = column;\n      });\n      for (var i = 0, j = cols.length; i < j; i++) {\n        var col = cols[i];\n        var name = col.getAttribute('name');\n        var column = columnsMap[name];\n        if (column) {\n          col.setAttribute('width', column.realWidth || column.width);\n        }\n      }\n    },\n    onScrollableChange: function onScrollableChange(layout) {\n      var cols = this.$el.querySelectorAll('colgroup > col[name=gutter]');\n      for (var i = 0, j = cols.length; i < j; i++) {\n        var col = cols[i];\n        col.setAttribute('width', layout.scrollY ? layout.gutterWidth : '0');\n      }\n      var ths = this.$el.querySelectorAll('th.gutter');\n      for (var _i = 0, _j = ths.length; _i < _j; _i++) {\n        var th = ths[_i];\n        th.style.width = layout.scrollY ? layout.gutterWidth + 'px' : '0';\n        th.style.display = layout.scrollY ? '' : 'none';\n      }\n    }\n  }\n});\n// CONCATENATED MODULE: ./packages/table/src/table-row.js\nvar table_row_extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\n\n/* harmony default export */ var table_row = ({\n  name: 'ElTableRow',\n  props: ['columns', 'row', 'index', 'isSelected', 'isExpanded', 'store', 'context', 'firstDefaultColumnIndex', 'treeRowData', 'treeIndent', 'columnsHidden', 'getSpan', 'getColspanRealWidth', 'getCellStyle', 'getCellClass', 'handleCellMouseLeave', 'handleCellMouseEnter', 'fixed'],\n  components: {\n    ElCheckbox: checkbox_default.a\n  },\n  render: function render() {\n    var _this = this;\n\n    var h = arguments[0];\n    var columns = this.columns,\n        row = this.row,\n        $index = this.index,\n        store = this.store,\n        context = this.context,\n        firstDefaultColumnIndex = this.firstDefaultColumnIndex,\n        treeRowData = this.treeRowData,\n        treeIndent = this.treeIndent,\n        _columnsHidden = this.columnsHidden,\n        columnsHidden = _columnsHidden === undefined ? [] : _columnsHidden,\n        isSelected = this.isSelected,\n        isExpanded = this.isExpanded;\n\n\n    return h('tr', [columns.map(function (column, cellIndex) {\n      var _getSpan = _this.getSpan(row, column, $index, cellIndex),\n          rowspan = _getSpan.rowspan,\n          colspan = _getSpan.colspan;\n\n      if (!rowspan || !colspan) {\n        return null;\n      }\n      var columnData = table_row_extends({}, column);\n      columnData.realWidth = _this.getColspanRealWidth(columns, colspan, cellIndex);\n      var data = {\n        store: store,\n        isSelected: isSelected,\n        isExpanded: isExpanded,\n        _self: context,\n        column: columnData,\n        row: row,\n        $index: $index\n      };\n      if (cellIndex === firstDefaultColumnIndex && treeRowData) {\n        data.treeNode = {\n          indent: treeRowData.level * treeIndent,\n          level: treeRowData.level\n        };\n        if (typeof treeRowData.expanded === 'boolean') {\n          data.treeNode.expanded = treeRowData.expanded;\n          // 表明是懒加载\n          if ('loading' in treeRowData) {\n            data.treeNode.loading = treeRowData.loading;\n          }\n          if ('noLazyChildren' in treeRowData) {\n            data.treeNode.noLazyChildren = treeRowData.noLazyChildren;\n          }\n        }\n      }\n      return h(\n        'td',\n        {\n          style: _this.getCellStyle($index, cellIndex, row, column),\n          'class': _this.getCellClass($index, cellIndex, row, column),\n          attrs: { rowspan: rowspan,\n            colspan: colspan\n          },\n          on: {\n            'mouseenter': function mouseenter($event) {\n              return _this.handleCellMouseEnter($event, row);\n            },\n            'mouseleave': _this.handleCellMouseLeave\n          }\n        },\n        [column.renderCell.call(_this._renderProxy, _this.$createElement, data, columnsHidden[cellIndex])]\n      );\n    })]);\n  }\n});\n// CONCATENATED MODULE: ./packages/table/src/table-body.js\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nvar table_body_extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\n\n\n\n\n\n\n\n\n\n\n/* harmony default export */ var table_body = ({\n  name: 'ElTableBody',\n\n  mixins: [layout_observer],\n\n  components: {\n    ElCheckbox: checkbox_default.a,\n    ElTooltip: tooltip_default.a,\n    TableRow: table_row\n  },\n\n  props: {\n    store: {\n      required: true\n    },\n    stripe: Boolean,\n    context: {},\n    rowClassName: [String, Function],\n    rowStyle: [Object, Function],\n    fixed: String,\n    highlight: Boolean\n  },\n\n  render: function render(h) {\n    var _this = this;\n\n    var data = this.data || [];\n    return h(\n      'table',\n      {\n        'class': 'el-table__body',\n        attrs: { cellspacing: '0',\n          cellpadding: '0',\n          border: '0' }\n      },\n      [h('colgroup', [this.columns.map(function (column) {\n        return h('col', {\n          attrs: { name: column.id },\n          key: column.id });\n      })]), h('tbody', [data.reduce(function (acc, row) {\n        return acc.concat(_this.wrappedRowRender(row, acc.length));\n      }, []), h('el-tooltip', {\n        attrs: { effect: this.table.tooltipEffect, placement: 'top', content: this.tooltipContent },\n        ref: 'tooltip' })])]\n    );\n  },\n\n\n  computed: table_body_extends({\n    table: function table() {\n      return this.$parent;\n    }\n  }, mapStates({\n    data: 'data',\n    columns: 'columns',\n    treeIndent: 'indent',\n    leftFixedLeafCount: 'fixedLeafColumnsLength',\n    rightFixedLeafCount: 'rightFixedLeafColumnsLength',\n    columnsCount: function columnsCount(states) {\n      return states.columns.length;\n    },\n    leftFixedCount: function leftFixedCount(states) {\n      return states.fixedColumns.length;\n    },\n    rightFixedCount: function rightFixedCount(states) {\n      return states.rightFixedColumns.length;\n    },\n    hasExpandColumn: function hasExpandColumn(states) {\n      return states.columns.some(function (_ref) {\n        var type = _ref.type;\n        return type === 'expand';\n      });\n    }\n  }), {\n    columnsHidden: function columnsHidden() {\n      var _this2 = this;\n\n      return this.columns.map(function (column, index) {\n        return _this2.isColumnHidden(index);\n      });\n    },\n    firstDefaultColumnIndex: function firstDefaultColumnIndex() {\n      return Object(util_[\"arrayFindIndex\"])(this.columns, function (_ref2) {\n        var type = _ref2.type;\n        return type === 'default';\n      });\n    }\n  }),\n\n  watch: {\n    // don't trigger getter of currentRow in getCellClass. see https://jsfiddle.net/oe2b4hqt/\n    // update DOM manually. see https://github.com/ElemeFE/element/pull/13954/files#diff-9b450c00d0a9dec0ffad5a3176972e40\n    'store.states.hoverRow': function storeStatesHoverRow(newVal, oldVal) {\n      var _this3 = this;\n\n      if (!this.store.states.isComplex || this.$isServer) return;\n      var raf = window.requestAnimationFrame;\n      if (!raf) {\n        raf = function raf(fn) {\n          return setTimeout(fn, 16);\n        };\n      }\n      raf(function () {\n        var rows = _this3.$el.querySelectorAll('.el-table__row');\n        var oldRow = rows[oldVal];\n        var newRow = rows[newVal];\n        if (oldRow) {\n          Object(dom_[\"removeClass\"])(oldRow, 'hover-row');\n        }\n        if (newRow) {\n          Object(dom_[\"addClass\"])(newRow, 'hover-row');\n        }\n      });\n    }\n  },\n\n  data: function data() {\n    return {\n      tooltipContent: ''\n    };\n  },\n  created: function created() {\n    this.activateTooltip = debounce_default()(50, function (tooltip) {\n      return tooltip.handleShowPopper();\n    });\n  },\n\n\n  methods: {\n    getKeyOfRow: function getKeyOfRow(row, index) {\n      var rowKey = this.table.rowKey;\n      if (rowKey) {\n        return Object(util[\"g\" /* getRowIdentity */])(row, rowKey);\n      }\n      return index;\n    },\n    isColumnHidden: function isColumnHidden(index) {\n      if (this.fixed === true || this.fixed === 'left') {\n        return index >= this.leftFixedLeafCount;\n      } else if (this.fixed === 'right') {\n        return index < this.columnsCount - this.rightFixedLeafCount;\n      } else {\n        return index < this.leftFixedLeafCount || index >= this.columnsCount - this.rightFixedLeafCount;\n      }\n    },\n    getSpan: function getSpan(row, column, rowIndex, columnIndex) {\n      var rowspan = 1;\n      var colspan = 1;\n      var fn = this.table.spanMethod;\n      if (typeof fn === 'function') {\n        var result = fn({\n          row: row,\n          column: column,\n          rowIndex: rowIndex,\n          columnIndex: columnIndex\n        });\n        if (Array.isArray(result)) {\n          rowspan = result[0];\n          colspan = result[1];\n        } else if ((typeof result === 'undefined' ? 'undefined' : _typeof(result)) === 'object') {\n          rowspan = result.rowspan;\n          colspan = result.colspan;\n        }\n      }\n      return { rowspan: rowspan, colspan: colspan };\n    },\n    getRowStyle: function getRowStyle(row, rowIndex) {\n      var rowStyle = this.table.rowStyle;\n      if (typeof rowStyle === 'function') {\n        return rowStyle.call(null, {\n          row: row,\n          rowIndex: rowIndex\n        });\n      }\n      return rowStyle || null;\n    },\n    getRowClass: function getRowClass(row, rowIndex) {\n      var classes = ['el-table__row'];\n      if (this.table.highlightCurrentRow && row === this.store.states.currentRow) {\n        classes.push('current-row');\n      }\n\n      if (this.stripe && rowIndex % 2 === 1) {\n        classes.push('el-table__row--striped');\n      }\n      var rowClassName = this.table.rowClassName;\n      if (typeof rowClassName === 'string') {\n        classes.push(rowClassName);\n      } else if (typeof rowClassName === 'function') {\n        classes.push(rowClassName.call(null, {\n          row: row,\n          rowIndex: rowIndex\n        }));\n      }\n\n      if (this.store.states.expandRows.indexOf(row) > -1) {\n        classes.push('expanded');\n      }\n\n      return classes;\n    },\n    getCellStyle: function getCellStyle(rowIndex, columnIndex, row, column) {\n      var cellStyle = this.table.cellStyle;\n      if (typeof cellStyle === 'function') {\n        return cellStyle.call(null, {\n          rowIndex: rowIndex,\n          columnIndex: columnIndex,\n          row: row,\n          column: column\n        });\n      }\n      return cellStyle;\n    },\n    getCellClass: function getCellClass(rowIndex, columnIndex, row, column) {\n      var classes = [column.id, column.align, column.className];\n\n      if (this.isColumnHidden(columnIndex)) {\n        classes.push('is-hidden');\n      }\n\n      var cellClassName = this.table.cellClassName;\n      if (typeof cellClassName === 'string') {\n        classes.push(cellClassName);\n      } else if (typeof cellClassName === 'function') {\n        classes.push(cellClassName.call(null, {\n          rowIndex: rowIndex,\n          columnIndex: columnIndex,\n          row: row,\n          column: column\n        }));\n      }\n\n      classes.push('el-table__cell');\n\n      return classes.join(' ');\n    },\n    getColspanRealWidth: function getColspanRealWidth(columns, colspan, index) {\n      if (colspan < 1) {\n        return columns[index].realWidth;\n      }\n      var widthArr = columns.map(function (_ref3) {\n        var realWidth = _ref3.realWidth;\n        return realWidth;\n      }).slice(index, index + colspan);\n      return widthArr.reduce(function (acc, width) {\n        return acc + width;\n      }, -1);\n    },\n    handleCellMouseEnter: function handleCellMouseEnter(event, row) {\n      var table = this.table;\n      var cell = Object(util[\"b\" /* getCell */])(event);\n\n      if (cell) {\n        var column = Object(util[\"c\" /* getColumnByCell */])(table, cell);\n        var hoverState = table.hoverState = { cell: cell, column: column, row: row };\n        table.$emit('cell-mouse-enter', hoverState.row, hoverState.column, hoverState.cell, event);\n      }\n\n      // 判断是否text-overflow, 如果是就显示tooltip\n      var cellChild = event.target.querySelector('.cell');\n      if (!(Object(dom_[\"hasClass\"])(cellChild, 'el-tooltip') && cellChild.childNodes.length)) {\n        return;\n      }\n      // use range width instead of scrollWidth to determine whether the text is overflowing\n      // to address a potential FireFox bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1074543#c3\n      var range = document.createRange();\n      range.setStart(cellChild, 0);\n      range.setEnd(cellChild, cellChild.childNodes.length);\n      var rangeWidth = range.getBoundingClientRect().width;\n      var padding = (parseInt(Object(dom_[\"getStyle\"])(cellChild, 'paddingLeft'), 10) || 0) + (parseInt(Object(dom_[\"getStyle\"])(cellChild, 'paddingRight'), 10) || 0);\n      if ((rangeWidth + padding > cellChild.offsetWidth || cellChild.scrollWidth > cellChild.offsetWidth) && this.$refs.tooltip) {\n        var tooltip = this.$refs.tooltip;\n        // TODO 会引起整个 Table 的重新渲染，需要优化\n        this.tooltipContent = cell.innerText || cell.textContent;\n        tooltip.referenceElm = cell;\n        tooltip.$refs.popper && (tooltip.$refs.popper.style.display = 'none');\n        tooltip.doDestroy();\n        tooltip.setExpectedState(true);\n        this.activateTooltip(tooltip);\n      }\n    },\n    handleCellMouseLeave: function handleCellMouseLeave(event) {\n      var tooltip = this.$refs.tooltip;\n      if (tooltip) {\n        tooltip.setExpectedState(false);\n        tooltip.handleClosePopper();\n      }\n      var cell = Object(util[\"b\" /* getCell */])(event);\n      if (!cell) return;\n\n      var oldHoverState = this.table.hoverState || {};\n      this.table.$emit('cell-mouse-leave', oldHoverState.row, oldHoverState.column, oldHoverState.cell, event);\n    },\n\n\n    handleMouseEnter: debounce_default()(30, function (index) {\n      this.store.commit('setHoverRow', index);\n    }),\n\n    handleMouseLeave: debounce_default()(30, function () {\n      this.store.commit('setHoverRow', null);\n    }),\n\n    handleContextMenu: function handleContextMenu(event, row) {\n      this.handleEvent(event, row, 'contextmenu');\n    },\n    handleDoubleClick: function handleDoubleClick(event, row) {\n      this.handleEvent(event, row, 'dblclick');\n    },\n    handleClick: function handleClick(event, row) {\n      this.store.commit('setCurrentRow', row);\n      this.handleEvent(event, row, 'click');\n    },\n    handleEvent: function handleEvent(event, row, name) {\n      var table = this.table;\n      var cell = Object(util[\"b\" /* getCell */])(event);\n      var column = void 0;\n      if (cell) {\n        column = Object(util[\"c\" /* getColumnByCell */])(table, cell);\n        if (column) {\n          table.$emit('cell-' + name, row, column, cell, event);\n        }\n      }\n      table.$emit('row-' + name, row, column, event);\n    },\n    rowRender: function rowRender(row, $index, treeRowData) {\n      var _this4 = this;\n\n      var h = this.$createElement;\n      var treeIndent = this.treeIndent,\n          columns = this.columns,\n          firstDefaultColumnIndex = this.firstDefaultColumnIndex;\n\n      var rowClasses = this.getRowClass(row, $index);\n      var display = true;\n      if (treeRowData) {\n        rowClasses.push('el-table__row--level-' + treeRowData.level);\n        display = treeRowData.display;\n      }\n      // 指令 v-show 会覆盖 row-style 中 display\n      // 使用 :style 代替 v-show https://github.com/ElemeFE/element/issues/16995\n      var displayStyle = display ? null : {\n        display: 'none'\n      };\n      return h(table_row, {\n        style: [displayStyle, this.getRowStyle(row, $index)],\n        'class': rowClasses,\n        key: this.getKeyOfRow(row, $index),\n        nativeOn: {\n          'dblclick': function dblclick($event) {\n            return _this4.handleDoubleClick($event, row);\n          },\n          'click': function click($event) {\n            return _this4.handleClick($event, row);\n          },\n          'contextmenu': function contextmenu($event) {\n            return _this4.handleContextMenu($event, row);\n          },\n          'mouseenter': function mouseenter(_) {\n            return _this4.handleMouseEnter($index);\n          },\n          'mouseleave': this.handleMouseLeave\n        },\n        attrs: {\n          columns: columns,\n          row: row,\n          index: $index,\n          store: this.store,\n          context: this.context || this.table.$vnode.context,\n          firstDefaultColumnIndex: firstDefaultColumnIndex,\n          treeRowData: treeRowData,\n          treeIndent: treeIndent,\n          columnsHidden: this.columnsHidden,\n          getSpan: this.getSpan,\n          getColspanRealWidth: this.getColspanRealWidth,\n          getCellStyle: this.getCellStyle,\n          getCellClass: this.getCellClass,\n          handleCellMouseEnter: this.handleCellMouseEnter,\n          handleCellMouseLeave: this.handleCellMouseLeave,\n          isSelected: this.store.isSelected(row),\n          isExpanded: this.store.states.expandRows.indexOf(row) > -1,\n          fixed: this.fixed\n        }\n      });\n    },\n    wrappedRowRender: function wrappedRowRender(row, $index) {\n      var _this5 = this;\n\n      var h = this.$createElement;\n\n      var store = this.store;\n      var isRowExpanded = store.isRowExpanded,\n          assertRowKey = store.assertRowKey;\n      var _store$states = store.states,\n          treeData = _store$states.treeData,\n          lazyTreeNodeMap = _store$states.lazyTreeNodeMap,\n          childrenColumnName = _store$states.childrenColumnName,\n          rowKey = _store$states.rowKey;\n\n      if (this.hasExpandColumn && isRowExpanded(row)) {\n        var renderExpanded = this.table.renderExpanded;\n        var tr = this.rowRender(row, $index);\n        if (!renderExpanded) {\n          console.error('[Element Error]renderExpanded is required.');\n          return tr;\n        }\n        // 使用二维数组，避免修改 $index\n        return [[tr, h(\n          'tr',\n          { key: 'expanded-row__' + tr.key },\n          [h(\n            'td',\n            {\n              attrs: { colspan: this.columnsCount },\n              'class': 'el-table__cell el-table__expanded-cell' },\n            [renderExpanded(this.$createElement, { row: row, $index: $index, store: this.store })]\n          )]\n        )]];\n      } else if (Object.keys(treeData).length) {\n        assertRowKey();\n        // TreeTable 时，rowKey 必须由用户设定，不使用 getKeyOfRow 计算\n        // 在调用 rowRender 函数时，仍然会计算 rowKey，不太好的操作\n        var key = Object(util[\"g\" /* getRowIdentity */])(row, rowKey);\n        var cur = treeData[key];\n        var treeRowData = null;\n        if (cur) {\n          treeRowData = {\n            expanded: cur.expanded,\n            level: cur.level,\n            display: true\n          };\n          if (typeof cur.lazy === 'boolean') {\n            if (typeof cur.loaded === 'boolean' && cur.loaded) {\n              treeRowData.noLazyChildren = !(cur.children && cur.children.length);\n            }\n            treeRowData.loading = cur.loading;\n          }\n        }\n        var tmp = [this.rowRender(row, $index, treeRowData)];\n        // 渲染嵌套数据\n        if (cur) {\n          // currentRow 记录的是 index，所以还需主动增加 TreeTable 的 index\n          var i = 0;\n          var traverse = function traverse(children, parent) {\n            if (!(children && children.length && parent)) return;\n            children.forEach(function (node) {\n              // 父节点的 display 状态影响子节点的显示状态\n              var innerTreeRowData = {\n                display: parent.display && parent.expanded,\n                level: parent.level + 1\n              };\n              var childKey = Object(util[\"g\" /* getRowIdentity */])(node, rowKey);\n              if (childKey === undefined || childKey === null) {\n                throw new Error('for nested data item, row-key is required.');\n              }\n              cur = table_body_extends({}, treeData[childKey]);\n              // 对于当前节点，分成有无子节点两种情况。\n              // 如果包含子节点的，设置 expanded 属性。\n              // 对于它子节点的 display 属性由它本身的 expanded 与 display 共同决定。\n              if (cur) {\n                innerTreeRowData.expanded = cur.expanded;\n                // 懒加载的某些节点，level 未知\n                cur.level = cur.level || innerTreeRowData.level;\n                cur.display = !!(cur.expanded && innerTreeRowData.display);\n                if (typeof cur.lazy === 'boolean') {\n                  if (typeof cur.loaded === 'boolean' && cur.loaded) {\n                    innerTreeRowData.noLazyChildren = !(cur.children && cur.children.length);\n                  }\n                  innerTreeRowData.loading = cur.loading;\n                }\n              }\n              i++;\n              tmp.push(_this5.rowRender(node, $index + i, innerTreeRowData));\n              if (cur) {\n                var _nodes = lazyTreeNodeMap[childKey] || node[childrenColumnName];\n                traverse(_nodes, cur);\n              }\n            });\n          };\n          // 对于 root 节点，display 一定为 true\n          cur.display = true;\n          var nodes = lazyTreeNodeMap[key] || row[childrenColumnName];\n          traverse(nodes, cur);\n        }\n        return tmp;\n      } else {\n        return this.rowRender(row, $index);\n      }\n    }\n  }\n});\n// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./packages/table/src/filter-panel.vue?vue&type=template&id=7f2c919f&\nvar filter_panelvue_type_template_id_7f2c919f_render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"transition\", { attrs: { name: \"el-zoom-in-top\" } }, [\n    _vm.multiple\n      ? _c(\n          \"div\",\n          {\n            directives: [\n              {\n                name: \"clickoutside\",\n                rawName: \"v-clickoutside\",\n                value: _vm.handleOutsideClick,\n                expression: \"handleOutsideClick\"\n              },\n              {\n                name: \"show\",\n                rawName: \"v-show\",\n                value: _vm.showPopper,\n                expression: \"showPopper\"\n              }\n            ],\n            staticClass: \"el-table-filter\"\n          },\n          [\n            _c(\n              \"div\",\n              { staticClass: \"el-table-filter__content\" },\n              [\n                _c(\n                  \"el-scrollbar\",\n                  { attrs: { \"wrap-class\": \"el-table-filter__wrap\" } },\n                  [\n                    _c(\n                      \"el-checkbox-group\",\n                      {\n                        staticClass: \"el-table-filter__checkbox-group\",\n                        model: {\n                          value: _vm.filteredValue,\n                          callback: function($$v) {\n                            _vm.filteredValue = $$v\n                          },\n                          expression: \"filteredValue\"\n                        }\n                      },\n                      _vm._l(_vm.filters, function(filter) {\n                        return _c(\n                          \"el-checkbox\",\n                          { key: filter.value, attrs: { label: filter.value } },\n                          [_vm._v(_vm._s(filter.text))]\n                        )\n                      }),\n                      1\n                    )\n                  ],\n                  1\n                )\n              ],\n              1\n            ),\n            _c(\"div\", { staticClass: \"el-table-filter__bottom\" }, [\n              _c(\n                \"button\",\n                {\n                  class: { \"is-disabled\": _vm.filteredValue.length === 0 },\n                  attrs: { disabled: _vm.filteredValue.length === 0 },\n                  on: { click: _vm.handleConfirm }\n                },\n                [_vm._v(_vm._s(_vm.t(\"el.table.confirmFilter\")))]\n              ),\n              _c(\"button\", { on: { click: _vm.handleReset } }, [\n                _vm._v(_vm._s(_vm.t(\"el.table.resetFilter\")))\n              ])\n            ])\n          ]\n        )\n      : _c(\n          \"div\",\n          {\n            directives: [\n              {\n                name: \"clickoutside\",\n                rawName: \"v-clickoutside\",\n                value: _vm.handleOutsideClick,\n                expression: \"handleOutsideClick\"\n              },\n              {\n                name: \"show\",\n                rawName: \"v-show\",\n                value: _vm.showPopper,\n                expression: \"showPopper\"\n              }\n            ],\n            staticClass: \"el-table-filter\"\n          },\n          [\n            _c(\n              \"ul\",\n              { staticClass: \"el-table-filter__list\" },\n              [\n                _c(\n                  \"li\",\n                  {\n                    staticClass: \"el-table-filter__list-item\",\n                    class: {\n                      \"is-active\":\n                        _vm.filterValue === undefined ||\n                        _vm.filterValue === null\n                    },\n                    on: {\n                      click: function($event) {\n                        _vm.handleSelect(null)\n                      }\n                    }\n                  },\n                  [_vm._v(_vm._s(_vm.t(\"el.table.clearFilter\")))]\n                ),\n                _vm._l(_vm.filters, function(filter) {\n                  return _c(\n                    \"li\",\n                    {\n                      key: filter.value,\n                      staticClass: \"el-table-filter__list-item\",\n                      class: { \"is-active\": _vm.isActive(filter) },\n                      attrs: { label: filter.value },\n                      on: {\n                        click: function($event) {\n                          _vm.handleSelect(filter.value)\n                        }\n                      }\n                    },\n                    [_vm._v(_vm._s(filter.text))]\n                  )\n                })\n              ],\n              2\n            )\n          ]\n        )\n  ])\n}\nvar filter_panelvue_type_template_id_7f2c919f_staticRenderFns = []\nfilter_panelvue_type_template_id_7f2c919f_render._withStripped = true\n\n\n// CONCATENATED MODULE: ./packages/table/src/filter-panel.vue?vue&type=template&id=7f2c919f&\n\n// EXTERNAL MODULE: external \"element-ui/lib/utils/vue-popper\"\nvar vue_popper_ = __webpack_require__(5);\nvar vue_popper_default = /*#__PURE__*/__webpack_require__.n(vue_popper_);\n\n// EXTERNAL MODULE: external \"element-ui/lib/utils/popup\"\nvar popup_ = __webpack_require__(13);\n\n// EXTERNAL MODULE: external \"element-ui/lib/utils/clickoutside\"\nvar clickoutside_ = __webpack_require__(12);\nvar clickoutside_default = /*#__PURE__*/__webpack_require__.n(clickoutside_);\n\n// CONCATENATED MODULE: ./packages/table/src/dropdown.js\n\nvar dropdowns = [];\n\n!external_vue_default.a.prototype.$isServer && document.addEventListener('click', function (event) {\n  dropdowns.forEach(function (dropdown) {\n    var target = event.target;\n    if (!dropdown || !dropdown.$el) return;\n    if (target === dropdown.$el || dropdown.$el.contains(target)) {\n      return;\n    }\n    dropdown.handleOutsideClick && dropdown.handleOutsideClick(event);\n  });\n});\n\n/* harmony default export */ var dropdown = ({\n  open: function open(instance) {\n    if (instance) {\n      dropdowns.push(instance);\n    }\n  },\n  close: function close(instance) {\n    var index = dropdowns.indexOf(instance);\n    if (index !== -1) {\n      dropdowns.splice(instance, 1);\n    }\n  }\n});\n// EXTERNAL MODULE: external \"element-ui/lib/checkbox-group\"\nvar checkbox_group_ = __webpack_require__(39);\nvar checkbox_group_default = /*#__PURE__*/__webpack_require__.n(checkbox_group_);\n\n// EXTERNAL MODULE: external \"element-ui/lib/scrollbar\"\nvar scrollbar_ = __webpack_require__(15);\nvar scrollbar_default = /*#__PURE__*/__webpack_require__.n(scrollbar_);\n\n// CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/table/src/filter-panel.vue?vue&type=script&lang=js&\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\n\n\n\n\n\n\n\n\n/* harmony default export */ var filter_panelvue_type_script_lang_js_ = ({\n  name: 'ElTableFilterPanel',\n\n  mixins: [vue_popper_default.a, locale_default.a],\n\n  directives: {\n    Clickoutside: clickoutside_default.a\n  },\n\n  components: {\n    ElCheckbox: checkbox_default.a,\n    ElCheckboxGroup: checkbox_group_default.a,\n    ElScrollbar: scrollbar_default.a\n  },\n\n  props: {\n    placement: {\n      type: String,\n      default: 'bottom-end'\n    }\n  },\n\n  methods: {\n    isActive: function isActive(filter) {\n      return filter.value === this.filterValue;\n    },\n    handleOutsideClick: function handleOutsideClick() {\n      var _this = this;\n\n      setTimeout(function () {\n        _this.showPopper = false;\n      }, 16);\n    },\n    handleConfirm: function handleConfirm() {\n      this.confirmFilter(this.filteredValue);\n      this.handleOutsideClick();\n    },\n    handleReset: function handleReset() {\n      this.filteredValue = [];\n      this.confirmFilter(this.filteredValue);\n      this.handleOutsideClick();\n    },\n    handleSelect: function handleSelect(filterValue) {\n      this.filterValue = filterValue;\n\n      if (typeof filterValue !== 'undefined' && filterValue !== null) {\n        this.confirmFilter(this.filteredValue);\n      } else {\n        this.confirmFilter([]);\n      }\n\n      this.handleOutsideClick();\n    },\n    confirmFilter: function confirmFilter(filteredValue) {\n      this.table.store.commit('filterChange', {\n        column: this.column,\n        values: filteredValue\n      });\n      this.table.store.updateAllSelected();\n    }\n  },\n\n  data: function data() {\n    return {\n      table: null,\n      cell: null,\n      column: null\n    };\n  },\n\n\n  computed: {\n    filters: function filters() {\n      return this.column && this.column.filters;\n    },\n\n\n    filterValue: {\n      get: function get() {\n        return (this.column.filteredValue || [])[0];\n      },\n      set: function set(value) {\n        if (this.filteredValue) {\n          if (typeof value !== 'undefined' && value !== null) {\n            this.filteredValue.splice(0, 1, value);\n          } else {\n            this.filteredValue.splice(0, 1);\n          }\n        }\n      }\n    },\n\n    filteredValue: {\n      get: function get() {\n        if (this.column) {\n          return this.column.filteredValue || [];\n        }\n        return [];\n      },\n      set: function set(value) {\n        if (this.column) {\n          this.column.filteredValue = value;\n        }\n      }\n    },\n\n    multiple: function multiple() {\n      if (this.column) {\n        return this.column.filterMultiple;\n      }\n      return true;\n    }\n  },\n\n  mounted: function mounted() {\n    var _this2 = this;\n\n    this.popperElm = this.$el;\n    this.referenceElm = this.cell;\n    this.table.bodyWrapper.addEventListener('scroll', function () {\n      _this2.updatePopper();\n    });\n\n    this.$watch('showPopper', function (value) {\n      if (_this2.column) _this2.column.filterOpened = value;\n      if (value) {\n        dropdown.open(_this2);\n      } else {\n        dropdown.close(_this2);\n      }\n    });\n  },\n\n  watch: {\n    showPopper: function showPopper(val) {\n      if (val === true && parseInt(this.popperJS._popper.style.zIndex, 10) < popup_[\"PopupManager\"].zIndex) {\n        this.popperJS._popper.style.zIndex = popup_[\"PopupManager\"].nextZIndex();\n      }\n    }\n  }\n});\n// CONCATENATED MODULE: ./packages/table/src/filter-panel.vue?vue&type=script&lang=js&\n /* harmony default export */ var src_filter_panelvue_type_script_lang_js_ = (filter_panelvue_type_script_lang_js_); \n// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js\nvar componentNormalizer = __webpack_require__(0);\n\n// CONCATENATED MODULE: ./packages/table/src/filter-panel.vue\n\n\n\n\n\n/* normalize component */\n\nvar component = Object(componentNormalizer[\"a\" /* default */])(\n  src_filter_panelvue_type_script_lang_js_,\n  filter_panelvue_type_template_id_7f2c919f_render,\n  filter_panelvue_type_template_id_7f2c919f_staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (false) { var api; }\ncomponent.options.__file = \"packages/table/src/filter-panel.vue\"\n/* harmony default export */ var filter_panel = (component.exports);\n// CONCATENATED MODULE: ./packages/table/src/table-header.js\nvar table_header_extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\n\n\n\n\n\n\n\nvar getAllColumns = function getAllColumns(columns) {\n  var result = [];\n  columns.forEach(function (column) {\n    if (column.children) {\n      result.push(column);\n      result.push.apply(result, getAllColumns(column.children));\n    } else {\n      result.push(column);\n    }\n  });\n  return result;\n};\n\nvar convertToRows = function convertToRows(originColumns) {\n  var maxLevel = 1;\n  var traverse = function traverse(column, parent) {\n    if (parent) {\n      column.level = parent.level + 1;\n      if (maxLevel < column.level) {\n        maxLevel = column.level;\n      }\n    }\n    if (column.children) {\n      var colSpan = 0;\n      column.children.forEach(function (subColumn) {\n        traverse(subColumn, column);\n        colSpan += subColumn.colSpan;\n      });\n      column.colSpan = colSpan;\n    } else {\n      column.colSpan = 1;\n    }\n  };\n\n  originColumns.forEach(function (column) {\n    column.level = 1;\n    traverse(column);\n  });\n\n  var rows = [];\n  for (var i = 0; i < maxLevel; i++) {\n    rows.push([]);\n  }\n\n  var allColumns = getAllColumns(originColumns);\n\n  allColumns.forEach(function (column) {\n    if (!column.children) {\n      column.rowSpan = maxLevel - column.level + 1;\n    } else {\n      column.rowSpan = 1;\n    }\n    rows[column.level - 1].push(column);\n  });\n\n  return rows;\n};\n\n/* harmony default export */ var table_header = ({\n  name: 'ElTableHeader',\n\n  mixins: [layout_observer],\n\n  render: function render(h) {\n    var _this = this;\n\n    var originColumns = this.store.states.originColumns;\n    var columnRows = convertToRows(originColumns, this.columns);\n    // 是否拥有多级表头\n    var isGroup = columnRows.length > 1;\n    if (isGroup) this.$parent.isGroup = true;\n    return h(\n      'table',\n      {\n        'class': 'el-table__header',\n        attrs: { cellspacing: '0',\n          cellpadding: '0',\n          border: '0' }\n      },\n      [h('colgroup', [this.columns.map(function (column) {\n        return h('col', {\n          attrs: { name: column.id },\n          key: column.id });\n      }), this.hasGutter ? h('col', {\n        attrs: { name: 'gutter' }\n      }) : '']), h(\n        'thead',\n        { 'class': [{ 'is-group': isGroup, 'has-gutter': this.hasGutter }] },\n        [this._l(columnRows, function (columns, rowIndex) {\n          return h(\n            'tr',\n            {\n              style: _this.getHeaderRowStyle(rowIndex),\n              'class': _this.getHeaderRowClass(rowIndex)\n            },\n            [columns.map(function (column, cellIndex) {\n              return h(\n                'th',\n                {\n                  attrs: {\n                    colspan: column.colSpan,\n                    rowspan: column.rowSpan\n                  },\n                  on: {\n                    'mousemove': function mousemove($event) {\n                      return _this.handleMouseMove($event, column);\n                    },\n                    'mouseout': _this.handleMouseOut,\n                    'mousedown': function mousedown($event) {\n                      return _this.handleMouseDown($event, column);\n                    },\n                    'click': function click($event) {\n                      return _this.handleHeaderClick($event, column);\n                    },\n                    'contextmenu': function contextmenu($event) {\n                      return _this.handleHeaderContextMenu($event, column);\n                    }\n                  },\n\n                  style: _this.getHeaderCellStyle(rowIndex, cellIndex, columns, column),\n                  'class': _this.getHeaderCellClass(rowIndex, cellIndex, columns, column),\n                  key: column.id },\n                [h(\n                  'div',\n                  { 'class': ['cell', column.filteredValue && column.filteredValue.length > 0 ? 'highlight' : '', column.labelClassName] },\n                  [column.renderHeader ? column.renderHeader.call(_this._renderProxy, h, { column: column, $index: cellIndex, store: _this.store, _self: _this.$parent.$vnode.context }) : column.label, column.sortable ? h(\n                    'span',\n                    {\n                      'class': 'caret-wrapper',\n                      on: {\n                        'click': function click($event) {\n                          return _this.handleSortClick($event, column);\n                        }\n                      }\n                    },\n                    [h('i', { 'class': 'sort-caret ascending',\n                      on: {\n                        'click': function click($event) {\n                          return _this.handleSortClick($event, column, 'ascending');\n                        }\n                      }\n                    }), h('i', { 'class': 'sort-caret descending',\n                      on: {\n                        'click': function click($event) {\n                          return _this.handleSortClick($event, column, 'descending');\n                        }\n                      }\n                    })]\n                  ) : '', column.filterable ? h(\n                    'span',\n                    {\n                      'class': 'el-table__column-filter-trigger',\n                      on: {\n                        'click': function click($event) {\n                          return _this.handleFilterClick($event, column);\n                        }\n                      }\n                    },\n                    [h('i', { 'class': ['el-icon-arrow-down', column.filterOpened ? 'el-icon-arrow-up' : ''] })]\n                  ) : '']\n                )]\n              );\n            }), _this.hasGutter ? h('th', { 'class': 'el-table__cell gutter' }) : '']\n          );\n        })]\n      )]\n    );\n  },\n\n\n  props: {\n    fixed: String,\n    store: {\n      required: true\n    },\n    border: Boolean,\n    defaultSort: {\n      type: Object,\n      default: function _default() {\n        return {\n          prop: '',\n          order: ''\n        };\n      }\n    }\n  },\n\n  components: {\n    ElCheckbox: checkbox_default.a\n  },\n\n  computed: table_header_extends({\n    table: function table() {\n      return this.$parent;\n    },\n    hasGutter: function hasGutter() {\n      return !this.fixed && this.tableLayout.gutterWidth;\n    }\n  }, mapStates({\n    columns: 'columns',\n    isAllSelected: 'isAllSelected',\n    leftFixedLeafCount: 'fixedLeafColumnsLength',\n    rightFixedLeafCount: 'rightFixedLeafColumnsLength',\n    columnsCount: function columnsCount(states) {\n      return states.columns.length;\n    },\n    leftFixedCount: function leftFixedCount(states) {\n      return states.fixedColumns.length;\n    },\n    rightFixedCount: function rightFixedCount(states) {\n      return states.rightFixedColumns.length;\n    }\n  })),\n\n  created: function created() {\n    this.filterPanels = {};\n  },\n  mounted: function mounted() {\n    var _this2 = this;\n\n    // nextTick 是有必要的 https://github.com/ElemeFE/element/pull/11311\n    this.$nextTick(function () {\n      var _defaultSort = _this2.defaultSort,\n          prop = _defaultSort.prop,\n          order = _defaultSort.order;\n\n      var init = true;\n      _this2.store.commit('sort', { prop: prop, order: order, init: init });\n    });\n  },\n  beforeDestroy: function beforeDestroy() {\n    var panels = this.filterPanels;\n    for (var prop in panels) {\n      if (panels.hasOwnProperty(prop) && panels[prop]) {\n        panels[prop].$destroy(true);\n      }\n    }\n  },\n\n\n  methods: {\n    isCellHidden: function isCellHidden(index, columns) {\n      var start = 0;\n      for (var i = 0; i < index; i++) {\n        start += columns[i].colSpan;\n      }\n      var after = start + columns[index].colSpan - 1;\n      if (this.fixed === true || this.fixed === 'left') {\n        return after >= this.leftFixedLeafCount;\n      } else if (this.fixed === 'right') {\n        return start < this.columnsCount - this.rightFixedLeafCount;\n      } else {\n        return after < this.leftFixedLeafCount || start >= this.columnsCount - this.rightFixedLeafCount;\n      }\n    },\n    getHeaderRowStyle: function getHeaderRowStyle(rowIndex) {\n      var headerRowStyle = this.table.headerRowStyle;\n      if (typeof headerRowStyle === 'function') {\n        return headerRowStyle.call(null, { rowIndex: rowIndex });\n      }\n      return headerRowStyle;\n    },\n    getHeaderRowClass: function getHeaderRowClass(rowIndex) {\n      var classes = [];\n\n      var headerRowClassName = this.table.headerRowClassName;\n      if (typeof headerRowClassName === 'string') {\n        classes.push(headerRowClassName);\n      } else if (typeof headerRowClassName === 'function') {\n        classes.push(headerRowClassName.call(null, { rowIndex: rowIndex }));\n      }\n\n      return classes.join(' ');\n    },\n    getHeaderCellStyle: function getHeaderCellStyle(rowIndex, columnIndex, row, column) {\n      var headerCellStyle = this.table.headerCellStyle;\n      if (typeof headerCellStyle === 'function') {\n        return headerCellStyle.call(null, {\n          rowIndex: rowIndex,\n          columnIndex: columnIndex,\n          row: row,\n          column: column\n        });\n      }\n      return headerCellStyle;\n    },\n    getHeaderCellClass: function getHeaderCellClass(rowIndex, columnIndex, row, column) {\n      var classes = [column.id, column.order, column.headerAlign, column.className, column.labelClassName];\n\n      if (rowIndex === 0 && this.isCellHidden(columnIndex, row)) {\n        classes.push('is-hidden');\n      }\n\n      if (!column.children) {\n        classes.push('is-leaf');\n      }\n\n      if (column.sortable) {\n        classes.push('is-sortable');\n      }\n\n      var headerCellClassName = this.table.headerCellClassName;\n      if (typeof headerCellClassName === 'string') {\n        classes.push(headerCellClassName);\n      } else if (typeof headerCellClassName === 'function') {\n        classes.push(headerCellClassName.call(null, {\n          rowIndex: rowIndex,\n          columnIndex: columnIndex,\n          row: row,\n          column: column\n        }));\n      }\n\n      classes.push('el-table__cell');\n\n      return classes.join(' ');\n    },\n    toggleAllSelection: function toggleAllSelection() {\n      this.store.commit('toggleAllSelection');\n    },\n    handleFilterClick: function handleFilterClick(event, column) {\n      event.stopPropagation();\n      var target = event.target;\n      var cell = target.tagName === 'TH' ? target : target.parentNode;\n      if (Object(dom_[\"hasClass\"])(cell, 'noclick')) return;\n      cell = cell.querySelector('.el-table__column-filter-trigger') || cell;\n      var table = this.$parent;\n\n      var filterPanel = this.filterPanels[column.id];\n\n      if (filterPanel && column.filterOpened) {\n        filterPanel.showPopper = false;\n        return;\n      }\n\n      if (!filterPanel) {\n        filterPanel = new external_vue_default.a(filter_panel);\n        this.filterPanels[column.id] = filterPanel;\n        if (column.filterPlacement) {\n          filterPanel.placement = column.filterPlacement;\n        }\n        filterPanel.table = table;\n        filterPanel.cell = cell;\n        filterPanel.column = column;\n        !this.$isServer && filterPanel.$mount(document.createElement('div'));\n      }\n\n      setTimeout(function () {\n        filterPanel.showPopper = true;\n      }, 16);\n    },\n    handleHeaderClick: function handleHeaderClick(event, column) {\n      if (!column.filters && column.sortable) {\n        this.handleSortClick(event, column);\n      } else if (column.filterable && !column.sortable) {\n        this.handleFilterClick(event, column);\n      }\n\n      this.$parent.$emit('header-click', column, event);\n    },\n    handleHeaderContextMenu: function handleHeaderContextMenu(event, column) {\n      this.$parent.$emit('header-contextmenu', column, event);\n    },\n    handleMouseDown: function handleMouseDown(event, column) {\n      var _this3 = this;\n\n      if (this.$isServer) return;\n      if (column.children && column.children.length > 0) return;\n      /* istanbul ignore if */\n      if (this.draggingColumn && this.border) {\n        this.dragging = true;\n\n        this.$parent.resizeProxyVisible = true;\n\n        var table = this.$parent;\n        var tableEl = table.$el;\n        var tableLeft = tableEl.getBoundingClientRect().left;\n        var columnEl = this.$el.querySelector('th.' + column.id);\n        var columnRect = columnEl.getBoundingClientRect();\n        var minLeft = columnRect.left - tableLeft + 30;\n\n        Object(dom_[\"addClass\"])(columnEl, 'noclick');\n\n        this.dragState = {\n          startMouseLeft: event.clientX,\n          startLeft: columnRect.right - tableLeft,\n          startColumnLeft: columnRect.left - tableLeft,\n          tableLeft: tableLeft\n        };\n\n        var resizeProxy = table.$refs.resizeProxy;\n        resizeProxy.style.left = this.dragState.startLeft + 'px';\n\n        document.onselectstart = function () {\n          return false;\n        };\n        document.ondragstart = function () {\n          return false;\n        };\n\n        var handleMouseMove = function handleMouseMove(event) {\n          var deltaLeft = event.clientX - _this3.dragState.startMouseLeft;\n          var proxyLeft = _this3.dragState.startLeft + deltaLeft;\n\n          resizeProxy.style.left = Math.max(minLeft, proxyLeft) + 'px';\n        };\n\n        var handleMouseUp = function handleMouseUp() {\n          if (_this3.dragging) {\n            var _dragState = _this3.dragState,\n                startColumnLeft = _dragState.startColumnLeft,\n                startLeft = _dragState.startLeft;\n\n            var finalLeft = parseInt(resizeProxy.style.left, 10);\n            var columnWidth = finalLeft - startColumnLeft;\n            column.width = column.realWidth = columnWidth;\n            table.$emit('header-dragend', column.width, startLeft - startColumnLeft, column, event);\n\n            _this3.store.scheduleLayout();\n\n            document.body.style.cursor = '';\n            _this3.dragging = false;\n            _this3.draggingColumn = null;\n            _this3.dragState = {};\n\n            table.resizeProxyVisible = false;\n          }\n\n          document.removeEventListener('mousemove', handleMouseMove);\n          document.removeEventListener('mouseup', handleMouseUp);\n          document.onselectstart = null;\n          document.ondragstart = null;\n\n          setTimeout(function () {\n            Object(dom_[\"removeClass\"])(columnEl, 'noclick');\n          }, 0);\n        };\n\n        document.addEventListener('mousemove', handleMouseMove);\n        document.addEventListener('mouseup', handleMouseUp);\n      }\n    },\n    handleMouseMove: function handleMouseMove(event, column) {\n      if (column.children && column.children.length > 0) return;\n      var target = event.target;\n      while (target && target.tagName !== 'TH') {\n        target = target.parentNode;\n      }\n\n      if (!column || !column.resizable) return;\n\n      if (!this.dragging && this.border) {\n        var rect = target.getBoundingClientRect();\n\n        var bodyStyle = document.body.style;\n        if (rect.width > 12 && rect.right - event.pageX < 8) {\n          bodyStyle.cursor = 'col-resize';\n          if (Object(dom_[\"hasClass\"])(target, 'is-sortable')) {\n            target.style.cursor = 'col-resize';\n          }\n          this.draggingColumn = column;\n        } else if (!this.dragging) {\n          bodyStyle.cursor = '';\n          if (Object(dom_[\"hasClass\"])(target, 'is-sortable')) {\n            target.style.cursor = 'pointer';\n          }\n          this.draggingColumn = null;\n        }\n      }\n    },\n    handleMouseOut: function handleMouseOut() {\n      if (this.$isServer) return;\n      document.body.style.cursor = '';\n    },\n    toggleOrder: function toggleOrder(_ref) {\n      var order = _ref.order,\n          sortOrders = _ref.sortOrders;\n\n      if (order === '') return sortOrders[0];\n      var index = sortOrders.indexOf(order || null);\n      return sortOrders[index > sortOrders.length - 2 ? 0 : index + 1];\n    },\n    handleSortClick: function handleSortClick(event, column, givenOrder) {\n      event.stopPropagation();\n      var order = column.order === givenOrder ? null : givenOrder || this.toggleOrder(column);\n\n      var target = event.target;\n      while (target && target.tagName !== 'TH') {\n        target = target.parentNode;\n      }\n\n      if (target && target.tagName === 'TH') {\n        if (Object(dom_[\"hasClass\"])(target, 'noclick')) {\n          Object(dom_[\"removeClass\"])(target, 'noclick');\n          return;\n        }\n      }\n\n      if (!column.sortable) return;\n\n      var states = this.store.states;\n      var sortProp = states.sortProp;\n      var sortOrder = void 0;\n      var sortingColumn = states.sortingColumn;\n\n      if (sortingColumn !== column || sortingColumn === column && sortingColumn.order === null) {\n        if (sortingColumn) {\n          sortingColumn.order = null;\n        }\n        states.sortingColumn = column;\n        sortProp = column.property;\n      }\n\n      if (!order) {\n        sortOrder = column.order = null;\n      } else {\n        sortOrder = column.order = order;\n      }\n\n      states.sortProp = sortProp;\n      states.sortOrder = sortOrder;\n\n      this.store.commit('changeSortCondition');\n    }\n  },\n\n  data: function data() {\n    return {\n      draggingColumn: null,\n      dragging: false,\n      dragState: {}\n    };\n  }\n});\n// CONCATENATED MODULE: ./packages/table/src/table-footer.js\nvar table_footer_extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\n\n\n\n/* harmony default export */ var table_footer = ({\n  name: 'ElTableFooter',\n\n  mixins: [layout_observer],\n\n  render: function render(h) {\n    var _this = this;\n\n    var sums = [];\n    if (this.summaryMethod) {\n      sums = this.summaryMethod({ columns: this.columns, data: this.store.states.data });\n    } else {\n      this.columns.forEach(function (column, index) {\n        if (index === 0) {\n          sums[index] = _this.sumText;\n          return;\n        }\n        var values = _this.store.states.data.map(function (item) {\n          return Number(item[column.property]);\n        });\n        var precisions = [];\n        var notNumber = true;\n        values.forEach(function (value) {\n          if (!isNaN(value)) {\n            notNumber = false;\n            var decimal = ('' + value).split('.')[1];\n            precisions.push(decimal ? decimal.length : 0);\n          }\n        });\n        var precision = Math.max.apply(null, precisions);\n        if (!notNumber) {\n          sums[index] = values.reduce(function (prev, curr) {\n            var value = Number(curr);\n            if (!isNaN(value)) {\n              return parseFloat((prev + curr).toFixed(Math.min(precision, 20)));\n            } else {\n              return prev;\n            }\n          }, 0);\n        } else {\n          sums[index] = '';\n        }\n      });\n    }\n\n    return h(\n      'table',\n      {\n        'class': 'el-table__footer',\n        attrs: { cellspacing: '0',\n          cellpadding: '0',\n          border: '0' }\n      },\n      [h('colgroup', [this.columns.map(function (column) {\n        return h('col', {\n          attrs: { name: column.id },\n          key: column.id });\n      }), this.hasGutter ? h('col', {\n        attrs: { name: 'gutter' }\n      }) : '']), h(\n        'tbody',\n        { 'class': [{ 'has-gutter': this.hasGutter }] },\n        [h('tr', [this.columns.map(function (column, cellIndex) {\n          return h(\n            'td',\n            {\n              key: cellIndex,\n              attrs: { colspan: column.colSpan,\n                rowspan: column.rowSpan\n              },\n              'class': [].concat(_this.getRowClasses(column, cellIndex), ['el-table__cell']) },\n            [h(\n              'div',\n              { 'class': ['cell', column.labelClassName] },\n              [sums[cellIndex]]\n            )]\n          );\n        }), this.hasGutter ? h('th', { 'class': 'el-table__cell gutter' }) : ''])]\n      )]\n    );\n  },\n\n\n  props: {\n    fixed: String,\n    store: {\n      required: true\n    },\n    summaryMethod: Function,\n    sumText: String,\n    border: Boolean,\n    defaultSort: {\n      type: Object,\n      default: function _default() {\n        return {\n          prop: '',\n          order: ''\n        };\n      }\n    }\n  },\n\n  computed: table_footer_extends({\n    table: function table() {\n      return this.$parent;\n    },\n    hasGutter: function hasGutter() {\n      return !this.fixed && this.tableLayout.gutterWidth;\n    }\n  }, mapStates({\n    columns: 'columns',\n    isAllSelected: 'isAllSelected',\n    leftFixedLeafCount: 'fixedLeafColumnsLength',\n    rightFixedLeafCount: 'rightFixedLeafColumnsLength',\n    columnsCount: function columnsCount(states) {\n      return states.columns.length;\n    },\n    leftFixedCount: function leftFixedCount(states) {\n      return states.fixedColumns.length;\n    },\n    rightFixedCount: function rightFixedCount(states) {\n      return states.rightFixedColumns.length;\n    }\n  })),\n\n  methods: {\n    isCellHidden: function isCellHidden(index, columns, column) {\n      if (this.fixed === true || this.fixed === 'left') {\n        return index >= this.leftFixedLeafCount;\n      } else if (this.fixed === 'right') {\n        var before = 0;\n        for (var i = 0; i < index; i++) {\n          before += columns[i].colSpan;\n        }\n        return before < this.columnsCount - this.rightFixedLeafCount;\n      } else if (!this.fixed && column.fixed) {\n        // hide cell when footer instance is not fixed and column is fixed\n        return true;\n      } else {\n        return index < this.leftFixedCount || index >= this.columnsCount - this.rightFixedCount;\n      }\n    },\n    getRowClasses: function getRowClasses(column, cellIndex) {\n      var classes = [column.id, column.align, column.labelClassName];\n      if (column.className) {\n        classes.push(column.className);\n      }\n      if (this.isCellHidden(cellIndex, this.columns, column)) {\n        classes.push('is-hidden');\n      }\n      if (!column.children) {\n        classes.push('is-leaf');\n      }\n      return classes;\n    }\n  }\n});\n// CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/table/src/table.vue?vue&type=script&lang=js&\nvar tablevue_type_script_lang_js_extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar tableIdSeed = 1;\n\n/* harmony default export */ var tablevue_type_script_lang_js_ = ({\n  name: 'ElTable',\n\n  mixins: [locale_default.a, migrating_default.a],\n\n  directives: {\n    Mousewheel: directives_mousewheel\n  },\n\n  props: {\n    data: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n\n    size: String,\n\n    width: [String, Number],\n\n    height: [String, Number],\n\n    maxHeight: [String, Number],\n\n    fit: {\n      type: Boolean,\n      default: true\n    },\n\n    stripe: Boolean,\n\n    border: Boolean,\n\n    rowKey: [String, Function],\n\n    context: {},\n\n    showHeader: {\n      type: Boolean,\n      default: true\n    },\n\n    showSummary: Boolean,\n\n    sumText: String,\n\n    summaryMethod: Function,\n\n    rowClassName: [String, Function],\n\n    rowStyle: [Object, Function],\n\n    cellClassName: [String, Function],\n\n    cellStyle: [Object, Function],\n\n    headerRowClassName: [String, Function],\n\n    headerRowStyle: [Object, Function],\n\n    headerCellClassName: [String, Function],\n\n    headerCellStyle: [Object, Function],\n\n    highlightCurrentRow: Boolean,\n\n    currentRowKey: [String, Number],\n\n    emptyText: String,\n\n    expandRowKeys: Array,\n\n    defaultExpandAll: Boolean,\n\n    defaultSort: Object,\n\n    tooltipEffect: String,\n\n    spanMethod: Function,\n\n    selectOnIndeterminate: {\n      type: Boolean,\n      default: true\n    },\n\n    indent: {\n      type: Number,\n      default: 16\n    },\n\n    treeProps: {\n      type: Object,\n      default: function _default() {\n        return {\n          hasChildren: 'hasChildren',\n          children: 'children'\n        };\n      }\n    },\n\n    lazy: Boolean,\n\n    load: Function\n  },\n\n  components: {\n    TableHeader: table_header,\n    TableFooter: table_footer,\n    TableBody: table_body,\n    ElCheckbox: checkbox_default.a\n  },\n\n  methods: {\n    getMigratingConfig: function getMigratingConfig() {\n      return {\n        events: {\n          expand: 'expand is renamed to expand-change'\n        }\n      };\n    },\n    setCurrentRow: function setCurrentRow(row) {\n      this.store.commit('setCurrentRow', row);\n    },\n    toggleRowSelection: function toggleRowSelection(row, selected) {\n      this.store.toggleRowSelection(row, selected, false);\n      this.store.updateAllSelected();\n    },\n    toggleRowExpansion: function toggleRowExpansion(row, expanded) {\n      this.store.toggleRowExpansionAdapter(row, expanded);\n    },\n    clearSelection: function clearSelection() {\n      this.store.clearSelection();\n    },\n    clearFilter: function clearFilter(columnKeys) {\n      this.store.clearFilter(columnKeys);\n    },\n    clearSort: function clearSort() {\n      this.store.clearSort();\n    },\n    handleMouseLeave: function handleMouseLeave() {\n      this.store.commit('setHoverRow', null);\n      if (this.hoverState) this.hoverState = null;\n    },\n    updateScrollY: function updateScrollY() {\n      var changed = this.layout.updateScrollY();\n      if (changed) {\n        this.layout.notifyObservers('scrollable');\n        this.layout.updateColumnsWidth();\n      }\n    },\n    handleFixedMousewheel: function handleFixedMousewheel(event, data) {\n      var bodyWrapper = this.bodyWrapper;\n      if (Math.abs(data.spinY) > 0) {\n        var currentScrollTop = bodyWrapper.scrollTop;\n        if (data.pixelY < 0 && currentScrollTop !== 0) {\n          event.preventDefault();\n        }\n        if (data.pixelY > 0 && bodyWrapper.scrollHeight - bodyWrapper.clientHeight > currentScrollTop) {\n          event.preventDefault();\n        }\n        bodyWrapper.scrollTop += Math.ceil(data.pixelY / 5);\n      } else {\n        bodyWrapper.scrollLeft += Math.ceil(data.pixelX / 5);\n      }\n    },\n    handleHeaderFooterMousewheel: function handleHeaderFooterMousewheel(event, data) {\n      var pixelX = data.pixelX,\n          pixelY = data.pixelY;\n\n      if (Math.abs(pixelX) >= Math.abs(pixelY)) {\n        this.bodyWrapper.scrollLeft += data.pixelX / 5;\n      }\n    },\n\n\n    // TODO 使用 CSS transform\n    syncPostion: function syncPostion() {\n      var _bodyWrapper = this.bodyWrapper,\n          scrollLeft = _bodyWrapper.scrollLeft,\n          scrollTop = _bodyWrapper.scrollTop,\n          offsetWidth = _bodyWrapper.offsetWidth,\n          scrollWidth = _bodyWrapper.scrollWidth;\n      var _$refs = this.$refs,\n          headerWrapper = _$refs.headerWrapper,\n          footerWrapper = _$refs.footerWrapper,\n          fixedBodyWrapper = _$refs.fixedBodyWrapper,\n          rightFixedBodyWrapper = _$refs.rightFixedBodyWrapper;\n\n      if (headerWrapper) headerWrapper.scrollLeft = scrollLeft;\n      if (footerWrapper) footerWrapper.scrollLeft = scrollLeft;\n      if (fixedBodyWrapper) fixedBodyWrapper.scrollTop = scrollTop;\n      if (rightFixedBodyWrapper) rightFixedBodyWrapper.scrollTop = scrollTop;\n      var maxScrollLeftPosition = scrollWidth - offsetWidth - 1;\n      if (scrollLeft >= maxScrollLeftPosition) {\n        this.scrollPosition = 'right';\n      } else if (scrollLeft === 0) {\n        this.scrollPosition = 'left';\n      } else {\n        this.scrollPosition = 'middle';\n      }\n    },\n\n\n    throttleSyncPostion: Object(external_throttle_debounce_[\"throttle\"])(16, function () {\n      this.syncPostion();\n    }),\n\n    onScroll: function onScroll(evt) {\n      var raf = window.requestAnimationFrame;\n      if (!raf) {\n        this.throttleSyncPostion();\n      } else {\n        raf(this.syncPostion);\n      }\n    },\n    bindEvents: function bindEvents() {\n      this.bodyWrapper.addEventListener('scroll', this.onScroll, { passive: true });\n      if (this.fit) {\n        Object(resize_event_[\"addResizeListener\"])(this.$el, this.resizeListener);\n      }\n    },\n    unbindEvents: function unbindEvents() {\n      this.bodyWrapper.removeEventListener('scroll', this.onScroll, { passive: true });\n      if (this.fit) {\n        Object(resize_event_[\"removeResizeListener\"])(this.$el, this.resizeListener);\n      }\n    },\n    resizeListener: function resizeListener() {\n      if (!this.$ready) return;\n      var shouldUpdateLayout = false;\n      var el = this.$el;\n      var _resizeState = this.resizeState,\n          oldWidth = _resizeState.width,\n          oldHeight = _resizeState.height;\n\n\n      var width = el.offsetWidth;\n      if (oldWidth !== width) {\n        shouldUpdateLayout = true;\n      }\n\n      var height = el.offsetHeight;\n      if ((this.height || this.shouldUpdateHeight) && oldHeight !== height) {\n        shouldUpdateLayout = true;\n      }\n\n      if (shouldUpdateLayout) {\n        this.resizeState.width = width;\n        this.resizeState.height = height;\n        this.doLayout();\n      }\n    },\n    doLayout: function doLayout() {\n      if (this.shouldUpdateHeight) {\n        this.layout.updateElsHeight();\n      }\n      this.layout.updateColumnsWidth();\n    },\n    sort: function sort(prop, order) {\n      this.store.commit('sort', { prop: prop, order: order });\n    },\n    toggleAllSelection: function toggleAllSelection() {\n      this.store.commit('toggleAllSelection');\n    }\n  },\n\n  computed: tablevue_type_script_lang_js_extends({\n    tableSize: function tableSize() {\n      return this.size || (this.$ELEMENT || {}).size;\n    },\n    bodyWrapper: function bodyWrapper() {\n      return this.$refs.bodyWrapper;\n    },\n    shouldUpdateHeight: function shouldUpdateHeight() {\n      return this.height || this.maxHeight || this.fixedColumns.length > 0 || this.rightFixedColumns.length > 0;\n    },\n    bodyWidth: function bodyWidth() {\n      var _layout = this.layout,\n          bodyWidth = _layout.bodyWidth,\n          scrollY = _layout.scrollY,\n          gutterWidth = _layout.gutterWidth;\n\n      return bodyWidth ? bodyWidth - (scrollY ? gutterWidth : 0) + 'px' : '';\n    },\n    bodyHeight: function bodyHeight() {\n      var _layout2 = this.layout,\n          _layout2$headerHeight = _layout2.headerHeight,\n          headerHeight = _layout2$headerHeight === undefined ? 0 : _layout2$headerHeight,\n          bodyHeight = _layout2.bodyHeight,\n          _layout2$footerHeight = _layout2.footerHeight,\n          footerHeight = _layout2$footerHeight === undefined ? 0 : _layout2$footerHeight;\n\n      if (this.height) {\n        return {\n          height: bodyHeight ? bodyHeight + 'px' : ''\n        };\n      } else if (this.maxHeight) {\n        var maxHeight = Object(util[\"j\" /* parseHeight */])(this.maxHeight);\n        if (typeof maxHeight === 'number') {\n          return {\n            'max-height': maxHeight - footerHeight - (this.showHeader ? headerHeight : 0) + 'px'\n          };\n        }\n      }\n      return {};\n    },\n    fixedBodyHeight: function fixedBodyHeight() {\n      if (this.height) {\n        return {\n          height: this.layout.fixedBodyHeight ? this.layout.fixedBodyHeight + 'px' : ''\n        };\n      } else if (this.maxHeight) {\n        var maxHeight = Object(util[\"j\" /* parseHeight */])(this.maxHeight);\n        if (typeof maxHeight === 'number') {\n          maxHeight = this.layout.scrollX ? maxHeight - this.layout.gutterWidth : maxHeight;\n          if (this.showHeader) {\n            maxHeight -= this.layout.headerHeight;\n          }\n          maxHeight -= this.layout.footerHeight;\n          return {\n            'max-height': maxHeight + 'px'\n          };\n        }\n      }\n      return {};\n    },\n    fixedHeight: function fixedHeight() {\n      if (this.maxHeight) {\n        if (this.showSummary) {\n          return {\n            bottom: 0\n          };\n        }\n        return {\n          bottom: this.layout.scrollX && this.data.length ? this.layout.gutterWidth + 'px' : ''\n        };\n      } else {\n        if (this.showSummary) {\n          return {\n            height: this.layout.tableHeight ? this.layout.tableHeight + 'px' : ''\n          };\n        }\n        return {\n          height: this.layout.viewportHeight ? this.layout.viewportHeight + 'px' : ''\n        };\n      }\n    },\n    emptyBlockStyle: function emptyBlockStyle() {\n      if (this.data && this.data.length) return null;\n      var height = '100%';\n      if (this.layout.appendHeight) {\n        height = 'calc(100% - ' + this.layout.appendHeight + 'px)';\n      }\n      return {\n        width: this.bodyWidth,\n        height: height\n      };\n    }\n  }, mapStates({\n    selection: 'selection',\n    columns: 'columns',\n    tableData: 'data',\n    fixedColumns: 'fixedColumns',\n    rightFixedColumns: 'rightFixedColumns'\n  })),\n\n  watch: {\n    height: {\n      immediate: true,\n      handler: function handler(value) {\n        this.layout.setHeight(value);\n      }\n    },\n\n    maxHeight: {\n      immediate: true,\n      handler: function handler(value) {\n        this.layout.setMaxHeight(value);\n      }\n    },\n\n    currentRowKey: {\n      immediate: true,\n      handler: function handler(value) {\n        if (!this.rowKey) return;\n        this.store.setCurrentRowKey(value);\n      }\n    },\n\n    data: {\n      immediate: true,\n      handler: function handler(value) {\n        this.store.commit('setData', value);\n      }\n    },\n\n    expandRowKeys: {\n      immediate: true,\n      handler: function handler(newVal) {\n        if (newVal) {\n          this.store.setExpandRowKeysAdapter(newVal);\n        }\n      }\n    }\n  },\n\n  created: function created() {\n    var _this = this;\n\n    this.tableId = 'el-table_' + tableIdSeed++;\n    this.debouncedUpdateLayout = Object(external_throttle_debounce_[\"debounce\"])(50, function () {\n      return _this.doLayout();\n    });\n  },\n  mounted: function mounted() {\n    var _this2 = this;\n\n    this.bindEvents();\n    this.store.updateColumns();\n    this.doLayout();\n\n    this.resizeState = {\n      width: this.$el.offsetWidth,\n      height: this.$el.offsetHeight\n    };\n\n    // init filters\n    this.store.states.columns.forEach(function (column) {\n      if (column.filteredValue && column.filteredValue.length) {\n        _this2.store.commit('filterChange', {\n          column: column,\n          values: column.filteredValue,\n          silent: true\n        });\n      }\n    });\n\n    this.$ready = true;\n  },\n  destroyed: function destroyed() {\n    this.unbindEvents();\n  },\n  data: function data() {\n    var _treeProps = this.treeProps,\n        _treeProps$hasChildre = _treeProps.hasChildren,\n        hasChildren = _treeProps$hasChildre === undefined ? 'hasChildren' : _treeProps$hasChildre,\n        _treeProps$children = _treeProps.children,\n        children = _treeProps$children === undefined ? 'children' : _treeProps$children;\n\n    this.store = createStore(this, {\n      rowKey: this.rowKey,\n      defaultExpandAll: this.defaultExpandAll,\n      selectOnIndeterminate: this.selectOnIndeterminate,\n      // TreeTable 的相关配置\n      indent: this.indent,\n      lazy: this.lazy,\n      lazyColumnIdentifier: hasChildren,\n      childrenColumnName: children\n    });\n    var layout = new table_layout({\n      store: this.store,\n      table: this,\n      fit: this.fit,\n      showHeader: this.showHeader\n    });\n    return {\n      layout: layout,\n      isHidden: false,\n      renderExpanded: null,\n      resizeProxyVisible: false,\n      resizeState: {\n        width: null,\n        height: null\n      },\n      // 是否拥有多级表头\n      isGroup: false,\n      scrollPosition: 'left'\n    };\n  }\n});\n// CONCATENATED MODULE: ./packages/table/src/table.vue?vue&type=script&lang=js&\n /* harmony default export */ var src_tablevue_type_script_lang_js_ = (tablevue_type_script_lang_js_); \n// CONCATENATED MODULE: ./packages/table/src/table.vue\n\n\n\n\n\n/* normalize component */\n\nvar table_component = Object(componentNormalizer[\"a\" /* default */])(\n  src_tablevue_type_script_lang_js_,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (false) { var table_api; }\ntable_component.options.__file = \"packages/table/src/table.vue\"\n/* harmony default export */ var src_table = (table_component.exports);\n// CONCATENATED MODULE: ./packages/table/index.js\n\n\n/* istanbul ignore next */\nsrc_table.install = function (Vue) {\n  Vue.component(src_table.name, src_table);\n};\n\n/* harmony default export */ var packages_table = __webpack_exports__[\"default\"] = (src_table);\n\n/***/ })\n/******/ ]);"], "mappings": ";;;;AAAAA,MAAM,CAACC,OAAP;AACA;AAAU,UAASC,OAAT,EAAkB;EAAE;;EAC9B;EAAU;;EACV;EAAU,IAAIC,gBAAgB,GAAG,EAAvB;EACV;;EACA;EAAU;;EACV;;EAAU,SAASC,mBAAT,CAA6BC,QAA7B,EAAuC;IACjD;;IACA;IAAW;;IACX;IAAW,IAAGF,gBAAgB,CAACE,QAAD,CAAnB,EAA+B;MAC1C;MAAY,OAAOF,gBAAgB,CAACE,QAAD,CAAhB,CAA2BJ,OAAlC;MACZ;IAAY;IACZ;IAAW;;IACX;;;IAAW,IAAID,MAAM,GAAGG,gBAAgB,CAACE,QAAD,CAAhB,GAA6B;MACrD;MAAYC,CAAC,EAAED,QADsC;;MAErD;MAAYE,CAAC,EAAE,KAFsC;;MAGrD;MAAYN,OAAO,EAAE;MACrB;;IAJqD,CAA1C;IAKX;;IACA;IAAW;;IACX;;IAAWC,OAAO,CAACG,QAAD,CAAP,CAAkBG,IAAlB,CAAuBR,MAAM,CAACC,OAA9B,EAAuCD,MAAvC,EAA+CA,MAAM,CAACC,OAAtD,EAA+DG,mBAA/D;IACX;;IACA;IAAW;;IACX;;IAAWJ,MAAM,CAACO,CAAP,GAAW,IAAX;IACX;;IACA;IAAW;;IACX;;IAAW,OAAOP,MAAM,CAACC,OAAd;IACX;EAAW;EACX;;EACA;;EACA;EAAU;;EACV;;;EAAUG,mBAAmB,CAACK,CAApB,GAAwBP,OAAxB;EACV;;EACA;EAAU;;EACV;;EAAUE,mBAAmB,CAACM,CAApB,GAAwBP,gBAAxB;EACV;;EACA;EAAU;;EACV;;EAAUC,mBAAmB,CAACO,CAApB,GAAwB,UAASV,OAAT,EAAkBW,IAAlB,EAAwBC,MAAxB,EAAgC;IAClE;IAAW,IAAG,CAACT,mBAAmB,CAACU,CAApB,CAAsBb,OAAtB,EAA+BW,IAA/B,CAAJ,EAA0C;MACrD;MAAYG,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+BW,IAA/B,EAAqC;QAAEK,UAAU,EAAE,IAAd;QAAoBC,GAAG,EAAEL;MAAzB,CAArC;MACZ;IAAY;IACZ;;EAAW,CAJD;EAKV;;EACA;EAAU;;EACV;;;EAAUT,mBAAmB,CAACe,CAApB,GAAwB,UAASlB,OAAT,EAAkB;IACpD;IAAW,IAAG,OAAOmB,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACC,WAA3C,EAAwD;MACnE;MAAYN,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+BmB,MAAM,CAACC,WAAtC,EAAmD;QAAEC,KAAK,EAAE;MAAT,CAAnD;MACZ;IAAY;IACZ;;;IAAWP,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+B,YAA/B,EAA6C;MAAEqB,KAAK,EAAE;IAAT,CAA7C;IACX;EAAW,CALD;EAMV;;EACA;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;;;EAAUlB,mBAAmB,CAACmB,CAApB,GAAwB,UAASD,KAAT,EAAgBE,IAAhB,EAAsB;IACxD;IAAW,IAAGA,IAAI,GAAG,CAAV,EAAaF,KAAK,GAAGlB,mBAAmB,CAACkB,KAAD,CAA3B;IACxB;;IAAW,IAAGE,IAAI,GAAG,CAAV,EAAa,OAAOF,KAAP;IACxB;;IAAW,IAAIE,IAAI,GAAG,CAAR,IAAc,OAAOF,KAAP,KAAiB,QAA/B,IAA2CA,KAA3C,IAAoDA,KAAK,CAACG,UAA7D,EAAyE,OAAOH,KAAP;IACpF;;IAAW,IAAII,EAAE,GAAGX,MAAM,CAACY,MAAP,CAAc,IAAd,CAAT;IACX;;IAAWvB,mBAAmB,CAACe,CAApB,CAAsBO,EAAtB;IACX;;;IAAWX,MAAM,CAACC,cAAP,CAAsBU,EAAtB,EAA0B,SAA1B,EAAqC;MAAET,UAAU,EAAE,IAAd;MAAoBK,KAAK,EAAEA;IAA3B,CAArC;IACX;;IAAW,IAAGE,IAAI,GAAG,CAAP,IAAY,OAAOF,KAAP,IAAgB,QAA/B,EAAyC,KAAI,IAAIM,GAAR,IAAeN,KAAf,EAAsBlB,mBAAmB,CAACO,CAApB,CAAsBe,EAAtB,EAA0BE,GAA1B,EAA+B,UAASA,GAAT,EAAc;MAAE,OAAON,KAAK,CAACM,GAAD,CAAZ;IAAoB,CAApC,CAAqCC,IAArC,CAA0C,IAA1C,EAAgDD,GAAhD,CAA/B;IAC1E;;IAAW,OAAOF,EAAP;IACX;EAAW,CATD;EAUV;;EACA;EAAU;;EACV;;;EAAUtB,mBAAmB,CAAC0B,CAApB,GAAwB,UAAS9B,MAAT,EAAiB;IACnD;IAAW,IAAIa,MAAM,GAAGb,MAAM,IAAIA,MAAM,CAACyB,UAAjB;IACxB;IAAY,SAASM,UAAT,GAAsB;MAAE,OAAO/B,MAAM,CAAC,SAAD,CAAb;IAA2B,CADvC;IAExB;IAAY,SAASgC,gBAAT,GAA4B;MAAE,OAAOhC,MAAP;IAAgB,CAF/C;IAGX;;IAAWI,mBAAmB,CAACO,CAApB,CAAsBE,MAAtB,EAA8B,GAA9B,EAAmCA,MAAnC;IACX;;;IAAW,OAAOA,MAAP;IACX;EAAW,CAND;EAOV;;EACA;EAAU;;EACV;;;EAAUT,mBAAmB,CAACU,CAApB,GAAwB,UAASmB,MAAT,EAAiBC,QAAjB,EAA2B;IAAE,OAAOnB,MAAM,CAACoB,SAAP,CAAiBC,cAAjB,CAAgC5B,IAAhC,CAAqCyB,MAArC,EAA6CC,QAA7C,CAAP;EAAgE,CAArH;EACV;;EACA;EAAU;;EACV;;;EAAU9B,mBAAmB,CAACiC,CAApB,GAAwB,QAAxB;EACV;;EACA;;EACA;EAAU;;EACV;;EAAU,OAAOjC,mBAAmB,CAACA,mBAAmB,CAACkC,CAApB,GAAwB,EAAzB,CAA1B;EACV;AAAU;AACV;;AACA;AAtFS,CAsFC;EAsGV;;EAaA;;EAiUA;;EAmBA;;EAaA;;EAaA;;EACA;;EACA;;EACA;;EACA;;EACA;;EACA;;EACA;;EACA;;EAOA;;EACA;;EACA;;EACA;;EACA;;EACA;;EACA;;EACA;;EAaA;;EACA;;EACA;;EAOA;;EACA;;EAOA;;EACA;;EACA;;EACA;;EACA;;EACA;;EACA;;EACA;;EACA;;EACA;;AA7hBA;;AACA;AAAO,UAAStC,MAAT,EAAiBuC,mBAAjB,EAAsCnC,mBAAtC,EAA2D;EAElE;EACA;;EAA+BA,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAOC,kBAAP;EAA4B,CAAzF;EAC/B;EAEA;EACA;EACA;;;EAEA,SAASA,kBAAT,CACEC,aADF,EAEEC,MAFF,EAGEC,eAHF,EAIEC,kBAJF,EAKEC,YALF,EAMEC,OANF,EAOEC,gBAPF;EAOoB;EAClBC;EAAW;EARb,EASE;IACA;IACA,IAAIC,OAAO,GAAG,OAAOR,aAAP,KAAyB,UAAzB,GACVA,aAAa,CAACQ,OADJ,GAEVR,aAFJ,CAFA,CAMA;;IACA,IAAIC,MAAJ,EAAY;MACVO,OAAO,CAACP,MAAR,GAAiBA,MAAjB;MACAO,OAAO,CAACN,eAAR,GAA0BA,eAA1B;MACAM,OAAO,CAACC,SAAR,GAAoB,IAApB;IACD,CAXD,CAaA;;;IACA,IAAIN,kBAAJ,EAAwB;MACtBK,OAAO,CAACE,UAAR,GAAqB,IAArB;IACD,CAhBD,CAkBA;;;IACA,IAAIL,OAAJ,EAAa;MACXG,OAAO,CAACG,QAAR,GAAmB,YAAYN,OAA/B;IACD;;IAED,IAAIO,IAAJ;;IACA,IAAIN,gBAAJ,EAAsB;MAAE;MACtBM,IAAI,GAAG,UAAUC,OAAV,EAAmB;QACxB;QACAA,OAAO,GACLA,OAAO,IAAI;QACV,KAAKC,MAAL,IAAe,KAAKA,MAAL,CAAYC,UAD5B,IAC2C;QAC1C,KAAKC,MAAL,IAAe,KAAKA,MAAL,CAAYF,MAA3B,IAAqC,KAAKE,MAAL,CAAYF,MAAZ,CAAmBC,UAH3D,CAFwB,CAK+C;QACvE;;QACA,IAAI,CAACF,OAAD,IAAY,OAAOI,mBAAP,KAA+B,WAA/C,EAA4D;UAC1DJ,OAAO,GAAGI,mBAAV;QACD,CATuB,CAUxB;;;QACA,IAAIb,YAAJ,EAAkB;UAChBA,YAAY,CAACrC,IAAb,CAAkB,IAAlB,EAAwB8C,OAAxB;QACD,CAbuB,CAcxB;;;QACA,IAAIA,OAAO,IAAIA,OAAO,CAACK,qBAAvB,EAA8C;UAC5CL,OAAO,CAACK,qBAAR,CAA8BC,GAA9B,CAAkCb,gBAAlC;QACD;MACF,CAlBD,CADoB,CAoBpB;MACA;;;MACAE,OAAO,CAACY,YAAR,GAAuBR,IAAvB;IACD,CAvBD,MAuBO,IAAIR,YAAJ,EAAkB;MACvBQ,IAAI,GAAGL,UAAU,GACb,YAAY;QAAEH,YAAY,CAACrC,IAAb,CAAkB,IAAlB,EAAwB,KAAKsD,KAAL,CAAWC,QAAX,CAAoBC,UAA5C;MAAyD,CAD1D,GAEbnB,YAFJ;IAGD;;IAED,IAAIQ,IAAJ,EAAU;MACR,IAAIJ,OAAO,CAACE,UAAZ,EAAwB;QACtB;QACA;QACAF,OAAO,CAACgB,aAAR,GAAwBZ,IAAxB,CAHsB,CAItB;;QACA,IAAIa,cAAc,GAAGjB,OAAO,CAACP,MAA7B;;QACAO,OAAO,CAACP,MAAR,GAAiB,SAASyB,wBAAT,CAAmCC,CAAnC,EAAsCd,OAAtC,EAA+C;UAC9DD,IAAI,CAAC7C,IAAL,CAAU8C,OAAV;UACA,OAAOY,cAAc,CAACE,CAAD,EAAId,OAAJ,CAArB;QACD,CAHD;MAID,CAVD,MAUO;QACL;QACA,IAAIe,QAAQ,GAAGpB,OAAO,CAACqB,YAAvB;QACArB,OAAO,CAACqB,YAAR,GAAuBD,QAAQ,GAC3B,GAAGE,MAAH,CAAUF,QAAV,EAAoBhB,IAApB,CAD2B,GAE3B,CAACA,IAAD,CAFJ;MAGD;IACF;;IAED,OAAO;MACLpD,OAAO,EAAEwC,aADJ;MAELQ,OAAO,EAAEA;IAFJ,CAAP;EAID;EAGD;;AAAO,CArGG;AAuGV;;AACA;AAAO,UAASjD,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,0BAAD,CAAxB;EAEA;AAAO,CA5GG;AA6GV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,2BAAD,CAAxB;EAEA;AAAO,CAlHG;AAoHV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,iCAAD,CAAxB;EAEA;AAAO,CAzHG;AA0HV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,8BAAD,CAAxB;EAEA;AAAO,CA/HG;AAgIV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,KAAD,CAAxB;EAEA;AAAO,CArIG;AAsIV;;AACA;AAAO,UAASxE,MAAT,EAAiBuC,mBAAjB,EAAsCnC,mBAAtC,EAA2D;EAElE;EACA;;EAA+BA,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAOkC,OAAP;EAAiB,CAA9E;EAC/B;;;EAA+BrE,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAOmC,OAAP;EAAiB,CAA9E;EAC/B;;;EAA+BtE,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAOoC,aAAP;EAAuB,CAApF;EAC/B;;;EAA+BvE,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAOqC,cAAP;EAAwB,CAArF;EAC/B;;;EAA+BxE,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAOsC,eAAP;EAAyB,CAAtF;EAC/B;;;EAA+BzE,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAOuC,cAAP;EAAwB,CAArF;EAC/B;;;EAA+B1E,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAOwC,UAAP;EAAoB,CAAjF;EAC/B;;;EAA+B3E,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAOyC,YAAP;EAAsB,CAAnF;EAC/B;;;EAA+B5E,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAO0C,UAAP;EAAoB,CAAjF;EAC/B;;;EAA+B7E,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAO2C,aAAP;EAAuB,CAApF;EAC/B;;;EAA+B9E,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAO4C,WAAP;EAAqB,CAAlF;EAC/B;;;EAA+B/E,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAO6C,OAAP;EAAiB,CAA9E;EAC/B;;;EAA+BhF,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAO8C,eAAP;EAAyB,CAAtF;EAC/B;;;EAA+BjF,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;IAAE,OAAO+C,YAAP;EAAsB,CAAnF;EAC/B;;;EAAqB,IAAIC,sDAAsD,GAAGnF,mBAAmB,CAAC,CAAD,CAAhF;EACrB;;;EAAqB,IAAIoF,8DAA8D,GAAG,aAAapF,mBAAmB,CAAC0B,CAApB,CAAsByD,sDAAtB,CAAlF;;EACrB,IAAIE,OAAO,GAAG,OAAOrE,MAAP,KAAkB,UAAlB,IAAgC,OAAOA,MAAM,CAACsE,QAAd,KAA2B,QAA3D,GAAsE,UAAUC,GAAV,EAAe;IAAE,OAAO,OAAOA,GAAd;EAAoB,CAA3G,GAA8G,UAAUA,GAAV,EAAe;IAAE,OAAOA,GAAG,IAAI,OAAOvE,MAAP,KAAkB,UAAzB,IAAuCuE,GAAG,CAACC,WAAJ,KAAoBxE,MAA3D,IAAqEuE,GAAG,KAAKvE,MAAM,CAACe,SAApF,GAAgG,QAAhG,GAA2G,OAAOwD,GAAzH;EAA+H,CAA5Q;;EAIA,IAAIlB,OAAO,GAAG,SAASA,OAAT,CAAiBoB,KAAjB,EAAwB;IACpC,IAAIC,IAAI,GAAGD,KAAK,CAACE,MAAjB;;IAEA,OAAOD,IAAI,IAAIA,IAAI,CAACE,OAAL,CAAaC,WAAb,OAA+B,MAA9C,EAAsD;MACpD,IAAIH,IAAI,CAACE,OAAL,CAAaC,WAAb,OAA+B,IAAnC,EAAyC;QACvC,OAAOH,IAAP;MACD;;MACDA,IAAI,GAAGA,IAAI,CAACI,UAAZ;IACD;;IAED,OAAO,IAAP;EACD,CAXD;;EAaA,IAAIC,QAAQ,GAAG,SAASA,QAAT,CAAkBR,GAAlB,EAAuB;IACpC,OAAOA,GAAG,KAAK,IAAR,IAAgB,CAAC,OAAOA,GAAP,KAAe,WAAf,GAA6B,WAA7B,GAA2CF,OAAO,CAACE,GAAD,CAAnD,MAA8D,QAArF;EACD,CAFD;;EAIA,IAAIjB,OAAO,GAAG,SAASA,OAAT,CAAiB0B,KAAjB,EAAwBC,OAAxB,EAAiCC,OAAjC,EAA0CC,UAA1C,EAAsDC,MAAtD,EAA8D;IAC1E,IAAI,CAACH,OAAD,IAAY,CAACE,UAAb,KAA4B,CAACC,MAAD,IAAWC,KAAK,CAACC,OAAN,CAAcF,MAAd,KAAyB,CAACA,MAAM,CAACG,MAAxE,CAAJ,EAAqF;MACnF,OAAOP,KAAP;IACD;;IACD,IAAI,OAAOE,OAAP,KAAmB,QAAvB,EAAiC;MAC/BA,OAAO,GAAGA,OAAO,KAAK,YAAZ,GAA2B,CAAC,CAA5B,GAAgC,CAA1C;IACD,CAFD,MAEO;MACLA,OAAO,GAAGA,OAAO,IAAIA,OAAO,GAAG,CAArB,GAAyB,CAAC,CAA1B,GAA8B,CAAxC;IACD;;IACD,IAAIM,MAAM,GAAGL,UAAU,GAAG,IAAH,GAAU,UAAUjF,KAAV,EAAiBuF,KAAjB,EAAwB;MACvD,IAAIL,MAAJ,EAAY;QACV,IAAI,CAACC,KAAK,CAACC,OAAN,CAAcF,MAAd,CAAL,EAA4B;UAC1BA,MAAM,GAAG,CAACA,MAAD,CAAT;QACD;;QACD,OAAOA,MAAM,CAACM,GAAP,CAAW,UAAUC,EAAV,EAAc;UAC9B,IAAI,OAAOA,EAAP,KAAc,QAAlB,EAA4B;YAC1B,OAAOhG,MAAM,CAACwE,sDAAsD,CAAC,gBAAD,CAAvD,CAAN,CAAiFjE,KAAjF,EAAwFyF,EAAxF,CAAP;UACD,CAFD,MAEO;YACL,OAAOA,EAAE,CAACzF,KAAD,EAAQuF,KAAR,EAAeT,KAAf,CAAT;UACD;QACF,CANM,CAAP;MAOD;;MACD,IAAIC,OAAO,KAAK,MAAhB,EAAwB;QACtB,IAAIF,QAAQ,CAAC7E,KAAD,CAAR,IAAmB,YAAYA,KAAnC,EAA0CA,KAAK,GAAGA,KAAK,CAAC0F,MAAd;MAC3C;;MACD,OAAO,CAACb,QAAQ,CAAC7E,KAAD,CAAR,GAAkBP,MAAM,CAACwE,sDAAsD,CAAC,gBAAD,CAAvD,CAAN,CAAiFjE,KAAjF,EAAwF+E,OAAxF,CAAlB,GAAqH/E,KAAtH,CAAP;IACD,CAjBD;;IAkBA,IAAI2F,OAAO,GAAG,SAASA,OAAT,CAAiBC,CAAjB,EAAoBC,CAApB,EAAuB;MACnC,IAAIZ,UAAJ,EAAgB;QACd,OAAOA,UAAU,CAACW,CAAC,CAAC5F,KAAH,EAAU6F,CAAC,CAAC7F,KAAZ,CAAjB;MACD;;MACD,KAAK,IAAIhB,CAAC,GAAG,CAAR,EAAW8G,GAAG,GAAGF,CAAC,CAACtF,GAAF,CAAM+E,MAA5B,EAAoCrG,CAAC,GAAG8G,GAAxC,EAA6C9G,CAAC,EAA9C,EAAkD;QAChD,IAAI4G,CAAC,CAACtF,GAAF,CAAMtB,CAAN,IAAW6G,CAAC,CAACvF,GAAF,CAAMtB,CAAN,CAAf,EAAyB;UACvB,OAAO,CAAC,CAAR;QACD;;QACD,IAAI4G,CAAC,CAACtF,GAAF,CAAMtB,CAAN,IAAW6G,CAAC,CAACvF,GAAF,CAAMtB,CAAN,CAAf,EAAyB;UACvB,OAAO,CAAP;QACD;MACF;;MACD,OAAO,CAAP;IACD,CAbD;;IAcA,OAAO8F,KAAK,CAACU,GAAN,CAAU,UAAUxF,KAAV,EAAiBuF,KAAjB,EAAwB;MACvC,OAAO;QACLvF,KAAK,EAAEA,KADF;QAELuF,KAAK,EAAEA,KAFF;QAGLjF,GAAG,EAAEgF,MAAM,GAAGA,MAAM,CAACtF,KAAD,EAAQuF,KAAR,CAAT,GAA0B;MAHhC,CAAP;IAKD,CANM,EAMJQ,IANI,CAMC,UAAUH,CAAV,EAAaC,CAAb,EAAgB;MACtB,IAAIG,KAAK,GAAGL,OAAO,CAACC,CAAD,EAAIC,CAAJ,CAAnB;;MACA,IAAI,CAACG,KAAL,EAAY;QACV;QACAA,KAAK,GAAGJ,CAAC,CAACL,KAAF,GAAUM,CAAC,CAACN,KAApB;MACD;;MACD,OAAOS,KAAK,GAAGhB,OAAf;IACD,CAbM,EAaJQ,GAbI,CAaA,UAAUS,IAAV,EAAgB;MACrB,OAAOA,IAAI,CAACjG,KAAZ;IACD,CAfM,CAAP;EAgBD,CAzDD;;EA2DA,IAAIqD,aAAa,GAAG,SAASA,aAAT,CAAuB6C,KAAvB,EAA8BC,QAA9B,EAAwC;IAC1D,IAAIC,MAAM,GAAG,IAAb;IACAF,KAAK,CAACG,OAAN,CAAcC,OAAd,CAAsB,UAAUL,IAAV,EAAgB;MACpC,IAAIA,IAAI,CAACM,EAAL,KAAYJ,QAAhB,EAA0B;QACxBC,MAAM,GAAGH,IAAT;MACD;IACF,CAJD;IAKA,OAAOG,MAAP;EACD,CARD;;EAUA,IAAI9C,cAAc,GAAG,SAASA,cAAT,CAAwB4C,KAAxB,EAA+BM,SAA/B,EAA0C;IAC7D,IAAIJ,MAAM,GAAG,IAAb;;IACA,KAAK,IAAIpH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkH,KAAK,CAACG,OAAN,CAAchB,MAAlC,EAA0CrG,CAAC,EAA3C,EAA+C;MAC7C,IAAIiH,IAAI,GAAGC,KAAK,CAACG,OAAN,CAAcrH,CAAd,CAAX;;MACA,IAAIiH,IAAI,CAACO,SAAL,KAAmBA,SAAvB,EAAkC;QAChCJ,MAAM,GAAGH,IAAT;QACA;MACD;IACF;;IACD,OAAOG,MAAP;EACD,CAVD;;EAYA,IAAI7C,eAAe,GAAG,SAASA,eAAT,CAAyB2C,KAAzB,EAAgC1B,IAAhC,EAAsC;IAC1D,IAAIiC,OAAO,GAAG,CAACjC,IAAI,CAACkC,SAAL,IAAkB,EAAnB,EAAuBC,KAAvB,CAA6B,mBAA7B,CAAd;;IACA,IAAIF,OAAJ,EAAa;MACX,OAAOpD,aAAa,CAAC6C,KAAD,EAAQO,OAAO,CAAC,CAAD,CAAf,CAApB;IACD;;IACD,OAAO,IAAP;EACD,CAND;;EAQA,IAAIjD,cAAc,GAAG,SAASA,cAAT,CAAwBoD,GAAxB,EAA6BC,MAA7B,EAAqC;IACxD,IAAI,CAACD,GAAL,EAAU,MAAM,IAAIE,KAAJ,CAAU,uCAAV,CAAN;;IACV,IAAI,OAAOD,MAAP,KAAkB,QAAtB,EAAgC;MAC9B,IAAIA,MAAM,CAACE,OAAP,CAAe,GAAf,IAAsB,CAA1B,EAA6B;QAC3B,OAAOH,GAAG,CAACC,MAAD,CAAV;MACD;;MACD,IAAIvG,GAAG,GAAGuG,MAAM,CAACG,KAAP,CAAa,GAAb,CAAV;MACA,IAAIC,OAAO,GAAGL,GAAd;;MACA,KAAK,IAAI5H,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsB,GAAG,CAAC+E,MAAxB,EAAgCrG,CAAC,EAAjC,EAAqC;QACnCiI,OAAO,GAAGA,OAAO,CAAC3G,GAAG,CAACtB,CAAD,CAAJ,CAAjB;MACD;;MACD,OAAOiI,OAAP;IACD,CAVD,MAUO,IAAI,OAAOJ,MAAP,KAAkB,UAAtB,EAAkC;MACvC,OAAOA,MAAM,CAAC3H,IAAP,CAAY,IAAZ,EAAkB0H,GAAlB,CAAP;IACD;EACF,CAfD;;EAiBA,IAAInD,UAAU,GAAG,SAASA,UAAT,CAAoBqB,KAApB,EAA2B+B,MAA3B,EAAmC;IAClD,IAAIK,QAAQ,GAAG,EAAf;IACA,CAACpC,KAAK,IAAI,EAAV,EAAcwB,OAAd,CAAsB,UAAUM,GAAV,EAAerB,KAAf,EAAsB;MAC1C2B,QAAQ,CAAC1D,cAAc,CAACoD,GAAD,EAAMC,MAAN,CAAf,CAAR,GAAwC;QAAED,GAAG,EAAEA,GAAP;QAAYrB,KAAK,EAAEA;MAAnB,CAAxC;IACD,CAFD;IAGA,OAAO2B,QAAP;EACD,CAND;;EAQA,SAASC,MAAT,CAAgB9C,GAAhB,EAAqB/D,GAArB,EAA0B;IACxB,OAAOb,MAAM,CAACoB,SAAP,CAAiBC,cAAjB,CAAgC5B,IAAhC,CAAqCmF,GAArC,EAA0C/D,GAA1C,CAAP;EACD;;EAED,SAASoD,YAAT,CAAsB0D,QAAtB,EAAgCC,MAAhC,EAAwC;IACtC,IAAI1F,OAAO,GAAG,EAAd;IACA,IAAIrB,GAAG,GAAG,KAAK,CAAf;;IACA,KAAKA,GAAL,IAAY8G,QAAZ,EAAsB;MACpBzF,OAAO,CAACrB,GAAD,CAAP,GAAe8G,QAAQ,CAAC9G,GAAD,CAAvB;IACD;;IACD,KAAKA,GAAL,IAAY+G,MAAZ,EAAoB;MAClB,IAAIF,MAAM,CAACE,MAAD,EAAS/G,GAAT,CAAV,EAAyB;QACvB,IAAIN,KAAK,GAAGqH,MAAM,CAAC/G,GAAD,CAAlB;;QACA,IAAI,OAAON,KAAP,KAAiB,WAArB,EAAkC;UAChC2B,OAAO,CAACrB,GAAD,CAAP,GAAeN,KAAf;QACD;MACF;IACF;;IACD,OAAO2B,OAAP;EACD;;EAED,SAASgC,UAAT,CAAoB2D,KAApB,EAA2B;IACzB,IAAIA,KAAK,KAAKC,SAAd,EAAyB;MACvBD,KAAK,GAAGE,QAAQ,CAACF,KAAD,EAAQ,EAAR,CAAhB;;MACA,IAAIG,KAAK,CAACH,KAAD,CAAT,EAAkB;QAChBA,KAAK,GAAG,IAAR;MACD;IACF;;IACD,OAAOA,KAAP;EACD;;EAED,SAAS1D,aAAT,CAAuB8D,QAAvB,EAAiC;IAC/B,IAAI,OAAOA,QAAP,KAAoB,WAAxB,EAAqC;MACnCA,QAAQ,GAAG/D,UAAU,CAAC+D,QAAD,CAArB;;MACA,IAAID,KAAK,CAACC,QAAD,CAAT,EAAqB;QACnBA,QAAQ,GAAG,EAAX;MACD;IACF;;IACD,OAAOA,QAAP;EACD;;EAAA;;EAED,SAAS7D,WAAT,CAAqB8D,MAArB,EAA6B;IAC3B,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;MAC9B,OAAOA,MAAP;IACD;;IACD,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;MAC9B,IAAI,eAAeC,IAAf,CAAoBD,MAApB,CAAJ,EAAiC;QAC/B,OAAOH,QAAQ,CAACG,MAAD,EAAS,EAAT,CAAf;MACD,CAFD,MAEO;QACL,OAAOA,MAAP;MACD;IACF;;IACD,OAAO,IAAP;EACD,CA/MiE,CAiNlE;;;EACA,SAAS7D,OAAT,GAAmB;IACjB,KAAK,IAAI+D,IAAI,GAAGC,SAAS,CAACzC,MAArB,EAA6B0C,KAAK,GAAG5C,KAAK,CAAC0C,IAAD,CAA1C,EAAkDG,IAAI,GAAG,CAA9D,EAAiEA,IAAI,GAAGH,IAAxE,EAA8EG,IAAI,EAAlF,EAAsF;MACpFD,KAAK,CAACC,IAAD,CAAL,GAAcF,SAAS,CAACE,IAAD,CAAvB;IACD;;IAED,IAAID,KAAK,CAAC1C,MAAN,KAAiB,CAArB,EAAwB;MACtB,OAAO,UAAU4C,GAAV,EAAe;QACpB,OAAOA,GAAP;MACD,CAFD;IAGD;;IACD,IAAIF,KAAK,CAAC1C,MAAN,KAAiB,CAArB,EAAwB;MACtB,OAAO0C,KAAK,CAAC,CAAD,CAAZ;IACD;;IACD,OAAOA,KAAK,CAACG,MAAN,CAAa,UAAUtC,CAAV,EAAaC,CAAb,EAAgB;MAClC,OAAO,YAAY;QACjB,OAAOD,CAAC,CAACC,CAAC,CAACsC,KAAF,CAAQZ,SAAR,EAAmBO,SAAnB,CAAD,CAAR;MACD,CAFD;IAGD,CAJM,CAAP;EAKD;;EAED,SAAS/D,eAAT,CAAyBqE,SAAzB,EAAoCxB,GAApC,EAAyCyB,MAAzC,EAAiD;IAC/C,IAAIC,OAAO,GAAG,KAAd;IACA,IAAI/C,KAAK,GAAG6C,SAAS,CAACrB,OAAV,CAAkBH,GAAlB,CAAZ;IACA,IAAI2B,QAAQ,GAAGhD,KAAK,KAAK,CAAC,CAA1B;;IAEA,IAAIiD,MAAM,GAAG,SAASA,MAAT,GAAkB;MAC7BJ,SAAS,CAACK,IAAV,CAAe7B,GAAf;MACA0B,OAAO,GAAG,IAAV;IACD,CAHD;;IAIA,IAAII,SAAS,GAAG,SAASA,SAAT,GAAqB;MACnCN,SAAS,CAACO,MAAV,CAAiBpD,KAAjB,EAAwB,CAAxB;MACA+C,OAAO,GAAG,IAAV;IACD,CAHD;;IAKA,IAAI,OAAOD,MAAP,KAAkB,SAAtB,EAAiC;MAC/B,IAAIA,MAAM,IAAI,CAACE,QAAf,EAAyB;QACvBC,MAAM;MACP,CAFD,MAEO,IAAI,CAACH,MAAD,IAAWE,QAAf,EAAyB;QAC9BG,SAAS;MACV;IACF,CAND,MAMO;MACL,IAAIH,QAAJ,EAAc;QACZG,SAAS;MACV,CAFD,MAEO;QACLF,MAAM;MACP;IACF;;IACD,OAAOF,OAAP;EACD;;EAED,SAAStE,YAAT,CAAsB4E,IAAtB,EAA4BC,EAA5B,EAAgC;IAC9B,IAAIC,WAAW,GAAGhB,SAAS,CAACzC,MAAV,GAAmB,CAAnB,IAAwByC,SAAS,CAAC,CAAD,CAAT,KAAiBP,SAAzC,GAAqDO,SAAS,CAAC,CAAD,CAA9D,GAAoE,UAAtF;IACA,IAAIiB,OAAO,GAAGjB,SAAS,CAACzC,MAAV,GAAmB,CAAnB,IAAwByC,SAAS,CAAC,CAAD,CAAT,KAAiBP,SAAzC,GAAqDO,SAAS,CAAC,CAAD,CAA9D,GAAoE,aAAlF;;IAEA,IAAIkB,KAAK,GAAG,SAASA,KAAT,CAAelE,KAAf,EAAsB;MAChC,OAAO,EAAEK,KAAK,CAACC,OAAN,CAAcN,KAAd,KAAwBA,KAAK,CAACO,MAAhC,CAAP;IACD,CAFD;;IAIA,SAAS4D,OAAT,CAAiB9G,MAAjB,EAAyB+G,QAAzB,EAAmCC,KAAnC,EAA0C;MACxCN,EAAE,CAAC1G,MAAD,EAAS+G,QAAT,EAAmBC,KAAnB,CAAF;MACAD,QAAQ,CAAC5C,OAAT,CAAiB,UAAUL,IAAV,EAAgB;QAC/B,IAAIA,IAAI,CAAC8C,OAAD,CAAR,EAAmB;UACjBF,EAAE,CAAC5C,IAAD,EAAO,IAAP,EAAakD,KAAK,GAAG,CAArB,CAAF;UACA;QACD;;QACD,IAAID,QAAQ,GAAGjD,IAAI,CAAC6C,WAAD,CAAnB;;QACA,IAAI,CAACE,KAAK,CAACE,QAAD,CAAV,EAAsB;UACpBD,OAAO,CAAChD,IAAD,EAAOiD,QAAP,EAAiBC,KAAK,GAAG,CAAzB,CAAP;QACD;MACF,CATD;IAUD;;IAEDP,IAAI,CAACtC,OAAL,CAAa,UAAUL,IAAV,EAAgB;MAC3B,IAAIA,IAAI,CAAC8C,OAAD,CAAR,EAAmB;QACjBF,EAAE,CAAC5C,IAAD,EAAO,IAAP,EAAa,CAAb,CAAF;QACA;MACD;;MACD,IAAIiD,QAAQ,GAAGjD,IAAI,CAAC6C,WAAD,CAAnB;;MACA,IAAI,CAACE,KAAK,CAACE,QAAD,CAAV,EAAsB;QACpBD,OAAO,CAAChD,IAAD,EAAOiD,QAAP,EAAiB,CAAjB,CAAP;MACD;IACF,CATD;EAUD;EAED;;AAAO,CA7aG;AA8aV;;AACA;AAAO,UAASxK,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,4BAAD,CAAxB;EAEA;AAAO,CAnbG;AAqbV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,iCAAD,CAAxB;EAEA;AAAO,CA1bG;AA2bV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,mCAAD,CAAxB;EAEA;AAAO,CAhcG;AAicV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,4BAAD,CAAxB;EAEA;AAAO,CAtcG;AAwcV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,0BAAD,CAAxB;EAEA;AAAO,CA7cG;AA8cV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,mCAAD,CAAxB;EAEA;AAAO,CAndG;AAqdV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,yBAAD,CAAxB;EAEA;AAAO,CA1dG;AA2dV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,4BAAD,CAAxB;EAEA;AAAO,CAheG;AA0eV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,wBAAD,CAAxB;EAEA;AAAO,CA/eG;AAwfV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,sCAAD,CAAxB;EAEA;AAAO,CA7fG;AA8fV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,+BAAD,CAAxB;EAEA;AAAO,CAngBG;AAugBV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,mBAAD,CAAxB;EAEA;AAAO,CA5gBG;AA+gBV;;AACA;AAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;EAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,iBAAD,CAAxB;EAEA;AAAO,CAphBG;AA+hBV;;AACA;AAAO,UAASxE,MAAT,EAAiBuC,mBAAjB,EAAsCnC,mBAAtC,EAA2D;EAElE;;EACAA,mBAAmB,CAACe,CAApB,CAAsBoB,mBAAtB,EAHkE,CAKlE;;;EACA,IAAIG,MAAM,GAAG,YAAW;IACtB,IAAIgI,GAAG,GAAG,IAAV;;IACA,IAAIC,EAAE,GAAGD,GAAG,CAACE,cAAb;;IACA,IAAIC,EAAE,GAAGH,GAAG,CAACI,KAAJ,CAAUD,EAAV,IAAgBF,EAAzB;;IACA,OAAOE,EAAE,CACP,KADO,EAEP;MACEE,WAAW,EAAE,UADf;MAEEC,KAAK,EAAE,CACL;QACE,iBAAiBN,GAAG,CAACO,GADvB;QAEE,qBAAqBP,GAAG,CAACQ,MAF3B;QAGE,oBAAoBR,GAAG,CAACS,MAAJ,IAAcT,GAAG,CAACU,OAHxC;QAIE,oBAAoBV,GAAG,CAACW,QAJ1B;QAKE,mBAAmBX,GAAG,CAACU,OALzB;QAME,0BAA0BV,GAAG,CAACY,SANhC;QAOE,0BAA0BZ,GAAG,CAACa,MAAJ,CAAWC,OAPvC;QAQE,0BAA0Bd,GAAG,CAACa,MAAJ,CAAWE,OARvC;QASE,8BAA8B,CAACf,GAAG,CAACgB,KAAJ,CAAUC,MAAV,CAAiBC,SATlD;QAUE,mCACE,CAAClB,GAAG,CAACgB,KAAJ,CAAUC,MAAV,CAAiBE,IAAjB,IAAyB,EAA1B,EAA8BlF,MAA9B,KAAyC,CAAzC,IACA,CAAC+D,GAAG,CAACgB,KAAJ,CAAUC,MAAV,CAAiBE,IAAjB,IAAyB,EAA1B,EAA8BlF,MAA9B,GAAuC;MAZ3C,CADK,EAeL+D,GAAG,CAACoB,SAAJ,GAAgB,eAAepB,GAAG,CAACoB,SAAnC,GAA+C,EAf1C,CAFT;MAmBEC,EAAE,EAAE;QACFC,UAAU,EAAE,UAASC,MAAT,EAAiB;UAC3BvB,GAAG,CAACwB,gBAAJ,CAAqBD,MAArB;QACD;MAHC;IAnBN,CAFO,EA2BP,CACEpB,EAAE,CACA,KADA,EAEA;MAAEsB,GAAG,EAAE,eAAP;MAAwBpB,WAAW,EAAE;IAArC,CAFA,EAGA,CAACL,GAAG,CAAC0B,EAAJ,CAAO,SAAP,CAAD,CAHA,EAIA,CAJA,CADJ,EAOE1B,GAAG,CAAC2B,UAAJ,GACIxB,EAAE,CACA,KADA,EAEA;MACEyB,UAAU,EAAE,CACV;QACE1L,IAAI,EAAE,YADR;QAEE2L,OAAO,EAAE,cAFX;QAGEjL,KAAK,EAAEoJ,GAAG,CAAC8B,4BAHb;QAIEC,UAAU,EAAE;MAJd,CADU,CADd;MASEN,GAAG,EAAE,eATP;MAUEpB,WAAW,EAAE;IAVf,CAFA,EAcA,CACEF,EAAE,CAAC,cAAD,EAAiB;MACjBsB,GAAG,EAAE,aADY;MAEjBO,KAAK,EAAE;QACL9D,KAAK,EAAE8B,GAAG,CAACa,MAAJ,CAAWoB,SAAX,GAAuBjC,GAAG,CAACa,MAAJ,CAAWoB,SAAX,GAAuB,IAA9C,GAAqD;MADvD,CAFU;MAKjBC,KAAK,EAAE;QACLlB,KAAK,EAAEhB,GAAG,CAACgB,KADN;QAELP,MAAM,EAAET,GAAG,CAACS,MAFP;QAGL,gBAAgBT,GAAG,CAACmC;MAHf;IALU,CAAjB,CADJ,CAdA,EA2BA,CA3BA,CADN,GA8BInC,GAAG,CAACoC,EAAJ,EArCN,EAsCEjC,EAAE,CACA,KADA,EAEA;MACEsB,GAAG,EAAE,aADP;MAEEpB,WAAW,EAAE,wBAFf;MAGEC,KAAK,EAAE,CACLN,GAAG,CAACa,MAAJ,CAAWC,OAAX,GACI,kBAAkBd,GAAG,CAACqC,cAD1B,GAEI,mBAHC,CAHT;MAQEL,KAAK,EAAE,CAAChC,GAAG,CAACsC,UAAL;IART,CAFA,EAYA,CACEnC,EAAE,CAAC,YAAD,EAAe;MACf6B,KAAK,EAAE;QACL9D,KAAK,EAAE8B,GAAG,CAACiC;MADN,CADQ;MAIfC,KAAK,EAAE;QACLtJ,OAAO,EAAEoH,GAAG,CAACpH,OADR;QAELoI,KAAK,EAAEhB,GAAG,CAACgB,KAFN;QAGLR,MAAM,EAAER,GAAG,CAACQ,MAHP;QAIL,kBAAkBR,GAAG,CAACuC,YAJjB;QAKL,aAAavC,GAAG,CAACwC,QALZ;QAMLC,SAAS,EAAEzC,GAAG,CAAC0C;MANV;IAJQ,CAAf,CADJ,EAcE,CAAC1C,GAAG,CAACmB,IAAL,IAAanB,GAAG,CAACmB,IAAJ,CAASlF,MAAT,KAAoB,CAAjC,GACIkE,EAAE,CACA,KADA,EAEA;MACEsB,GAAG,EAAE,YADP;MAEEpB,WAAW,EAAE,uBAFf;MAGE2B,KAAK,EAAEhC,GAAG,CAAC2C;IAHb,CAFA,EAOA,CACExC,EAAE,CACA,MADA,EAEA;MAAEE,WAAW,EAAE;IAAf,CAFA,EAGA,CACEL,GAAG,CAAC0B,EAAJ,CAAO,OAAP,EAAgB,CACd1B,GAAG,CAAC4C,EAAJ,CACE5C,GAAG,CAAC6C,EAAJ,CAAO7C,GAAG,CAAC8C,SAAJ,IAAiB9C,GAAG,CAACnJ,CAAJ,CAAM,oBAAN,CAAxB,CADF,CADc,CAAhB,CADF,CAHA,EAUA,CAVA,CADJ,CAPA,CADN,GAuBImJ,GAAG,CAACoC,EAAJ,EArCN,EAsCEpC,GAAG,CAAC+C,MAAJ,CAAWC,MAAX,GACI7C,EAAE,CACA,KADA,EAEA;MACEsB,GAAG,EAAE,eADP;MAEEpB,WAAW,EAAE;IAFf,CAFA,EAMA,CAACL,GAAG,CAAC0B,EAAJ,CAAO,QAAP,CAAD,CANA,EAOA,CAPA,CADN,GAUI1B,GAAG,CAACoC,EAAJ,EAhDN,CAZA,EA8DA,CA9DA,CAtCJ,EAsGEpC,GAAG,CAACiD,WAAJ,GACI9C,EAAE,CACA,KADA,EAEA;MACEyB,UAAU,EAAE,CACV;QACE1L,IAAI,EAAE,MADR;QAEE2L,OAAO,EAAE,QAFX;QAGEjL,KAAK,EAAEoJ,GAAG,CAACmB,IAAJ,IAAYnB,GAAG,CAACmB,IAAJ,CAASlF,MAAT,GAAkB,CAHvC;QAIE8F,UAAU,EAAE;MAJd,CADU,EAOV;QACE7L,IAAI,EAAE,YADR;QAEE2L,OAAO,EAAE,cAFX;QAGEjL,KAAK,EAAEoJ,GAAG,CAAC8B,4BAHb;QAIEC,UAAU,EAAE;MAJd,CAPU,CADd;MAeEN,GAAG,EAAE,eAfP;MAgBEpB,WAAW,EAAE;IAhBf,CAFA,EAoBA,CACEF,EAAE,CAAC,cAAD,EAAiB;MACjB6B,KAAK,EAAE;QACL9D,KAAK,EAAE8B,GAAG,CAACa,MAAJ,CAAWoB,SAAX,GAAuBjC,GAAG,CAACa,MAAJ,CAAWoB,SAAX,GAAuB,IAA9C,GAAqD;MADvD,CADU;MAIjBC,KAAK,EAAE;QACLlB,KAAK,EAAEhB,GAAG,CAACgB,KADN;QAELP,MAAM,EAAET,GAAG,CAACS,MAFP;QAGL,YAAYT,GAAG,CAACkD,OAAJ,IAAelD,GAAG,CAACnJ,CAAJ,CAAM,kBAAN,CAHtB;QAIL,kBAAkBmJ,GAAG,CAACmD,aAJjB;QAKL,gBAAgBnD,GAAG,CAACmC;MALf;IAJU,CAAjB,CADJ,CApBA,EAkCA,CAlCA,CADN,GAqCInC,GAAG,CAACoC,EAAJ,EA3IN,EA4IEpC,GAAG,CAACoD,YAAJ,CAAiBnH,MAAjB,GAA0B,CAA1B,GACIkE,EAAE,CACA,KADA,EAEA;MACEyB,UAAU,EAAE,CACV;QACE1L,IAAI,EAAE,YADR;QAEE2L,OAAO,EAAE,cAFX;QAGEjL,KAAK,EAAEoJ,GAAG,CAACqD,qBAHb;QAIEtB,UAAU,EAAE;MAJd,CADU,CADd;MASEN,GAAG,EAAE,cATP;MAUEpB,WAAW,EAAE,iBAVf;MAWE2B,KAAK,EAAE,CACL;QACE9D,KAAK,EAAE8B,GAAG,CAACa,MAAJ,CAAWyC,UAAX,GACHtD,GAAG,CAACa,MAAJ,CAAWyC,UAAX,GAAwB,IADrB,GAEH;MAHN,CADK,EAMLtD,GAAG,CAACuD,WANC;IAXT,CAFA,EAsBA,CACEvD,GAAG,CAAC2B,UAAJ,GACIxB,EAAE,CACA,KADA,EAEA;MACEsB,GAAG,EAAE,oBADP;MAEEpB,WAAW,EAAE;IAFf,CAFA,EAMA,CACEF,EAAE,CAAC,cAAD,EAAiB;MACjBsB,GAAG,EAAE,kBADY;MAEjBO,KAAK,EAAE;QACL9D,KAAK,EAAE8B,GAAG,CAACiC;MADN,CAFU;MAKjBC,KAAK,EAAE;QACLsB,KAAK,EAAE,MADF;QAEL/C,MAAM,EAAET,GAAG,CAACS,MAFP;QAGLO,KAAK,EAAEhB,GAAG,CAACgB;MAHN;IALU,CAAjB,CADJ,CANA,EAmBA,CAnBA,CADN,GAsBIhB,GAAG,CAACoC,EAAJ,EAvBN,EAwBEjC,EAAE,CACA,KADA,EAEA;MACEsB,GAAG,EAAE,kBADP;MAEEpB,WAAW,EAAE,8BAFf;MAGE2B,KAAK,EAAE,CACL;QACEyB,GAAG,EAAEzD,GAAG,CAACa,MAAJ,CAAW6C,YAAX,GAA0B;MADjC,CADK,EAIL1D,GAAG,CAAC2D,eAJC;IAHT,CAFA,EAYA,CACExD,EAAE,CAAC,YAAD,EAAe;MACf6B,KAAK,EAAE;QACL9D,KAAK,EAAE8B,GAAG,CAACiC;MADN,CADQ;MAIfC,KAAK,EAAE;QACLsB,KAAK,EAAE,MADF;QAELxC,KAAK,EAAEhB,GAAG,CAACgB,KAFN;QAGLR,MAAM,EAAER,GAAG,CAACQ,MAHP;QAILiC,SAAS,EAAEzC,GAAG,CAAC0C,mBAJV;QAKL,kBAAkB1C,GAAG,CAACuC,YALjB;QAML,aAAavC,GAAG,CAACwC;MANZ;IAJQ,CAAf,CADJ,EAcExC,GAAG,CAAC+C,MAAJ,CAAWC,MAAX,GACI7C,EAAE,CAAC,KAAD,EAAQ;MACRE,WAAW,EAAE,yBADL;MAER2B,KAAK,EAAE;QAAEzD,MAAM,EAAEyB,GAAG,CAACa,MAAJ,CAAW+C,YAAX,GAA0B;MAApC;IAFC,CAAR,CADN,GAKI5D,GAAG,CAACoC,EAAJ,EAnBN,CAZA,EAiCA,CAjCA,CAxBJ,EA2DEpC,GAAG,CAACiD,WAAJ,GACI9C,EAAE,CACA,KADA,EAEA;MACEyB,UAAU,EAAE,CACV;QACE1L,IAAI,EAAE,MADR;QAEE2L,OAAO,EAAE,QAFX;QAGEjL,KAAK,EAAEoJ,GAAG,CAACmB,IAAJ,IAAYnB,GAAG,CAACmB,IAAJ,CAASlF,MAAT,GAAkB,CAHvC;QAIE8F,UAAU,EAAE;MAJd,CADU,CADd;MASEN,GAAG,EAAE,oBATP;MAUEpB,WAAW,EAAE;IAVf,CAFA,EAcA,CACEF,EAAE,CAAC,cAAD,EAAiB;MACjB6B,KAAK,EAAE;QACL9D,KAAK,EAAE8B,GAAG,CAACiC;MADN,CADU;MAIjBC,KAAK,EAAE;QACLsB,KAAK,EAAE,MADF;QAEL/C,MAAM,EAAET,GAAG,CAACS,MAFP;QAGL,YAAYT,GAAG,CAACkD,OAAJ,IAAelD,GAAG,CAACnJ,CAAJ,CAAM,kBAAN,CAHtB;QAIL,kBAAkBmJ,GAAG,CAACmD,aAJjB;QAKLnC,KAAK,EAAEhB,GAAG,CAACgB;MALN;IAJU,CAAjB,CADJ,CAdA,EA4BA,CA5BA,CADN,GA+BIhB,GAAG,CAACoC,EAAJ,EA1FN,CAtBA,CADN,GAoHIpC,GAAG,CAACoC,EAAJ,EAhQN,EAiQEpC,GAAG,CAAC6D,iBAAJ,CAAsB5H,MAAtB,GAA+B,CAA/B,GACIkE,EAAE,CACA,KADA,EAEA;MACEyB,UAAU,EAAE,CACV;QACE1L,IAAI,EAAE,YADR;QAEE2L,OAAO,EAAE,cAFX;QAGEjL,KAAK,EAAEoJ,GAAG,CAACqD,qBAHb;QAIEtB,UAAU,EAAE;MAJd,CADU,CADd;MASEN,GAAG,EAAE,mBATP;MAUEpB,WAAW,EAAE,uBAVf;MAWE2B,KAAK,EAAE,CACL;QACE9D,KAAK,EAAE8B,GAAG,CAACa,MAAJ,CAAWiD,eAAX,GACH9D,GAAG,CAACa,MAAJ,CAAWiD,eAAX,GAA6B,IAD1B,GAEH,EAHN;QAIEC,KAAK,EAAE/D,GAAG,CAACa,MAAJ,CAAWE,OAAX,GACH,CAACf,GAAG,CAACS,MAAJ,GACGT,GAAG,CAACa,MAAJ,CAAWmD,WADd,GAEGhE,GAAG,CAACa,MAAJ,CAAWmD,WAAX,IAA0B,CAF9B,IAEmC,IAHhC,GAIH;MARN,CADK,EAWLhE,GAAG,CAACuD,WAXC;IAXT,CAFA,EA2BA,CACEvD,GAAG,CAAC2B,UAAJ,GACIxB,EAAE,CACA,KADA,EAEA;MACEsB,GAAG,EAAE,yBADP;MAEEpB,WAAW,EAAE;IAFf,CAFA,EAMA,CACEF,EAAE,CAAC,cAAD,EAAiB;MACjBsB,GAAG,EAAE,uBADY;MAEjBO,KAAK,EAAE;QACL9D,KAAK,EAAE8B,GAAG,CAACiC;MADN,CAFU;MAKjBC,KAAK,EAAE;QACLsB,KAAK,EAAE,OADF;QAEL/C,MAAM,EAAET,GAAG,CAACS,MAFP;QAGLO,KAAK,EAAEhB,GAAG,CAACgB;MAHN;IALU,CAAjB,CADJ,CANA,EAmBA,CAnBA,CADN,GAsBIhB,GAAG,CAACoC,EAAJ,EAvBN,EAwBEjC,EAAE,CACA,KADA,EAEA;MACEsB,GAAG,EAAE,uBADP;MAEEpB,WAAW,EAAE,8BAFf;MAGE2B,KAAK,EAAE,CACL;QACEyB,GAAG,EAAEzD,GAAG,CAACa,MAAJ,CAAW6C,YAAX,GAA0B;MADjC,CADK,EAIL1D,GAAG,CAAC2D,eAJC;IAHT,CAFA,EAYA,CACExD,EAAE,CAAC,YAAD,EAAe;MACf6B,KAAK,EAAE;QACL9D,KAAK,EAAE8B,GAAG,CAACiC;MADN,CADQ;MAIfC,KAAK,EAAE;QACLsB,KAAK,EAAE,OADF;QAELxC,KAAK,EAAEhB,GAAG,CAACgB,KAFN;QAGLR,MAAM,EAAER,GAAG,CAACQ,MAHP;QAIL,kBAAkBR,GAAG,CAACuC,YAJjB;QAKL,aAAavC,GAAG,CAACwC,QALZ;QAMLC,SAAS,EAAEzC,GAAG,CAAC0C;MANV;IAJQ,CAAf,CADJ,EAcE1C,GAAG,CAAC+C,MAAJ,CAAWC,MAAX,GACI7C,EAAE,CAAC,KAAD,EAAQ;MACRE,WAAW,EAAE,yBADL;MAER2B,KAAK,EAAE;QAAEzD,MAAM,EAAEyB,GAAG,CAACa,MAAJ,CAAW+C,YAAX,GAA0B;MAApC;IAFC,CAAR,CADN,GAKI5D,GAAG,CAACoC,EAAJ,EAnBN,CAZA,EAiCA,CAjCA,CAxBJ,EA2DEpC,GAAG,CAACiD,WAAJ,GACI9C,EAAE,CACA,KADA,EAEA;MACEyB,UAAU,EAAE,CACV;QACE1L,IAAI,EAAE,MADR;QAEE2L,OAAO,EAAE,QAFX;QAGEjL,KAAK,EAAEoJ,GAAG,CAACmB,IAAJ,IAAYnB,GAAG,CAACmB,IAAJ,CAASlF,MAAT,GAAkB,CAHvC;QAIE8F,UAAU,EAAE;MAJd,CADU,CADd;MASEN,GAAG,EAAE,yBATP;MAUEpB,WAAW,EAAE;IAVf,CAFA,EAcA,CACEF,EAAE,CAAC,cAAD,EAAiB;MACjB6B,KAAK,EAAE;QACL9D,KAAK,EAAE8B,GAAG,CAACiC;MADN,CADU;MAIjBC,KAAK,EAAE;QACLsB,KAAK,EAAE,OADF;QAEL/C,MAAM,EAAET,GAAG,CAACS,MAFP;QAGL,YAAYT,GAAG,CAACkD,OAAJ,IAAelD,GAAG,CAACnJ,CAAJ,CAAM,kBAAN,CAHtB;QAIL,kBAAkBmJ,GAAG,CAACmD,aAJjB;QAKLnC,KAAK,EAAEhB,GAAG,CAACgB;MALN;IAJU,CAAjB,CADJ,CAdA,EA4BA,CA5BA,CADN,GA+BIhB,GAAG,CAACoC,EAAJ,EA1FN,CA3BA,CADN,GAyHIpC,GAAG,CAACoC,EAAJ,EA1XN,EA2XEpC,GAAG,CAAC6D,iBAAJ,CAAsB5H,MAAtB,GAA+B,CAA/B,GACIkE,EAAE,CAAC,KAAD,EAAQ;MACRsB,GAAG,EAAE,iBADG;MAERpB,WAAW,EAAE,6BAFL;MAGR2B,KAAK,EAAE;QACL9D,KAAK,EAAE8B,GAAG,CAACa,MAAJ,CAAWE,OAAX,GAAqBf,GAAG,CAACa,MAAJ,CAAWmD,WAAX,GAAyB,IAA9C,GAAqD,GADvD;QAELzF,MAAM,EAAEyB,GAAG,CAACa,MAAJ,CAAW6C,YAAX,GAA0B;MAF7B;IAHC,CAAR,CADN,GASI1D,GAAG,CAACoC,EAAJ,EApYN,EAqYEjC,EAAE,CAAC,KAAD,EAAQ;MACRyB,UAAU,EAAE,CACV;QACE1L,IAAI,EAAE,MADR;QAEE2L,OAAO,EAAE,QAFX;QAGEjL,KAAK,EAAEoJ,GAAG,CAACiE,kBAHb;QAIElC,UAAU,EAAE;MAJd,CADU,CADJ;MASRN,GAAG,EAAE,aATG;MAURpB,WAAW,EAAE;IAVL,CAAR,CArYJ,CA3BO,CAAT;EA8aD,CAlbD;;EAmbA,IAAIpI,eAAe,GAAG,EAAtB;EACAD,MAAM,CAACkM,aAAP,GAAuB,IAAvB,CA1bkE,CA6blE;EAEA;;EACA,IAAIC,SAAS,GAAGzO,mBAAmB,CAAC,EAAD,CAAnC;;EACA,IAAI0O,gBAAgB,GAAG,aAAa1O,mBAAmB,CAAC0B,CAApB,CAAsB+M,SAAtB,CAApC,CAjckE,CAmclE;;;EACA,IAAIE,2BAA2B,GAAG3O,mBAAmB,CAAC,EAAD,CAArD,CApckE,CAsclE;;;EACA,IAAI4O,aAAa,GAAG5O,mBAAmB,CAAC,EAAD,CAAvC,CAvckE,CAyclE;;;EACA,IAAI6O,yBAAyB,GAAG7O,mBAAmB,CAAC,EAAD,CAAnD;;EACA,IAAI8O,gCAAgC,GAAG,aAAa9O,mBAAmB,CAAC0B,CAApB,CAAsBmN,yBAAtB,CAApD,CA3ckE,CA6clE;;;EAGA,IAAIE,SAAS,GAAG,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAAS,CAACC,SAAV,CAAoBC,WAApB,GAAkCjH,OAAlC,CAA0C,SAA1C,IAAuD,CAAC,CAA5G;;EAEA,IAAIkH,qBAAqB,GAAG,SAASC,UAAT,CAAoBC,OAApB,EAA6BC,QAA7B,EAAuC;IACjE,IAAID,OAAO,IAAIA,OAAO,CAACE,gBAAvB,EAAyC;MACvCF,OAAO,CAACE,gBAAR,CAAyBR,SAAS,GAAG,gBAAH,GAAsB,YAAxD,EAAsE,UAAUtJ,KAAV,EAAiB;QACrF,IAAI+J,UAAU,GAAGV,gCAAgC,GAAGrJ,KAAH,CAAjD;QACA6J,QAAQ,IAAIA,QAAQ,CAACjG,KAAT,CAAe,IAAf,EAAqB,CAAC5D,KAAD,EAAQ+J,UAAR,CAArB,CAAZ;MACD,CAHD;IAID;EACF,CAPD;EASA;;;EAA6B,IAAIC,qBAAqB,GAAI;IACxDhO,IAAI,EAAE,SAASA,IAAT,CAAciO,EAAd,EAAkBC,OAAlB,EAA2B;MAC/BR,qBAAqB,CAACO,EAAD,EAAKC,OAAO,CAACzO,KAAb,CAArB;IACD;EAHuD,CAA7B,CA3dqC,CAgelE;;EACA,IAAI0O,OAAO,GAAG5P,mBAAmB,CAAC,CAAD,CAAjC;;EACA,IAAI6P,cAAc,GAAG,aAAa7P,mBAAmB,CAAC0B,CAApB,CAAsBkO,OAAtB,CAAlC,CAlekE,CAoelE;;;EACA,IAAIE,UAAU,GAAG9P,mBAAmB,CAAC,EAAD,CAApC;;EACA,IAAI+P,iBAAiB,GAAG,aAAa/P,mBAAmB,CAAC0B,CAApB,CAAsBoO,UAAtB,CAArC,CAtekE,CAwelE;;;EACA,IAAIE,aAAa,GAAGhQ,mBAAmB,CAAC,CAAD,CAAvC;;EACA,IAAIiQ,oBAAoB,GAAG,aAAajQ,mBAAmB,CAAC0B,CAApB,CAAsBsO,aAAtB,CAAxC,CA1ekE,CA4elE;;;EACA,IAAIE,MAAM,GAAGlQ,mBAAmB,CAAC,CAAD,CAAhC;;EACA,IAAImQ,aAAa,GAAG,aAAanQ,mBAAmB,CAAC0B,CAApB,CAAsBwO,MAAtB,CAAjC,CA9ekE,CAgflE;;;EACA,IAAIE,IAAI,GAAGpQ,mBAAmB,CAAC,CAAD,CAA9B,CAjfkE,CAmflE;;EAGA;;;EAA6B,IAAIqQ,MAAM,GAAI;IACzC5E,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,OAAO;QACLF,MAAM,EAAE;UACN+E,gBAAgB,EAAE,KADZ;UAENC,UAAU,EAAE;QAFN;MADH,CAAP;IAMD,CARwC;IAWzCC,OAAO,EAAE;MACPC,gBAAgB,EAAE,SAASA,gBAAT,GAA4B;QAC5C,IAAIC,OAAO,GAAG,KAAKnF,MAAnB;QAAA,IACIoF,YAAY,GAAGD,OAAO,CAACjF,IAD3B;QAAA,IAEIA,IAAI,GAAGkF,YAAY,KAAKlI,SAAjB,GAA6B,EAA7B,GAAkCkI,YAF7C;QAAA,IAGI5I,MAAM,GAAG2I,OAAO,CAAC3I,MAHrB;QAAA,IAIIuI,gBAAgB,GAAGI,OAAO,CAACJ,gBAJ/B;QAAA,IAKIC,UAAU,GAAGG,OAAO,CAACH,UALzB;;QAOA,IAAID,gBAAJ,EAAsB;UACpB,KAAK/E,MAAL,CAAYgF,UAAZ,GAAyB9E,IAAI,CAACmF,KAAL,EAAzB;QACD,CAFD,MAEO,IAAI7I,MAAJ,EAAY;UACjB;UACA,IAAI8I,aAAa,GAAGlQ,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAmCG,UAAnC,EAA+CxI,MAA/C,CAApB;UACA,KAAKwD,MAAL,CAAYgF,UAAZ,GAAyB9E,IAAI,CAACrC,MAAL,CAAY,UAAU0H,IAAV,EAAgBhJ,GAAhB,EAAqB;YACxD,IAAIiJ,KAAK,GAAGpQ,MAAM,CAACyP,IAAI,CAAC;YAAI;YAAL,CAAL,CAAN,CAAuCtI,GAAvC,EAA4CC,MAA5C,CAAZ;YACA,IAAIiJ,OAAO,GAAGH,aAAa,CAACE,KAAD,CAA3B;;YACA,IAAIC,OAAJ,EAAa;cACXF,IAAI,CAACnH,IAAL,CAAU7B,GAAV;YACD;;YACD,OAAOgJ,IAAP;UACD,CAPwB,EAOtB,EAPsB,CAAzB;QAQD,CAXM,MAWA;UACL,KAAKvF,MAAL,CAAYgF,UAAZ,GAAyB,EAAzB;QACD;MACF,CAzBM;MA0BPU,kBAAkB,EAAE,SAASA,kBAAT,CAA4BnJ,GAA5B,EAAiCoJ,QAAjC,EAA2C;QAC7D,IAAI1H,OAAO,GAAG7I,MAAM,CAACyP,IAAI,CAAC;QAAI;QAAL,CAAL,CAAN,CAAwC,KAAK7E,MAAL,CAAYgF,UAApD,EAAgEzI,GAAhE,EAAqEoJ,QAArE,CAAd;;QACA,IAAI1H,OAAJ,EAAa;UACX,KAAKpC,KAAL,CAAW+J,KAAX,CAAiB,eAAjB,EAAkCrJ,GAAlC,EAAuC,KAAKyD,MAAL,CAAYgF,UAAZ,CAAuBK,KAAvB,EAAvC;UACA,KAAKQ,cAAL;QACD;MACF,CAhCM;MAiCPC,gBAAgB,EAAE,SAASA,gBAAT,CAA0BC,OAA1B,EAAmC;QACnD,KAAKC,YAAL,GADmD,CAEnD;;QACA,IAAIC,QAAQ,GAAG,KAAKjG,MAApB;QAAA,IACIE,IAAI,GAAG+F,QAAQ,CAAC/F,IADpB;QAAA,IAEI1D,MAAM,GAAGyJ,QAAQ,CAACzJ,MAFtB;QAIA,IAAI0J,OAAO,GAAG9Q,MAAM,CAACyP,IAAI,CAAC;QAAI;QAAL,CAAL,CAAN,CAAmC3E,IAAnC,EAAyC1D,MAAzC,CAAd;QACA,KAAKwD,MAAL,CAAYgF,UAAZ,GAAyBe,OAAO,CAAClI,MAAR,CAAe,UAAU0H,IAAV,EAAgBY,GAAhB,EAAqB;UAC3D,IAAIC,IAAI,GAAGF,OAAO,CAACC,GAAD,CAAlB;;UACA,IAAIC,IAAJ,EAAU;YACRb,IAAI,CAACnH,IAAL,CAAUgI,IAAI,CAAC7J,GAAf;UACD;;UACD,OAAOgJ,IAAP;QACD,CANwB,EAMtB,EANsB,CAAzB;MAOD,CAhDM;MAiDPc,aAAa,EAAE,SAASA,aAAT,CAAuB9J,GAAvB,EAA4B;QACzC,IAAI+J,QAAQ,GAAG,KAAKtG,MAApB;QAAA,IACIuG,mBAAmB,GAAGD,QAAQ,CAACtB,UADnC;QAAA,IAEIA,UAAU,GAAGuB,mBAAmB,KAAKrJ,SAAxB,GAAoC,EAApC,GAAyCqJ,mBAF1D;QAAA,IAGI/J,MAAM,GAAG8J,QAAQ,CAAC9J,MAHtB;;QAKA,IAAIA,MAAJ,EAAY;UACV,IAAIgK,SAAS,GAAGpR,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAmCG,UAAnC,EAA+CxI,MAA/C,CAAhB;UACA,OAAO,CAAC,CAACgK,SAAS,CAACpR,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAuCtI,GAAvC,EAA4CC,MAA5C,CAAD,CAAlB;QACD;;QACD,OAAOwI,UAAU,CAACtI,OAAX,CAAmBH,GAAnB,MAA4B,CAAC,CAApC;MACD;IA5DM;EAXgC,CAAd,CAtfqC,CAgkBlE;;EACA,IAAIkK,KAAK,GAAGhS,mBAAmB,CAAC,CAAD,CAA/B,CAjkBkE,CAmkBlE;;EAIA;;;EAA6B,IAAImI,OAAO,GAAI;IAC1CsD,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,OAAO;QACLF,MAAM,EAAE;UACN;UACA;UACA0G,cAAc,EAAE,IAHV;UAINC,UAAU,EAAE;QAJN;MADH,CAAP;IAQD,CAVyC;IAa1C1B,OAAO,EAAE;MACP2B,gBAAgB,EAAE,SAASA,gBAAT,CAA0B3Q,GAA1B,EAA+B;QAC/C,KAAK+P,YAAL;QACA,KAAKhG,MAAL,CAAY0G,cAAZ,GAA6BzQ,GAA7B;QACA,KAAK4Q,kBAAL,CAAwB5Q,GAAxB;MACD,CALM;MAMP6Q,oBAAoB,EAAE,SAASA,oBAAT,GAAgC;QACpD,KAAK9G,MAAL,CAAY0G,cAAZ,GAA6B,IAA7B;MACD,CARM;MASPG,kBAAkB,EAAE,SAASA,kBAAT,CAA4B5Q,GAA5B,EAAiC;QACnD,IAAI+J,MAAM,GAAG,KAAKA,MAAlB;QACA,IAAIoF,YAAY,GAAGpF,MAAM,CAACE,IAA1B;QAAA,IACIA,IAAI,GAAGkF,YAAY,KAAKlI,SAAjB,GAA6B,EAA7B,GAAkCkI,YAD7C;QAAA,IAEI5I,MAAM,GAAGwD,MAAM,CAACxD,MAFpB;QAIA,IAAImK,UAAU,GAAG,IAAjB;;QACA,IAAInK,MAAJ,EAAY;UACVmK,UAAU,GAAGvR,MAAM,CAACqR,KAAK,CAAC,WAAD,CAAN,CAAN,CAA2BvG,IAA3B,EAAiC,UAAUtE,IAAV,EAAgB;YAC5D,OAAOxG,MAAM,CAACyP,IAAI,CAAC;YAAI;YAAL,CAAL,CAAN,CAAuCjJ,IAAvC,EAA6CY,MAA7C,MAAyDvG,GAAhE;UACD,CAFY,CAAb;QAGD;;QACD+J,MAAM,CAAC2G,UAAP,GAAoBA,UAApB;MACD,CAtBM;MAuBPI,gBAAgB,EAAE,SAASA,gBAAT,CAA0BJ,UAA1B,EAAsC;QACtD,IAAI3G,MAAM,GAAG,KAAKA,MAAlB;QAAA,IACInE,KAAK,GAAG,KAAKA,KADjB;QAGA,IAAImL,aAAa,GAAGhH,MAAM,CAAC2G,UAA3B;;QACA,IAAIA,UAAU,IAAIA,UAAU,KAAKK,aAAjC,EAAgD;UAC9ChH,MAAM,CAAC2G,UAAP,GAAoBA,UAApB;UACA9K,KAAK,CAAC+J,KAAN,CAAY,gBAAZ,EAA8Be,UAA9B,EAA0CK,aAA1C;UACA;QACD;;QACD,IAAI,CAACL,UAAD,IAAeK,aAAnB,EAAkC;UAChChH,MAAM,CAAC2G,UAAP,GAAoB,IAApB;UACA9K,KAAK,CAAC+J,KAAN,CAAY,gBAAZ,EAA8B,IAA9B,EAAoCoB,aAApC;QACD;MACF,CArCM;MAsCPC,oBAAoB,EAAE,SAASA,oBAAT,GAAgC;QACpD,IAAIjH,MAAM,GAAG,KAAKA,MAAlB;QAAA,IACInE,KAAK,GAAG,KAAKA,KADjB;QAEA,IAAIW,MAAM,GAAGwD,MAAM,CAACxD,MAApB;QAAA,IACIkK,cAAc,GAAG1G,MAAM,CAAC0G,cAD5B,CAHoD,CAKpD;;QAEA,IAAIxG,IAAI,GAAGF,MAAM,CAACE,IAAP,IAAe,EAA1B;QACA,IAAI8G,aAAa,GAAGhH,MAAM,CAAC2G,UAA3B,CARoD,CAUpD;;QACA,IAAIzG,IAAI,CAACxD,OAAL,CAAasK,aAAb,MAAgC,CAAC,CAAjC,IAAsCA,aAA1C,EAAyD;UACvD,IAAIxK,MAAJ,EAAY;YACV,IAAI0K,aAAa,GAAG9R,MAAM,CAACyP,IAAI,CAAC;YAAI;YAAL,CAAL,CAAN,CAAuCmC,aAAvC,EAAsDxK,MAAtD,CAApB;YACA,KAAKqK,kBAAL,CAAwBK,aAAxB;UACD,CAHD,MAGO;YACLlH,MAAM,CAAC2G,UAAP,GAAoB,IAApB;UACD;;UACD,IAAI3G,MAAM,CAAC2G,UAAP,KAAsB,IAA1B,EAAgC;YAC9B9K,KAAK,CAAC+J,KAAN,CAAY,gBAAZ,EAA8B,IAA9B,EAAoCoB,aAApC;UACD;QACF,CAVD,MAUO,IAAIN,cAAJ,EAAoB;UACzB;UACA,KAAKG,kBAAL,CAAwBH,cAAxB;UACA,KAAKI,oBAAL;QACD;MACF;IAhEM;EAbiC,CAAf,CAvkBqC,CAupBlE;;EACA,IAAIK,QAAQ,GAAG/R,MAAM,CAACgS,MAAP,IAAiB,UAAUhN,MAAV,EAAkB;IAAE,KAAK,IAAIzF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8I,SAAS,CAACzC,MAA9B,EAAsCrG,CAAC,EAAvC,EAA2C;MAAE,IAAI0S,MAAM,GAAG5J,SAAS,CAAC9I,CAAD,CAAtB;;MAA2B,KAAK,IAAIsB,GAAT,IAAgBoR,MAAhB,EAAwB;QAAE,IAAIjS,MAAM,CAACoB,SAAP,CAAiBC,cAAjB,CAAgC5B,IAAhC,CAAqCwS,MAArC,EAA6CpR,GAA7C,CAAJ,EAAuD;UAAEmE,MAAM,CAACnE,GAAD,CAAN,GAAcoR,MAAM,CAACpR,GAAD,CAApB;QAA4B;MAAE;IAAE;;IAAC,OAAOmE,MAAP;EAAgB,CAAhQ;EAIA;;;EAA6B,IAAIkN,IAAI,GAAI;IACvCpH,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,OAAO;QACLF,MAAM,EAAE;UACN;UACA;UACA;UACAuH,aAAa,EAAE,EAJT;UAKNC,QAAQ,EAAE,EALJ;UAMNC,MAAM,EAAE,EANF;UAONC,IAAI,EAAE,KAPA;UAQNC,eAAe,EAAE,EARX;UASNC,oBAAoB,EAAE,aAThB;UAUNC,kBAAkB,EAAE;QAVd;MADH,CAAP;IAcD,CAhBsC;IAmBvCC,QAAQ,EAAE;MACR;MACA;MACA;MACAC,cAAc,EAAE,SAASA,cAAT,GAA0B;QACxC,IAAI,CAAC,KAAK/H,MAAL,CAAYxD,MAAjB,EAAyB,OAAO,EAAP;QACzB,IAAI0D,IAAI,GAAG,KAAKF,MAAL,CAAYE,IAAZ,IAAoB,EAA/B;QACA,OAAO,KAAK8H,SAAL,CAAe9H,IAAf,CAAP;MACD,CARO;MAUR;MACA;MACA+H,kBAAkB,EAAE,SAASA,kBAAT,GAA8B;QAChD,IAAI9C,OAAO,GAAG,KAAKnF,MAAnB;QAAA,IACIxD,MAAM,GAAG2I,OAAO,CAAC3I,MADrB;QAAA,IAEImL,eAAe,GAAGxC,OAAO,CAACwC,eAF9B;QAAA,IAGIC,oBAAoB,GAAGzC,OAAO,CAACyC,oBAHnC;QAKA,IAAIM,IAAI,GAAG9S,MAAM,CAAC8S,IAAP,CAAYP,eAAZ,CAAX;QACA,IAAIQ,GAAG,GAAG,EAAV;QACA,IAAI,CAACD,IAAI,CAAClN,MAAV,EAAkB,OAAOmN,GAAP;QAClBD,IAAI,CAACjM,OAAL,CAAa,UAAUhG,GAAV,EAAe;UAC1B,IAAI0R,eAAe,CAAC1R,GAAD,CAAf,CAAqB+E,MAAzB,EAAiC;YAC/B,IAAIY,IAAI,GAAG;cAAEiD,QAAQ,EAAE;YAAZ,CAAX;YACA8I,eAAe,CAAC1R,GAAD,CAAf,CAAqBgG,OAArB,CAA6B,UAAUM,GAAV,EAAe;cAC1C,IAAI2K,aAAa,GAAG9R,MAAM,CAACyP,IAAI,CAAC;cAAI;cAAL,CAAL,CAAN,CAAuCtI,GAAvC,EAA4CC,MAA5C,CAApB;cACAZ,IAAI,CAACiD,QAAL,CAAcT,IAAd,CAAmB8I,aAAnB;;cACA,IAAI3K,GAAG,CAACqL,oBAAD,CAAH,IAA6B,CAACO,GAAG,CAACjB,aAAD,CAArC,EAAsD;gBACpDiB,GAAG,CAACjB,aAAD,CAAH,GAAqB;kBAAErI,QAAQ,EAAE;gBAAZ,CAArB;cACD;YACF,CAND;YAOAsJ,GAAG,CAAClS,GAAD,CAAH,GAAW2F,IAAX;UACD;QACF,CAZD;QAaA,OAAOuM,GAAP;MACD;IAnCO,CAnB6B;IAyDvCC,KAAK,EAAE;MACLL,cAAc,EAAE,gBADX;MAELE,kBAAkB,EAAE;IAFf,CAzDgC;IA8DvChD,OAAO,EAAE;MACP+C,SAAS,EAAE,SAASA,SAAT,CAAmB9H,IAAnB,EAAyB;QAClC,IAAI+F,QAAQ,GAAG,KAAKjG,MAApB;QAAA,IACI6H,kBAAkB,GAAG5B,QAAQ,CAAC4B,kBADlC;QAAA,IAEID,oBAAoB,GAAG3B,QAAQ,CAAC2B,oBAFpC;QAAA,IAGIpL,MAAM,GAAGyJ,QAAQ,CAACzJ,MAHtB;QAAA,IAIIkL,IAAI,GAAGzB,QAAQ,CAACyB,IAJpB;QAMA,IAAIS,GAAG,GAAG,EAAV;QACA/S,MAAM,CAACyP,IAAI,CAAC;QAAI;QAAL,CAAL,CAAN,CAAqC3E,IAArC,EAA2C,UAAUpI,MAAV,EAAkB+G,QAAlB,EAA4BC,KAA5B,EAAmC;UAC5E,IAAIuJ,QAAQ,GAAGjT,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAuC/M,MAAvC,EAA+C0E,MAA/C,CAAf;;UACA,IAAI1B,KAAK,CAACC,OAAN,CAAc8D,QAAd,CAAJ,EAA6B;YAC3BsJ,GAAG,CAACE,QAAD,CAAH,GAAgB;cACdxJ,QAAQ,EAAEA,QAAQ,CAAC1D,GAAT,CAAa,UAAUoB,GAAV,EAAe;gBACpC,OAAOnH,MAAM,CAACyP,IAAI,CAAC;gBAAI;gBAAL,CAAL,CAAN,CAAuCtI,GAAvC,EAA4CC,MAA5C,CAAP;cACD,CAFS,CADI;cAIdsC,KAAK,EAAEA;YAJO,CAAhB;UAMD,CAPD,MAOO,IAAI4I,IAAJ,EAAU;YACf;YACAS,GAAG,CAACE,QAAD,CAAH,GAAgB;cACdxJ,QAAQ,EAAE,EADI;cAEd6I,IAAI,EAAE,IAFQ;cAGd5I,KAAK,EAAEA;YAHO,CAAhB;UAKD;QACF,CAjBD,EAiBG+I,kBAjBH,EAiBuBD,oBAjBvB;QAkBA,OAAOO,GAAP;MACD,CA5BM;MA6BPG,cAAc,EAAE,SAASA,cAAT,GAA0B;QACxC,IAAIC,MAAM,GAAG,KAAKR,cAAlB;QACA,IAAIE,kBAAkB,GAAG,KAAKA,kBAA9B;QACA,IAAIC,IAAI,GAAG9S,MAAM,CAAC8S,IAAP,CAAYK,MAAZ,CAAX;QACA,IAAIC,WAAW,GAAG,EAAlB;;QACA,IAAIN,IAAI,CAAClN,MAAT,EAAiB;UACf,IAAIsL,QAAQ,GAAG,KAAKtG,MAApB;UAAA,IACIyI,WAAW,GAAGnC,QAAQ,CAACkB,QAD3B;UAAA,IAEIzC,gBAAgB,GAAGuB,QAAQ,CAACvB,gBAFhC;UAAA,IAGIwC,aAAa,GAAGjB,QAAQ,CAACiB,aAH7B;UAAA,IAIIG,IAAI,GAAGpB,QAAQ,CAACoB,IAJpB;UAMA,IAAIgB,eAAe,GAAG,EAAtB;;UACA,IAAIC,WAAW,GAAG,SAASA,WAAT,CAAqBC,QAArB,EAA+B3S,GAA/B,EAAoC;YACpD,IAAIiI,QAAQ,GAAG6G,gBAAgB,IAAIwC,aAAa,IAAIA,aAAa,CAAC7K,OAAd,CAAsBzG,GAAtB,MAA+B,CAAC,CAApF;YACA,OAAO,CAAC,EAAE2S,QAAQ,IAAIA,QAAQ,CAACjD,QAArB,IAAiCzH,QAAnC,CAAR;UACD,CAHD,CARe,CAYf;;;UACAgK,IAAI,CAACjM,OAAL,CAAa,UAAUhG,GAAV,EAAe;YAC1B,IAAI2S,QAAQ,GAAGH,WAAW,CAACxS,GAAD,CAA1B;;YACA,IAAI4S,QAAQ,GAAG1B,QAAQ,CAAC,EAAD,EAAKoB,MAAM,CAACtS,GAAD,CAAX,CAAvB;;YACA4S,QAAQ,CAAClD,QAAT,GAAoBgD,WAAW,CAACC,QAAD,EAAW3S,GAAX,CAA/B;;YACA,IAAI4S,QAAQ,CAACnB,IAAb,EAAmB;cACjB,IAAIoB,IAAI,GAAGF,QAAQ,IAAI,EAAvB;cAAA,IACIG,WAAW,GAAGD,IAAI,CAACE,MADvB;cAAA,IAEIA,MAAM,GAAGD,WAAW,KAAK7L,SAAhB,GAA4B,KAA5B,GAAoC6L,WAFjD;cAAA,IAGIE,YAAY,GAAGH,IAAI,CAACI,OAHxB;cAAA,IAIIA,OAAO,GAAGD,YAAY,KAAK/L,SAAjB,GAA6B,KAA7B,GAAqC+L,YAJnD;;cAMAJ,QAAQ,CAACG,MAAT,GAAkB,CAAC,CAACA,MAApB;cACAH,QAAQ,CAACK,OAAT,GAAmB,CAAC,CAACA,OAArB;cACAR,eAAe,CAACtK,IAAhB,CAAqBnI,GAArB;YACD;;YACDuS,WAAW,CAACvS,GAAD,CAAX,GAAmB4S,QAAnB;UACD,CAhBD,EAbe,CA8Bf;;UACA,IAAIM,QAAQ,GAAG/T,MAAM,CAAC8S,IAAP,CAAYD,kBAAZ,CAAf;;UACA,IAAIP,IAAI,IAAIyB,QAAQ,CAACnO,MAAjB,IAA2B0N,eAAe,CAAC1N,MAA/C,EAAuD;YACrDmO,QAAQ,CAAClN,OAAT,CAAiB,UAAUhG,GAAV,EAAe;cAC9B,IAAI2S,QAAQ,GAAGH,WAAW,CAACxS,GAAD,CAA1B;cACA,IAAImT,gBAAgB,GAAGnB,kBAAkB,CAAChS,GAAD,CAAlB,CAAwB4I,QAA/C;;cACA,IAAI6J,eAAe,CAAChM,OAAhB,CAAwBzG,GAAxB,MAAiC,CAAC,CAAtC,EAAyC;gBACvC;gBACA,IAAIuS,WAAW,CAACvS,GAAD,CAAX,CAAiB4I,QAAjB,CAA0B7D,MAA1B,KAAqC,CAAzC,EAA4C;kBAC1C,MAAM,IAAIyB,KAAJ,CAAU,2CAAV,CAAN;gBACD;;gBACD+L,WAAW,CAACvS,GAAD,CAAX,CAAiB4I,QAAjB,GAA4BuK,gBAA5B;cACD,CAND,MAMO;gBACL,IAAIC,KAAK,GAAGT,QAAQ,IAAI,EAAxB;gBAAA,IACIU,YAAY,GAAGD,KAAK,CAACL,MADzB;gBAAA,IAEIA,MAAM,GAAGM,YAAY,KAAKpM,SAAjB,GAA6B,KAA7B,GAAqCoM,YAFlD;gBAAA,IAGIC,aAAa,GAAGF,KAAK,CAACH,OAH1B;gBAAA,IAIIA,OAAO,GAAGK,aAAa,KAAKrM,SAAlB,GAA8B,KAA9B,GAAsCqM,aAJpD;;gBAMAf,WAAW,CAACvS,GAAD,CAAX,GAAmB;kBACjByR,IAAI,EAAE,IADW;kBAEjBsB,MAAM,EAAE,CAAC,CAACA,MAFO;kBAGjBE,OAAO,EAAE,CAAC,CAACA,OAHM;kBAIjBvD,QAAQ,EAAEgD,WAAW,CAACC,QAAD,EAAW3S,GAAX,CAJJ;kBAKjB4I,QAAQ,EAAEuK,gBALO;kBAMjBtK,KAAK,EAAE;gBANU,CAAnB;cAQD;YACF,CAzBD;UA0BD;QACF;;QACD,KAAKkB,MAAL,CAAYwH,QAAZ,GAAuBgB,WAAvB;QACA,KAAKgB,kBAAL;MACD,CAjGM;MAkGPC,oBAAoB,EAAE,SAASA,oBAAT,CAA8B9T,KAA9B,EAAqC;QACzD,KAAKqK,MAAL,CAAYuH,aAAZ,GAA4B5R,KAA5B;QACA,KAAK2S,cAAL;MACD,CArGM;MAsGPoB,mBAAmB,EAAE,SAASA,mBAAT,CAA6BnN,GAA7B,EAAkCoJ,QAAlC,EAA4C;QAC/D,KAAKK,YAAL;QAEA,IAAI2D,QAAQ,GAAG,KAAK3J,MAApB;QAAA,IACIxD,MAAM,GAAGmN,QAAQ,CAACnN,MADtB;QAAA,IAEIgL,QAAQ,GAAGmC,QAAQ,CAACnC,QAFxB;QAIA,IAAItL,EAAE,GAAG9G,MAAM,CAACyP,IAAI,CAAC;QAAI;QAAL,CAAL,CAAN,CAAuCtI,GAAvC,EAA4CC,MAA5C,CAAT;QACA,IAAI0D,IAAI,GAAGhE,EAAE,IAAIsL,QAAQ,CAACtL,EAAD,CAAzB;;QACA,IAAIA,EAAE,IAAIgE,IAAN,IAAc,cAAcA,IAAhC,EAAsC;UACpC,IAAI0J,WAAW,GAAG1J,IAAI,CAACyF,QAAvB;UACAA,QAAQ,GAAG,OAAOA,QAAP,KAAoB,WAApB,GAAkC,CAACzF,IAAI,CAACyF,QAAxC,GAAmDA,QAA9D;UACA6B,QAAQ,CAACtL,EAAD,CAAR,CAAayJ,QAAb,GAAwBA,QAAxB;;UACA,IAAIiE,WAAW,KAAKjE,QAApB,EAA8B;YAC5B,KAAK9J,KAAL,CAAW+J,KAAX,CAAiB,eAAjB,EAAkCrJ,GAAlC,EAAuCoJ,QAAvC;UACD;;UACD,KAAK6D,kBAAL;QACD;MACF,CAxHM;MAyHPK,YAAY,EAAE,SAASA,YAAT,CAAsBtN,GAAtB,EAA2B;QACvC,KAAKyJ,YAAL;QACA,IAAI8D,QAAQ,GAAG,KAAK9J,MAApB;QAAA,IACI0H,IAAI,GAAGoC,QAAQ,CAACpC,IADpB;QAAA,IAEIF,QAAQ,GAAGsC,QAAQ,CAACtC,QAFxB;QAAA,IAGIhL,MAAM,GAAGsN,QAAQ,CAACtN,MAHtB;QAKA,IAAIN,EAAE,GAAG9G,MAAM,CAACyP,IAAI,CAAC;QAAI;QAAL,CAAL,CAAN,CAAuCtI,GAAvC,EAA4CC,MAA5C,CAAT;QACA,IAAI0D,IAAI,GAAGsH,QAAQ,CAACtL,EAAD,CAAnB;;QACA,IAAIwL,IAAI,IAAIxH,IAAR,IAAgB,YAAYA,IAA5B,IAAoC,CAACA,IAAI,CAAC8I,MAA9C,EAAsD;UACpD,KAAKe,QAAL,CAAcxN,GAAd,EAAmBL,EAAnB,EAAuBgE,IAAvB;QACD,CAFD,MAEO;UACL,KAAKwJ,mBAAL,CAAyBnN,GAAzB;QACD;MACF,CAvIM;MAwIPwN,QAAQ,EAAE,SAASA,QAAT,CAAkBxN,GAAlB,EAAuBtG,GAAvB,EAA4B+T,QAA5B,EAAsC;QAC9C,IAAIC,KAAK,GAAG,IAAZ;;QAEA,IAAIC,IAAI,GAAG,KAAKrO,KAAL,CAAWqO,IAAtB;QACA,IAAIC,WAAW,GAAG,KAAKnK,MAAL,CAAYwH,QAA9B;;QAEA,IAAI0C,IAAI,IAAI,CAACC,WAAW,CAAClU,GAAD,CAAX,CAAiB+S,MAA9B,EAAsC;UACpCmB,WAAW,CAAClU,GAAD,CAAX,CAAiBiT,OAAjB,GAA2B,IAA3B;UACAgB,IAAI,CAAC3N,GAAD,EAAMyN,QAAN,EAAgB,UAAU9J,IAAV,EAAgB;YAClC,IAAI,CAACpF,KAAK,CAACC,OAAN,CAAcmF,IAAd,CAAL,EAA0B;cACxB,MAAM,IAAIzD,KAAJ,CAAU,iCAAV,CAAN;YACD;;YACD,IAAI2N,QAAQ,GAAGH,KAAK,CAACjK,MAArB;YAAA,IACI2H,eAAe,GAAGyC,QAAQ,CAACzC,eAD/B;YAAA,IAEIH,QAAQ,GAAG4C,QAAQ,CAAC5C,QAFxB;YAIAA,QAAQ,CAACvR,GAAD,CAAR,CAAciT,OAAd,GAAwB,KAAxB;YACA1B,QAAQ,CAACvR,GAAD,CAAR,CAAc+S,MAAd,GAAuB,IAAvB;YACAxB,QAAQ,CAACvR,GAAD,CAAR,CAAc0P,QAAd,GAAyB,IAAzB;;YACA,IAAIzF,IAAI,CAAClF,MAAT,EAAiB;cACfiP,KAAK,CAACI,IAAN,CAAW1C,eAAX,EAA4B1R,GAA5B,EAAiCiK,IAAjC;YACD;;YACD+J,KAAK,CAACpO,KAAN,CAAY+J,KAAZ,CAAkB,eAAlB,EAAmCrJ,GAAnC,EAAwC,IAAxC;UACD,CAfG,CAAJ;QAgBD;MACF;IAjKM;EA9D8B,CAAZ,CA5pBqC,CA83BlE;;EAQA,IAAI+N,gBAAgB,GAAG,SAASC,QAAT,CAAkBrK,IAAlB,EAAwBF,MAAxB,EAAgC;IACrD,IAAIwK,aAAa,GAAGxK,MAAM,CAACwK,aAA3B;;IACA,IAAI,CAACA,aAAD,IAAkB,OAAOA,aAAa,CAACC,QAArB,KAAkC,QAAxD,EAAkE;MAChE,OAAOvK,IAAP;IACD;;IACD,OAAO9K,MAAM,CAACyP,IAAI,CAAC;IAAI;IAAL,CAAL,CAAN,CAAgC3E,IAAhC,EAAsCF,MAAM,CAAC0K,QAA7C,EAAuD1K,MAAM,CAAC2K,SAA9D,EAAyEH,aAAa,CAAC5P,UAAvF,EAAmG4P,aAAa,CAAC3P,MAAjH,CAAP;EACD,CAND;;EAQA,IAAI+P,gBAAgB,GAAG,SAASA,gBAAT,CAA0B5O,OAA1B,EAAmC;IACxD,IAAI6O,MAAM,GAAG,EAAb;IACA7O,OAAO,CAACC,OAAR,CAAgB,UAAUF,MAAV,EAAkB;MAChC,IAAIA,MAAM,CAAC8C,QAAX,EAAqB;QACnBgM,MAAM,CAACzM,IAAP,CAAYN,KAAZ,CAAkB+M,MAAlB,EAA0BD,gBAAgB,CAAC7O,MAAM,CAAC8C,QAAR,CAA1C;MACD,CAFD,MAEO;QACLgM,MAAM,CAACzM,IAAP,CAAYrC,MAAZ;MACD;IACF,CAND;IAOA,OAAO8O,MAAP;EACD,CAVD;EAYA;;;EAA6B,IAAIC,OAAO,GAAIpG,oBAAoB,CAACnJ,CAArB,CAAuBwP,MAAvB,CAA8B;IACxE7K,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,OAAO;QACLF,MAAM,EAAE;UACN;UACAxD,MAAM,EAAE,IAFF;UAIN;UACA0D,IAAI,EAAE,EALA;UAON;UACAD,SAAS,EAAE,KARL;UAUN;UACA+K,QAAQ,EAAE,EAXJ;UAWQ;UACdC,aAAa,EAAE,EAZT;UAaNjP,OAAO,EAAE,EAbH;UAcNmG,YAAY,EAAE,EAdR;UAeNS,iBAAiB,EAAE,EAfb;UAgBNsI,WAAW,EAAE,EAhBP;UAiBNC,gBAAgB,EAAE,EAjBZ;UAkBNC,qBAAqB,EAAE,EAlBjB;UAmBNC,iBAAiB,EAAE,CAnBb;UAoBNC,sBAAsB,EAAE,CApBlB;UAqBNC,2BAA2B,EAAE,CArBvB;UAuBN;UACAC,aAAa,EAAE,KAxBT;UAyBNC,SAAS,EAAE,EAzBL;UA0BNC,gBAAgB,EAAE,KA1BZ;UA2BNC,qBAAqB,EAAE,KA3BjB;UA4BNC,UAAU,EAAE,IA5BN;UA8BN;UACAC,OAAO,EAAE,EA/BH;UA+BO;UACbC,YAAY,EAAE,IAhCR;UAkCN;UACAtB,aAAa,EAAE,IAnCT;UAoCNE,QAAQ,EAAE,IApCJ;UAqCNC,SAAS,EAAE,IArCL;UAuCNoB,QAAQ,EAAE;QAvCJ;MADH,CAAP;IA2CD,CA7CuE;IAgDxEC,MAAM,EAAE,CAAClH,MAAD,EAASlI,OAAT,EAAkB0K,IAAlB,CAhDgE;IAkDxErC,OAAO,EAAE;MACP;MACAe,YAAY,EAAE,SAASA,YAAT,GAAwB;QACpC,IAAIxJ,MAAM,GAAG,KAAKwD,MAAL,CAAYxD,MAAzB;QACA,IAAI,CAACA,MAAL,EAAa,MAAM,IAAIC,KAAJ,CAAU,oCAAV,CAAN;MACd,CALM;MAQP;MACAwP,aAAa,EAAE,SAASA,aAAT,GAAyB;QACtC,IAAIjM,MAAM,GAAG,KAAKA,MAAlB;;QACA,IAAIgL,QAAQ,GAAGhL,MAAM,CAACgL,QAAP,IAAmB,EAAlC;;QACAhL,MAAM,CAACmC,YAAP,GAAsB6I,QAAQ,CAACkB,MAAT,CAAgB,UAAUnQ,MAAV,EAAkB;UACtD,OAAOA,MAAM,CAACwG,KAAP,KAAiB,IAAjB,IAAyBxG,MAAM,CAACwG,KAAP,KAAiB,MAAjD;QACD,CAFqB,CAAtB;QAGAvC,MAAM,CAAC4C,iBAAP,GAA2BoI,QAAQ,CAACkB,MAAT,CAAgB,UAAUnQ,MAAV,EAAkB;UAC3D,OAAOA,MAAM,CAACwG,KAAP,KAAiB,OAAxB;QACD,CAF0B,CAA3B;;QAIA,IAAIvC,MAAM,CAACmC,YAAP,CAAoBnH,MAApB,GAA6B,CAA7B,IAAkCgQ,QAAQ,CAAC,CAAD,CAA1C,IAAiDA,QAAQ,CAAC,CAAD,CAAR,CAAYmB,IAAZ,KAAqB,WAAtE,IAAqF,CAACnB,QAAQ,CAAC,CAAD,CAAR,CAAYzI,KAAtG,EAA6G;UAC3GyI,QAAQ,CAAC,CAAD,CAAR,CAAYzI,KAAZ,GAAoB,IAApB;UACAvC,MAAM,CAACmC,YAAP,CAAoBiK,OAApB,CAA4BpB,QAAQ,CAAC,CAAD,CAApC;QACD;;QAED,IAAIqB,eAAe,GAAGrB,QAAQ,CAACkB,MAAT,CAAgB,UAAUnQ,MAAV,EAAkB;UACtD,OAAO,CAACA,MAAM,CAACwG,KAAf;QACD,CAFqB,CAAtB;;QAGAvC,MAAM,CAACiL,aAAP,GAAuB,GAAGrS,MAAH,CAAUoH,MAAM,CAACmC,YAAjB,EAA+BvJ,MAA/B,CAAsCyT,eAAtC,EAAuDzT,MAAvD,CAA8DoH,MAAM,CAAC4C,iBAArE,CAAvB;QAEA,IAAIsI,WAAW,GAAGN,gBAAgB,CAACyB,eAAD,CAAlC;QACA,IAAIlB,gBAAgB,GAAGP,gBAAgB,CAAC5K,MAAM,CAACmC,YAAR,CAAvC;QACA,IAAIiJ,qBAAqB,GAAGR,gBAAgB,CAAC5K,MAAM,CAAC4C,iBAAR,CAA5C;QAEA5C,MAAM,CAACqL,iBAAP,GAA2BH,WAAW,CAAClQ,MAAvC;QACAgF,MAAM,CAACsL,sBAAP,GAAgCH,gBAAgB,CAACnQ,MAAjD;QACAgF,MAAM,CAACuL,2BAAP,GAAqCH,qBAAqB,CAACpQ,MAA3D;QAEAgF,MAAM,CAAChE,OAAP,GAAiB,GAAGpD,MAAH,CAAUuS,gBAAV,EAA4BvS,MAA5B,CAAmCsS,WAAnC,EAAgDtS,MAAhD,CAAuDwS,qBAAvD,CAAjB;QACApL,MAAM,CAACC,SAAP,GAAmBD,MAAM,CAACmC,YAAP,CAAoBnH,MAApB,GAA6B,CAA7B,IAAkCgF,MAAM,CAAC4C,iBAAP,CAAyB5H,MAAzB,GAAkC,CAAvF;MACD,CAvCM;MA0CP;MACA6K,cAAc,EAAE,SAASA,cAAT,CAAwByG,iBAAxB,EAA2C;QACzD,IAAIA,iBAAJ,EAAuB;UACrB,KAAKL,aAAL;QACD;;QACD,KAAKpQ,KAAL,CAAW0Q,qBAAX;MACD,CAhDM;MAmDP;MACAC,UAAU,EAAE,SAASA,UAAT,CAAoBjQ,GAApB,EAAyB;QACnC,IAAIkQ,iBAAiB,GAAG,KAAKzM,MAAL,CAAYyL,SAApC;QAAA,IACIA,SAAS,GAAGgB,iBAAiB,KAAKvP,SAAtB,GAAkC,EAAlC,GAAuCuP,iBADvD;QAGA,OAAOhB,SAAS,CAAC/O,OAAV,CAAkBH,GAAlB,IAAyB,CAAC,CAAjC;MACD,CAzDM;MA0DPmQ,cAAc,EAAE,SAASA,cAAT,GAA0B;QACxC,IAAI1M,MAAM,GAAG,KAAKA,MAAlB;QACAA,MAAM,CAACwL,aAAP,GAAuB,KAAvB;QACA,IAAImB,YAAY,GAAG3M,MAAM,CAACyL,SAA1B;;QACA,IAAIkB,YAAY,CAAC3R,MAAjB,EAAyB;UACvBgF,MAAM,CAACyL,SAAP,GAAmB,EAAnB;UACA,KAAK5P,KAAL,CAAW+J,KAAX,CAAiB,kBAAjB,EAAqC,EAArC;QACD;MACF,CAlEM;MAmEPgH,cAAc,EAAE,SAASA,cAAT,GAA0B;QACxC,IAAI5M,MAAM,GAAG,KAAKA,MAAlB;QACA,IAAIE,IAAI,GAAGF,MAAM,CAACE,IAAlB;QAAA,IACI1D,MAAM,GAAGwD,MAAM,CAACxD,MADpB;QAAA,IAEIiP,SAAS,GAAGzL,MAAM,CAACyL,SAFvB;QAIA,IAAIoB,OAAO,GAAG,KAAK,CAAnB;;QACA,IAAIrQ,MAAJ,EAAY;UACVqQ,OAAO,GAAG,EAAV;UACA,IAAIC,WAAW,GAAG1X,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAmC4G,SAAnC,EAA8CjP,MAA9C,CAAlB;UACA,IAAIuQ,OAAO,GAAG3X,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAmC3E,IAAnC,EAAyC1D,MAAzC,CAAd;;UACA,KAAK,IAAIvG,GAAT,IAAgB6W,WAAhB,EAA6B;YAC3B,IAAIA,WAAW,CAACrW,cAAZ,CAA2BR,GAA3B,KAAmC,CAAC8W,OAAO,CAAC9W,GAAD,CAA/C,EAAsD;cACpD4W,OAAO,CAACzO,IAAR,CAAa0O,WAAW,CAAC7W,GAAD,CAAX,CAAiBsG,GAA9B;YACD;UACF;QACF,CATD,MASO;UACLsQ,OAAO,GAAGpB,SAAS,CAACS,MAAV,CAAiB,UAAUtQ,IAAV,EAAgB;YACzC,OAAOsE,IAAI,CAACxD,OAAL,CAAad,IAAb,MAAuB,CAAC,CAA/B;UACD,CAFS,CAAV;QAGD;;QACD,IAAIiR,OAAO,CAAC7R,MAAZ,EAAoB;UAClB,IAAIgS,YAAY,GAAGvB,SAAS,CAACS,MAAV,CAAiB,UAAUtQ,IAAV,EAAgB;YAClD,OAAOiR,OAAO,CAACnQ,OAAR,CAAgBd,IAAhB,MAA0B,CAAC,CAAlC;UACD,CAFkB,CAAnB;UAGAoE,MAAM,CAACyL,SAAP,GAAmBuB,YAAnB;UACA,KAAKnR,KAAL,CAAW+J,KAAX,CAAiB,kBAAjB,EAAqCoH,YAAY,CAAC3H,KAAb,EAArC;QACD;MACF,CA/FM;MAgGP4H,kBAAkB,EAAE,SAASA,kBAAT,CAA4B1Q,GAA5B,EAAiC2Q,QAAjC,EAA2C;QAC7D,IAAIC,UAAU,GAAG1P,SAAS,CAACzC,MAAV,GAAmB,CAAnB,IAAwByC,SAAS,CAAC,CAAD,CAAT,KAAiBP,SAAzC,GAAqDO,SAAS,CAAC,CAAD,CAA9D,GAAoE,IAArF;QAEA,IAAIQ,OAAO,GAAG7I,MAAM,CAACyP,IAAI,CAAC;QAAI;QAAL,CAAL,CAAN,CAAwC,KAAK7E,MAAL,CAAYyL,SAApD,EAA+DlP,GAA/D,EAAoE2Q,QAApE,CAAd;;QACA,IAAIjP,OAAJ,EAAa;UACX,IAAI+O,YAAY,GAAG,CAAC,KAAKhN,MAAL,CAAYyL,SAAZ,IAAyB,EAA1B,EAA8BpG,KAA9B,EAAnB,CADW,CAEX;;UACA,IAAI8H,UAAJ,EAAgB;YACd,KAAKtR,KAAL,CAAW+J,KAAX,CAAiB,QAAjB,EAA2BoH,YAA3B,EAAyCzQ,GAAzC;UACD;;UACD,KAAKV,KAAL,CAAW+J,KAAX,CAAiB,kBAAjB,EAAqCoH,YAArC;QACD;MACF,CA5GM;MA6GPI,mBAAmB,EAAE,SAASA,mBAAT,GAA+B;QAClD,IAAIpN,MAAM,GAAG,KAAKA,MAAlB;QACA,IAAIoF,YAAY,GAAGpF,MAAM,CAACE,IAA1B;QAAA,IACIA,IAAI,GAAGkF,YAAY,KAAKlI,SAAjB,GAA6B,EAA7B,GAAkCkI,YAD7C;QAAA,IAEIqG,SAAS,GAAGzL,MAAM,CAACyL,SAFvB,CAFkD,CAKlD;QACA;;QAEA,IAAI9V,KAAK,GAAGqK,MAAM,CAAC2L,qBAAP,GAA+B,CAAC3L,MAAM,CAACwL,aAAvC,GAAuD,EAAExL,MAAM,CAACwL,aAAP,IAAwBC,SAAS,CAACzQ,MAApC,CAAnE;QACAgF,MAAM,CAACwL,aAAP,GAAuB7V,KAAvB;QAEA,IAAI0X,gBAAgB,GAAG,KAAvB;QACAnN,IAAI,CAACjE,OAAL,CAAa,UAAUM,GAAV,EAAerB,KAAf,EAAsB;UACjC,IAAI8E,MAAM,CAAC4L,UAAX,EAAuB;YACrB,IAAI5L,MAAM,CAAC4L,UAAP,CAAkB/W,IAAlB,CAAuB,IAAvB,EAA6B0H,GAA7B,EAAkCrB,KAAlC,KAA4C9F,MAAM,CAACyP,IAAI,CAAC;YAAI;YAAL,CAAL,CAAN,CAAwC4G,SAAxC,EAAmDlP,GAAnD,EAAwD5G,KAAxD,CAAhD,EAAgH;cAC9G0X,gBAAgB,GAAG,IAAnB;YACD;UACF,CAJD,MAIO;YACL,IAAIjY,MAAM,CAACyP,IAAI,CAAC;YAAI;YAAL,CAAL,CAAN,CAAwC4G,SAAxC,EAAmDlP,GAAnD,EAAwD5G,KAAxD,CAAJ,EAAoE;cAClE0X,gBAAgB,GAAG,IAAnB;YACD;UACF;QACF,CAVD;;QAYA,IAAIA,gBAAJ,EAAsB;UACpB,KAAKxR,KAAL,CAAW+J,KAAX,CAAiB,kBAAjB,EAAqC6F,SAAS,GAAGA,SAAS,CAACpG,KAAV,EAAH,GAAuB,EAArE;QACD;;QACD,KAAKxJ,KAAL,CAAW+J,KAAX,CAAiB,YAAjB,EAA+B6F,SAA/B;MACD,CAzIM;MA0IP6B,uBAAuB,EAAE,SAASA,uBAAT,GAAmC;QAC1D,IAAItN,MAAM,GAAG,KAAKA,MAAlB;QACA,IAAIyL,SAAS,GAAGzL,MAAM,CAACyL,SAAvB;QAAA,IACIjP,MAAM,GAAGwD,MAAM,CAACxD,MADpB;QAAA,IAEI0D,IAAI,GAAGF,MAAM,CAACE,IAFlB;QAIA,IAAI4M,WAAW,GAAG1X,MAAM,CAACyP,IAAI,CAAC;QAAI;QAAL,CAAL,CAAN,CAAmC4G,SAAnC,EAA8CjP,MAA9C,CAAlB;QACA0D,IAAI,CAACjE,OAAL,CAAa,UAAUM,GAAV,EAAe;UAC1B,IAAIiJ,KAAK,GAAGpQ,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAuCtI,GAAvC,EAA4CC,MAA5C,CAAZ;UACA,IAAIiJ,OAAO,GAAGqH,WAAW,CAACtH,KAAD,CAAzB;;UACA,IAAIC,OAAJ,EAAa;YACXgG,SAAS,CAAChG,OAAO,CAACvK,KAAT,CAAT,GAA2BqB,GAA3B;UACD;QACF,CAND;MAOD,CAxJM;MAyJPgR,iBAAiB,EAAE,SAASA,iBAAT,GAA6B;QAC9C,IAAIvN,MAAM,GAAG,KAAKA,MAAlB;QACA,IAAIyL,SAAS,GAAGzL,MAAM,CAACyL,SAAvB;QAAA,IACIjP,MAAM,GAAGwD,MAAM,CAACxD,MADpB;QAAA,IAEIoP,UAAU,GAAG5L,MAAM,CAAC4L,UAFxB,CAF8C,CAK9C;;QAEA,IAAI1L,IAAI,GAAGF,MAAM,CAACE,IAAP,IAAe,EAA1B;;QACA,IAAIA,IAAI,CAAClF,MAAL,KAAgB,CAApB,EAAuB;UACrBgF,MAAM,CAACwL,aAAP,GAAuB,KAAvB;UACA;QACD;;QAED,IAAIsB,WAAW,GAAG,KAAK,CAAvB;;QACA,IAAItQ,MAAJ,EAAY;UACVsQ,WAAW,GAAG1X,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAmC4G,SAAnC,EAA8CjP,MAA9C,CAAd;QACD;;QACD,IAAIgQ,UAAU,GAAG,SAASA,UAAT,CAAoBjQ,GAApB,EAAyB;UACxC,IAAIuQ,WAAJ,EAAiB;YACf,OAAO,CAAC,CAACA,WAAW,CAAC1X,MAAM,CAACyP,IAAI,CAAC;YAAI;YAAL,CAAL,CAAN,CAAuCtI,GAAvC,EAA4CC,MAA5C,CAAD,CAApB;UACD,CAFD,MAEO;YACL,OAAOiP,SAAS,CAAC/O,OAAV,CAAkBH,GAAlB,MAA2B,CAAC,CAAnC;UACD;QACF,CAND;;QAOA,IAAIiP,aAAa,GAAG,IAApB;QACA,IAAIgC,aAAa,GAAG,CAApB;;QACA,KAAK,IAAI7Y,CAAC,GAAG,CAAR,EAAW8Y,CAAC,GAAGvN,IAAI,CAAClF,MAAzB,EAAiCrG,CAAC,GAAG8Y,CAArC,EAAwC9Y,CAAC,EAAzC,EAA6C;UAC3C,IAAIiH,IAAI,GAAGsE,IAAI,CAACvL,CAAD,CAAf;UACA,IAAI+Y,eAAe,GAAG9B,UAAU,IAAIA,UAAU,CAAC/W,IAAX,CAAgB,IAAhB,EAAsB+G,IAAtB,EAA4BjH,CAA5B,CAApC;;UACA,IAAI,CAAC6X,UAAU,CAAC5Q,IAAD,CAAf,EAAuB;YACrB,IAAI,CAACgQ,UAAD,IAAe8B,eAAnB,EAAoC;cAClClC,aAAa,GAAG,KAAhB;cACA;YACD;UACF,CALD,MAKO;YACLgC,aAAa;UACd;QACF;;QAED,IAAIA,aAAa,KAAK,CAAtB,EAAyBhC,aAAa,GAAG,KAAhB;QACzBxL,MAAM,CAACwL,aAAP,GAAuBA,aAAvB;MACD,CAlMM;MAqMP;MACAmC,aAAa,EAAE,SAASA,aAAT,CAAuB3R,OAAvB,EAAgC4R,MAAhC,EAAwC;QACrD,IAAI,CAAC9S,KAAK,CAACC,OAAN,CAAciB,OAAd,CAAL,EAA6B;UAC3BA,OAAO,GAAG,CAACA,OAAD,CAAV;QACD;;QACD,IAAIgE,MAAM,GAAG,KAAKA,MAAlB;QACA,IAAI6L,OAAO,GAAG,EAAd;QACA7P,OAAO,CAACC,OAAR,CAAgB,UAAU4R,GAAV,EAAe;UAC7B7N,MAAM,CAAC6L,OAAP,CAAegC,GAAG,CAAC3R,EAAnB,IAAyB0R,MAAzB;UACA/B,OAAO,CAACgC,GAAG,CAAC1R,SAAJ,IAAiB0R,GAAG,CAAC3R,EAAtB,CAAP,GAAmC0R,MAAnC;QACD,CAHD;QAKA,OAAO/B,OAAP;MACD,CAlNM;MAmNPiC,UAAU,EAAE,SAASA,UAAT,CAAoB/R,MAApB,EAA4BgS,IAA5B,EAAkCpS,KAAlC,EAAyC;QACnD,IAAI,KAAKqE,MAAL,CAAYwK,aAAZ,IAA6B,KAAKxK,MAAL,CAAYwK,aAAZ,KAA8BzO,MAA/D,EAAuE;UACrE,KAAKiE,MAAL,CAAYwK,aAAZ,CAA0B7O,KAA1B,GAAkC,IAAlC;QACD;;QACD,KAAKqE,MAAL,CAAYwK,aAAZ,GAA4BzO,MAA5B;QACA,KAAKiE,MAAL,CAAY0K,QAAZ,GAAuBqD,IAAvB;QACA,KAAK/N,MAAL,CAAY2K,SAAZ,GAAwBhP,KAAxB;MACD,CA1NM;MA2NPqS,UAAU,EAAE,SAASA,UAAT,GAAsB;QAChC,IAAI/D,KAAK,GAAG,IAAZ;;QAEA,IAAIjK,MAAM,GAAG,KAAKA,MAAlB;QACA,IAAIiO,KAAK,GAAGjO,MAAM,CAACiO,KAAnB;QAAA,IACIpC,OAAO,GAAG7L,MAAM,CAAC6L,OADrB;QAGA,IAAI3L,IAAI,GAAG+N,KAAX;QAEA7Y,MAAM,CAAC8S,IAAP,CAAY2D,OAAZ,EAAqB5P,OAArB,CAA6B,UAAUH,QAAV,EAAoB;UAC/C,IAAI8R,MAAM,GAAG5N,MAAM,CAAC6L,OAAP,CAAe/P,QAAf,CAAb;UACA,IAAI,CAAC8R,MAAD,IAAWA,MAAM,CAAC5S,MAAP,KAAkB,CAAjC,EAAoC;UACpC,IAAIe,MAAM,GAAG3G,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAsCoF,KAAK,CAACjK,MAA5C,EAAoDlE,QAApD,CAAb;;UACA,IAAIC,MAAM,IAAIA,MAAM,CAACmS,YAArB,EAAmC;YACjChO,IAAI,GAAGA,IAAI,CAACgM,MAAL,CAAY,UAAU3P,GAAV,EAAe;cAChC,OAAOqR,MAAM,CAACO,IAAP,CAAY,UAAUxY,KAAV,EAAiB;gBAClC,OAAOoG,MAAM,CAACmS,YAAP,CAAoBrZ,IAApB,CAAyB,IAAzB,EAA+Bc,KAA/B,EAAsC4G,GAAtC,EAA2CR,MAA3C,CAAP;cACD,CAFM,CAAP;YAGD,CAJM,CAAP;UAKD;QACF,CAXD;QAaAiE,MAAM,CAAC8L,YAAP,GAAsB5L,IAAtB;MACD,CAlPM;MAmPPkO,QAAQ,EAAE,SAASA,QAAT,GAAoB;QAC5B,IAAIpO,MAAM,GAAG,KAAKA,MAAlB;QACAA,MAAM,CAACE,IAAP,GAAcoK,gBAAgB,CAACtK,MAAM,CAAC8L,YAAR,EAAsB9L,MAAtB,CAA9B;MACD,CAtPM;MAyPP;MACAqO,SAAS,EAAE,SAASA,SAAT,CAAmBC,MAAnB,EAA2B;QACpC,IAAI,EAAEA,MAAM,IAAIA,MAAM,CAACpC,MAAnB,CAAJ,EAAgC;UAC9B,KAAK8B,UAAL;QACD;;QACD,KAAKI,QAAL;MACD,CA/PM;MAgQPG,WAAW,EAAE,SAASA,WAAT,CAAqBC,UAArB,EAAiC;QAC5C,IAAIxO,MAAM,GAAG,KAAKA,MAAlB;QACA,IAAIyO,YAAY,GAAG,KAAK5S,KAAL,CAAW6S,KAA9B;QAAA,IACIC,WAAW,GAAGF,YAAY,CAACE,WAD/B;QAAA,IAEIC,gBAAgB,GAAGH,YAAY,CAACG,gBAFpC;QAAA,IAGIC,qBAAqB,GAAGJ,YAAY,CAACI,qBAHzC;QAMA,IAAIC,MAAM,GAAG,EAAb;QACA,IAAIH,WAAJ,EAAiBG,MAAM,GAAGlK,aAAa,GAAGkK,MAAH,EAAWH,WAAW,CAACI,YAAvB,CAAtB;QACjB,IAAIH,gBAAJ,EAAsBE,MAAM,GAAGlK,aAAa,GAAGkK,MAAH,EAAWF,gBAAgB,CAACG,YAA5B,CAAtB;QACtB,IAAIF,qBAAJ,EAA2BC,MAAM,GAAGlK,aAAa,GAAGkK,MAAH,EAAWD,qBAAqB,CAACE,YAAjC,CAAtB;QAE3B,IAAI7G,IAAI,GAAG9S,MAAM,CAAC8S,IAAP,CAAY4G,MAAZ,CAAX;QACA,IAAI,CAAC5G,IAAI,CAAClN,MAAV,EAAkB;;QAElB,IAAI,OAAOwT,UAAP,KAAsB,QAA1B,EAAoC;UAClCA,UAAU,GAAG,CAACA,UAAD,CAAb;QACD;;QAED,IAAI1T,KAAK,CAACC,OAAN,CAAcyT,UAAd,CAAJ,EAA+B;UAC7B,IAAIxS,OAAO,GAAGwS,UAAU,CAACrT,GAAX,CAAe,UAAUlF,GAAV,EAAe;YAC1C,OAAOb,MAAM,CAACyP,IAAI,CAAC;YAAI;YAAL,CAAL,CAAN,CAAuC7E,MAAvC,EAA+C/J,GAA/C,CAAP;UACD,CAFa,CAAd;UAGAiS,IAAI,CAACjM,OAAL,CAAa,UAAUhG,GAAV,EAAe;YAC1B,IAAI8F,MAAM,GAAGC,OAAO,CAACgT,IAAR,CAAa,UAAUnB,GAAV,EAAe;cACvC,OAAOA,GAAG,CAAC3R,EAAJ,KAAWjG,GAAlB;YACD,CAFY,CAAb;;YAGA,IAAI8F,MAAJ,EAAY;cACV;cACA+S,MAAM,CAAC7Y,GAAD,CAAN,CAAYgZ,aAAZ,GAA4B,EAA5B;YACD;UACF,CARD;UASA,KAAKC,MAAL,CAAY,cAAZ,EAA4B;YAC1BnT,MAAM,EAAEC,OADkB;YAE1B4R,MAAM,EAAE,EAFkB;YAG1BuB,MAAM,EAAE,IAHkB;YAI1BC,KAAK,EAAE;UAJmB,CAA5B;QAMD,CAnBD,MAmBO;UACLlH,IAAI,CAACjM,OAAL,CAAa,UAAUhG,GAAV,EAAe;YAC1B;YACA6Y,MAAM,CAAC7Y,GAAD,CAAN,CAAYgZ,aAAZ,GAA4B,EAA5B;UACD,CAHD;UAKAjP,MAAM,CAAC6L,OAAP,GAAiB,EAAjB;UACA,KAAKqD,MAAL,CAAY,cAAZ,EAA4B;YAC1BnT,MAAM,EAAE,EADkB;YAE1B6R,MAAM,EAAE,EAFkB;YAG1BuB,MAAM,EAAE;UAHkB,CAA5B;QAKD;MACF,CApTM;MAqTPE,SAAS,EAAE,SAASA,SAAT,GAAqB;QAC9B,IAAIrP,MAAM,GAAG,KAAKA,MAAlB;QACA,IAAI,CAACA,MAAM,CAACwK,aAAZ,EAA2B;QAE3B,KAAKsD,UAAL,CAAgB,IAAhB,EAAsB,IAAtB,EAA4B,IAA5B;QACA,KAAKoB,MAAL,CAAY,qBAAZ,EAAmC;UACjCC,MAAM,EAAE;QADyB,CAAnC;MAGD,CA7TM;MAgUP;MACAG,uBAAuB,EAAE,SAASA,uBAAT,CAAiCC,GAAjC,EAAsC;QAC7D;QACA,KAAKzJ,gBAAL,CAAsByJ,GAAtB;QACA,KAAK9F,oBAAL,CAA0B8F,GAA1B;MACD,CArUM;MAwUP;MACAC,yBAAyB,EAAE,SAASA,yBAAT,CAAmCjT,GAAnC,EAAwCoJ,QAAxC,EAAkD;QAC3E,IAAI8J,eAAe,GAAG,KAAKzP,MAAL,CAAYhE,OAAZ,CAAoBmS,IAApB,CAAyB,UAAUrF,IAAV,EAAgB;UAC7D,IAAIqD,IAAI,GAAGrD,IAAI,CAACqD,IAAhB;UACA,OAAOA,IAAI,KAAK,QAAhB;QACD,CAHqB,CAAtB;;QAIA,IAAIsD,eAAJ,EAAqB;UACnB,KAAK/J,kBAAL,CAAwBnJ,GAAxB,EAA6BoJ,QAA7B;QACD,CAFD,MAEO;UACL,KAAK+D,mBAAL,CAAyBnN,GAAzB,EAA8BoJ,QAA9B;QACD;MACF;IAnVM;EAlD+D,CAA9B,CAAf,CA15BqC,CAkyClE;;EAKAmF,OAAO,CAACtU,SAAR,CAAkBkZ,SAAlB,GAA8B;IAC5BC,OAAO,EAAE,SAASA,OAAT,CAAiB3P,MAAjB,EAAyBE,IAAzB,EAA+B;MACtC,IAAI0P,mBAAmB,GAAG5P,MAAM,CAACiO,KAAP,KAAiB/N,IAA3C;MACAF,MAAM,CAACiO,KAAP,GAAe/N,IAAf;MAEA,KAAKmO,SAAL,GAJsC,CAKtC;MACA;;MACA,KAAKpH,oBAAL;MACA,KAAK/B,gBAAL;;MACA,IAAIlF,MAAM,CAAC0L,gBAAX,EAA6B;QAC3B,KAAK1F,YAAL;QACA,KAAKsH,uBAAL;MACD,CAHD,MAGO;QACL,IAAIsC,mBAAJ,EAAyB;UACvB,KAAKlD,cAAL;QACD,CAFD,MAEO;UACL,KAAKE,cAAL;QACD;MACF;;MACD,KAAKW,iBAAL;MAEA,KAAK/D,kBAAL;IACD,CAvB2B;IAwB5BqG,YAAY,EAAE,SAASA,YAAT,CAAsB7P,MAAtB,EAA8BjE,MAA9B,EAAsCb,KAAtC,EAA6CpD,MAA7C,EAAqD;MACjE,IAAI2C,KAAK,GAAGuF,MAAM,CAACgL,QAAnB;;MACA,IAAIlT,MAAJ,EAAY;QACV2C,KAAK,GAAG3C,MAAM,CAAC+G,QAAf;QACA,IAAI,CAACpE,KAAL,EAAYA,KAAK,GAAG3C,MAAM,CAAC+G,QAAP,GAAkB,EAA1B;MACb;;MAED,IAAI,OAAO3D,KAAP,KAAiB,WAArB,EAAkC;QAChCT,KAAK,CAAC6D,MAAN,CAAapD,KAAb,EAAoB,CAApB,EAAuBa,MAAvB;MACD,CAFD,MAEO;QACLtB,KAAK,CAAC2D,IAAN,CAAWrC,MAAX;MACD;;MAED,IAAIA,MAAM,CAACoQ,IAAP,KAAgB,WAApB,EAAiC;QAC/BnM,MAAM,CAAC4L,UAAP,GAAoB7P,MAAM,CAAC6P,UAA3B;QACA5L,MAAM,CAAC0L,gBAAP,GAA0B3P,MAAM,CAAC2P,gBAAjC;MACD;;MAED,IAAI,KAAK7P,KAAL,CAAWiU,MAAf,EAAuB;QACrB,KAAK7D,aAAL,GADqB,CACC;;QACtB,KAAKpG,cAAL;MACD;IACF,CA9C2B;IA+C5BkK,YAAY,EAAE,SAASA,YAAT,CAAsB/P,MAAtB,EAA8BjE,MAA9B,EAAsCjE,MAAtC,EAA8C;MAC1D,IAAI2C,KAAK,GAAGuF,MAAM,CAACgL,QAAnB;;MACA,IAAIlT,MAAJ,EAAY;QACV2C,KAAK,GAAG3C,MAAM,CAAC+G,QAAf;QACA,IAAI,CAACpE,KAAL,EAAYA,KAAK,GAAG3C,MAAM,CAAC+G,QAAP,GAAkB,EAA1B;MACb;;MACD,IAAIpE,KAAJ,EAAW;QACTA,KAAK,CAAC6D,MAAN,CAAa7D,KAAK,CAACiC,OAAN,CAAcX,MAAd,CAAb,EAAoC,CAApC;MACD;;MAED,IAAI,KAAKF,KAAL,CAAWiU,MAAf,EAAuB;QACrB,KAAK7D,aAAL,GADqB,CACC;;QACtB,KAAKpG,cAAL;MACD;IACF,CA7D2B;IA8D5BnK,IAAI,EAAE,SAASA,IAAT,CAAcsE,MAAd,EAAsB1I,OAAtB,EAA+B;MACnC,IAAIyW,IAAI,GAAGzW,OAAO,CAACyW,IAAnB;MAAA,IACIpS,KAAK,GAAGrE,OAAO,CAACqE,KADpB;MAAA,IAEIqU,IAAI,GAAG1Y,OAAO,CAAC0Y,IAFnB;;MAIA,IAAIjC,IAAJ,EAAU;QACR,IAAIhS,MAAM,GAAG3G,MAAM,CAACqR,KAAK,CAAC,WAAD,CAAN,CAAN,CAA2BzG,MAAM,CAAChE,OAAlC,EAA2C,UAAUD,MAAV,EAAkB;UACxE,OAAOA,MAAM,CAACxF,QAAP,KAAoBwX,IAA3B;QACD,CAFY,CAAb;;QAGA,IAAIhS,MAAJ,EAAY;UACVA,MAAM,CAACJ,KAAP,GAAeA,KAAf;UACA,KAAKmS,UAAL,CAAgB/R,MAAhB,EAAwBgS,IAAxB,EAA8BpS,KAA9B;UACA,KAAKuT,MAAL,CAAY,qBAAZ,EAAmC;YAAEc,IAAI,EAAEA;UAAR,CAAnC;QACD;MACF;IACF,CA7E2B;IA8E5BC,mBAAmB,EAAE,SAASA,mBAAT,CAA6BjQ,MAA7B,EAAqC1I,OAArC,EAA8C;MACjE;MACA,IAAIyE,MAAM,GAAGiE,MAAM,CAACwK,aAApB;MAAA,IACIuD,IAAI,GAAG/N,MAAM,CAAC0K,QADlB;MAAA,IAEI/O,KAAK,GAAGqE,MAAM,CAAC2K,SAFnB;;MAIA,IAAIhP,KAAK,KAAK,IAAd,EAAoB;QAClBqE,MAAM,CAACwK,aAAP,GAAuB,IAAvB;QACAxK,MAAM,CAAC0K,QAAP,GAAkB,IAAlB;MACD;;MACD,IAAIwF,MAAM,GAAG;QAAEhE,MAAM,EAAE;MAAV,CAAb;MACA,KAAKmC,SAAL,CAAe6B,MAAf;;MAEA,IAAI,CAAC5Y,OAAD,IAAY,EAAEA,OAAO,CAAC6X,MAAR,IAAkB7X,OAAO,CAAC0Y,IAA5B,CAAhB,EAAmD;QACjD,KAAKnU,KAAL,CAAW+J,KAAX,CAAiB,aAAjB,EAAgC;UAC9B7J,MAAM,EAAEA,MADsB;UAE9BgS,IAAI,EAAEA,IAFwB;UAG9BpS,KAAK,EAAEA;QAHuB,CAAhC;MAKD;;MAED,KAAK6N,kBAAL;IACD,CApG2B;IAqG5B2G,YAAY,EAAE,SAASA,YAAT,CAAsBnQ,MAAtB,EAA8B1I,OAA9B,EAAuC;MACnD,IAAIyE,MAAM,GAAGzE,OAAO,CAACyE,MAArB;MAAA,IACI6R,MAAM,GAAGtW,OAAO,CAACsW,MADrB;MAAA,IAEIuB,MAAM,GAAG7X,OAAO,CAAC6X,MAFrB;MAIA,IAAIiB,UAAU,GAAG,KAAKzC,aAAL,CAAmB5R,MAAnB,EAA2B6R,MAA3B,CAAjB;MAEA,KAAKS,SAAL;;MAEA,IAAI,CAACc,MAAL,EAAa;QACX,KAAKtT,KAAL,CAAW+J,KAAX,CAAiB,eAAjB,EAAkCwK,UAAlC;MACD;;MAED,KAAK5G,kBAAL;IACD,CAnH2B;IAoH5B6G,kBAAkB,EAAE,SAASA,kBAAT,GAA8B;MAChD,KAAKA,kBAAL;IACD,CAtH2B;IAuH5BC,kBAAkB,EAAE,SAASA,kBAAT,CAA4BtQ,MAA5B,EAAoCzD,GAApC,EAAyC;MAC3D,KAAK0Q,kBAAL,CAAwB1Q,GAAxB;MACA,KAAKgR,iBAAL;IACD,CA1H2B;IA2H5BgD,WAAW,EAAE,SAASA,WAAT,CAAqBvQ,MAArB,EAA6BzD,GAA7B,EAAkC;MAC7CyD,MAAM,CAAC+L,QAAP,GAAkBxP,GAAlB;IACD,CA7H2B;IA8H5BiU,aAAa,EAAE,SAASA,aAAT,CAAuBxQ,MAAvB,EAA+BzD,GAA/B,EAAoC;MACjD,KAAKwK,gBAAL,CAAsBxK,GAAtB;IACD;EAhI2B,CAA9B;;EAmIAuO,OAAO,CAACtU,SAAR,CAAkB0Y,MAAlB,GAA2B,UAAUja,IAAV,EAAgB;IACzC,IAAIya,SAAS,GAAG,KAAKA,SAArB;;IACA,IAAIA,SAAS,CAACza,IAAD,CAAb,EAAqB;MACnB,KAAK,IAAIuI,IAAI,GAAGC,SAAS,CAACzC,MAArB,EAA6ByV,IAAI,GAAG3V,KAAK,CAAC0C,IAAI,GAAG,CAAP,GAAWA,IAAI,GAAG,CAAlB,GAAsB,CAAvB,CAAzC,EAAoEG,IAAI,GAAG,CAAhF,EAAmFA,IAAI,GAAGH,IAA1F,EAAgGG,IAAI,EAApG,EAAwG;QACtG8S,IAAI,CAAC9S,IAAI,GAAG,CAAR,CAAJ,GAAiBF,SAAS,CAACE,IAAD,CAA1B;MACD;;MAED+R,SAAS,CAACza,IAAD,CAAT,CAAgB6I,KAAhB,CAAsB,IAAtB,EAA4B,CAAC,KAAKkC,MAAN,EAAcpH,MAAd,CAAqB6X,IAArB,CAA5B;IACD,CAND,MAMO;MACL,MAAM,IAAIhU,KAAJ,CAAU,uBAAuBxH,IAAjC,CAAN;IACD;EACF,CAXD;;EAaA6V,OAAO,CAACtU,SAAR,CAAkBgT,kBAAlB,GAAuC,YAAY;IACjD9E,oBAAoB,CAACnJ,CAArB,CAAuBmV,QAAvB,CAAgC,KAAK7U,KAAL,CAAW8U,aAA3C;EACD,CAFD;EAIA;;;EAA6B,IAAIC,SAAS,GAAI9F,OAAjB,CA37CqC,CA47ClE;;EACA,IAAI+F,SAAS,GAAGpc,mBAAmB,CAAC,EAAD,CAAnC;;EACA,IAAIqc,gBAAgB,GAAG,aAAarc,mBAAmB,CAAC0B,CAApB,CAAsB0a,SAAtB,CAApC,CA97CkE,CAg8ClE;;;EAIA,SAASE,WAAT,CAAqBlV,KAArB,EAA4B;IAC1B,IAAImV,YAAY,GAAGvT,SAAS,CAACzC,MAAV,GAAmB,CAAnB,IAAwByC,SAAS,CAAC,CAAD,CAAT,KAAiBP,SAAzC,GAAqDO,SAAS,CAAC,CAAD,CAA9D,GAAoE,EAAvF;;IAEA,IAAI,CAAC5B,KAAL,EAAY;MACV,MAAM,IAAIY,KAAJ,CAAU,oBAAV,CAAN;IACD;;IAED,IAAIsD,KAAK,GAAG,IAAI6Q,SAAJ,EAAZ;IACA7Q,KAAK,CAAClE,KAAN,GAAcA,KAAd,CAR0B,CAS1B;IACA;;IACAkE,KAAK,CAACsQ,kBAAN,GAA2BS,gBAAgB,GAAG,EAAH,EAAO/Q,KAAK,CAACqN,mBAAb,CAA3C;IACAhY,MAAM,CAAC8S,IAAP,CAAY8I,YAAZ,EAA0B/U,OAA1B,CAAkC,UAAUhG,GAAV,EAAe;MAC/C8J,KAAK,CAACC,MAAN,CAAa/J,GAAb,IAAoB+a,YAAY,CAAC/a,GAAD,CAAhC;IACD,CAFD;IAGA,OAAO8J,KAAP;EACD;;EAED,SAASkR,SAAT,CAAmBC,MAAnB,EAA2B;IACzB,IAAI/I,GAAG,GAAG,EAAV;IACA/S,MAAM,CAAC8S,IAAP,CAAYgJ,MAAZ,EAAoBjV,OAApB,CAA4B,UAAUhG,GAAV,EAAe;MACzC,IAAIN,KAAK,GAAGub,MAAM,CAACjb,GAAD,CAAlB;MACA,IAAIkb,EAAE,GAAG,KAAK,CAAd;;MACA,IAAI,OAAOxb,KAAP,KAAiB,QAArB,EAA+B;QAC7Bwb,EAAE,GAAG,SAASA,EAAT,GAAc;UACjB,OAAO,KAAKpR,KAAL,CAAWC,MAAX,CAAkBrK,KAAlB,CAAP;QACD,CAFD;MAGD,CAJD,MAIO,IAAI,OAAOA,KAAP,KAAiB,UAArB,EAAiC;QACtCwb,EAAE,GAAG,SAASA,EAAT,GAAc;UACjB,OAAOxb,KAAK,CAACd,IAAN,CAAW,IAAX,EAAiB,KAAKkL,KAAL,CAAWC,MAA5B,CAAP;QACD,CAFD;MAGD,CAJM,MAIA;QACLoR,OAAO,CAACC,KAAR,CAAc,oBAAd;MACD;;MACD,IAAIF,EAAJ,EAAQ;QACNhJ,GAAG,CAAClS,GAAD,CAAH,GAAWkb,EAAX;MACD;IACF,CAjBD;IAkBA,OAAOhJ,GAAP;EACD;;EAAA,CA3+CiE,CA4+ClE;;EACA,IAAImJ,gBAAgB,GAAG7c,mBAAmB,CAAC,EAAD,CAA1C;;EACA,IAAI8c,uBAAuB,GAAG,aAAa9c,mBAAmB,CAAC0B,CAApB,CAAsBmb,gBAAtB,CAA3C,CA9+CkE,CAg/ClE;;;EACA,SAASE,eAAT,CAAyBC,QAAzB,EAAmCC,WAAnC,EAAgD;IAAE,IAAI,EAAED,QAAQ,YAAYC,WAAtB,CAAJ,EAAwC;MAAE,MAAM,IAAIC,SAAJ,CAAc,mCAAd,CAAN;IAA2D;EAAE;;EAMzJ,IAAIC,wBAAwB,GAAG,YAAY;IACzC,SAASC,WAAT,CAAqBva,OAArB,EAA8B;MAC5Bka,eAAe,CAAC,IAAD,EAAOK,WAAP,CAAf;;MAEA,KAAKC,SAAL,GAAiB,EAAjB;MACA,KAAKjW,KAAL,GAAa,IAAb;MACA,KAAKkE,KAAL,GAAa,IAAb;MACA,KAAK/D,OAAL,GAAe,IAAf;MACA,KAAKsD,GAAL,GAAW,IAAX;MACA,KAAKoB,UAAL,GAAkB,IAAlB;MAEA,KAAKpD,MAAL,GAAc,IAAd;MACA,KAAKuC,OAAL,GAAe,KAAf;MACA,KAAKC,OAAL,GAAe,KAAf;MACA,KAAKkB,SAAL,GAAiB,IAAjB;MACA,KAAKqB,UAAL,GAAkB,IAAlB;MACA,KAAKQ,eAAL,GAAuB,IAAvB;MACA,KAAKkP,WAAL,GAAmB,IAAnB;MACA,KAAKtP,YAAL,GAAoB,EAApB,CAjB4B,CAiBJ;;MACxB,KAAKE,YAAL,GAAoB,CAApB,CAlB4B,CAkBL;;MACvB,KAAKqP,YAAL,GAAoB,EAApB,CAnB4B,CAmBJ;;MACxB,KAAKC,cAAL,GAAsB,IAAtB,CApB4B,CAoBA;;MAC5B,KAAK5Q,UAAL,GAAkB,IAAlB,CArB4B,CAqBJ;;MACxB,KAAKqB,eAAL,GAAuB,IAAvB,CAtB4B,CAsBC;;MAC7B,KAAKK,WAAL,GAAmBwO,uBAAuB,IAA1C;;MAEA,KAAK,IAAItc,IAAT,IAAiBqC,OAAjB,EAA0B;QACxB,IAAIA,OAAO,CAACb,cAAR,CAAuBxB,IAAvB,CAAJ,EAAkC;UAChC,KAAKA,IAAL,IAAaqC,OAAO,CAACrC,IAAD,CAApB;QACD;MACF;;MAED,IAAI,CAAC,KAAK4G,KAAV,EAAiB;QACf,MAAM,IAAIY,KAAJ,CAAU,oCAAV,CAAN;MACD;;MACD,IAAI,CAAC,KAAKsD,KAAV,EAAiB;QACf,MAAM,IAAItD,KAAJ,CAAU,oCAAV,CAAN;MACD;IACF;;IAEDoV,WAAW,CAACrb,SAAZ,CAAsBma,aAAtB,GAAsC,SAASA,aAAT,GAAyB;MAC7D,IAAIrT,MAAM,GAAG,KAAKA,MAAlB;MACA,IAAIA,MAAM,KAAK,IAAf,EAAqB,OAAO,KAAP;MACrB,IAAI4U,WAAW,GAAG,KAAKrW,KAAL,CAAWqW,WAA7B;;MACA,IAAI,KAAKrW,KAAL,CAAWsW,GAAX,IAAkBD,WAAtB,EAAmC;QACjC,IAAIE,IAAI,GAAGF,WAAW,CAACG,aAAZ,CAA0B,iBAA1B,CAAX;QACA,IAAIC,WAAW,GAAG,KAAKxS,OAAvB;QACA,IAAIA,OAAO,GAAGsS,IAAI,CAACG,YAAL,GAAoB,KAAKlR,UAAvC;QACA,KAAKvB,OAAL,GAAeA,OAAf;QACA,OAAOwS,WAAW,KAAKxS,OAAvB;MACD;;MACD,OAAO,KAAP;IACD,CAZD;;IAcA+R,WAAW,CAACrb,SAAZ,CAAsBgc,SAAtB,GAAkC,SAASA,SAAT,CAAmB7c,KAAnB,EAA0B;MAC1D,IAAIsU,KAAK,GAAG,IAAZ;;MAEA,IAAI8D,IAAI,GAAGtQ,SAAS,CAACzC,MAAV,GAAmB,CAAnB,IAAwByC,SAAS,CAAC,CAAD,CAAT,KAAiBP,SAAzC,GAAqDO,SAAS,CAAC,CAAD,CAA9D,GAAoE,QAA/E;MAEA,IAAIiH,oBAAoB,CAACnJ,CAArB,CAAuB/E,SAAvB,CAAiCic,SAArC,EAAgD;MAChD,IAAItO,EAAE,GAAG,KAAKtI,KAAL,CAAWsW,GAApB;MACAxc,KAAK,GAAGP,MAAM,CAACyP,IAAI,CAAC;MAAI;MAAL,CAAL,CAAN,CAAoClP,KAApC,CAAR;MACA,KAAK2H,MAAL,GAAc3H,KAAd;MAEA,IAAI,CAACwO,EAAD,KAAQxO,KAAK,IAAIA,KAAK,KAAK,CAA3B,CAAJ,EAAmC,OAAO+O,oBAAoB,CAACnJ,CAArB,CAAuBmV,QAAvB,CAAgC,YAAY;QACpF,OAAOzG,KAAK,CAACuI,SAAN,CAAgB7c,KAAhB,EAAuBoY,IAAvB,CAAP;MACD,CAFyC,CAAP;;MAInC,IAAI,OAAOpY,KAAP,KAAiB,QAArB,EAA+B;QAC7BwO,EAAE,CAACpD,KAAH,CAASgN,IAAT,IAAiBpY,KAAK,GAAG,IAAzB;QACA,KAAK+c,eAAL;MACD,CAHD,MAGO,IAAI,OAAO/c,KAAP,KAAiB,QAArB,EAA+B;QACpCwO,EAAE,CAACpD,KAAH,CAASgN,IAAT,IAAiBpY,KAAjB;QACA,KAAK+c,eAAL;MACD;IACF,CArBD;;IAuBAb,WAAW,CAACrb,SAAZ,CAAsBmc,YAAtB,GAAqC,SAASA,YAAT,CAAsBhd,KAAtB,EAA6B;MAChE,KAAK6c,SAAL,CAAe7c,KAAf,EAAsB,YAAtB;IACD,CAFD;;IAIAkc,WAAW,CAACrb,SAAZ,CAAsBoc,iBAAtB,GAA0C,SAASA,iBAAT,GAA6B;MACrE,IAAIC,cAAc,GAAG,EAArB;MACA,IAAI7W,OAAO,GAAG,KAAKH,KAAL,CAAWG,OAAzB;MACAA,OAAO,CAACC,OAAR,CAAgB,UAAUF,MAAV,EAAkB;QAChC,IAAIA,MAAM,CAAC+W,aAAX,EAA0B;UACxBD,cAAc,CAACzU,IAAf,CAAoBN,KAApB,CAA0B+U,cAA1B,EAA0C9W,MAAM,CAACC,OAAjD;QACD,CAFD,MAEO;UACL6W,cAAc,CAACzU,IAAf,CAAoBrC,MAApB;QACD;MACF,CAND;MAQA,OAAO8W,cAAP;IACD,CAZD;;IAcAhB,WAAW,CAACrb,SAAZ,CAAsBkc,eAAtB,GAAwC,SAASA,eAAT,GAA2B;MACjE,IAAIK,MAAM,GAAG,IAAb;;MAEA,IAAI,CAAC,KAAKlX,KAAL,CAAWiU,MAAhB,EAAwB,OAAOpL,oBAAoB,CAACnJ,CAArB,CAAuBmV,QAAvB,CAAgC,YAAY;QACzE,OAAOqC,MAAM,CAACL,eAAP,EAAP;MACD,CAF8B,CAAP;MAGxB,IAAIjE,YAAY,GAAG,KAAK5S,KAAL,CAAW6S,KAA9B;MAAA,IACIsE,aAAa,GAAGvE,YAAY,CAACuE,aADjC;MAAA,IAEIC,aAAa,GAAGxE,YAAY,CAACwE,aAFjC;MAAA,IAGIC,aAAa,GAAGzE,YAAY,CAACyE,aAHjC;MAKA,KAAKvQ,YAAL,GAAoBsQ,aAAa,GAAGA,aAAa,CAACV,YAAjB,GAAgC,CAAjE;MAEA,IAAI,KAAK7R,UAAL,IAAmB,CAACsS,aAAxB,EAAuC,OAb0B,CAejE;;MACA,IAAIG,WAAW,GAAGH,aAAa,GAAGA,aAAa,CAACX,aAAd,CAA4B,sBAA5B,CAAH,GAAyD,IAAxF;MACA,IAAIe,UAAU,GAAG,KAAKC,iBAAL,CAAuBF,WAAvB,CAAjB;MAEA,IAAI1Q,YAAY,GAAG,KAAKA,YAAL,GAAoB,CAAC,KAAK/B,UAAN,GAAmB,CAAnB,GAAuBsS,aAAa,CAACT,YAA5E;;MACA,IAAI,KAAK7R,UAAL,IAAmB,CAAC0S,UAApB,IAAkCJ,aAAa,CAACM,WAAd,GAA4B,CAA9D,IAAmE,CAAC,KAAKzX,KAAL,CAAWG,OAAX,IAAsB,EAAvB,EAA2BhB,MAA3B,GAAoC,CAAvG,IAA4GyH,YAAY,GAAG,CAA/H,EAAkI;QAChI,OAAOiC,oBAAoB,CAACnJ,CAArB,CAAuBmV,QAAvB,CAAgC,YAAY;UACjD,OAAOqC,MAAM,CAACL,eAAP,EAAP;QACD,CAFM,CAAP;MAGD;;MACD,IAAIX,WAAW,GAAG,KAAKA,WAAL,GAAmB,KAAKlW,KAAL,CAAWsW,GAAX,CAAeoB,YAApD;MACA,IAAIvB,YAAY,GAAG,KAAKA,YAAL,GAAoBkB,aAAa,GAAGA,aAAa,CAACX,YAAjB,GAAgC,CAApF;;MACA,IAAI,KAAKjV,MAAL,KAAgB,IAApB,EAA0B;QACxB,KAAK+D,UAAL,GAAkB0Q,WAAW,GAAGtP,YAAd,GAA6BuP,YAA7B,IAA6CkB,aAAa,GAAG,CAAH,GAAO,CAAjE,CAAlB;MACD;;MACD,KAAKxQ,eAAL,GAAuB,KAAK7C,OAAL,GAAe,KAAKwB,UAAL,GAAkB,KAAK0B,WAAtC,GAAoD,KAAK1B,UAAhF;MAEA,IAAImS,MAAM,GAAG,EAAE,KAAKzT,KAAL,CAAWC,MAAX,CAAkBE,IAAlB,IAA0B,KAAKH,KAAL,CAAWC,MAAX,CAAkBE,IAAlB,CAAuBlF,MAAnD,CAAb;MACA,KAAKiX,cAAL,GAAsB,KAAKpS,OAAL,GAAekS,WAAW,IAAIyB,MAAM,GAAG,CAAH,GAAO,KAAKzQ,WAAtB,CAA1B,GAA+DgP,WAArF;MACA,KAAKpB,aAAL;MACA,KAAK8C,eAAL,CAAqB,YAArB;IACD,CApCD;;IAsCA5B,WAAW,CAACrb,SAAZ,CAAsB6c,iBAAtB,GAA0C,SAASA,iBAAT,CAA2BK,GAA3B,EAAgC;MACxE,IAAI,CAACA,GAAL,EAAU,OAAO,IAAP;MACV,IAAIC,WAAW,GAAGD,GAAlB;;MACA,OAAOC,WAAW,CAACtZ,OAAZ,KAAwB,KAA/B,EAAsC;QACpC,IAAIuZ,gBAAgB,CAACD,WAAD,CAAhB,CAA8BE,OAA9B,KAA0C,MAA9C,EAAsD;UACpD,OAAO,IAAP;QACD;;QACDF,WAAW,GAAGA,WAAW,CAACG,aAA1B;MACD;;MACD,OAAO,KAAP;IACD,CAVD;;IAYAjC,WAAW,CAACrb,SAAZ,CAAsBud,kBAAtB,GAA2C,SAASA,kBAAT,GAA8B;MACvE,IAAIrP,oBAAoB,CAACnJ,CAArB,CAAuB/E,SAAvB,CAAiCic,SAArC,EAAgD;MAChD,IAAInT,GAAG,GAAG,KAAKA,GAAf;MACA,IAAI0B,SAAS,GAAG,KAAKnF,KAAL,CAAWsW,GAAX,CAAe6B,WAA/B;MACA,IAAIC,YAAY,GAAG,CAAnB;MAEA,IAAIpB,cAAc,GAAG,KAAKD,iBAAL,EAArB;MACA,IAAIsB,WAAW,GAAGrB,cAAc,CAAC3G,MAAf,CAAsB,UAAUnQ,MAAV,EAAkB;QACxD,OAAO,OAAOA,MAAM,CAACkB,KAAd,KAAwB,QAA/B;MACD,CAFiB,CAAlB;MAIA4V,cAAc,CAAC5W,OAAf,CAAuB,UAAUF,MAAV,EAAkB;QACvC;QACA,IAAI,OAAOA,MAAM,CAACkB,KAAd,KAAwB,QAAxB,IAAoClB,MAAM,CAACoY,SAA/C,EAA0DpY,MAAM,CAACoY,SAAP,GAAmB,IAAnB;MAC3D,CAHD;;MAKA,IAAID,WAAW,CAAClZ,MAAZ,GAAqB,CAArB,IAA0BsE,GAA9B,EAAmC;QACjCuT,cAAc,CAAC5W,OAAf,CAAuB,UAAUF,MAAV,EAAkB;UACvCkY,YAAY,IAAIlY,MAAM,CAACkB,KAAP,IAAgBlB,MAAM,CAACsB,QAAvB,IAAmC,EAAnD;QACD,CAFD;QAIA,IAAI+W,YAAY,GAAG,KAAKtU,OAAL,GAAe,KAAKiD,WAApB,GAAkC,CAArD;;QAEA,IAAIkR,YAAY,IAAIjT,SAAS,GAAGoT,YAAhC,EAA8C;UAC5C;UACA,KAAKvU,OAAL,GAAe,KAAf;UAEA,IAAIwU,cAAc,GAAGrT,SAAS,GAAGoT,YAAZ,GAA2BH,YAAhD;;UAEA,IAAIC,WAAW,CAAClZ,MAAZ,KAAuB,CAA3B,EAA8B;YAC5BkZ,WAAW,CAAC,CAAD,CAAX,CAAeC,SAAf,GAA2B,CAACD,WAAW,CAAC,CAAD,CAAX,CAAe7W,QAAf,IAA2B,EAA5B,IAAkCgX,cAA7D;UACD,CAFD,MAEO;YACL,IAAIC,eAAe,GAAGJ,WAAW,CAACrW,MAAZ,CAAmB,UAAU0H,IAAV,EAAgBxJ,MAAhB,EAAwB;cAC/D,OAAOwJ,IAAI,IAAIxJ,MAAM,CAACsB,QAAP,IAAmB,EAAvB,CAAX;YACD,CAFqB,EAEnB,CAFmB,CAAtB;YAGA,IAAIkX,iBAAiB,GAAGF,cAAc,GAAGC,eAAzC;YACA,IAAIE,cAAc,GAAG,CAArB;YAEAN,WAAW,CAACjY,OAAZ,CAAoB,UAAUF,MAAV,EAAkBb,KAAlB,EAAyB;cAC3C,IAAIA,KAAK,KAAK,CAAd,EAAiB;cACjB,IAAIuZ,SAAS,GAAGC,IAAI,CAACC,KAAL,CAAW,CAAC5Y,MAAM,CAACsB,QAAP,IAAmB,EAApB,IAA0BkX,iBAArC,CAAhB;cACAC,cAAc,IAAIC,SAAlB;cACA1Y,MAAM,CAACoY,SAAP,GAAmB,CAACpY,MAAM,CAACsB,QAAP,IAAmB,EAApB,IAA0BoX,SAA7C;YACD,CALD;YAOAP,WAAW,CAAC,CAAD,CAAX,CAAeC,SAAf,GAA2B,CAACD,WAAW,CAAC,CAAD,CAAX,CAAe7W,QAAf,IAA2B,EAA5B,IAAkCgX,cAAlC,GAAmDG,cAA9E;UACD;QACF,CAxBD,MAwBO;UACL;UACA,KAAK3U,OAAL,GAAe,IAAf;UACAqU,WAAW,CAACjY,OAAZ,CAAoB,UAAUF,MAAV,EAAkB;YACpCA,MAAM,CAACoY,SAAP,GAAmBpY,MAAM,CAACsB,QAA1B;UACD,CAFD;QAGD;;QAED,KAAK2D,SAAL,GAAiB0T,IAAI,CAACE,GAAL,CAASX,YAAT,EAAuBjT,SAAvB,CAAjB;QACA,KAAKnF,KAAL,CAAWgZ,WAAX,CAAuB5X,KAAvB,GAA+B,KAAK+D,SAApC;MACD,CAzCD,MAyCO;QACL6R,cAAc,CAAC5W,OAAf,CAAuB,UAAUF,MAAV,EAAkB;UACvC,IAAI,CAACA,MAAM,CAACkB,KAAR,IAAiB,CAAClB,MAAM,CAACsB,QAA7B,EAAuC;YACrCtB,MAAM,CAACoY,SAAP,GAAmB,EAAnB;UACD,CAFD,MAEO;YACLpY,MAAM,CAACoY,SAAP,GAAmBpY,MAAM,CAACkB,KAAP,IAAgBlB,MAAM,CAACsB,QAA1C;UACD;;UAED4W,YAAY,IAAIlY,MAAM,CAACoY,SAAvB;QACD,CARD;QASA,KAAKtU,OAAL,GAAeoU,YAAY,GAAGjT,SAA9B;QAEA,KAAKA,SAAL,GAAiBiT,YAAjB;MACD;;MAED,IAAI9R,YAAY,GAAG,KAAKpC,KAAL,CAAWC,MAAX,CAAkBmC,YAArC;;MAEA,IAAIA,YAAY,CAACnH,MAAb,GAAsB,CAA1B,EAA6B;QAC3B,IAAIqH,UAAU,GAAG,CAAjB;QACAF,YAAY,CAAClG,OAAb,CAAqB,UAAUF,MAAV,EAAkB;UACrCsG,UAAU,IAAItG,MAAM,CAACoY,SAAP,IAAoBpY,MAAM,CAACkB,KAAzC;QACD,CAFD;QAIA,KAAKoF,UAAL,GAAkBA,UAAlB;MACD;;MAED,IAAIO,iBAAiB,GAAG,KAAK7C,KAAL,CAAWC,MAAX,CAAkB4C,iBAA1C;;MACA,IAAIA,iBAAiB,CAAC5H,MAAlB,GAA2B,CAA/B,EAAkC;QAChC,IAAI6H,eAAe,GAAG,CAAtB;QACAD,iBAAiB,CAAC3G,OAAlB,CAA0B,UAAUF,MAAV,EAAkB;UAC1C8G,eAAe,IAAI9G,MAAM,CAACoY,SAAP,IAAoBpY,MAAM,CAACkB,KAA9C;QACD,CAFD;QAIA,KAAK4F,eAAL,GAAuBA,eAAvB;MACD;;MAED,KAAK4Q,eAAL,CAAqB,SAArB;IACD,CA9FD;;IAgGA5B,WAAW,CAACrb,SAAZ,CAAsBse,WAAtB,GAAoC,SAASA,WAAT,CAAqBC,QAArB,EAA+B;MACjE,KAAKjD,SAAL,CAAe1T,IAAf,CAAoB2W,QAApB;IACD,CAFD;;IAIAlD,WAAW,CAACrb,SAAZ,CAAsBwe,cAAtB,GAAuC,SAASA,cAAT,CAAwBD,QAAxB,EAAkC;MACvE,IAAI7Z,KAAK,GAAG,KAAK4W,SAAL,CAAepV,OAAf,CAAuBqY,QAAvB,CAAZ;;MACA,IAAI7Z,KAAK,KAAK,CAAC,CAAf,EAAkB;QAChB,KAAK4W,SAAL,CAAexT,MAAf,CAAsBpD,KAAtB,EAA6B,CAA7B;MACD;IACF,CALD;;IAOA2W,WAAW,CAACrb,SAAZ,CAAsBid,eAAtB,GAAwC,SAASA,eAAT,CAAyBvZ,KAAzB,EAAgC;MACtE,IAAI+a,MAAM,GAAG,IAAb;;MAEA,IAAInD,SAAS,GAAG,KAAKA,SAArB;MACAA,SAAS,CAAC7V,OAAV,CAAkB,UAAU8Y,QAAV,EAAoB;QACpC,QAAQ7a,KAAR;UACE,KAAK,SAAL;YACE6a,QAAQ,CAACG,eAAT,CAAyBD,MAAzB;YACA;;UACF,KAAK,YAAL;YACEF,QAAQ,CAACI,kBAAT,CAA4BF,MAA5B;YACA;;UACF;YACE,MAAM,IAAIxY,KAAJ,CAAU,oCAAoCvC,KAApC,GAA4C,GAAtD,CAAN;QARJ;MAUD,CAXD;IAYD,CAhBD;;IAkBA,OAAO2X,WAAP;EACD,CA/Q8B,EAA/B;EAiRA;;;EAA6B,IAAIuD,YAAY,GAAIxD,wBAApB,CAxwDqC,CAywDlE;;EACA,IAAIyD,IAAI,GAAG5gB,mBAAmB,CAAC,CAAD,CAA9B,CA1wDkE,CA4wDlE;;;EACA,IAAI6gB,QAAQ,GAAG7gB,mBAAmB,CAAC,EAAD,CAAlC;;EACA,IAAI8gB,eAAe,GAAG,aAAa9gB,mBAAmB,CAAC0B,CAApB,CAAsBmf,QAAtB,CAAnC,CA9wDkE,CAgxDlE;;EACA;;;EAA6B,IAAIE,eAAe,GAAI;IAClDC,OAAO,EAAE,SAASA,OAAT,GAAmB;MAC1B,KAAKC,WAAL,CAAiBZ,WAAjB,CAA6B,IAA7B;IACD,CAHiD;IAIlDa,SAAS,EAAE,SAASA,SAAT,GAAqB;MAC9B,KAAKD,WAAL,CAAiBV,cAAjB,CAAgC,IAAhC;IACD,CANiD;IASlDlN,QAAQ,EAAE;MACR4N,WAAW,EAAE,SAASA,WAAT,GAAuB;QAClC,IAAI9V,MAAM,GAAG,KAAKA,MAAlB;;QACA,IAAI,CAACA,MAAD,IAAW,KAAK/D,KAApB,EAA2B;UACzB+D,MAAM,GAAG,KAAK/D,KAAL,CAAW+D,MAApB;QACD;;QACD,IAAI,CAACA,MAAL,EAAa;UACX,MAAM,IAAInD,KAAJ,CAAU,4BAAV,CAAN;QACD;;QACD,OAAOmD,MAAP;MACD;IAVO,CATwC;IAsBlDgW,OAAO,EAAE,SAASA,OAAT,GAAmB;MAC1B,KAAKV,eAAL,CAAqB,KAAKQ,WAA1B;MACA,KAAKP,kBAAL,CAAwB,KAAKO,WAA7B;IACD,CAzBiD;IA0BlDG,OAAO,EAAE,SAASA,OAAT,GAAmB;MAC1B,IAAI,KAAKC,WAAT,EAAsB;MACtB,KAAKZ,eAAL,CAAqB,KAAKQ,WAA1B;MACA,KAAKP,kBAAL,CAAwB,KAAKO,WAA7B;MACA,KAAKI,WAAL,GAAmB,IAAnB;IACD,CA/BiD;IAkClD7Q,OAAO,EAAE;MACPiQ,eAAe,EAAE,SAASA,eAAT,CAAyBtV,MAAzB,EAAiC;QAChD,IAAImW,IAAI,GAAG,KAAK5D,GAAL,CAAS6D,gBAAT,CAA0B,gBAA1B,CAAX;QACA,IAAI,CAACD,IAAI,CAAC/a,MAAV,EAAkB;QAClB,IAAI6X,cAAc,GAAGjT,MAAM,CAACgT,iBAAP,EAArB;QACA,IAAIqD,UAAU,GAAG,EAAjB;QACApD,cAAc,CAAC5W,OAAf,CAAuB,UAAUF,MAAV,EAAkB;UACvCka,UAAU,CAACla,MAAM,CAACG,EAAR,CAAV,GAAwBH,MAAxB;QACD,CAFD;;QAGA,KAAK,IAAIpH,CAAC,GAAG,CAAR,EAAW8Y,CAAC,GAAGsI,IAAI,CAAC/a,MAAzB,EAAiCrG,CAAC,GAAG8Y,CAArC,EAAwC9Y,CAAC,EAAzC,EAA6C;UAC3C,IAAIkZ,GAAG,GAAGkI,IAAI,CAACphB,CAAD,CAAd;UACA,IAAIM,IAAI,GAAG4Y,GAAG,CAACqI,YAAJ,CAAiB,MAAjB,CAAX;UACA,IAAIna,MAAM,GAAGka,UAAU,CAAChhB,IAAD,CAAvB;;UACA,IAAI8G,MAAJ,EAAY;YACV8R,GAAG,CAACsI,YAAJ,CAAiB,OAAjB,EAA0Bpa,MAAM,CAACoY,SAAP,IAAoBpY,MAAM,CAACkB,KAArD;UACD;QACF;MACF,CAjBM;MAkBPkY,kBAAkB,EAAE,SAASA,kBAAT,CAA4BvV,MAA5B,EAAoC;QACtD,IAAImW,IAAI,GAAG,KAAK5D,GAAL,CAAS6D,gBAAT,CAA0B,6BAA1B,CAAX;;QACA,KAAK,IAAIrhB,CAAC,GAAG,CAAR,EAAW8Y,CAAC,GAAGsI,IAAI,CAAC/a,MAAzB,EAAiCrG,CAAC,GAAG8Y,CAArC,EAAwC9Y,CAAC,EAAzC,EAA6C;UAC3C,IAAIkZ,GAAG,GAAGkI,IAAI,CAACphB,CAAD,CAAd;UACAkZ,GAAG,CAACsI,YAAJ,CAAiB,OAAjB,EAA0BvW,MAAM,CAACE,OAAP,GAAiBF,MAAM,CAACmD,WAAxB,GAAsC,GAAhE;QACD;;QACD,IAAIqT,GAAG,GAAG,KAAKjE,GAAL,CAAS6D,gBAAT,CAA0B,WAA1B,CAAV;;QACA,KAAK,IAAIK,EAAE,GAAG,CAAT,EAAYC,EAAE,GAAGF,GAAG,CAACpb,MAA1B,EAAkCqb,EAAE,GAAGC,EAAvC,EAA2CD,EAAE,EAA7C,EAAiD;UAC/C,IAAIE,EAAE,GAAGH,GAAG,CAACC,EAAD,CAAZ;UACAE,EAAE,CAACxV,KAAH,CAAS9D,KAAT,GAAiB2C,MAAM,CAACE,OAAP,GAAiBF,MAAM,CAACmD,WAAP,GAAqB,IAAtC,GAA6C,GAA9D;UACAwT,EAAE,CAACxV,KAAH,CAAS8S,OAAT,GAAmBjU,MAAM,CAACE,OAAP,GAAiB,EAAjB,GAAsB,MAAzC;QACD;MACF;IA9BM;EAlCyC,CAAvB,CAjxDqC,CAo1DlE;;EACA,IAAI0W,iBAAiB,GAAGphB,MAAM,CAACgS,MAAP,IAAiB,UAAUhN,MAAV,EAAkB;IAAE,KAAK,IAAIzF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8I,SAAS,CAACzC,MAA9B,EAAsCrG,CAAC,EAAvC,EAA2C;MAAE,IAAI0S,MAAM,GAAG5J,SAAS,CAAC9I,CAAD,CAAtB;;MAA2B,KAAK,IAAIsB,GAAT,IAAgBoR,MAAhB,EAAwB;QAAE,IAAIjS,MAAM,CAACoB,SAAP,CAAiBC,cAAjB,CAAgC5B,IAAhC,CAAqCwS,MAArC,EAA6CpR,GAA7C,CAAJ,EAAuD;UAAEmE,MAAM,CAACnE,GAAD,CAAN,GAAcoR,MAAM,CAACpR,GAAD,CAApB;QAA4B;MAAE;IAAE;;IAAC,OAAOmE,MAAP;EAAgB,CAAzQ;EAGA;;;EAA6B,IAAIqc,SAAS,GAAI;IAC5CxhB,IAAI,EAAE,YADsC;IAE5CyhB,KAAK,EAAE,CAAC,SAAD,EAAY,KAAZ,EAAmB,OAAnB,EAA4B,YAA5B,EAA0C,YAA1C,EAAwD,OAAxD,EAAiE,SAAjE,EAA4E,yBAA5E,EAAuG,aAAvG,EAAsH,YAAtH,EAAoI,eAApI,EAAqJ,SAArJ,EAAgK,qBAAhK,EAAuL,cAAvL,EAAuM,cAAvM,EAAuN,sBAAvN,EAA+O,sBAA/O,EAAuQ,OAAvQ,CAFqC;IAG5CC,UAAU,EAAE;MACVC,UAAU,EAAEzT,gBAAgB,CAAC5H;IADnB,CAHgC;IAM5CxE,MAAM,EAAE,SAASA,MAAT,GAAkB;MACxB,IAAIkT,KAAK,GAAG,IAAZ;;MAEA,IAAIxR,CAAC,GAAGgF,SAAS,CAAC,CAAD,CAAjB;MACA,IAAIzB,OAAO,GAAG,KAAKA,OAAnB;MAAA,IACIO,GAAG,GAAG,KAAKA,GADf;MAAA,IAEIsa,MAAM,GAAG,KAAK3b,KAFlB;MAAA,IAGI6E,KAAK,GAAG,KAAKA,KAHjB;MAAA,IAIIpI,OAAO,GAAG,KAAKA,OAJnB;MAAA,IAKImf,uBAAuB,GAAG,KAAKA,uBALnC;MAAA,IAMIC,WAAW,GAAG,KAAKA,WANvB;MAAA,IAOIC,UAAU,GAAG,KAAKA,UAPtB;MAAA,IAQIC,cAAc,GAAG,KAAKC,aAR1B;MAAA,IASIA,aAAa,GAAGD,cAAc,KAAK/Z,SAAnB,GAA+B,EAA/B,GAAoC+Z,cATxD;MAAA,IAUIzK,UAAU,GAAG,KAAKA,UAVtB;MAAA,IAWI2K,UAAU,GAAG,KAAKA,UAXtB;MAcA,OAAO1e,CAAC,CAAC,IAAD,EAAO,CAACuD,OAAO,CAACb,GAAR,CAAY,UAAUY,MAAV,EAAkBqb,SAAlB,EAA6B;QACvD,IAAIC,QAAQ,GAAGpN,KAAK,CAACqN,OAAN,CAAc/a,GAAd,EAAmBR,MAAnB,EAA2B8a,MAA3B,EAAmCO,SAAnC,CAAf;QAAA,IACIG,OAAO,GAAGF,QAAQ,CAACE,OADvB;QAAA,IAEIC,OAAO,GAAGH,QAAQ,CAACG,OAFvB;;QAIA,IAAI,CAACD,OAAD,IAAY,CAACC,OAAjB,EAA0B;UACxB,OAAO,IAAP;QACD;;QACD,IAAIC,UAAU,GAAGjB,iBAAiB,CAAC,EAAD,EAAKza,MAAL,CAAlC;QACA0b,UAAU,CAACtD,SAAX,GAAuBlK,KAAK,CAACyN,mBAAN,CAA0B1b,OAA1B,EAAmCwb,OAAnC,EAA4CJ,SAA5C,CAAvB;QACA,IAAIlX,IAAI,GAAG;UACTH,KAAK,EAAEA,KADE;UAETyM,UAAU,EAAEA,UAFH;UAGT2K,UAAU,EAAEA,UAHH;UAIThY,KAAK,EAAExH,OAJE;UAKToE,MAAM,EAAE0b,UALC;UAMTlb,GAAG,EAAEA,GANI;UAOTsa,MAAM,EAAEA;QAPC,CAAX;;QASA,IAAIO,SAAS,KAAKN,uBAAd,IAAyCC,WAA7C,EAA0D;UACxD7W,IAAI,CAAC8J,QAAL,GAAgB;YACdvC,MAAM,EAAEsP,WAAW,CAACjY,KAAZ,GAAoBkY,UADd;YAEdlY,KAAK,EAAEiY,WAAW,CAACjY;UAFL,CAAhB;;UAIA,IAAI,OAAOiY,WAAW,CAACpR,QAAnB,KAAgC,SAApC,EAA+C;YAC7CzF,IAAI,CAAC8J,QAAL,CAAcrE,QAAd,GAAyBoR,WAAW,CAACpR,QAArC,CAD6C,CAE7C;;YACA,IAAI,aAAaoR,WAAjB,EAA8B;cAC5B7W,IAAI,CAAC8J,QAAL,CAAcd,OAAd,GAAwB6N,WAAW,CAAC7N,OAApC;YACD;;YACD,IAAI,oBAAoB6N,WAAxB,EAAqC;cACnC7W,IAAI,CAAC8J,QAAL,CAAc2N,cAAd,GAA+BZ,WAAW,CAACY,cAA3C;YACD;UACF;QACF;;QACD,OAAOlf,CAAC,CACN,IADM,EAEN;UACEsI,KAAK,EAAEkJ,KAAK,CAAC2N,YAAN,CAAmBf,MAAnB,EAA2BO,SAA3B,EAAsC7a,GAAtC,EAA2CR,MAA3C,CADT;UAEE,SAASkO,KAAK,CAAC4N,YAAN,CAAmBhB,MAAnB,EAA2BO,SAA3B,EAAsC7a,GAAtC,EAA2CR,MAA3C,CAFX;UAGEkF,KAAK,EAAE;YAAEsW,OAAO,EAAEA,OAAX;YACLC,OAAO,EAAEA;UADJ,CAHT;UAMEpX,EAAE,EAAE;YACF,cAAc,SAAS0X,UAAT,CAAoBxX,MAApB,EAA4B;cACxC,OAAO2J,KAAK,CAAC8N,oBAAN,CAA2BzX,MAA3B,EAAmC/D,GAAnC,CAAP;YACD,CAHC;YAIF,cAAc0N,KAAK,CAAC+N;UAJlB;QANN,CAFM,EAeN,CAACjc,MAAM,CAACkc,UAAP,CAAkBpjB,IAAlB,CAAuBoV,KAAK,CAACiO,YAA7B,EAA2CjO,KAAK,CAAChL,cAAjD,EAAiEiB,IAAjE,EAAuEgX,aAAa,CAACE,SAAD,CAApF,CAAD,CAfM,CAAR;MAiBD,CApDe,CAAD,CAAP,CAAR;IAqDD;EA7E2C,CAAjB,CAx1DqC,CAu6DlE;;EACA,IAAItd,OAAO,GAAG,OAAOrE,MAAP,KAAkB,UAAlB,IAAgC,OAAOA,MAAM,CAACsE,QAAd,KAA2B,QAA3D,GAAsE,UAAUC,GAAV,EAAe;IAAE,OAAO,OAAOA,GAAd;EAAoB,CAA3G,GAA8G,UAAUA,GAAV,EAAe;IAAE,OAAOA,GAAG,IAAI,OAAOvE,MAAP,KAAkB,UAAzB,IAAuCuE,GAAG,CAACC,WAAJ,KAAoBxE,MAA3D,IAAqEuE,GAAG,KAAKvE,MAAM,CAACe,SAApF,GAAgG,QAAhG,GAA2G,OAAOwD,GAAzH;EAA+H,CAA5Q;;EAEA,IAAIme,kBAAkB,GAAG/iB,MAAM,CAACgS,MAAP,IAAiB,UAAUhN,MAAV,EAAkB;IAAE,KAAK,IAAIzF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8I,SAAS,CAACzC,MAA9B,EAAsCrG,CAAC,EAAvC,EAA2C;MAAE,IAAI0S,MAAM,GAAG5J,SAAS,CAAC9I,CAAD,CAAtB;;MAA2B,KAAK,IAAIsB,GAAT,IAAgBoR,MAAhB,EAAwB;QAAE,IAAIjS,MAAM,CAACoB,SAAP,CAAiBC,cAAjB,CAAgC5B,IAAhC,CAAqCwS,MAArC,EAA6CpR,GAA7C,CAAJ,EAAuD;UAAEmE,MAAM,CAACnE,GAAD,CAAN,GAAcoR,MAAM,CAACpR,GAAD,CAApB;QAA4B;MAAE;IAAE;;IAAC,OAAOmE,MAAP;EAAgB,CAA1Q;EAYA;;;EAA6B,IAAIge,UAAU,GAAI;IAC7CnjB,IAAI,EAAE,aADuC;IAG7C+W,MAAM,EAAE,CAACwJ,eAAD,CAHqC;IAK7CmB,UAAU,EAAE;MACVC,UAAU,EAAEzT,gBAAgB,CAAC5H,CADnB;MAEV8c,SAAS,EAAE9C,eAAe,CAACha,CAFjB;MAGV+c,QAAQ,EAAE7B;IAHA,CALiC;IAW7CC,KAAK,EAAE;MACL3W,KAAK,EAAE;QACLwY,QAAQ,EAAE;MADL,CADF;MAILhZ,MAAM,EAAEiZ,OAJH;MAKL7gB,OAAO,EAAE,EALJ;MAML2J,YAAY,EAAE,CAACmX,MAAD,EAASC,QAAT,CANT;MAOLnX,QAAQ,EAAE,CAACnM,MAAD,EAASsjB,QAAT,CAPL;MAQLnW,KAAK,EAAEkW,MARF;MASLjX,SAAS,EAAEgX;IATN,CAXsC;IAuB7CzhB,MAAM,EAAE,SAASA,MAAT,CAAgB0B,CAAhB,EAAmB;MACzB,IAAIwR,KAAK,GAAG,IAAZ;;MAEA,IAAI/J,IAAI,GAAG,KAAKA,IAAL,IAAa,EAAxB;MACA,OAAOzH,CAAC,CACN,OADM,EAEN;QACE,SAAS,gBADX;QAEEwI,KAAK,EAAE;UAAE0X,WAAW,EAAE,GAAf;UACLC,WAAW,EAAE,GADR;UAELpZ,MAAM,EAAE;QAFH;MAFT,CAFM,EAQN,CAAC/G,CAAC,CAAC,UAAD,EAAa,CAAC,KAAKuD,OAAL,CAAab,GAAb,CAAiB,UAAUY,MAAV,EAAkB;QACjD,OAAOtD,CAAC,CAAC,KAAD,EAAQ;UACdwI,KAAK,EAAE;YAAEhM,IAAI,EAAE8G,MAAM,CAACG;UAAf,CADO;UAEdjG,GAAG,EAAE8F,MAAM,CAACG;QAFE,CAAR,CAAR;MAGD,CAJe,CAAD,CAAb,CAAF,EAIMzD,CAAC,CAAC,OAAD,EAAU,CAACyH,IAAI,CAACrC,MAAL,CAAY,UAAUgb,GAAV,EAAetc,GAAf,EAAoB;QAChD,OAAOsc,GAAG,CAACjgB,MAAJ,CAAWqR,KAAK,CAAC6O,gBAAN,CAAuBvc,GAAvB,EAA4Bsc,GAAG,CAAC7d,MAAhC,CAAX,CAAP;MACD,CAFiB,EAEf,EAFe,CAAD,EAETvC,CAAC,CAAC,YAAD,EAAe;QACtBwI,KAAK,EAAE;UAAE8X,MAAM,EAAE,KAAKld,KAAL,CAAWmd,aAArB;UAAoCC,SAAS,EAAE,KAA/C;UAAsDC,OAAO,EAAE,KAAKC;QAApE,CADe;QAEtB3Y,GAAG,EAAE;MAFiB,CAAf,CAFQ,CAAV,CAJP,CARM,CAAR;IAkBD,CA7C4C;IAgD7CsH,QAAQ,EAAEqQ,kBAAkB,CAAC;MAC3Btc,KAAK,EAAE,SAASA,KAAT,GAAiB;QACtB,OAAO,KAAKud,OAAZ;MACD;IAH0B,CAAD,EAIzBnI,SAAS,CAAC;MACX/Q,IAAI,EAAE,MADK;MAEXlE,OAAO,EAAE,SAFE;MAGXgb,UAAU,EAAE,QAHD;MAIXqC,kBAAkB,EAAE,wBAJT;MAKXC,mBAAmB,EAAE,6BALV;MAMXC,YAAY,EAAE,SAASA,YAAT,CAAsBvZ,MAAtB,EAA8B;QAC1C,OAAOA,MAAM,CAAChE,OAAP,CAAehB,MAAtB;MACD,CARU;MASXwe,cAAc,EAAE,SAASA,cAAT,CAAwBxZ,MAAxB,EAAgC;QAC9C,OAAOA,MAAM,CAACmC,YAAP,CAAoBnH,MAA3B;MACD,CAXU;MAYXye,eAAe,EAAE,SAASA,eAAT,CAAyBzZ,MAAzB,EAAiC;QAChD,OAAOA,MAAM,CAAC4C,iBAAP,CAAyB5H,MAAhC;MACD,CAdU;MAeXyU,eAAe,EAAE,SAASA,eAAT,CAAyBzP,MAAzB,EAAiC;QAChD,OAAOA,MAAM,CAAChE,OAAP,CAAemS,IAAf,CAAoB,UAAUrF,IAAV,EAAgB;UACzC,IAAIqD,IAAI,GAAGrD,IAAI,CAACqD,IAAhB;UACA,OAAOA,IAAI,KAAK,QAAhB;QACD,CAHM,CAAP;MAID;IApBU,CAAD,CAJgB,EAyBxB;MACF+K,aAAa,EAAE,SAASA,aAAT,GAAyB;QACtC,IAAInE,MAAM,GAAG,IAAb;;QAEA,OAAO,KAAK/W,OAAL,CAAab,GAAb,CAAiB,UAAUY,MAAV,EAAkBb,KAAlB,EAAyB;UAC/C,OAAO6X,MAAM,CAAC2G,cAAP,CAAsBxe,KAAtB,CAAP;QACD,CAFM,CAAP;MAGD,CAPC;MAQF4b,uBAAuB,EAAE,SAASA,uBAAT,GAAmC;QAC1D,OAAO1hB,MAAM,CAACqR,KAAK,CAAC,gBAAD,CAAN,CAAN,CAAgC,KAAKzK,OAArC,EAA8C,UAAUqN,KAAV,EAAiB;UACpE,IAAI8C,IAAI,GAAG9C,KAAK,CAAC8C,IAAjB;UACA,OAAOA,IAAI,KAAK,SAAhB;QACD,CAHM,CAAP;MAID;IAbC,CAzBwB,CAhDiB;IAyF7C/D,KAAK,EAAE;MACL;MACA;MACA,yBAAyB,SAASuR,mBAAT,CAA6B3b,MAA7B,EAAqC4b,MAArC,EAA6C;QACpE,IAAI3E,MAAM,GAAG,IAAb;;QAEA,IAAI,CAAC,KAAKlV,KAAL,CAAWC,MAAX,CAAkBC,SAAnB,IAAgC,KAAKwS,SAAzC,EAAoD;QACpD,IAAIoH,GAAG,GAAGC,MAAM,CAACC,qBAAjB;;QACA,IAAI,CAACF,GAAL,EAAU;UACRA,GAAG,GAAG,SAASA,GAAT,CAAa1I,EAAb,EAAiB;YACrB,OAAO6I,UAAU,CAAC7I,EAAD,EAAK,EAAL,CAAjB;UACD,CAFD;QAGD;;QACD0I,GAAG,CAAC,YAAY;UACd,IAAII,IAAI,GAAGhF,MAAM,CAAC9C,GAAP,CAAW6D,gBAAX,CAA4B,gBAA5B,CAAX;;UACA,IAAIkE,MAAM,GAAGD,IAAI,CAACL,MAAD,CAAjB;UACA,IAAIO,MAAM,GAAGF,IAAI,CAACjc,MAAD,CAAjB;;UACA,IAAIkc,MAAJ,EAAY;YACV9kB,MAAM,CAACigB,IAAI,CAAC,aAAD,CAAL,CAAN,CAA4B6E,MAA5B,EAAoC,WAApC;UACD;;UACD,IAAIC,MAAJ,EAAY;YACV/kB,MAAM,CAACigB,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyB8E,MAAzB,EAAiC,WAAjC;UACD;QACF,CAVE,CAAH;MAWD;IAxBI,CAzFsC;IAoH7Cja,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,OAAO;QACLiZ,cAAc,EAAE;MADX,CAAP;IAGD,CAxH4C;IAyH7C1D,OAAO,EAAE,SAASA,OAAT,GAAmB;MAC1B,KAAK2E,eAAL,GAAuBtJ,gBAAgB,GAAG,EAAH,EAAO,UAAUuJ,OAAV,EAAmB;QAC/D,OAAOA,OAAO,CAACC,gBAAR,EAAP;MACD,CAFsC,CAAvC;IAGD,CA7H4C;IAgI7CrV,OAAO,EAAE;MACPsV,WAAW,EAAE,SAASA,WAAT,CAAqBhe,GAArB,EAA0BrB,KAA1B,EAAiC;QAC5C,IAAIsB,MAAM,GAAG,KAAKX,KAAL,CAAWW,MAAxB;;QACA,IAAIA,MAAJ,EAAY;UACV,OAAOpH,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAuCtI,GAAvC,EAA4CC,MAA5C,CAAP;QACD;;QACD,OAAOtB,KAAP;MACD,CAPM;MAQPwe,cAAc,EAAE,SAASA,cAAT,CAAwBxe,KAAxB,EAA+B;QAC7C,IAAI,KAAKqH,KAAL,KAAe,IAAf,IAAuB,KAAKA,KAAL,KAAe,MAA1C,EAAkD;UAChD,OAAOrH,KAAK,IAAI,KAAKme,kBAArB;QACD,CAFD,MAEO,IAAI,KAAK9W,KAAL,KAAe,OAAnB,EAA4B;UACjC,OAAOrH,KAAK,GAAG,KAAKqe,YAAL,GAAoB,KAAKD,mBAAxC;QACD,CAFM,MAEA;UACL,OAAOpe,KAAK,GAAG,KAAKme,kBAAb,IAAmCne,KAAK,IAAI,KAAKqe,YAAL,GAAoB,KAAKD,mBAA5E;QACD;MACF,CAhBM;MAiBPhC,OAAO,EAAE,SAASA,OAAT,CAAiB/a,GAAjB,EAAsBR,MAAtB,EAA8Bye,QAA9B,EAAwCC,WAAxC,EAAqD;QAC5D,IAAIlD,OAAO,GAAG,CAAd;QACA,IAAIC,OAAO,GAAG,CAAd;QACA,IAAIrG,EAAE,GAAG,KAAKtV,KAAL,CAAW6e,UAApB;;QACA,IAAI,OAAOvJ,EAAP,KAAc,UAAlB,EAA8B;UAC5B,IAAItG,MAAM,GAAGsG,EAAE,CAAC;YACd5U,GAAG,EAAEA,GADS;YAEdR,MAAM,EAAEA,MAFM;YAGdye,QAAQ,EAAEA,QAHI;YAIdC,WAAW,EAAEA;UAJC,CAAD,CAAf;;UAMA,IAAI3f,KAAK,CAACC,OAAN,CAAc8P,MAAd,CAAJ,EAA2B;YACzB0M,OAAO,GAAG1M,MAAM,CAAC,CAAD,CAAhB;YACA2M,OAAO,GAAG3M,MAAM,CAAC,CAAD,CAAhB;UACD,CAHD,MAGO,IAAI,CAAC,OAAOA,MAAP,KAAkB,WAAlB,GAAgC,WAAhC,GAA8C/Q,OAAO,CAAC+Q,MAAD,CAAtD,MAAoE,QAAxE,EAAkF;YACvF0M,OAAO,GAAG1M,MAAM,CAAC0M,OAAjB;YACAC,OAAO,GAAG3M,MAAM,CAAC2M,OAAjB;UACD;QACF;;QACD,OAAO;UAAED,OAAO,EAAEA,OAAX;UAAoBC,OAAO,EAAEA;QAA7B,CAAP;MACD,CArCM;MAsCPmD,WAAW,EAAE,SAASA,WAAT,CAAqBpe,GAArB,EAA0Bie,QAA1B,EAAoC;QAC/C,IAAIjZ,QAAQ,GAAG,KAAK1F,KAAL,CAAW0F,QAA1B;;QACA,IAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;UAClC,OAAOA,QAAQ,CAAC1M,IAAT,CAAc,IAAd,EAAoB;YACzB0H,GAAG,EAAEA,GADoB;YAEzBie,QAAQ,EAAEA;UAFe,CAApB,CAAP;QAID;;QACD,OAAOjZ,QAAQ,IAAI,IAAnB;MACD,CA/CM;MAgDPqZ,WAAW,EAAE,SAASA,WAAT,CAAqBre,GAArB,EAA0Bie,QAA1B,EAAoC;QAC/C,IAAIK,OAAO,GAAG,CAAC,eAAD,CAAd;;QACA,IAAI,KAAKhf,KAAL,CAAW4F,mBAAX,IAAkClF,GAAG,KAAK,KAAKwD,KAAL,CAAWC,MAAX,CAAkB2G,UAAhE,EAA4E;UAC1EkU,OAAO,CAACzc,IAAR,CAAa,aAAb;QACD;;QAED,IAAI,KAAKmB,MAAL,IAAeib,QAAQ,GAAG,CAAX,KAAiB,CAApC,EAAuC;UACrCK,OAAO,CAACzc,IAAR,CAAa,wBAAb;QACD;;QACD,IAAIkD,YAAY,GAAG,KAAKzF,KAAL,CAAWyF,YAA9B;;QACA,IAAI,OAAOA,YAAP,KAAwB,QAA5B,EAAsC;UACpCuZ,OAAO,CAACzc,IAAR,CAAakD,YAAb;QACD,CAFD,MAEO,IAAI,OAAOA,YAAP,KAAwB,UAA5B,EAAwC;UAC7CuZ,OAAO,CAACzc,IAAR,CAAakD,YAAY,CAACzM,IAAb,CAAkB,IAAlB,EAAwB;YACnC0H,GAAG,EAAEA,GAD8B;YAEnCie,QAAQ,EAAEA;UAFyB,CAAxB,CAAb;QAID;;QAED,IAAI,KAAKza,KAAL,CAAWC,MAAX,CAAkBgF,UAAlB,CAA6BtI,OAA7B,CAAqCH,GAArC,IAA4C,CAAC,CAAjD,EAAoD;UAClDse,OAAO,CAACzc,IAAR,CAAa,UAAb;QACD;;QAED,OAAOyc,OAAP;MACD,CAxEM;MAyEPjD,YAAY,EAAE,SAASA,YAAT,CAAsB4C,QAAtB,EAAgCC,WAAhC,EAA6Cle,GAA7C,EAAkDR,MAAlD,EAA0D;QACtE,IAAI+e,SAAS,GAAG,KAAKjf,KAAL,CAAWif,SAA3B;;QACA,IAAI,OAAOA,SAAP,KAAqB,UAAzB,EAAqC;UACnC,OAAOA,SAAS,CAACjmB,IAAV,CAAe,IAAf,EAAqB;YAC1B2lB,QAAQ,EAAEA,QADgB;YAE1BC,WAAW,EAAEA,WAFa;YAG1Ble,GAAG,EAAEA,GAHqB;YAI1BR,MAAM,EAAEA;UAJkB,CAArB,CAAP;QAMD;;QACD,OAAO+e,SAAP;MACD,CApFM;MAqFPjD,YAAY,EAAE,SAASA,YAAT,CAAsB2C,QAAtB,EAAgCC,WAAhC,EAA6Cle,GAA7C,EAAkDR,MAAlD,EAA0D;QACtE,IAAI8e,OAAO,GAAG,CAAC9e,MAAM,CAACG,EAAR,EAAYH,MAAM,CAACgf,KAAnB,EAA0Bhf,MAAM,CAACM,SAAjC,CAAd;;QAEA,IAAI,KAAKqd,cAAL,CAAoBe,WAApB,CAAJ,EAAsC;UACpCI,OAAO,CAACzc,IAAR,CAAa,WAAb;QACD;;QAED,IAAI4c,aAAa,GAAG,KAAKnf,KAAL,CAAWmf,aAA/B;;QACA,IAAI,OAAOA,aAAP,KAAyB,QAA7B,EAAuC;UACrCH,OAAO,CAACzc,IAAR,CAAa4c,aAAb;QACD,CAFD,MAEO,IAAI,OAAOA,aAAP,KAAyB,UAA7B,EAAyC;UAC9CH,OAAO,CAACzc,IAAR,CAAa4c,aAAa,CAACnmB,IAAd,CAAmB,IAAnB,EAAyB;YACpC2lB,QAAQ,EAAEA,QAD0B;YAEpCC,WAAW,EAAEA,WAFuB;YAGpCle,GAAG,EAAEA,GAH+B;YAIpCR,MAAM,EAAEA;UAJ4B,CAAzB,CAAb;QAMD;;QAED8e,OAAO,CAACzc,IAAR,CAAa,gBAAb;QAEA,OAAOyc,OAAO,CAACI,IAAR,CAAa,GAAb,CAAP;MACD,CA3GM;MA4GPvD,mBAAmB,EAAE,SAASA,mBAAT,CAA6B1b,OAA7B,EAAsCwb,OAAtC,EAA+Ctc,KAA/C,EAAsD;QACzE,IAAIsc,OAAO,GAAG,CAAd,EAAiB;UACf,OAAOxb,OAAO,CAACd,KAAD,CAAP,CAAeiZ,SAAtB;QACD;;QACD,IAAI+G,QAAQ,GAAGlf,OAAO,CAACb,GAAR,CAAY,UAAUggB,KAAV,EAAiB;UAC1C,IAAIhH,SAAS,GAAGgH,KAAK,CAAChH,SAAtB;UACA,OAAOA,SAAP;QACD,CAHc,EAGZ9O,KAHY,CAGNnK,KAHM,EAGCA,KAAK,GAAGsc,OAHT,CAAf;QAIA,OAAO0D,QAAQ,CAACrd,MAAT,CAAgB,UAAUgb,GAAV,EAAe5b,KAAf,EAAsB;UAC3C,OAAO4b,GAAG,GAAG5b,KAAb;QACD,CAFM,EAEJ,CAAC,CAFG,CAAP;MAGD,CAvHM;MAwHP8a,oBAAoB,EAAE,SAASA,oBAAT,CAA8B7d,KAA9B,EAAqCqC,GAArC,EAA0C;QAC9D,IAAIV,KAAK,GAAG,KAAKA,KAAjB;QACA,IAAI1B,IAAI,GAAG/E,MAAM,CAACyP,IAAI,CAAC;QAAI;QAAL,CAAL,CAAN,CAAgC3K,KAAhC,CAAX;;QAEA,IAAIC,IAAJ,EAAU;UACR,IAAI4B,MAAM,GAAG3G,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAwChJ,KAAxC,EAA+C1B,IAA/C,CAAb;UACA,IAAIihB,UAAU,GAAGvf,KAAK,CAACuf,UAAN,GAAmB;YAAEjhB,IAAI,EAAEA,IAAR;YAAc4B,MAAM,EAAEA,MAAtB;YAA8BQ,GAAG,EAAEA;UAAnC,CAApC;UACAV,KAAK,CAAC+J,KAAN,CAAY,kBAAZ,EAAgCwV,UAAU,CAAC7e,GAA3C,EAAgD6e,UAAU,CAACrf,MAA3D,EAAmEqf,UAAU,CAACjhB,IAA9E,EAAoFD,KAApF;QACD,CAR6D,CAU9D;;;QACA,IAAImhB,SAAS,GAAGnhB,KAAK,CAACE,MAAN,CAAaiY,aAAb,CAA2B,OAA3B,CAAhB;;QACA,IAAI,EAAEjd,MAAM,CAACigB,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBgG,SAAzB,EAAoC,YAApC,KAAqDA,SAAS,CAACC,UAAV,CAAqBtgB,MAA5E,CAAJ,EAAyF;UACvF;QACD,CAd6D,CAe9D;QACA;;;QACA,IAAIugB,KAAK,GAAGC,QAAQ,CAACC,WAAT,EAAZ;QACAF,KAAK,CAACG,QAAN,CAAeL,SAAf,EAA0B,CAA1B;QACAE,KAAK,CAACI,MAAN,CAAaN,SAAb,EAAwBA,SAAS,CAACC,UAAV,CAAqBtgB,MAA7C;QACA,IAAI4gB,UAAU,GAAGL,KAAK,CAACM,qBAAN,GAA8B5e,KAA/C;QACA,IAAI6e,OAAO,GAAG,CAAC3e,QAAQ,CAAC/H,MAAM,CAACigB,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBgG,SAAzB,EAAoC,aAApC,CAAD,EAAqD,EAArD,CAAR,IAAoE,CAArE,KAA2Ele,QAAQ,CAAC/H,MAAM,CAACigB,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBgG,SAAzB,EAAoC,cAApC,CAAD,EAAsD,EAAtD,CAAR,IAAqE,CAAhJ,CAAd;;QACA,IAAI,CAACO,UAAU,GAAGE,OAAb,GAAuBT,SAAS,CAAC/H,WAAjC,IAAgD+H,SAAS,CAACU,WAAV,GAAwBV,SAAS,CAAC/H,WAAnF,KAAmG,KAAK5E,KAAL,CAAW2L,OAAlH,EAA2H;UACzH,IAAIA,OAAO,GAAG,KAAK3L,KAAL,CAAW2L,OAAzB,CADyH,CAEzH;;UACA,KAAKlB,cAAL,GAAsBhf,IAAI,CAAC6hB,SAAL,IAAkB7hB,IAAI,CAAC8hB,WAA7C;UACA5B,OAAO,CAAC6B,YAAR,GAAuB/hB,IAAvB;UACAkgB,OAAO,CAAC3L,KAAR,CAAcyN,MAAd,KAAyB9B,OAAO,CAAC3L,KAAR,CAAcyN,MAAd,CAAqBpb,KAArB,CAA2B8S,OAA3B,GAAqC,MAA9D;UACAwG,OAAO,CAAC+B,SAAR;UACA/B,OAAO,CAACgC,gBAAR,CAAyB,IAAzB;UACA,KAAKjC,eAAL,CAAqBC,OAArB;QACD;MACF,CAxJM;MAyJPrC,oBAAoB,EAAE,SAASA,oBAAT,CAA8B9d,KAA9B,EAAqC;QACzD,IAAImgB,OAAO,GAAG,KAAK3L,KAAL,CAAW2L,OAAzB;;QACA,IAAIA,OAAJ,EAAa;UACXA,OAAO,CAACgC,gBAAR,CAAyB,KAAzB;UACAhC,OAAO,CAACiC,iBAAR;QACD;;QACD,IAAIniB,IAAI,GAAG/E,MAAM,CAACyP,IAAI,CAAC;QAAI;QAAL,CAAL,CAAN,CAAgC3K,KAAhC,CAAX;QACA,IAAI,CAACC,IAAL,EAAW;QAEX,IAAIoiB,aAAa,GAAG,KAAK1gB,KAAL,CAAWuf,UAAX,IAAyB,EAA7C;QACA,KAAKvf,KAAL,CAAW+J,KAAX,CAAiB,kBAAjB,EAAqC2W,aAAa,CAAChgB,GAAnD,EAAwDggB,aAAa,CAACxgB,MAAtE,EAA8EwgB,aAAa,CAACpiB,IAA5F,EAAkGD,KAAlG;MACD,CApKM;MAuKPsiB,gBAAgB,EAAE1L,gBAAgB,GAAG,EAAH,EAAO,UAAU5V,KAAV,EAAiB;QACxD,KAAK6E,KAAL,CAAWmP,MAAX,CAAkB,aAAlB,EAAiChU,KAAjC;MACD,CAFiC,CAvK3B;MA2KPqF,gBAAgB,EAAEuQ,gBAAgB,GAAG,EAAH,EAAO,YAAY;QACnD,KAAK/Q,KAAL,CAAWmP,MAAX,CAAkB,aAAlB,EAAiC,IAAjC;MACD,CAFiC,CA3K3B;MA+KPuN,iBAAiB,EAAE,SAASA,iBAAT,CAA2BviB,KAA3B,EAAkCqC,GAAlC,EAAuC;QACxD,KAAKmgB,WAAL,CAAiBxiB,KAAjB,EAAwBqC,GAAxB,EAA6B,aAA7B;MACD,CAjLM;MAkLPogB,iBAAiB,EAAE,SAASA,iBAAT,CAA2BziB,KAA3B,EAAkCqC,GAAlC,EAAuC;QACxD,KAAKmgB,WAAL,CAAiBxiB,KAAjB,EAAwBqC,GAAxB,EAA6B,UAA7B;MACD,CApLM;MAqLPqgB,WAAW,EAAE,SAASA,WAAT,CAAqB1iB,KAArB,EAA4BqC,GAA5B,EAAiC;QAC5C,KAAKwD,KAAL,CAAWmP,MAAX,CAAkB,eAAlB,EAAmC3S,GAAnC;QACA,KAAKmgB,WAAL,CAAiBxiB,KAAjB,EAAwBqC,GAAxB,EAA6B,OAA7B;MACD,CAxLM;MAyLPmgB,WAAW,EAAE,SAASA,WAAT,CAAqBxiB,KAArB,EAA4BqC,GAA5B,EAAiCtH,IAAjC,EAAuC;QAClD,IAAI4G,KAAK,GAAG,KAAKA,KAAjB;QACA,IAAI1B,IAAI,GAAG/E,MAAM,CAACyP,IAAI,CAAC;QAAI;QAAL,CAAL,CAAN,CAAgC3K,KAAhC,CAAX;QACA,IAAI6B,MAAM,GAAG,KAAK,CAAlB;;QACA,IAAI5B,IAAJ,EAAU;UACR4B,MAAM,GAAG3G,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAwChJ,KAAxC,EAA+C1B,IAA/C,CAAT;;UACA,IAAI4B,MAAJ,EAAY;YACVF,KAAK,CAAC+J,KAAN,CAAY,UAAU3Q,IAAtB,EAA4BsH,GAA5B,EAAiCR,MAAjC,EAAyC5B,IAAzC,EAA+CD,KAA/C;UACD;QACF;;QACD2B,KAAK,CAAC+J,KAAN,CAAY,SAAS3Q,IAArB,EAA2BsH,GAA3B,EAAgCR,MAAhC,EAAwC7B,KAAxC;MACD,CApMM;MAqMP2iB,SAAS,EAAE,SAASA,SAAT,CAAmBtgB,GAAnB,EAAwBsa,MAAxB,EAAgCE,WAAhC,EAA6C;QACtD,IAAI+F,MAAM,GAAG,IAAb;;QAEA,IAAIrkB,CAAC,GAAG,KAAKwG,cAAb;QACA,IAAI+X,UAAU,GAAG,KAAKA,UAAtB;QAAA,IACIhb,OAAO,GAAG,KAAKA,OADnB;QAAA,IAEI8a,uBAAuB,GAAG,KAAKA,uBAFnC;QAIA,IAAIiG,UAAU,GAAG,KAAKnC,WAAL,CAAiBre,GAAjB,EAAsBsa,MAAtB,CAAjB;QACA,IAAIhD,OAAO,GAAG,IAAd;;QACA,IAAIkD,WAAJ,EAAiB;UACfgG,UAAU,CAAC3e,IAAX,CAAgB,0BAA0B2Y,WAAW,CAACjY,KAAtD;UACA+U,OAAO,GAAGkD,WAAW,CAAClD,OAAtB;QACD,CAbqD,CActD;QACA;;;QACA,IAAImJ,YAAY,GAAGnJ,OAAO,GAAG,IAAH,GAAU;UAClCA,OAAO,EAAE;QADyB,CAApC;QAGA,OAAOpb,CAAC,CAACge,SAAD,EAAY;UAClB1V,KAAK,EAAE,CAACic,YAAD,EAAe,KAAKrC,WAAL,CAAiBpe,GAAjB,EAAsBsa,MAAtB,CAAf,CADW;UAElB,SAASkG,UAFS;UAGlB9mB,GAAG,EAAE,KAAKskB,WAAL,CAAiBhe,GAAjB,EAAsBsa,MAAtB,CAHa;UAIlBoG,QAAQ,EAAE;YACR,YAAY,SAASC,QAAT,CAAkB5c,MAAlB,EAA0B;cACpC,OAAOwc,MAAM,CAACH,iBAAP,CAAyBrc,MAAzB,EAAiC/D,GAAjC,CAAP;YACD,CAHO;YAIR,SAAS,SAAS4gB,KAAT,CAAe7c,MAAf,EAAuB;cAC9B,OAAOwc,MAAM,CAACF,WAAP,CAAmBtc,MAAnB,EAA2B/D,GAA3B,CAAP;YACD,CANO;YAOR,eAAe,SAAS6gB,WAAT,CAAqB9c,MAArB,EAA6B;cAC1C,OAAOwc,MAAM,CAACL,iBAAP,CAAyBnc,MAAzB,EAAiC/D,GAAjC,CAAP;YACD,CATO;YAUR,cAAc,SAASub,UAAT,CAAoBuF,CAApB,EAAuB;cACnC,OAAOP,MAAM,CAACN,gBAAP,CAAwB3F,MAAxB,CAAP;YACD,CAZO;YAaR,cAAc,KAAKtW;UAbX,CAJQ;UAmBlBU,KAAK,EAAE;YACLjF,OAAO,EAAEA,OADJ;YAELO,GAAG,EAAEA,GAFA;YAGLrB,KAAK,EAAE2b,MAHF;YAIL9W,KAAK,EAAE,KAAKA,KAJP;YAKLpI,OAAO,EAAE,KAAKA,OAAL,IAAgB,KAAKkE,KAAL,CAAWjE,MAAX,CAAkBD,OALtC;YAMLmf,uBAAuB,EAAEA,uBANpB;YAOLC,WAAW,EAAEA,WAPR;YAQLC,UAAU,EAAEA,UARP;YASLE,aAAa,EAAE,KAAKA,aATf;YAULI,OAAO,EAAE,KAAKA,OAVT;YAWLI,mBAAmB,EAAE,KAAKA,mBAXrB;YAYLE,YAAY,EAAE,KAAKA,YAZd;YAaLC,YAAY,EAAE,KAAKA,YAbd;YAcLE,oBAAoB,EAAE,KAAKA,oBAdtB;YAeLC,oBAAoB,EAAE,KAAKA,oBAftB;YAgBLxL,UAAU,EAAE,KAAKzM,KAAL,CAAWyM,UAAX,CAAsBjQ,GAAtB,CAhBP;YAiBL4a,UAAU,EAAE,KAAKpX,KAAL,CAAWC,MAAX,CAAkBgF,UAAlB,CAA6BtI,OAA7B,CAAqCH,GAArC,IAA4C,CAAC,CAjBpD;YAkBLgG,KAAK,EAAE,KAAKA;UAlBP;QAnBW,CAAZ,CAAR;MAwCD,CAhQM;MAiQPuW,gBAAgB,EAAE,SAASA,gBAAT,CAA0Bvc,GAA1B,EAA+Bsa,MAA/B,EAAuC;QACvD,IAAIyG,MAAM,GAAG,IAAb;;QAEA,IAAI7kB,CAAC,GAAG,KAAKwG,cAAb;QAEA,IAAIc,KAAK,GAAG,KAAKA,KAAjB;QACA,IAAIsG,aAAa,GAAGtG,KAAK,CAACsG,aAA1B;QAAA,IACIL,YAAY,GAAGjG,KAAK,CAACiG,YADzB;QAEA,IAAIuX,aAAa,GAAGxd,KAAK,CAACC,MAA1B;QAAA,IACIwH,QAAQ,GAAG+V,aAAa,CAAC/V,QAD7B;QAAA,IAEIG,eAAe,GAAG4V,aAAa,CAAC5V,eAFpC;QAAA,IAGIE,kBAAkB,GAAG0V,aAAa,CAAC1V,kBAHvC;QAAA,IAIIrL,MAAM,GAAG+gB,aAAa,CAAC/gB,MAJ3B;;QAMA,IAAI,KAAKiT,eAAL,IAAwBpJ,aAAa,CAAC9J,GAAD,CAAzC,EAAgD;UAC9C,IAAIihB,cAAc,GAAG,KAAK3hB,KAAL,CAAW2hB,cAAhC;UACA,IAAIC,EAAE,GAAG,KAAKZ,SAAL,CAAetgB,GAAf,EAAoBsa,MAApB,CAAT;;UACA,IAAI,CAAC2G,cAAL,EAAqB;YACnBpM,OAAO,CAACC,KAAR,CAAc,4CAAd;YACA,OAAOoM,EAAP;UACD,CAN6C,CAO9C;;;UACA,OAAO,CAAC,CAACA,EAAD,EAAKhlB,CAAC,CACZ,IADY,EAEZ;YAAExC,GAAG,EAAE,mBAAmBwnB,EAAE,CAACxnB;UAA7B,CAFY,EAGZ,CAACwC,CAAC,CACA,IADA,EAEA;YACEwI,KAAK,EAAE;cAAEuW,OAAO,EAAE,KAAK+B;YAAhB,CADT;YAEE,SAAS;UAFX,CAFA,EAKA,CAACiE,cAAc,CAAC,KAAKve,cAAN,EAAsB;YAAE1C,GAAG,EAAEA,GAAP;YAAYsa,MAAM,EAAEA,MAApB;YAA4B9W,KAAK,EAAE,KAAKA;UAAxC,CAAtB,CAAf,CALA,CAAF,CAHY,CAAN,CAAD,CAAP;QAWD,CAnBD,MAmBO,IAAI3K,MAAM,CAAC8S,IAAP,CAAYV,QAAZ,EAAsBxM,MAA1B,EAAkC;UACvCgL,YAAY,GAD2B,CAEvC;UACA;;UACA,IAAI/P,GAAG,GAAGb,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAuCtI,GAAvC,EAA4CC,MAA5C,CAAV;UACA,IAAI2J,GAAG,GAAGqB,QAAQ,CAACvR,GAAD,CAAlB;UACA,IAAI8gB,WAAW,GAAG,IAAlB;;UACA,IAAI5Q,GAAJ,EAAS;YACP4Q,WAAW,GAAG;cACZpR,QAAQ,EAAEQ,GAAG,CAACR,QADF;cAEZ7G,KAAK,EAAEqH,GAAG,CAACrH,KAFC;cAGZ+U,OAAO,EAAE;YAHG,CAAd;;YAKA,IAAI,OAAO1N,GAAG,CAACuB,IAAX,KAAoB,SAAxB,EAAmC;cACjC,IAAI,OAAOvB,GAAG,CAAC6C,MAAX,KAAsB,SAAtB,IAAmC7C,GAAG,CAAC6C,MAA3C,EAAmD;gBACjD+N,WAAW,CAACY,cAAZ,GAA6B,EAAExR,GAAG,CAACtH,QAAJ,IAAgBsH,GAAG,CAACtH,QAAJ,CAAa7D,MAA/B,CAA7B;cACD;;cACD+b,WAAW,CAAC7N,OAAZ,GAAsB/C,GAAG,CAAC+C,OAA1B;YACD;UACF;;UACD,IAAIwU,GAAG,GAAG,CAAC,KAAKb,SAAL,CAAetgB,GAAf,EAAoBsa,MAApB,EAA4BE,WAA5B,CAAD,CAAV,CApBuC,CAqBvC;;UACA,IAAI5Q,GAAJ,EAAS;YACP;YACA,IAAIxR,CAAC,GAAG,CAAR;;YACA,IAAIgpB,QAAQ,GAAG,SAASA,QAAT,CAAkB9e,QAAlB,EAA4B/G,MAA5B,EAAoC;cACjD,IAAI,EAAE+G,QAAQ,IAAIA,QAAQ,CAAC7D,MAArB,IAA+BlD,MAAjC,CAAJ,EAA8C;cAC9C+G,QAAQ,CAAC5C,OAAT,CAAiB,UAAU2hB,IAAV,EAAgB;gBAC/B;gBACA,IAAIC,gBAAgB,GAAG;kBACrBhK,OAAO,EAAE/b,MAAM,CAAC+b,OAAP,IAAkB/b,MAAM,CAAC6N,QADb;kBAErB7G,KAAK,EAAEhH,MAAM,CAACgH,KAAP,GAAe;gBAFD,CAAvB;gBAIA,IAAIgf,QAAQ,GAAG1oB,MAAM,CAACyP,IAAI,CAAC;gBAAI;gBAAL,CAAL,CAAN,CAAuC+Y,IAAvC,EAA6CphB,MAA7C,CAAf;;gBACA,IAAIshB,QAAQ,KAAK5gB,SAAb,IAA0B4gB,QAAQ,KAAK,IAA3C,EAAiD;kBAC/C,MAAM,IAAIrhB,KAAJ,CAAU,4CAAV,CAAN;gBACD;;gBACD0J,GAAG,GAAGgS,kBAAkB,CAAC,EAAD,EAAK3Q,QAAQ,CAACsW,QAAD,CAAb,CAAxB,CAV+B,CAW/B;gBACA;gBACA;;gBACA,IAAI3X,GAAJ,EAAS;kBACP0X,gBAAgB,CAAClY,QAAjB,GAA4BQ,GAAG,CAACR,QAAhC,CADO,CAEP;;kBACAQ,GAAG,CAACrH,KAAJ,GAAYqH,GAAG,CAACrH,KAAJ,IAAa+e,gBAAgB,CAAC/e,KAA1C;kBACAqH,GAAG,CAAC0N,OAAJ,GAAc,CAAC,EAAE1N,GAAG,CAACR,QAAJ,IAAgBkY,gBAAgB,CAAChK,OAAnC,CAAf;;kBACA,IAAI,OAAO1N,GAAG,CAACuB,IAAX,KAAoB,SAAxB,EAAmC;oBACjC,IAAI,OAAOvB,GAAG,CAAC6C,MAAX,KAAsB,SAAtB,IAAmC7C,GAAG,CAAC6C,MAA3C,EAAmD;sBACjD6U,gBAAgB,CAAClG,cAAjB,GAAkC,EAAExR,GAAG,CAACtH,QAAJ,IAAgBsH,GAAG,CAACtH,QAAJ,CAAa7D,MAA/B,CAAlC;oBACD;;oBACD6iB,gBAAgB,CAAC3U,OAAjB,GAA2B/C,GAAG,CAAC+C,OAA/B;kBACD;gBACF;;gBACDvU,CAAC;gBACD+oB,GAAG,CAACtf,IAAJ,CAASkf,MAAM,CAACT,SAAP,CAAiBe,IAAjB,EAAuB/G,MAAM,GAAGliB,CAAhC,EAAmCkpB,gBAAnC,CAAT;;gBACA,IAAI1X,GAAJ,EAAS;kBACP,IAAI4X,MAAM,GAAGpW,eAAe,CAACmW,QAAD,CAAf,IAA6BF,IAAI,CAAC/V,kBAAD,CAA9C;;kBACA8V,QAAQ,CAACI,MAAD,EAAS5X,GAAT,CAAR;gBACD;cACF,CAhCD;YAiCD,CAnCD,CAHO,CAuCP;;;YACAA,GAAG,CAAC0N,OAAJ,GAAc,IAAd;YACA,IAAImK,KAAK,GAAGrW,eAAe,CAAC1R,GAAD,CAAf,IAAwBsG,GAAG,CAACsL,kBAAD,CAAvC;YACA8V,QAAQ,CAACK,KAAD,EAAQ7X,GAAR,CAAR;UACD;;UACD,OAAOuX,GAAP;QACD,CAnEM,MAmEA;UACL,OAAO,KAAKb,SAAL,CAAetgB,GAAf,EAAoBsa,MAApB,CAAP;QACD;MACF;IAxWM;EAhIoC,CAAlB,CAt7DqC,CAi6ElE;;EACA,IAAIoH,gDAAgD,GAAG,YAAW;IAChE,IAAIlf,GAAG,GAAG,IAAV;;IACA,IAAIC,EAAE,GAAGD,GAAG,CAACE,cAAb;;IACA,IAAIC,EAAE,GAAGH,GAAG,CAACI,KAAJ,CAAUD,EAAV,IAAgBF,EAAzB;;IACA,OAAOE,EAAE,CAAC,YAAD,EAAe;MAAE+B,KAAK,EAAE;QAAEhM,IAAI,EAAE;MAAR;IAAT,CAAf,EAAsD,CAC7D8J,GAAG,CAACmf,QAAJ,GACIhf,EAAE,CACA,KADA,EAEA;MACEyB,UAAU,EAAE,CACV;QACE1L,IAAI,EAAE,cADR;QAEE2L,OAAO,EAAE,gBAFX;QAGEjL,KAAK,EAAEoJ,GAAG,CAACof,kBAHb;QAIErd,UAAU,EAAE;MAJd,CADU,EAOV;QACE7L,IAAI,EAAE,MADR;QAEE2L,OAAO,EAAE,QAFX;QAGEjL,KAAK,EAAEoJ,GAAG,CAACqf,UAHb;QAIEtd,UAAU,EAAE;MAJd,CAPU,CADd;MAeE1B,WAAW,EAAE;IAff,CAFA,EAmBA,CACEF,EAAE,CACA,KADA,EAEA;MAAEE,WAAW,EAAE;IAAf,CAFA,EAGA,CACEF,EAAE,CACA,cADA,EAEA;MAAE+B,KAAK,EAAE;QAAE,cAAc;MAAhB;IAAT,CAFA,EAGA,CACE/B,EAAE,CACA,mBADA,EAEA;MACEE,WAAW,EAAE,iCADf;MAEEif,KAAK,EAAE;QACL1oB,KAAK,EAAEoJ,GAAG,CAACkQ,aADN;QAELlL,QAAQ,EAAE,UAASua,GAAT,EAAc;UACtBvf,GAAG,CAACkQ,aAAJ,GAAoBqP,GAApB;QACD,CAJI;QAKLxd,UAAU,EAAE;MALP;IAFT,CAFA,EAYA/B,GAAG,CAACwf,EAAJ,CAAOxf,GAAG,CAAC8M,OAAX,EAAoB,UAASK,MAAT,EAAiB;MACnC,OAAOhN,EAAE,CACP,aADO,EAEP;QAAEjJ,GAAG,EAAEiW,MAAM,CAACvW,KAAd;QAAqBsL,KAAK,EAAE;UAAEud,KAAK,EAAEtS,MAAM,CAACvW;QAAhB;MAA5B,CAFO,EAGP,CAACoJ,GAAG,CAAC4C,EAAJ,CAAO5C,GAAG,CAAC6C,EAAJ,CAAOsK,MAAM,CAACuS,IAAd,CAAP,CAAD,CAHO,CAAT;IAKD,CAND,CAZA,EAmBA,CAnBA,CADJ,CAHA,EA0BA,CA1BA,CADJ,CAHA,EAiCA,CAjCA,CADJ,EAoCEvf,EAAE,CAAC,KAAD,EAAQ;MAAEE,WAAW,EAAE;IAAf,CAAR,EAAoD,CACpDF,EAAE,CACA,QADA,EAEA;MACEG,KAAK,EAAE;QAAE,eAAeN,GAAG,CAACkQ,aAAJ,CAAkBjU,MAAlB,KAA6B;MAA9C,CADT;MAEEiG,KAAK,EAAE;QAAEyd,QAAQ,EAAE3f,GAAG,CAACkQ,aAAJ,CAAkBjU,MAAlB,KAA6B;MAAzC,CAFT;MAGEoF,EAAE,EAAE;QAAE+c,KAAK,EAAEpe,GAAG,CAAC4f;MAAb;IAHN,CAFA,EAOA,CAAC5f,GAAG,CAAC4C,EAAJ,CAAO5C,GAAG,CAAC6C,EAAJ,CAAO7C,GAAG,CAACnJ,CAAJ,CAAM,wBAAN,CAAP,CAAP,CAAD,CAPA,CADkD,EAUpDsJ,EAAE,CAAC,QAAD,EAAW;MAAEkB,EAAE,EAAE;QAAE+c,KAAK,EAAEpe,GAAG,CAAC6f;MAAb;IAAN,CAAX,EAA+C,CAC/C7f,GAAG,CAAC4C,EAAJ,CAAO5C,GAAG,CAAC6C,EAAJ,CAAO7C,GAAG,CAACnJ,CAAJ,CAAM,sBAAN,CAAP,CAAP,CAD+C,CAA/C,CAVkD,CAApD,CApCJ,CAnBA,CADN,GAwEIsJ,EAAE,CACA,KADA,EAEA;MACEyB,UAAU,EAAE,CACV;QACE1L,IAAI,EAAE,cADR;QAEE2L,OAAO,EAAE,gBAFX;QAGEjL,KAAK,EAAEoJ,GAAG,CAACof,kBAHb;QAIErd,UAAU,EAAE;MAJd,CADU,EAOV;QACE7L,IAAI,EAAE,MADR;QAEE2L,OAAO,EAAE,QAFX;QAGEjL,KAAK,EAAEoJ,GAAG,CAACqf,UAHb;QAIEtd,UAAU,EAAE;MAJd,CAPU,CADd;MAeE1B,WAAW,EAAE;IAff,CAFA,EAmBA,CACEF,EAAE,CACA,IADA,EAEA;MAAEE,WAAW,EAAE;IAAf,CAFA,EAGA,CACEF,EAAE,CACA,IADA,EAEA;MACEE,WAAW,EAAE,4BADf;MAEEC,KAAK,EAAE;QACL,aACEN,GAAG,CAAC8f,WAAJ,KAAoB3hB,SAApB,IACA6B,GAAG,CAAC8f,WAAJ,KAAoB;MAHjB,CAFT;MAOEze,EAAE,EAAE;QACF+c,KAAK,EAAE,UAAS7c,MAAT,EAAiB;UACtBvB,GAAG,CAAC+f,YAAJ,CAAiB,IAAjB;QACD;MAHC;IAPN,CAFA,EAeA,CAAC/f,GAAG,CAAC4C,EAAJ,CAAO5C,GAAG,CAAC6C,EAAJ,CAAO7C,GAAG,CAACnJ,CAAJ,CAAM,sBAAN,CAAP,CAAP,CAAD,CAfA,CADJ,EAkBEmJ,GAAG,CAACwf,EAAJ,CAAOxf,GAAG,CAAC8M,OAAX,EAAoB,UAASK,MAAT,EAAiB;MACnC,OAAOhN,EAAE,CACP,IADO,EAEP;QACEjJ,GAAG,EAAEiW,MAAM,CAACvW,KADd;QAEEyJ,WAAW,EAAE,4BAFf;QAGEC,KAAK,EAAE;UAAE,aAAaN,GAAG,CAACggB,QAAJ,CAAa7S,MAAb;QAAf,CAHT;QAIEjL,KAAK,EAAE;UAAEud,KAAK,EAAEtS,MAAM,CAACvW;QAAhB,CAJT;QAKEyK,EAAE,EAAE;UACF+c,KAAK,EAAE,UAAS7c,MAAT,EAAiB;YACtBvB,GAAG,CAAC+f,YAAJ,CAAiB5S,MAAM,CAACvW,KAAxB;UACD;QAHC;MALN,CAFO,EAaP,CAACoJ,GAAG,CAAC4C,EAAJ,CAAO5C,GAAG,CAAC6C,EAAJ,CAAOsK,MAAM,CAACuS,IAAd,CAAP,CAAD,CAbO,CAAT;IAeD,CAhBD,CAlBF,CAHA,EAuCA,CAvCA,CADJ,CAnBA,CAzEuD,CAAtD,CAAT;EAyID,CA7ID;;EA8IA,IAAIO,yDAAyD,GAAG,EAAhE;EACAf,gDAAgD,CAAChb,aAAjD,GAAiE,IAAjE,CAjjFkE,CAojFlE;EAEA;;EACA,IAAIgc,WAAW,GAAGxqB,mBAAmB,CAAC,CAAD,CAArC;;EACA,IAAIyqB,kBAAkB,GAAG,aAAazqB,mBAAmB,CAAC0B,CAApB,CAAsB8oB,WAAtB,CAAtC,CAxjFkE,CA0jFlE;;;EACA,IAAIE,MAAM,GAAG1qB,mBAAmB,CAAC,EAAD,CAAhC,CA3jFkE,CA6jFlE;;;EACA,IAAI2qB,aAAa,GAAG3qB,mBAAmB,CAAC,EAAD,CAAvC;;EACA,IAAI4qB,oBAAoB,GAAG,aAAa5qB,mBAAmB,CAAC0B,CAApB,CAAsBipB,aAAtB,CAAxC,CA/jFkE,CAikFlE;;;EAEA,IAAIE,SAAS,GAAG,EAAhB;EAEA,CAAC5a,oBAAoB,CAACnJ,CAArB,CAAuB/E,SAAvB,CAAiCic,SAAlC,IAA+C+I,QAAQ,CAACxX,gBAAT,CAA0B,OAA1B,EAAmC,UAAU9J,KAAV,EAAiB;IACjGolB,SAAS,CAACrjB,OAAV,CAAkB,UAAUsjB,QAAV,EAAoB;MACpC,IAAInlB,MAAM,GAAGF,KAAK,CAACE,MAAnB;MACA,IAAI,CAACmlB,QAAD,IAAa,CAACA,QAAQ,CAACpN,GAA3B,EAAgC;;MAChC,IAAI/X,MAAM,KAAKmlB,QAAQ,CAACpN,GAApB,IAA2BoN,QAAQ,CAACpN,GAAT,CAAaqN,QAAb,CAAsBplB,MAAtB,CAA/B,EAA8D;QAC5D;MACD;;MACDmlB,QAAQ,CAACpB,kBAAT,IAA+BoB,QAAQ,CAACpB,kBAAT,CAA4BjkB,KAA5B,CAA/B;IACD,CAPD;EAQD,CAT8C,CAA/C;EAWA;;EAA6B,IAAIqlB,QAAQ,GAAI;IAC3CE,IAAI,EAAE,SAASA,IAAT,CAAchO,QAAd,EAAwB;MAC5B,IAAIA,QAAJ,EAAc;QACZ6N,SAAS,CAAClhB,IAAV,CAAeqT,QAAf;MACD;IACF,CAL0C;IAM3CiO,KAAK,EAAE,SAASA,KAAT,CAAejO,QAAf,EAAyB;MAC9B,IAAIvW,KAAK,GAAGokB,SAAS,CAAC5iB,OAAV,CAAkB+U,QAAlB,CAAZ;;MACA,IAAIvW,KAAK,KAAK,CAAC,CAAf,EAAkB;QAChBokB,SAAS,CAAChhB,MAAV,CAAiBmT,QAAjB,EAA2B,CAA3B;MACD;IACF;EAX0C,CAAhB,CAhlFqC,CA6lFlE;;EACA,IAAIkO,eAAe,GAAGlrB,mBAAmB,CAAC,EAAD,CAAzC;;EACA,IAAImrB,sBAAsB,GAAG,aAAanrB,mBAAmB,CAAC0B,CAApB,CAAsBwpB,eAAtB,CAA1C,CA/lFkE,CAimFlE;;;EACA,IAAIE,UAAU,GAAGprB,mBAAmB,CAAC,EAAD,CAApC;;EACA,IAAIqrB,iBAAiB,GAAG,aAAarrB,mBAAmB,CAAC0B,CAApB,CAAsB0pB,UAAtB,CAArC,CAnmFkE,CAqmFlE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAWA;;;EAA6B,IAAIE,oCAAoC,GAAI;IACvE9qB,IAAI,EAAE,oBADiE;IAGvE+W,MAAM,EAAE,CAACkT,kBAAkB,CAAC3jB,CAApB,EAAuB+I,cAAc,CAAC/I,CAAtC,CAH+D;IAKvEoF,UAAU,EAAE;MACVqf,YAAY,EAAEX,oBAAoB,CAAC9jB;IADzB,CAL2D;IASvEob,UAAU,EAAE;MACVC,UAAU,EAAEzT,gBAAgB,CAAC5H,CADnB;MAEV0kB,eAAe,EAAEL,sBAAsB,CAACrkB,CAF9B;MAGV2kB,WAAW,EAAEJ,iBAAiB,CAACvkB;IAHrB,CAT2D;IAevEmb,KAAK,EAAE;MACLuC,SAAS,EAAE;QACT9M,IAAI,EAAEsM,MADG;QAET0H,OAAO,EAAE;MAFA;IADN,CAfgE;IAsBvElb,OAAO,EAAE;MACP8Z,QAAQ,EAAE,SAASA,QAAT,CAAkB7S,MAAlB,EAA0B;QAClC,OAAOA,MAAM,CAACvW,KAAP,KAAiB,KAAKkpB,WAA7B;MACD,CAHM;MAIPV,kBAAkB,EAAE,SAASA,kBAAT,GAA8B;QAChD,IAAIlU,KAAK,GAAG,IAAZ;;QAEA+P,UAAU,CAAC,YAAY;UACrB/P,KAAK,CAACmU,UAAN,GAAmB,KAAnB;QACD,CAFS,EAEP,EAFO,CAAV;MAGD,CAVM;MAWPO,aAAa,EAAE,SAASA,aAAT,GAAyB;QACtC,KAAKyB,aAAL,CAAmB,KAAKnR,aAAxB;QACA,KAAKkP,kBAAL;MACD,CAdM;MAePS,WAAW,EAAE,SAASA,WAAT,GAAuB;QAClC,KAAK3P,aAAL,GAAqB,EAArB;QACA,KAAKmR,aAAL,CAAmB,KAAKnR,aAAxB;QACA,KAAKkP,kBAAL;MACD,CAnBM;MAoBPW,YAAY,EAAE,SAASA,YAAT,CAAsBD,WAAtB,EAAmC;QAC/C,KAAKA,WAAL,GAAmBA,WAAnB;;QAEA,IAAI,OAAOA,WAAP,KAAuB,WAAvB,IAAsCA,WAAW,KAAK,IAA1D,EAAgE;UAC9D,KAAKuB,aAAL,CAAmB,KAAKnR,aAAxB;QACD,CAFD,MAEO;UACL,KAAKmR,aAAL,CAAmB,EAAnB;QACD;;QAED,KAAKjC,kBAAL;MACD,CA9BM;MA+BPiC,aAAa,EAAE,SAASA,aAAT,CAAuBnR,aAAvB,EAAsC;QACnD,KAAKpT,KAAL,CAAWkE,KAAX,CAAiBmP,MAAjB,CAAwB,cAAxB,EAAwC;UACtCnT,MAAM,EAAE,KAAKA,MADyB;UAEtC6R,MAAM,EAAEqB;QAF8B,CAAxC;QAIA,KAAKpT,KAAL,CAAWkE,KAAX,CAAiBwN,iBAAjB;MACD;IArCM,CAtB8D;IA8DvErN,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,OAAO;QACLrE,KAAK,EAAE,IADF;QAEL1B,IAAI,EAAE,IAFD;QAGL4B,MAAM,EAAE;MAHH,CAAP;IAKD,CApEsE;IAuEvE+L,QAAQ,EAAE;MACR+D,OAAO,EAAE,SAASA,OAAT,GAAmB;QAC1B,OAAO,KAAK9P,MAAL,IAAe,KAAKA,MAAL,CAAY8P,OAAlC;MACD,CAHO;MAMRgT,WAAW,EAAE;QACXtpB,GAAG,EAAE,SAASA,GAAT,GAAe;UAClB,OAAO,CAAC,KAAKwG,MAAL,CAAYkT,aAAZ,IAA6B,EAA9B,EAAkC,CAAlC,CAAP;QACD,CAHU;QAIXoR,GAAG,EAAE,SAASA,GAAT,CAAa1qB,KAAb,EAAoB;UACvB,IAAI,KAAKsZ,aAAT,EAAwB;YACtB,IAAI,OAAOtZ,KAAP,KAAiB,WAAjB,IAAgCA,KAAK,KAAK,IAA9C,EAAoD;cAClD,KAAKsZ,aAAL,CAAmB3Q,MAAnB,CAA0B,CAA1B,EAA6B,CAA7B,EAAgC3I,KAAhC;YACD,CAFD,MAEO;cACL,KAAKsZ,aAAL,CAAmB3Q,MAAnB,CAA0B,CAA1B,EAA6B,CAA7B;YACD;UACF;QACF;MAZU,CANL;MAqBR2Q,aAAa,EAAE;QACb1Z,GAAG,EAAE,SAASA,GAAT,GAAe;UAClB,IAAI,KAAKwG,MAAT,EAAiB;YACf,OAAO,KAAKA,MAAL,CAAYkT,aAAZ,IAA6B,EAApC;UACD;;UACD,OAAO,EAAP;QACD,CANY;QAOboR,GAAG,EAAE,SAASA,GAAT,CAAa1qB,KAAb,EAAoB;UACvB,IAAI,KAAKoG,MAAT,EAAiB;YACf,KAAKA,MAAL,CAAYkT,aAAZ,GAA4BtZ,KAA5B;UACD;QACF;MAXY,CArBP;MAmCRuoB,QAAQ,EAAE,SAASA,QAAT,GAAoB;QAC5B,IAAI,KAAKniB,MAAT,EAAiB;UACf,OAAO,KAAKA,MAAL,CAAYukB,cAAnB;QACD;;QACD,OAAO,IAAP;MACD;IAxCO,CAvE6D;IAkHvE1K,OAAO,EAAE,SAASA,OAAT,GAAmB;MAC1B,IAAI7C,MAAM,GAAG,IAAb;;MAEA,KAAKwN,SAAL,GAAiB,KAAKpO,GAAtB;MACA,KAAK+J,YAAL,GAAoB,KAAK/hB,IAAzB;MACA,KAAK0B,KAAL,CAAWqW,WAAX,CAAuBlO,gBAAvB,CAAwC,QAAxC,EAAkD,YAAY;QAC5D+O,MAAM,CAACyN,YAAP;MACD,CAFD;MAIA,KAAKC,MAAL,CAAY,YAAZ,EAA0B,UAAU9qB,KAAV,EAAiB;QACzC,IAAIod,MAAM,CAAChX,MAAX,EAAmBgX,MAAM,CAAChX,MAAP,CAAc2kB,YAAd,GAA6B/qB,KAA7B;;QACnB,IAAIA,KAAJ,EAAW;UACT4pB,QAAQ,CAACE,IAAT,CAAc1M,MAAd;QACD,CAFD,MAEO;UACLwM,QAAQ,CAACG,KAAT,CAAe3M,MAAf;QACD;MACF,CAPD;IAQD,CAnIsE;IAqIvE3K,KAAK,EAAE;MACLgW,UAAU,EAAE,SAASA,UAAT,CAAoB7O,GAApB,EAAyB;QACnC,IAAIA,GAAG,KAAK,IAAR,IAAgBpS,QAAQ,CAAC,KAAKwjB,QAAL,CAAcC,OAAd,CAAsB7f,KAAtB,CAA4B8f,MAA7B,EAAqC,EAArC,CAAR,GAAmD1B,MAAM,CAAC,cAAD,CAAN,CAAuB0B,MAA9F,EAAsG;UACpG,KAAKF,QAAL,CAAcC,OAAd,CAAsB7f,KAAtB,CAA4B8f,MAA5B,GAAqC1B,MAAM,CAAC,cAAD,CAAN,CAAuB2B,UAAvB,EAArC;QACD;MACF;IALI;EArIgE,CAA5C,CA5pFqC,CAyyFlE;;EACC;;EAA6B,IAAIC,wCAAwC,GAAIhB,oCAAhD,CA1yFoC,CA2yFlE;;EACA,IAAIiB,mBAAmB,GAAGvsB,mBAAmB,CAAC,CAAD,CAA7C,CA5yFkE,CA8yFlE;;EAMA;;;EAEA,IAAIwsB,SAAS,GAAG7rB,MAAM,CAAC4rB,mBAAmB,CAAC;EAAI;EAAL,CAApB,CAAN,CACdD,wCADc,EAEd9C,gDAFc,EAGde,yDAHc,EAId,KAJc,EAKd,IALc,EAMd,IANc,EAOd,IAPc,CAAhB;EAWA;;EACA,IAAI,KAAJ,EAAW;IAAE,IAAIkC,GAAJ;EAAU;;EACvBD,SAAS,CAAC3pB,OAAV,CAAkB6pB,MAAlB,GAA2B,qCAA3B;EACA;;EAA6B,IAAIC,YAAY,GAAIH,SAAS,CAAC3sB,OAA9B,CAp0FqC,CAq0FlE;;EACA,IAAI+sB,oBAAoB,GAAGjsB,MAAM,CAACgS,MAAP,IAAiB,UAAUhN,MAAV,EAAkB;IAAE,KAAK,IAAIzF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8I,SAAS,CAACzC,MAA9B,EAAsCrG,CAAC,EAAvC,EAA2C;MAAE,IAAI0S,MAAM,GAAG5J,SAAS,CAAC9I,CAAD,CAAtB;;MAA2B,KAAK,IAAIsB,GAAT,IAAgBoR,MAAhB,EAAwB;QAAE,IAAIjS,MAAM,CAACoB,SAAP,CAAiBC,cAAjB,CAAgC5B,IAAhC,CAAqCwS,MAArC,EAA6CpR,GAA7C,CAAJ,EAAuD;UAAEmE,MAAM,CAACnE,GAAD,CAAN,GAAcoR,MAAM,CAACpR,GAAD,CAApB;QAA4B;MAAE;IAAE;;IAAC,OAAOmE,MAAP;EAAgB,CAA5Q;;EASA,IAAIknB,aAAa,GAAG,SAASA,aAAT,CAAuBtlB,OAAvB,EAAgC;IAClD,IAAI6O,MAAM,GAAG,EAAb;IACA7O,OAAO,CAACC,OAAR,CAAgB,UAAUF,MAAV,EAAkB;MAChC,IAAIA,MAAM,CAAC8C,QAAX,EAAqB;QACnBgM,MAAM,CAACzM,IAAP,CAAYrC,MAAZ;QACA8O,MAAM,CAACzM,IAAP,CAAYN,KAAZ,CAAkB+M,MAAlB,EAA0ByW,aAAa,CAACvlB,MAAM,CAAC8C,QAAR,CAAvC;MACD,CAHD,MAGO;QACLgM,MAAM,CAACzM,IAAP,CAAYrC,MAAZ;MACD;IACF,CAPD;IAQA,OAAO8O,MAAP;EACD,CAXD;;EAaA,IAAI0W,aAAa,GAAG,SAASA,aAAT,CAAuBtW,aAAvB,EAAsC;IACxD,IAAIuW,QAAQ,GAAG,CAAf;;IACA,IAAI7D,QAAQ,GAAG,SAASA,QAAT,CAAkB5hB,MAAlB,EAA0BjE,MAA1B,EAAkC;MAC/C,IAAIA,MAAJ,EAAY;QACViE,MAAM,CAAC+C,KAAP,GAAehH,MAAM,CAACgH,KAAP,GAAe,CAA9B;;QACA,IAAI0iB,QAAQ,GAAGzlB,MAAM,CAAC+C,KAAtB,EAA6B;UAC3B0iB,QAAQ,GAAGzlB,MAAM,CAAC+C,KAAlB;QACD;MACF;;MACD,IAAI/C,MAAM,CAAC8C,QAAX,EAAqB;QACnB,IAAI4iB,OAAO,GAAG,CAAd;QACA1lB,MAAM,CAAC8C,QAAP,CAAgB5C,OAAhB,CAAwB,UAAUylB,SAAV,EAAqB;UAC3C/D,QAAQ,CAAC+D,SAAD,EAAY3lB,MAAZ,CAAR;UACA0lB,OAAO,IAAIC,SAAS,CAACD,OAArB;QACD,CAHD;QAIA1lB,MAAM,CAAC0lB,OAAP,GAAiBA,OAAjB;MACD,CAPD,MAOO;QACL1lB,MAAM,CAAC0lB,OAAP,GAAiB,CAAjB;MACD;IACF,CAjBD;;IAmBAxW,aAAa,CAAChP,OAAd,CAAsB,UAAUF,MAAV,EAAkB;MACtCA,MAAM,CAAC+C,KAAP,GAAe,CAAf;MACA6e,QAAQ,CAAC5hB,MAAD,CAAR;IACD,CAHD;IAKA,IAAIke,IAAI,GAAG,EAAX;;IACA,KAAK,IAAItlB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6sB,QAApB,EAA8B7sB,CAAC,EAA/B,EAAmC;MACjCslB,IAAI,CAAC7b,IAAL,CAAU,EAAV;IACD;;IAED,IAAIujB,UAAU,GAAGL,aAAa,CAACrW,aAAD,CAA9B;IAEA0W,UAAU,CAAC1lB,OAAX,CAAmB,UAAUF,MAAV,EAAkB;MACnC,IAAI,CAACA,MAAM,CAAC8C,QAAZ,EAAsB;QACpB9C,MAAM,CAAC6lB,OAAP,GAAiBJ,QAAQ,GAAGzlB,MAAM,CAAC+C,KAAlB,GAA0B,CAA3C;MACD,CAFD,MAEO;QACL/C,MAAM,CAAC6lB,OAAP,GAAiB,CAAjB;MACD;;MACD3H,IAAI,CAACle,MAAM,CAAC+C,KAAP,GAAe,CAAhB,CAAJ,CAAuBV,IAAvB,CAA4BrC,MAA5B;IACD,CAPD;IASA,OAAOke,IAAP;EACD,CA3CD;EA6CA;;;EAA6B,IAAI4H,YAAY,GAAI;IAC/C5sB,IAAI,EAAE,eADyC;IAG/C+W,MAAM,EAAE,CAACwJ,eAAD,CAHuC;IAK/Cze,MAAM,EAAE,SAASA,MAAT,CAAgB0B,CAAhB,EAAmB;MACzB,IAAIwR,KAAK,GAAG,IAAZ;;MAEA,IAAIgB,aAAa,GAAG,KAAKlL,KAAL,CAAWC,MAAX,CAAkBiL,aAAtC;MACA,IAAI6W,UAAU,GAAGP,aAAa,CAACtW,aAAD,EAAgB,KAAKjP,OAArB,CAA9B,CAJyB,CAKzB;;MACA,IAAIyD,OAAO,GAAGqiB,UAAU,CAAC9mB,MAAX,GAAoB,CAAlC;MACA,IAAIyE,OAAJ,EAAa,KAAK2Z,OAAL,CAAa3Z,OAAb,GAAuB,IAAvB;MACb,OAAOhH,CAAC,CACN,OADM,EAEN;QACE,SAAS,kBADX;QAEEwI,KAAK,EAAE;UAAE0X,WAAW,EAAE,GAAf;UACLC,WAAW,EAAE,GADR;UAELpZ,MAAM,EAAE;QAFH;MAFT,CAFM,EAQN,CAAC/G,CAAC,CAAC,UAAD,EAAa,CAAC,KAAKuD,OAAL,CAAab,GAAb,CAAiB,UAAUY,MAAV,EAAkB;QACjD,OAAOtD,CAAC,CAAC,KAAD,EAAQ;UACdwI,KAAK,EAAE;YAAEhM,IAAI,EAAE8G,MAAM,CAACG;UAAf,CADO;UAEdjG,GAAG,EAAE8F,MAAM,CAACG;QAFE,CAAR,CAAR;MAGD,CAJe,CAAD,EAIX,KAAK6lB,SAAL,GAAiBtpB,CAAC,CAAC,KAAD,EAAQ;QAC5BwI,KAAK,EAAE;UAAEhM,IAAI,EAAE;QAAR;MADqB,CAAR,CAAlB,GAEC,EANU,CAAb,CAAF,EAMWwD,CAAC,CACV,OADU,EAEV;QAAE,SAAS,CAAC;UAAE,YAAYgH,OAAd;UAAuB,cAAc,KAAKsiB;QAA1C,CAAD;MAAX,CAFU,EAGV,CAAC,KAAKxD,EAAL,CAAQuD,UAAR,EAAoB,UAAU9lB,OAAV,EAAmBwe,QAAnB,EAA6B;QAChD,OAAO/hB,CAAC,CACN,IADM,EAEN;UACEsI,KAAK,EAAEkJ,KAAK,CAAC+X,iBAAN,CAAwBxH,QAAxB,CADT;UAEE,SAASvQ,KAAK,CAACgY,iBAAN,CAAwBzH,QAAxB;QAFX,CAFM,EAMN,CAACxe,OAAO,CAACb,GAAR,CAAY,UAAUY,MAAV,EAAkBqb,SAAlB,EAA6B;UACxC,OAAO3e,CAAC,CACN,IADM,EAEN;YACEwI,KAAK,EAAE;cACLuW,OAAO,EAAEzb,MAAM,CAAC0lB,OADX;cAELlK,OAAO,EAAExb,MAAM,CAAC6lB;YAFX,CADT;YAKExhB,EAAE,EAAE;cACF,aAAa,SAAS8hB,SAAT,CAAmB5hB,MAAnB,EAA2B;gBACtC,OAAO2J,KAAK,CAACkY,eAAN,CAAsB7hB,MAAtB,EAA8BvE,MAA9B,CAAP;cACD,CAHC;cAIF,YAAYkO,KAAK,CAACmY,cAJhB;cAKF,aAAa,SAASC,SAAT,CAAmB/hB,MAAnB,EAA2B;gBACtC,OAAO2J,KAAK,CAACqY,eAAN,CAAsBhiB,MAAtB,EAA8BvE,MAA9B,CAAP;cACD,CAPC;cAQF,SAAS,SAASohB,KAAT,CAAe7c,MAAf,EAAuB;gBAC9B,OAAO2J,KAAK,CAACsY,iBAAN,CAAwBjiB,MAAxB,EAAgCvE,MAAhC,CAAP;cACD,CAVC;cAWF,eAAe,SAASqhB,WAAT,CAAqB9c,MAArB,EAA6B;gBAC1C,OAAO2J,KAAK,CAACuY,uBAAN,CAA8BliB,MAA9B,EAAsCvE,MAAtC,CAAP;cACD;YAbC,CALN;YAqBEgF,KAAK,EAAEkJ,KAAK,CAACwY,kBAAN,CAAyBjI,QAAzB,EAAmCpD,SAAnC,EAA8Cpb,OAA9C,EAAuDD,MAAvD,CArBT;YAsBE,SAASkO,KAAK,CAACyY,kBAAN,CAAyBlI,QAAzB,EAAmCpD,SAAnC,EAA8Cpb,OAA9C,EAAuDD,MAAvD,CAtBX;YAuBE9F,GAAG,EAAE8F,MAAM,CAACG;UAvBd,CAFM,EA0BN,CAACzD,CAAC,CACA,KADA,EAEA;YAAE,SAAS,CAAC,MAAD,EAASsD,MAAM,CAACkT,aAAP,IAAwBlT,MAAM,CAACkT,aAAP,CAAqBjU,MAArB,GAA8B,CAAtD,GAA0D,WAA1D,GAAwE,EAAjF,EAAqFe,MAAM,CAAC4mB,cAA5F;UAAX,CAFA,EAGA,CAAC5mB,MAAM,CAAC6mB,YAAP,GAAsB7mB,MAAM,CAAC6mB,YAAP,CAAoB/tB,IAApB,CAAyBoV,KAAK,CAACiO,YAA/B,EAA6Czf,CAA7C,EAAgD;YAAEsD,MAAM,EAAEA,MAAV;YAAkB8a,MAAM,EAAEO,SAA1B;YAAqCrX,KAAK,EAAEkK,KAAK,CAAClK,KAAlD;YAAyDZ,KAAK,EAAE8K,KAAK,CAACmP,OAAN,CAAcxhB,MAAd,CAAqBD;UAArF,CAAhD,CAAtB,GAAwKoE,MAAM,CAACyiB,KAAhL,EAAuLziB,MAAM,CAAC0O,QAAP,GAAkBhS,CAAC,CACxM,MADwM,EAExM;YACE,SAAS,eADX;YAEE2H,EAAE,EAAE;cACF,SAAS,SAAS+c,KAAT,CAAe7c,MAAf,EAAuB;gBAC9B,OAAO2J,KAAK,CAAC4Y,eAAN,CAAsBviB,MAAtB,EAA8BvE,MAA9B,CAAP;cACD;YAHC;UAFN,CAFwM,EAUxM,CAACtD,CAAC,CAAC,GAAD,EAAM;YAAE,SAAS,sBAAX;YACN2H,EAAE,EAAE;cACF,SAAS,SAAS+c,KAAT,CAAe7c,MAAf,EAAuB;gBAC9B,OAAO2J,KAAK,CAAC4Y,eAAN,CAAsBviB,MAAtB,EAA8BvE,MAA9B,EAAsC,WAAtC,CAAP;cACD;YAHC;UADE,CAAN,CAAF,EAMItD,CAAC,CAAC,GAAD,EAAM;YAAE,SAAS,uBAAX;YACT2H,EAAE,EAAE;cACF,SAAS,SAAS+c,KAAT,CAAe7c,MAAf,EAAuB;gBAC9B,OAAO2J,KAAK,CAAC4Y,eAAN,CAAsBviB,MAAtB,EAA8BvE,MAA9B,EAAsC,YAAtC,CAAP;cACD;YAHC;UADK,CAAN,CANL,CAVwM,CAAnB,GAuBnL,EAvBJ,EAuBQA,MAAM,CAAC+mB,UAAP,GAAoBrqB,CAAC,CAC3B,MAD2B,EAE3B;YACE,SAAS,iCADX;YAEE2H,EAAE,EAAE;cACF,SAAS,SAAS+c,KAAT,CAAe7c,MAAf,EAAuB;gBAC9B,OAAO2J,KAAK,CAAC8Y,iBAAN,CAAwBziB,MAAxB,EAAgCvE,MAAhC,CAAP;cACD;YAHC;UAFN,CAF2B,EAU3B,CAACtD,CAAC,CAAC,GAAD,EAAM;YAAE,SAAS,CAAC,oBAAD,EAAuBsD,MAAM,CAAC2kB,YAAP,GAAsB,kBAAtB,GAA2C,EAAlE;UAAX,CAAN,CAAF,CAV2B,CAArB,GAWJ,EAlCJ,CAHA,CAAF,CA1BM,CAAR;QAkED,CAnEA,CAAD,EAmEIzW,KAAK,CAAC8X,SAAN,GAAkBtpB,CAAC,CAAC,IAAD,EAAO;UAAE,SAAS;QAAX,CAAP,CAAnB,GAAkE,EAnEtE,CANM,CAAR;MA2ED,CA5EA,CAAD,CAHU,CANZ,CARM,CAAR;IAgGD,CA7G8C;IAgH/Cie,KAAK,EAAE;MACLnU,KAAK,EAAEkW,MADF;MAEL1Y,KAAK,EAAE;QACLwY,QAAQ,EAAE;MADL,CAFF;MAKL/Y,MAAM,EAAEgZ,OALH;MAMLtX,WAAW,EAAE;QACXiL,IAAI,EAAE/W,MADK;QAEX+qB,OAAO,EAAE,SAAS6C,QAAT,GAAoB;UAC3B,OAAO;YACLjV,IAAI,EAAE,EADD;YAELpS,KAAK,EAAE;UAFF,CAAP;QAID;MAPU;IANR,CAhHwC;IAiI/Cgb,UAAU,EAAE;MACVC,UAAU,EAAEzT,gBAAgB,CAAC5H;IADnB,CAjImC;IAqI/CuM,QAAQ,EAAEuZ,oBAAoB,CAAC;MAC7BxlB,KAAK,EAAE,SAASA,KAAT,GAAiB;QACtB,OAAO,KAAKud,OAAZ;MACD,CAH4B;MAI7B2I,SAAS,EAAE,SAASA,SAAT,GAAqB;QAC9B,OAAO,CAAC,KAAKxf,KAAN,IAAe,KAAKmT,WAAL,CAAiB3S,WAAvC;MACD;IAN4B,CAAD,EAO3BkO,SAAS,CAAC;MACXjV,OAAO,EAAE,SADE;MAEXwP,aAAa,EAAE,eAFJ;MAGX6N,kBAAkB,EAAE,wBAHT;MAIXC,mBAAmB,EAAE,6BAJV;MAKXC,YAAY,EAAE,SAASA,YAAT,CAAsBvZ,MAAtB,EAA8B;QAC1C,OAAOA,MAAM,CAAChE,OAAP,CAAehB,MAAtB;MACD,CAPU;MAQXwe,cAAc,EAAE,SAASA,cAAT,CAAwBxZ,MAAxB,EAAgC;QAC9C,OAAOA,MAAM,CAACmC,YAAP,CAAoBnH,MAA3B;MACD,CAVU;MAWXye,eAAe,EAAE,SAASA,eAAT,CAAyBzZ,MAAzB,EAAiC;QAChD,OAAOA,MAAM,CAAC4C,iBAAP,CAAyB5H,MAAhC;MACD;IAbU,CAAD,CAPkB,CArIiB;IA4J/Cya,OAAO,EAAE,SAASA,OAAT,GAAmB;MAC1B,KAAK1G,YAAL,GAAoB,EAApB;IACD,CA9J8C;IA+J/C6G,OAAO,EAAE,SAASA,OAAT,GAAmB;MAC1B,IAAI7C,MAAM,GAAG,IAAb,CAD0B,CAG1B;;;MACA,KAAKkQ,SAAL,CAAe,YAAY;QACzB,IAAIC,YAAY,GAAGnQ,MAAM,CAAC7R,WAA1B;QAAA,IACI6M,IAAI,GAAGmV,YAAY,CAACnV,IADxB;QAAA,IAEIpS,KAAK,GAAGunB,YAAY,CAACvnB,KAFzB;QAIA,IAAIqU,IAAI,GAAG,IAAX;;QACA+C,MAAM,CAAChT,KAAP,CAAamP,MAAb,CAAoB,MAApB,EAA4B;UAAEnB,IAAI,EAAEA,IAAR;UAAcpS,KAAK,EAAEA,KAArB;UAA4BqU,IAAI,EAAEA;QAAlC,CAA5B;MACD,CAPD;IAQD,CA3K8C;IA4K/CmT,aAAa,EAAE,SAASA,aAAT,GAAyB;MACtC,IAAIrU,MAAM,GAAG,KAAKC,YAAlB;;MACA,KAAK,IAAIhB,IAAT,IAAiBe,MAAjB,EAAyB;QACvB,IAAIA,MAAM,CAACrY,cAAP,CAAsBsX,IAAtB,KAA+Be,MAAM,CAACf,IAAD,CAAzC,EAAiD;UAC/Ce,MAAM,CAACf,IAAD,CAAN,CAAaqV,QAAb,CAAsB,IAAtB;QACD;MACF;IACF,CAnL8C;IAsL/Cne,OAAO,EAAE;MACPoe,YAAY,EAAE,SAASA,YAAT,CAAsBnoB,KAAtB,EAA6Bc,OAA7B,EAAsC;QAClD,IAAIsnB,KAAK,GAAG,CAAZ;;QACA,KAAK,IAAI3uB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuG,KAApB,EAA2BvG,CAAC,EAA5B,EAAgC;UAC9B2uB,KAAK,IAAItnB,OAAO,CAACrH,CAAD,CAAP,CAAW8sB,OAApB;QACD;;QACD,IAAI8B,KAAK,GAAGD,KAAK,GAAGtnB,OAAO,CAACd,KAAD,CAAP,CAAeumB,OAAvB,GAAiC,CAA7C;;QACA,IAAI,KAAKlf,KAAL,KAAe,IAAf,IAAuB,KAAKA,KAAL,KAAe,MAA1C,EAAkD;UAChD,OAAOghB,KAAK,IAAI,KAAKlK,kBAArB;QACD,CAFD,MAEO,IAAI,KAAK9W,KAAL,KAAe,OAAnB,EAA4B;UACjC,OAAO+gB,KAAK,GAAG,KAAK/J,YAAL,GAAoB,KAAKD,mBAAxC;QACD,CAFM,MAEA;UACL,OAAOiK,KAAK,GAAG,KAAKlK,kBAAb,IAAmCiK,KAAK,IAAI,KAAK/J,YAAL,GAAoB,KAAKD,mBAA5E;QACD;MACF,CAdM;MAeP0I,iBAAiB,EAAE,SAASA,iBAAT,CAA2BxH,QAA3B,EAAqC;QACtD,IAAIgJ,cAAc,GAAG,KAAK3nB,KAAL,CAAW2nB,cAAhC;;QACA,IAAI,OAAOA,cAAP,KAA0B,UAA9B,EAA0C;UACxC,OAAOA,cAAc,CAAC3uB,IAAf,CAAoB,IAApB,EAA0B;YAAE2lB,QAAQ,EAAEA;UAAZ,CAA1B,CAAP;QACD;;QACD,OAAOgJ,cAAP;MACD,CArBM;MAsBPvB,iBAAiB,EAAE,SAASA,iBAAT,CAA2BzH,QAA3B,EAAqC;QACtD,IAAIK,OAAO,GAAG,EAAd;QAEA,IAAI4I,kBAAkB,GAAG,KAAK5nB,KAAL,CAAW4nB,kBAApC;;QACA,IAAI,OAAOA,kBAAP,KAA8B,QAAlC,EAA4C;UAC1C5I,OAAO,CAACzc,IAAR,CAAaqlB,kBAAb;QACD,CAFD,MAEO,IAAI,OAAOA,kBAAP,KAA8B,UAAlC,EAA8C;UACnD5I,OAAO,CAACzc,IAAR,CAAaqlB,kBAAkB,CAAC5uB,IAAnB,CAAwB,IAAxB,EAA8B;YAAE2lB,QAAQ,EAAEA;UAAZ,CAA9B,CAAb;QACD;;QAED,OAAOK,OAAO,CAACI,IAAR,CAAa,GAAb,CAAP;MACD,CAjCM;MAkCPwH,kBAAkB,EAAE,SAASA,kBAAT,CAA4BjI,QAA5B,EAAsCC,WAAtC,EAAmDle,GAAnD,EAAwDR,MAAxD,EAAgE;QAClF,IAAI2nB,eAAe,GAAG,KAAK7nB,KAAL,CAAW6nB,eAAjC;;QACA,IAAI,OAAOA,eAAP,KAA2B,UAA/B,EAA2C;UACzC,OAAOA,eAAe,CAAC7uB,IAAhB,CAAqB,IAArB,EAA2B;YAChC2lB,QAAQ,EAAEA,QADsB;YAEhCC,WAAW,EAAEA,WAFmB;YAGhCle,GAAG,EAAEA,GAH2B;YAIhCR,MAAM,EAAEA;UAJwB,CAA3B,CAAP;QAMD;;QACD,OAAO2nB,eAAP;MACD,CA7CM;MA8CPhB,kBAAkB,EAAE,SAASA,kBAAT,CAA4BlI,QAA5B,EAAsCC,WAAtC,EAAmDle,GAAnD,EAAwDR,MAAxD,EAAgE;QAClF,IAAI8e,OAAO,GAAG,CAAC9e,MAAM,CAACG,EAAR,EAAYH,MAAM,CAACJ,KAAnB,EAA0BI,MAAM,CAAC4nB,WAAjC,EAA8C5nB,MAAM,CAACM,SAArD,EAAgEN,MAAM,CAAC4mB,cAAvE,CAAd;;QAEA,IAAInI,QAAQ,KAAK,CAAb,IAAkB,KAAK6I,YAAL,CAAkB5I,WAAlB,EAA+Ble,GAA/B,CAAtB,EAA2D;UACzDse,OAAO,CAACzc,IAAR,CAAa,WAAb;QACD;;QAED,IAAI,CAACrC,MAAM,CAAC8C,QAAZ,EAAsB;UACpBgc,OAAO,CAACzc,IAAR,CAAa,SAAb;QACD;;QAED,IAAIrC,MAAM,CAAC0O,QAAX,EAAqB;UACnBoQ,OAAO,CAACzc,IAAR,CAAa,aAAb;QACD;;QAED,IAAIwlB,mBAAmB,GAAG,KAAK/nB,KAAL,CAAW+nB,mBAArC;;QACA,IAAI,OAAOA,mBAAP,KAA+B,QAAnC,EAA6C;UAC3C/I,OAAO,CAACzc,IAAR,CAAawlB,mBAAb;QACD,CAFD,MAEO,IAAI,OAAOA,mBAAP,KAA+B,UAAnC,EAA+C;UACpD/I,OAAO,CAACzc,IAAR,CAAawlB,mBAAmB,CAAC/uB,IAApB,CAAyB,IAAzB,EAA+B;YAC1C2lB,QAAQ,EAAEA,QADgC;YAE1CC,WAAW,EAAEA,WAF6B;YAG1Cle,GAAG,EAAEA,GAHqC;YAI1CR,MAAM,EAAEA;UAJkC,CAA/B,CAAb;QAMD;;QAED8e,OAAO,CAACzc,IAAR,CAAa,gBAAb;QAEA,OAAOyc,OAAO,CAACI,IAAR,CAAa,GAAb,CAAP;MACD,CA5EM;MA6EP5K,kBAAkB,EAAE,SAASA,kBAAT,GAA8B;QAChD,KAAKtQ,KAAL,CAAWmP,MAAX,CAAkB,oBAAlB;MACD,CA/EM;MAgFP6T,iBAAiB,EAAE,SAASA,iBAAT,CAA2B7oB,KAA3B,EAAkC6B,MAAlC,EAA0C;QAC3D7B,KAAK,CAAC2pB,eAAN;QACA,IAAIzpB,MAAM,GAAGF,KAAK,CAACE,MAAnB;QACA,IAAID,IAAI,GAAGC,MAAM,CAACC,OAAP,KAAmB,IAAnB,GAA0BD,MAA1B,GAAmCA,MAAM,CAACG,UAArD;QACA,IAAInF,MAAM,CAACigB,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBlb,IAAzB,EAA+B,SAA/B,CAAJ,EAA+C;QAC/CA,IAAI,GAAGA,IAAI,CAACkY,aAAL,CAAmB,kCAAnB,KAA0DlY,IAAjE;QACA,IAAI0B,KAAK,GAAG,KAAKud,OAAjB;QAEA,IAAI0K,WAAW,GAAG,KAAK/U,YAAL,CAAkBhT,MAAM,CAACG,EAAzB,CAAlB;;QAEA,IAAI4nB,WAAW,IAAI/nB,MAAM,CAAC2kB,YAA1B,EAAwC;UACtCoD,WAAW,CAAC1F,UAAZ,GAAyB,KAAzB;UACA;QACD;;QAED,IAAI,CAAC0F,WAAL,EAAkB;UAChBA,WAAW,GAAG,IAAIpf,oBAAoB,CAACnJ,CAAzB,CAA2B6lB,YAA3B,CAAd;UACA,KAAKrS,YAAL,CAAkBhT,MAAM,CAACG,EAAzB,IAA+B4nB,WAA/B;;UACA,IAAI/nB,MAAM,CAACgoB,eAAX,EAA4B;YAC1BD,WAAW,CAAC7K,SAAZ,GAAwBld,MAAM,CAACgoB,eAA/B;UACD;;UACDD,WAAW,CAACjoB,KAAZ,GAAoBA,KAApB;UACAioB,WAAW,CAAC3pB,IAAZ,GAAmBA,IAAnB;UACA2pB,WAAW,CAAC/nB,MAAZ,GAAqBA,MAArB;UACA,CAAC,KAAK0W,SAAN,IAAmBqR,WAAW,CAACE,MAAZ,CAAmBxI,QAAQ,CAACyI,aAAT,CAAuB,KAAvB,CAAnB,CAAnB;QACD;;QAEDjK,UAAU,CAAC,YAAY;UACrB8J,WAAW,CAAC1F,UAAZ,GAAyB,IAAzB;QACD,CAFS,EAEP,EAFO,CAAV;MAGD,CA9GM;MA+GPmE,iBAAiB,EAAE,SAASA,iBAAT,CAA2BroB,KAA3B,EAAkC6B,MAAlC,EAA0C;QAC3D,IAAI,CAACA,MAAM,CAAC8P,OAAR,IAAmB9P,MAAM,CAAC0O,QAA9B,EAAwC;UACtC,KAAKoY,eAAL,CAAqB3oB,KAArB,EAA4B6B,MAA5B;QACD,CAFD,MAEO,IAAIA,MAAM,CAAC+mB,UAAP,IAAqB,CAAC/mB,MAAM,CAAC0O,QAAjC,EAA2C;UAChD,KAAKsY,iBAAL,CAAuB7oB,KAAvB,EAA8B6B,MAA9B;QACD;;QAED,KAAKqd,OAAL,CAAaxT,KAAb,CAAmB,cAAnB,EAAmC7J,MAAnC,EAA2C7B,KAA3C;MACD,CAvHM;MAwHPsoB,uBAAuB,EAAE,SAASA,uBAAT,CAAiCtoB,KAAjC,EAAwC6B,MAAxC,EAAgD;QACvE,KAAKqd,OAAL,CAAaxT,KAAb,CAAmB,oBAAnB,EAAyC7J,MAAzC,EAAiD7B,KAAjD;MACD,CA1HM;MA2HPooB,eAAe,EAAE,SAASA,eAAT,CAAyBpoB,KAAzB,EAAgC6B,MAAhC,EAAwC;QACvD,IAAIkZ,MAAM,GAAG,IAAb;;QAEA,IAAI,KAAKxC,SAAT,EAAoB;QACpB,IAAI1W,MAAM,CAAC8C,QAAP,IAAmB9C,MAAM,CAAC8C,QAAP,CAAgB7D,MAAhB,GAAyB,CAAhD,EAAmD;QACnD;;QACA,IAAI,KAAKkpB,cAAL,IAAuB,KAAK1kB,MAAhC,EAAwC;UACtC,KAAK2kB,QAAL,GAAgB,IAAhB;UAEA,KAAK/K,OAAL,CAAapW,kBAAb,GAAkC,IAAlC;UAEA,IAAInH,KAAK,GAAG,KAAKud,OAAjB;UACA,IAAIgL,OAAO,GAAGvoB,KAAK,CAACsW,GAApB;UACA,IAAIkS,SAAS,GAAGD,OAAO,CAACvI,qBAAR,GAAgCyI,IAAhD;UACA,IAAIC,QAAQ,GAAG,KAAKpS,GAAL,CAASE,aAAT,CAAuB,QAAQtW,MAAM,CAACG,EAAtC,CAAf;UACA,IAAIsoB,UAAU,GAAGD,QAAQ,CAAC1I,qBAAT,EAAjB;UACA,IAAI4I,OAAO,GAAGD,UAAU,CAACF,IAAX,GAAkBD,SAAlB,GAA8B,EAA5C;UAEAjvB,MAAM,CAACigB,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBkP,QAAzB,EAAmC,SAAnC;UAEA,KAAKG,SAAL,GAAiB;YACfC,cAAc,EAAEzqB,KAAK,CAAC0qB,OADP;YAEfC,SAAS,EAAEL,UAAU,CAAC1hB,KAAX,GAAmBuhB,SAFf;YAGfS,eAAe,EAAEN,UAAU,CAACF,IAAX,GAAkBD,SAHpB;YAIfA,SAAS,EAAEA;UAJI,CAAjB;UAOA,IAAIU,WAAW,GAAGlpB,KAAK,CAAC6S,KAAN,CAAYqW,WAA9B;UACAA,WAAW,CAAChkB,KAAZ,CAAkBujB,IAAlB,GAAyB,KAAKI,SAAL,CAAeG,SAAf,GAA2B,IAApD;;UAEArJ,QAAQ,CAACwJ,aAAT,GAAyB,YAAY;YACnC,OAAO,KAAP;UACD,CAFD;;UAGAxJ,QAAQ,CAACyJ,WAAT,GAAuB,YAAY;YACjC,OAAO,KAAP;UACD,CAFD;;UAIA,IAAI9C,eAAe,GAAG,SAASA,eAAT,CAAyBjoB,KAAzB,EAAgC;YACpD,IAAIgrB,SAAS,GAAGhrB,KAAK,CAAC0qB,OAAN,GAAgB3P,MAAM,CAACyP,SAAP,CAAiBC,cAAjD;YACA,IAAIQ,SAAS,GAAGlQ,MAAM,CAACyP,SAAP,CAAiBG,SAAjB,GAA6BK,SAA7C;YAEAH,WAAW,CAAChkB,KAAZ,CAAkBujB,IAAlB,GAAyB5P,IAAI,CAACE,GAAL,CAAS6P,OAAT,EAAkBU,SAAlB,IAA+B,IAAxD;UACD,CALD;;UAOA,IAAIC,aAAa,GAAG,SAASA,aAAT,GAAyB;YAC3C,IAAInQ,MAAM,CAACkP,QAAX,EAAqB;cACnB,IAAIkB,UAAU,GAAGpQ,MAAM,CAACyP,SAAxB;cAAA,IACII,eAAe,GAAGO,UAAU,CAACP,eADjC;cAAA,IAEID,SAAS,GAAGQ,UAAU,CAACR,SAF3B;cAIA,IAAIS,SAAS,GAAGnoB,QAAQ,CAAC4nB,WAAW,CAAChkB,KAAZ,CAAkBujB,IAAnB,EAAyB,EAAzB,CAAxB;cACA,IAAIiB,WAAW,GAAGD,SAAS,GAAGR,eAA9B;cACA/oB,MAAM,CAACkB,KAAP,GAAelB,MAAM,CAACoY,SAAP,GAAmBoR,WAAlC;cACA1pB,KAAK,CAAC+J,KAAN,CAAY,gBAAZ,EAA8B7J,MAAM,CAACkB,KAArC,EAA4C4nB,SAAS,GAAGC,eAAxD,EAAyE/oB,MAAzE,EAAiF7B,KAAjF;;cAEA+a,MAAM,CAAClV,KAAP,CAAa8F,cAAb;;cAEA2V,QAAQ,CAACpJ,IAAT,CAAcrR,KAAd,CAAoBykB,MAApB,GAA6B,EAA7B;cACAvQ,MAAM,CAACkP,QAAP,GAAkB,KAAlB;cACAlP,MAAM,CAACiP,cAAP,GAAwB,IAAxB;cACAjP,MAAM,CAACyP,SAAP,GAAmB,EAAnB;cAEA7oB,KAAK,CAACmH,kBAAN,GAA2B,KAA3B;YACD;;YAEDwY,QAAQ,CAACiK,mBAAT,CAA6B,WAA7B,EAA0CtD,eAA1C;YACA3G,QAAQ,CAACiK,mBAAT,CAA6B,SAA7B,EAAwCL,aAAxC;YACA5J,QAAQ,CAACwJ,aAAT,GAAyB,IAAzB;YACAxJ,QAAQ,CAACyJ,WAAT,GAAuB,IAAvB;YAEAjL,UAAU,CAAC,YAAY;cACrB5kB,MAAM,CAACigB,IAAI,CAAC,aAAD,CAAL,CAAN,CAA4BkP,QAA5B,EAAsC,SAAtC;YACD,CAFS,EAEP,CAFO,CAAV;UAGD,CA7BD;;UA+BA/I,QAAQ,CAACxX,gBAAT,CAA0B,WAA1B,EAAuCme,eAAvC;UACA3G,QAAQ,CAACxX,gBAAT,CAA0B,SAA1B,EAAqCohB,aAArC;QACD;MACF,CAzMM;MA0MPjD,eAAe,EAAE,SAASA,eAAT,CAAyBjoB,KAAzB,EAAgC6B,MAAhC,EAAwC;QACvD,IAAIA,MAAM,CAAC8C,QAAP,IAAmB9C,MAAM,CAAC8C,QAAP,CAAgB7D,MAAhB,GAAyB,CAAhD,EAAmD;QACnD,IAAIZ,MAAM,GAAGF,KAAK,CAACE,MAAnB;;QACA,OAAOA,MAAM,IAAIA,MAAM,CAACC,OAAP,KAAmB,IAApC,EAA0C;UACxCD,MAAM,GAAGA,MAAM,CAACG,UAAhB;QACD;;QAED,IAAI,CAACwB,MAAD,IAAW,CAACA,MAAM,CAAC2pB,SAAvB,EAAkC;;QAElC,IAAI,CAAC,KAAKvB,QAAN,IAAkB,KAAK3kB,MAA3B,EAAmC;UACjC,IAAImmB,IAAI,GAAGvrB,MAAM,CAACyhB,qBAAP,EAAX;UAEA,IAAI+J,SAAS,GAAGpK,QAAQ,CAACpJ,IAAT,CAAcrR,KAA9B;;UACA,IAAI4kB,IAAI,CAAC1oB,KAAL,GAAa,EAAb,IAAmB0oB,IAAI,CAAC7iB,KAAL,GAAa5I,KAAK,CAAC2rB,KAAnB,GAA2B,CAAlD,EAAqD;YACnDD,SAAS,CAACJ,MAAV,GAAmB,YAAnB;;YACA,IAAIpwB,MAAM,CAACigB,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBjb,MAAzB,EAAiC,aAAjC,CAAJ,EAAqD;cACnDA,MAAM,CAAC2G,KAAP,CAAaykB,MAAb,GAAsB,YAAtB;YACD;;YACD,KAAKtB,cAAL,GAAsBnoB,MAAtB;UACD,CAND,MAMO,IAAI,CAAC,KAAKooB,QAAV,EAAoB;YACzByB,SAAS,CAACJ,MAAV,GAAmB,EAAnB;;YACA,IAAIpwB,MAAM,CAACigB,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBjb,MAAzB,EAAiC,aAAjC,CAAJ,EAAqD;cACnDA,MAAM,CAAC2G,KAAP,CAAaykB,MAAb,GAAsB,SAAtB;YACD;;YACD,KAAKtB,cAAL,GAAsB,IAAtB;UACD;QACF;MACF,CArOM;MAsOP9B,cAAc,EAAE,SAASA,cAAT,GAA0B;QACxC,IAAI,KAAK3P,SAAT,EAAoB;QACpB+I,QAAQ,CAACpJ,IAAT,CAAcrR,KAAd,CAAoBykB,MAApB,GAA6B,EAA7B;MACD,CAzOM;MA0OPM,WAAW,EAAE,SAASA,WAAT,CAAqBhd,IAArB,EAA2B;QACtC,IAAInN,KAAK,GAAGmN,IAAI,CAACnN,KAAjB;QAAA,IACIoqB,UAAU,GAAGjd,IAAI,CAACid,UADtB;QAGA,IAAIpqB,KAAK,KAAK,EAAd,EAAkB,OAAOoqB,UAAU,CAAC,CAAD,CAAjB;QAClB,IAAI7qB,KAAK,GAAG6qB,UAAU,CAACrpB,OAAX,CAAmBf,KAAK,IAAI,IAA5B,CAAZ;QACA,OAAOoqB,UAAU,CAAC7qB,KAAK,GAAG6qB,UAAU,CAAC/qB,MAAX,GAAoB,CAA5B,GAAgC,CAAhC,GAAoCE,KAAK,GAAG,CAA7C,CAAjB;MACD,CAjPM;MAkPP2nB,eAAe,EAAE,SAASA,eAAT,CAAyB3oB,KAAzB,EAAgC6B,MAAhC,EAAwCiqB,UAAxC,EAAoD;QACnE9rB,KAAK,CAAC2pB,eAAN;QACA,IAAIloB,KAAK,GAAGI,MAAM,CAACJ,KAAP,KAAiBqqB,UAAjB,GAA8B,IAA9B,GAAqCA,UAAU,IAAI,KAAKF,WAAL,CAAiB/pB,MAAjB,CAA/D;QAEA,IAAI3B,MAAM,GAAGF,KAAK,CAACE,MAAnB;;QACA,OAAOA,MAAM,IAAIA,MAAM,CAACC,OAAP,KAAmB,IAApC,EAA0C;UACxCD,MAAM,GAAGA,MAAM,CAACG,UAAhB;QACD;;QAED,IAAIH,MAAM,IAAIA,MAAM,CAACC,OAAP,KAAmB,IAAjC,EAAuC;UACrC,IAAIjF,MAAM,CAACigB,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBjb,MAAzB,EAAiC,SAAjC,CAAJ,EAAiD;YAC/ChF,MAAM,CAACigB,IAAI,CAAC,aAAD,CAAL,CAAN,CAA4Bjb,MAA5B,EAAoC,SAApC;YACA;UACD;QACF;;QAED,IAAI,CAAC2B,MAAM,CAAC0O,QAAZ,EAAsB;QAEtB,IAAIzK,MAAM,GAAG,KAAKD,KAAL,CAAWC,MAAxB;QACA,IAAI0K,QAAQ,GAAG1K,MAAM,CAAC0K,QAAtB;QACA,IAAIC,SAAS,GAAG,KAAK,CAArB;QACA,IAAIH,aAAa,GAAGxK,MAAM,CAACwK,aAA3B;;QAEA,IAAIA,aAAa,KAAKzO,MAAlB,IAA4ByO,aAAa,KAAKzO,MAAlB,IAA4ByO,aAAa,CAAC7O,KAAd,KAAwB,IAApF,EAA0F;UACxF,IAAI6O,aAAJ,EAAmB;YACjBA,aAAa,CAAC7O,KAAd,GAAsB,IAAtB;UACD;;UACDqE,MAAM,CAACwK,aAAP,GAAuBzO,MAAvB;UACA2O,QAAQ,GAAG3O,MAAM,CAACxF,QAAlB;QACD;;QAED,IAAI,CAACoF,KAAL,EAAY;UACVgP,SAAS,GAAG5O,MAAM,CAACJ,KAAP,GAAe,IAA3B;QACD,CAFD,MAEO;UACLgP,SAAS,GAAG5O,MAAM,CAACJ,KAAP,GAAeA,KAA3B;QACD;;QAEDqE,MAAM,CAAC0K,QAAP,GAAkBA,QAAlB;QACA1K,MAAM,CAAC2K,SAAP,GAAmBA,SAAnB;QAEA,KAAK5K,KAAL,CAAWmP,MAAX,CAAkB,qBAAlB;MACD;IA3RM,CAtLsC;IAod/ChP,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,OAAO;QACLgkB,cAAc,EAAE,IADX;QAELC,QAAQ,EAAE,KAFL;QAGLO,SAAS,EAAE;MAHN,CAAP;IAKD;EA1d8C,CAApB,CAz4FqC,CAq2GlE;;EACA,IAAIuB,oBAAoB,GAAG7wB,MAAM,CAACgS,MAAP,IAAiB,UAAUhN,MAAV,EAAkB;IAAE,KAAK,IAAIzF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8I,SAAS,CAACzC,MAA9B,EAAsCrG,CAAC,EAAvC,EAA2C;MAAE,IAAI0S,MAAM,GAAG5J,SAAS,CAAC9I,CAAD,CAAtB;;MAA2B,KAAK,IAAIsB,GAAT,IAAgBoR,MAAhB,EAAwB;QAAE,IAAIjS,MAAM,CAACoB,SAAP,CAAiBC,cAAjB,CAAgC5B,IAAhC,CAAqCwS,MAArC,EAA6CpR,GAA7C,CAAJ,EAAuD;UAAEmE,MAAM,CAACnE,GAAD,CAAN,GAAcoR,MAAM,CAACpR,GAAD,CAApB;QAA4B;MAAE;IAAE;;IAAC,OAAOmE,MAAP;EAAgB,CAA5Q;EAKA;;;EAA6B,IAAI8rB,YAAY,GAAI;IAC/CjxB,IAAI,EAAE,eADyC;IAG/C+W,MAAM,EAAE,CAACwJ,eAAD,CAHuC;IAK/Cze,MAAM,EAAE,SAASA,MAAT,CAAgB0B,CAAhB,EAAmB;MACzB,IAAIwR,KAAK,GAAG,IAAZ;;MAEA,IAAIkc,IAAI,GAAG,EAAX;;MACA,IAAI,KAAKjkB,aAAT,EAAwB;QACtBikB,IAAI,GAAG,KAAKjkB,aAAL,CAAmB;UAAElG,OAAO,EAAE,KAAKA,OAAhB;UAAyBkE,IAAI,EAAE,KAAKH,KAAL,CAAWC,MAAX,CAAkBE;QAAjD,CAAnB,CAAP;MACD,CAFD,MAEO;QACL,KAAKlE,OAAL,CAAaC,OAAb,CAAqB,UAAUF,MAAV,EAAkBb,KAAlB,EAAyB;UAC5C,IAAIA,KAAK,KAAK,CAAd,EAAiB;YACfirB,IAAI,CAACjrB,KAAD,CAAJ,GAAc+O,KAAK,CAAChI,OAApB;YACA;UACD;;UACD,IAAI2L,MAAM,GAAG3D,KAAK,CAAClK,KAAN,CAAYC,MAAZ,CAAmBE,IAAnB,CAAwB/E,GAAxB,CAA4B,UAAUS,IAAV,EAAgB;YACvD,OAAOwqB,MAAM,CAACxqB,IAAI,CAACG,MAAM,CAACxF,QAAR,CAAL,CAAb;UACD,CAFY,CAAb;;UAGA,IAAI8vB,UAAU,GAAG,EAAjB;UACA,IAAIC,SAAS,GAAG,IAAhB;UACA1Y,MAAM,CAAC3R,OAAP,CAAe,UAAUtG,KAAV,EAAiB;YAC9B,IAAI,CAACyH,KAAK,CAACzH,KAAD,CAAV,EAAmB;cACjB2wB,SAAS,GAAG,KAAZ;cACA,IAAIC,OAAO,GAAG,CAAC,KAAK5wB,KAAN,EAAagH,KAAb,CAAmB,GAAnB,EAAwB,CAAxB,CAAd;cACA0pB,UAAU,CAACjoB,IAAX,CAAgBmoB,OAAO,GAAGA,OAAO,CAACvrB,MAAX,GAAoB,CAA3C;YACD;UACF,CAND;UAOA,IAAIwrB,SAAS,GAAG9R,IAAI,CAACE,GAAL,CAAS9W,KAAT,CAAe,IAAf,EAAqBuoB,UAArB,CAAhB;;UACA,IAAI,CAACC,SAAL,EAAgB;YACdH,IAAI,CAACjrB,KAAD,CAAJ,GAAc0S,MAAM,CAAC/P,MAAP,CAAc,UAAU0H,IAAV,EAAgBkhB,IAAhB,EAAsB;cAChD,IAAI9wB,KAAK,GAAGywB,MAAM,CAACK,IAAD,CAAlB;;cACA,IAAI,CAACrpB,KAAK,CAACzH,KAAD,CAAV,EAAmB;gBACjB,OAAO+wB,UAAU,CAAC,CAACnhB,IAAI,GAAGkhB,IAAR,EAAcE,OAAd,CAAsBjS,IAAI,CAACkS,GAAL,CAASJ,SAAT,EAAoB,EAApB,CAAtB,CAAD,CAAjB;cACD,CAFD,MAEO;gBACL,OAAOjhB,IAAP;cACD;YACF,CAPa,EAOX,CAPW,CAAd;UAQD,CATD,MASO;YACL4gB,IAAI,CAACjrB,KAAD,CAAJ,GAAc,EAAd;UACD;QACF,CA9BD;MA+BD;;MAED,OAAOzC,CAAC,CACN,OADM,EAEN;QACE,SAAS,kBADX;QAEEwI,KAAK,EAAE;UAAE0X,WAAW,EAAE,GAAf;UACLC,WAAW,EAAE,GADR;UAELpZ,MAAM,EAAE;QAFH;MAFT,CAFM,EAQN,CAAC/G,CAAC,CAAC,UAAD,EAAa,CAAC,KAAKuD,OAAL,CAAab,GAAb,CAAiB,UAAUY,MAAV,EAAkB;QACjD,OAAOtD,CAAC,CAAC,KAAD,EAAQ;UACdwI,KAAK,EAAE;YAAEhM,IAAI,EAAE8G,MAAM,CAACG;UAAf,CADO;UAEdjG,GAAG,EAAE8F,MAAM,CAACG;QAFE,CAAR,CAAR;MAGD,CAJe,CAAD,EAIX,KAAK6lB,SAAL,GAAiBtpB,CAAC,CAAC,KAAD,EAAQ;QAC5BwI,KAAK,EAAE;UAAEhM,IAAI,EAAE;QAAR;MADqB,CAAR,CAAlB,GAEC,EANU,CAAb,CAAF,EAMWwD,CAAC,CACV,OADU,EAEV;QAAE,SAAS,CAAC;UAAE,cAAc,KAAKspB;QAArB,CAAD;MAAX,CAFU,EAGV,CAACtpB,CAAC,CAAC,IAAD,EAAO,CAAC,KAAKuD,OAAL,CAAab,GAAb,CAAiB,UAAUY,MAAV,EAAkBqb,SAAlB,EAA6B;QACtD,OAAO3e,CAAC,CACN,IADM,EAEN;UACExC,GAAG,EAAEmhB,SADP;UAEEnW,KAAK,EAAE;YAAEuW,OAAO,EAAEzb,MAAM,CAAC0lB,OAAlB;YACLlK,OAAO,EAAExb,MAAM,CAAC6lB;UADX,CAFT;UAKE,SAAS,GAAGhpB,MAAH,CAAUqR,KAAK,CAAC4c,aAAN,CAAoB9qB,MAApB,EAA4Bqb,SAA5B,CAAV,EAAkD,CAAC,gBAAD,CAAlD;QALX,CAFM,EAQN,CAAC3e,CAAC,CACA,KADA,EAEA;UAAE,SAAS,CAAC,MAAD,EAASsD,MAAM,CAAC4mB,cAAhB;QAAX,CAFA,EAGA,CAACwD,IAAI,CAAC/O,SAAD,CAAL,CAHA,CAAF,CARM,CAAR;MAcD,CAfS,CAAD,EAeL,KAAK2K,SAAL,GAAiBtpB,CAAC,CAAC,IAAD,EAAO;QAAE,SAAS;MAAX,CAAP,CAAlB,GAAiE,EAf5D,CAAP,CAAF,CAHU,CANZ,CARM,CAAR;IAmCD,CAhF8C;IAmF/Cie,KAAK,EAAE;MACLnU,KAAK,EAAEkW,MADF;MAEL1Y,KAAK,EAAE;QACLwY,QAAQ,EAAE;MADL,CAFF;MAKLrW,aAAa,EAAEwW,QALV;MAMLzW,OAAO,EAAEwW,MANJ;MAOLjZ,MAAM,EAAEgZ,OAPH;MAQLtX,WAAW,EAAE;QACXiL,IAAI,EAAE/W,MADK;QAEX+qB,OAAO,EAAE,SAAS6C,QAAT,GAAoB;UAC3B,OAAO;YACLjV,IAAI,EAAE,EADD;YAELpS,KAAK,EAAE;UAFF,CAAP;QAID;MAPU;IARR,CAnFwC;IAsG/CmM,QAAQ,EAAEme,oBAAoB,CAAC;MAC7BpqB,KAAK,EAAE,SAASA,KAAT,GAAiB;QACtB,OAAO,KAAKud,OAAZ;MACD,CAH4B;MAI7B2I,SAAS,EAAE,SAASA,SAAT,GAAqB;QAC9B,OAAO,CAAC,KAAKxf,KAAN,IAAe,KAAKmT,WAAL,CAAiB3S,WAAvC;MACD;IAN4B,CAAD,EAO3BkO,SAAS,CAAC;MACXjV,OAAO,EAAE,SADE;MAEXwP,aAAa,EAAE,eAFJ;MAGX6N,kBAAkB,EAAE,wBAHT;MAIXC,mBAAmB,EAAE,6BAJV;MAKXC,YAAY,EAAE,SAASA,YAAT,CAAsBvZ,MAAtB,EAA8B;QAC1C,OAAOA,MAAM,CAAChE,OAAP,CAAehB,MAAtB;MACD,CAPU;MAQXwe,cAAc,EAAE,SAASA,cAAT,CAAwBxZ,MAAxB,EAAgC;QAC9C,OAAOA,MAAM,CAACmC,YAAP,CAAoBnH,MAA3B;MACD,CAVU;MAWXye,eAAe,EAAE,SAASA,eAAT,CAAyBzZ,MAAzB,EAAiC;QAChD,OAAOA,MAAM,CAAC4C,iBAAP,CAAyB5H,MAAhC;MACD;IAbU,CAAD,CAPkB,CAtGiB;IA6H/CiK,OAAO,EAAE;MACPoe,YAAY,EAAE,SAASA,YAAT,CAAsBnoB,KAAtB,EAA6Bc,OAA7B,EAAsCD,MAAtC,EAA8C;QAC1D,IAAI,KAAKwG,KAAL,KAAe,IAAf,IAAuB,KAAKA,KAAL,KAAe,MAA1C,EAAkD;UAChD,OAAOrH,KAAK,IAAI,KAAKme,kBAArB;QACD,CAFD,MAEO,IAAI,KAAK9W,KAAL,KAAe,OAAnB,EAA4B;UACjC,IAAIukB,MAAM,GAAG,CAAb;;UACA,KAAK,IAAInyB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuG,KAApB,EAA2BvG,CAAC,EAA5B,EAAgC;YAC9BmyB,MAAM,IAAI9qB,OAAO,CAACrH,CAAD,CAAP,CAAW8sB,OAArB;UACD;;UACD,OAAOqF,MAAM,GAAG,KAAKvN,YAAL,GAAoB,KAAKD,mBAAzC;QACD,CANM,MAMA,IAAI,CAAC,KAAK/W,KAAN,IAAexG,MAAM,CAACwG,KAA1B,EAAiC;UACtC;UACA,OAAO,IAAP;QACD,CAHM,MAGA;UACL,OAAOrH,KAAK,GAAG,KAAKse,cAAb,IAA+Bte,KAAK,IAAI,KAAKqe,YAAL,GAAoB,KAAKE,eAAxE;QACD;MACF,CAhBM;MAiBPoN,aAAa,EAAE,SAASA,aAAT,CAAuB9qB,MAAvB,EAA+Bqb,SAA/B,EAA0C;QACvD,IAAIyD,OAAO,GAAG,CAAC9e,MAAM,CAACG,EAAR,EAAYH,MAAM,CAACgf,KAAnB,EAA0Bhf,MAAM,CAAC4mB,cAAjC,CAAd;;QACA,IAAI5mB,MAAM,CAACM,SAAX,EAAsB;UACpBwe,OAAO,CAACzc,IAAR,CAAarC,MAAM,CAACM,SAApB;QACD;;QACD,IAAI,KAAKgnB,YAAL,CAAkBjM,SAAlB,EAA6B,KAAKpb,OAAlC,EAA2CD,MAA3C,CAAJ,EAAwD;UACtD8e,OAAO,CAACzc,IAAR,CAAa,WAAb;QACD;;QACD,IAAI,CAACrC,MAAM,CAAC8C,QAAZ,EAAsB;UACpBgc,OAAO,CAACzc,IAAR,CAAa,SAAb;QACD;;QACD,OAAOyc,OAAP;MACD;IA7BM;EA7HsC,CAApB,CA32GqC,CAwgHlE;;EACA,IAAIkM,oCAAoC,GAAG3xB,MAAM,CAACgS,MAAP,IAAiB,UAAUhN,MAAV,EAAkB;IAAE,KAAK,IAAIzF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8I,SAAS,CAACzC,MAA9B,EAAsCrG,CAAC,EAAvC,EAA2C;MAAE,IAAI0S,MAAM,GAAG5J,SAAS,CAAC9I,CAAD,CAAtB;;MAA2B,KAAK,IAAIsB,GAAT,IAAgBoR,MAAhB,EAAwB;QAAE,IAAIjS,MAAM,CAACoB,SAAP,CAAiBC,cAAjB,CAAgC5B,IAAhC,CAAqCwS,MAArC,EAA6CpR,GAA7C,CAAJ,EAAuD;UAAEmE,MAAM,CAACnE,GAAD,CAAN,GAAcoR,MAAM,CAACpR,GAAD,CAApB;QAA4B;MAAE;IAAE;;IAAC,OAAOmE,MAAP;EAAgB,CAA5R,CAzgHkE,CA2gHlE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAeA,IAAI4sB,WAAW,GAAG,CAAlB;EAEA;;EAA6B,IAAIC,6BAA6B,GAAI;IAChEhyB,IAAI,EAAE,SAD0D;IAGhE+W,MAAM,EAAE,CAAC1H,cAAc,CAAC/I,CAAhB,EAAmBiJ,iBAAiB,CAACjJ,CAArC,CAHwD;IAKhEoF,UAAU,EAAE;MACVumB,UAAU,EAAEhjB;IADF,CALoD;IAShEwS,KAAK,EAAE;MACLxW,IAAI,EAAE;QACJiM,IAAI,EAAErR,KADF;QAEJqlB,OAAO,EAAE,SAAS6C,QAAT,GAAoB;UAC3B,OAAO,EAAP;QACD;MAJG,CADD;MAQLmE,IAAI,EAAE1O,MARD;MAULxb,KAAK,EAAE,CAACwb,MAAD,EAAS2N,MAAT,CAVF;MAYL9oB,MAAM,EAAE,CAACmb,MAAD,EAAS2N,MAAT,CAZH;MAcLzmB,SAAS,EAAE,CAAC8Y,MAAD,EAAS2N,MAAT,CAdN;MAgBL9mB,GAAG,EAAE;QACH6M,IAAI,EAAEqM,OADH;QAEH2H,OAAO,EAAE;MAFN,CAhBA;MAqBL5gB,MAAM,EAAEiZ,OArBH;MAuBLhZ,MAAM,EAAEgZ,OAvBH;MAyBLhc,MAAM,EAAE,CAACic,MAAD,EAASC,QAAT,CAzBH;MA2BL/gB,OAAO,EAAE,EA3BJ;MA6BL+I,UAAU,EAAE;QACVyL,IAAI,EAAEqM,OADI;QAEV2H,OAAO,EAAE;MAFC,CA7BP;MAkCLne,WAAW,EAAEwW,OAlCR;MAoCLvW,OAAO,EAAEwW,MApCJ;MAsCLvW,aAAa,EAAEwW,QAtCV;MAwCLpX,YAAY,EAAE,CAACmX,MAAD,EAASC,QAAT,CAxCT;MA0CLnX,QAAQ,EAAE,CAACnM,MAAD,EAASsjB,QAAT,CA1CL;MA4CLsC,aAAa,EAAE,CAACvC,MAAD,EAASC,QAAT,CA5CV;MA8CLoC,SAAS,EAAE,CAAC1lB,MAAD,EAASsjB,QAAT,CA9CN;MAgDL+K,kBAAkB,EAAE,CAAChL,MAAD,EAASC,QAAT,CAhDf;MAkDL8K,cAAc,EAAE,CAACpuB,MAAD,EAASsjB,QAAT,CAlDX;MAoDLkL,mBAAmB,EAAE,CAACnL,MAAD,EAASC,QAAT,CApDhB;MAsDLgL,eAAe,EAAE,CAACtuB,MAAD,EAASsjB,QAAT,CAtDZ;MAwDLjX,mBAAmB,EAAE+W,OAxDhB;MA0DLtR,aAAa,EAAE,CAACuR,MAAD,EAAS2N,MAAT,CA1DV;MA4DLvkB,SAAS,EAAE4W,MA5DN;MA8DLlR,aAAa,EAAEzM,KA9DV;MAgELiK,gBAAgB,EAAEyT,OAhEb;MAkELtX,WAAW,EAAE9L,MAlER;MAoEL4jB,aAAa,EAAEP,MApEV;MAsELiC,UAAU,EAAEhC,QAtEP;MAwEL/M,qBAAqB,EAAE;QACrBQ,IAAI,EAAEqM,OADe;QAErB2H,OAAO,EAAE;MAFY,CAxElB;MA6EL1Y,MAAM,EAAE;QACN0E,IAAI,EAAEia,MADA;QAENjG,OAAO,EAAE;MAFH,CA7EH;MAkFLiH,SAAS,EAAE;QACTjb,IAAI,EAAE/W,MADG;QAET+qB,OAAO,EAAE,SAAS6C,QAAT,GAAoB;UAC3B,OAAO;YACLqE,WAAW,EAAE,aADR;YAELxoB,QAAQ,EAAE;UAFL,CAAP;QAID;MAPQ,CAlFN;MA4FL6I,IAAI,EAAE8Q,OA5FD;MA8FLtO,IAAI,EAAEwO;IA9FD,CATyD;IA0GhE/B,UAAU,EAAE;MACV2Q,WAAW,EAAEzF,YADH;MAEV0F,WAAW,EAAErB,YAFH;MAGVsB,SAAS,EAAEpP,UAHD;MAIVxB,UAAU,EAAEzT,gBAAgB,CAAC5H;IAJnB,CA1GoD;IAiHhE0J,OAAO,EAAE;MACPwiB,kBAAkB,EAAE,SAASA,kBAAT,GAA8B;QAChD,OAAO;UACLC,MAAM,EAAE;YACN5iB,MAAM,EAAE;UADF;QADH,CAAP;MAKD,CAPM;MAQP0L,aAAa,EAAE,SAASA,aAAT,CAAuBjU,GAAvB,EAA4B;QACzC,KAAKwD,KAAL,CAAWmP,MAAX,CAAkB,eAAlB,EAAmC3S,GAAnC;MACD,CAVM;MAWP0Q,kBAAkB,EAAE,SAASA,kBAAT,CAA4B1Q,GAA5B,EAAiC2Q,QAAjC,EAA2C;QAC7D,KAAKnN,KAAL,CAAWkN,kBAAX,CAA8B1Q,GAA9B,EAAmC2Q,QAAnC,EAA6C,KAA7C;QACA,KAAKnN,KAAL,CAAWwN,iBAAX;MACD,CAdM;MAeP7H,kBAAkB,EAAE,SAASA,kBAAT,CAA4BnJ,GAA5B,EAAiCoJ,QAAjC,EAA2C;QAC7D,KAAK5F,KAAL,CAAWyP,yBAAX,CAAqCjT,GAArC,EAA0CoJ,QAA1C;MACD,CAjBM;MAkBP+G,cAAc,EAAE,SAASA,cAAT,GAA0B;QACxC,KAAK3M,KAAL,CAAW2M,cAAX;MACD,CApBM;MAqBP6B,WAAW,EAAE,SAASA,WAAT,CAAqBC,UAArB,EAAiC;QAC5C,KAAKzO,KAAL,CAAWwO,WAAX,CAAuBC,UAAvB;MACD,CAvBM;MAwBPa,SAAS,EAAE,SAASA,SAAT,GAAqB;QAC9B,KAAKtP,KAAL,CAAWsP,SAAX;MACD,CA1BM;MA2BP9O,gBAAgB,EAAE,SAASA,gBAAT,GAA4B;QAC5C,KAAKR,KAAL,CAAWmP,MAAX,CAAkB,aAAlB,EAAiC,IAAjC;QACA,IAAI,KAAKkM,UAAT,EAAqB,KAAKA,UAAL,GAAkB,IAAlB;MACtB,CA9BM;MA+BPzK,aAAa,EAAE,SAASA,aAAT,GAAyB;QACtC,IAAI1S,OAAO,GAAG,KAAK2B,MAAL,CAAY+Q,aAAZ,EAAd;;QACA,IAAI1S,OAAJ,EAAa;UACX,KAAK2B,MAAL,CAAY6T,eAAZ,CAA4B,YAA5B;UACA,KAAK7T,MAAL,CAAYmU,kBAAZ;QACD;MACF,CArCM;MAsCP3R,qBAAqB,EAAE,SAASA,qBAAT,CAA+BlI,KAA/B,EAAsCgG,IAAtC,EAA4C;QACjE,IAAIgS,WAAW,GAAG,KAAKA,WAAvB;;QACA,IAAIwC,IAAI,CAACiT,GAAL,CAASznB,IAAI,CAAC0nB,KAAd,IAAuB,CAA3B,EAA8B;UAC5B,IAAIC,gBAAgB,GAAG3V,WAAW,CAAC4V,SAAnC;;UACA,IAAI5nB,IAAI,CAAC6nB,MAAL,GAAc,CAAd,IAAmBF,gBAAgB,KAAK,CAA5C,EAA+C;YAC7C3tB,KAAK,CAAC8tB,cAAN;UACD;;UACD,IAAI9nB,IAAI,CAAC6nB,MAAL,GAAc,CAAd,IAAmB7V,WAAW,CAAC+V,YAAZ,GAA2B/V,WAAW,CAACqB,YAAvC,GAAsDsU,gBAA7E,EAA+F;YAC7F3tB,KAAK,CAAC8tB,cAAN;UACD;;UACD9V,WAAW,CAAC4V,SAAZ,IAAyBpT,IAAI,CAACwT,IAAL,CAAUhoB,IAAI,CAAC6nB,MAAL,GAAc,CAAxB,CAAzB;QACD,CATD,MASO;UACL7V,WAAW,CAACiW,UAAZ,IAA0BzT,IAAI,CAACwT,IAAL,CAAUhoB,IAAI,CAACkoB,MAAL,GAAc,CAAxB,CAA1B;QACD;MACF,CApDM;MAqDPvnB,4BAA4B,EAAE,SAASA,4BAAT,CAAsC3G,KAAtC,EAA6CgG,IAA7C,EAAmD;QAC/E,IAAIkoB,MAAM,GAAGloB,IAAI,CAACkoB,MAAlB;QAAA,IACIL,MAAM,GAAG7nB,IAAI,CAAC6nB,MADlB;;QAGA,IAAIrT,IAAI,CAACiT,GAAL,CAASS,MAAT,KAAoB1T,IAAI,CAACiT,GAAL,CAASI,MAAT,CAAxB,EAA0C;UACxC,KAAK7V,WAAL,CAAiBiW,UAAjB,IAA+BjoB,IAAI,CAACkoB,MAAL,GAAc,CAA7C;QACD;MACF,CA5DM;MA+DP;MACAC,WAAW,EAAE,SAASA,WAAT,GAAuB;QAClC,IAAIC,YAAY,GAAG,KAAKpW,WAAxB;QAAA,IACIiW,UAAU,GAAGG,YAAY,CAACH,UAD9B;QAAA,IAEIL,SAAS,GAAGQ,YAAY,CAACR,SAF7B;QAAA,IAGIxU,WAAW,GAAGgV,YAAY,CAAChV,WAH/B;QAAA,IAIIyI,WAAW,GAAGuM,YAAY,CAACvM,WAJ/B;QAKA,IAAIwM,MAAM,GAAG,KAAK7Z,KAAlB;QAAA,IACIsE,aAAa,GAAGuV,MAAM,CAACvV,aAD3B;QAAA,IAEIE,aAAa,GAAGqV,MAAM,CAACrV,aAF3B;QAAA,IAGIsV,gBAAgB,GAAGD,MAAM,CAACC,gBAH9B;QAAA,IAIIC,qBAAqB,GAAGF,MAAM,CAACE,qBAJnC;QAMA,IAAIzV,aAAJ,EAAmBA,aAAa,CAACmV,UAAd,GAA2BA,UAA3B;QACnB,IAAIjV,aAAJ,EAAmBA,aAAa,CAACiV,UAAd,GAA2BA,UAA3B;QACnB,IAAIK,gBAAJ,EAAsBA,gBAAgB,CAACV,SAAjB,GAA6BA,SAA7B;QACtB,IAAIW,qBAAJ,EAA2BA,qBAAqB,CAACX,SAAtB,GAAkCA,SAAlC;QAC3B,IAAIY,qBAAqB,GAAG3M,WAAW,GAAGzI,WAAd,GAA4B,CAAxD;;QACA,IAAI6U,UAAU,IAAIO,qBAAlB,EAAyC;UACvC,KAAKtnB,cAAL,GAAsB,OAAtB;QACD,CAFD,MAEO,IAAI+mB,UAAU,KAAK,CAAnB,EAAsB;UAC3B,KAAK/mB,cAAL,GAAsB,MAAtB;QACD,CAFM,MAEA;UACL,KAAKA,cAAL,GAAsB,QAAtB;QACD;MACF,CAxFM;MA2FPunB,mBAAmB,EAAEvzB,MAAM,CAACgO,2BAA2B,CAAC,UAAD,CAA5B,CAAN,CAAgD,EAAhD,EAAoD,YAAY;QACnF,KAAKilB,WAAL;MACD,CAFoB,CA3Fd;MA+FPO,QAAQ,EAAE,SAASA,QAAT,CAAkBC,GAAlB,EAAuB;QAC/B,IAAIhP,GAAG,GAAGC,MAAM,CAACC,qBAAjB;;QACA,IAAI,CAACF,GAAL,EAAU;UACR,KAAK8O,mBAAL;QACD,CAFD,MAEO;UACL9O,GAAG,CAAC,KAAKwO,WAAN,CAAH;QACD;MACF,CAtGM;MAuGPS,UAAU,EAAE,SAASA,UAAT,GAAsB;QAChC,KAAK5W,WAAL,CAAiBlO,gBAAjB,CAAkC,QAAlC,EAA4C,KAAK4kB,QAAjD,EAA2D;UAAEG,OAAO,EAAE;QAAX,CAA3D;;QACA,IAAI,KAAKzpB,GAAT,EAAc;UACZlK,MAAM,CAACiO,aAAa,CAAC,mBAAD,CAAd,CAAN,CAA2C,KAAK8O,GAAhD,EAAqD,KAAK6W,cAA1D;QACD;MACF,CA5GM;MA6GPC,YAAY,EAAE,SAASA,YAAT,GAAwB;QACpC,KAAK/W,WAAL,CAAiBuT,mBAAjB,CAAqC,QAArC,EAA+C,KAAKmD,QAApD,EAA8D;UAAEG,OAAO,EAAE;QAAX,CAA9D;;QACA,IAAI,KAAKzpB,GAAT,EAAc;UACZlK,MAAM,CAACiO,aAAa,CAAC,sBAAD,CAAd,CAAN,CAA8C,KAAK8O,GAAnD,EAAwD,KAAK6W,cAA7D;QACD;MACF,CAlHM;MAmHPA,cAAc,EAAE,SAASA,cAAT,GAA0B;QACxC,IAAI,CAAC,KAAKlZ,MAAV,EAAkB;QAClB,IAAIoZ,kBAAkB,GAAG,KAAzB;QACA,IAAI/kB,EAAE,GAAG,KAAKgO,GAAd;QACA,IAAIgX,YAAY,GAAG,KAAKtU,WAAxB;QAAA,IACIuU,QAAQ,GAAGD,YAAY,CAAClsB,KAD5B;QAAA,IAEIosB,SAAS,GAAGF,YAAY,CAAC7rB,MAF7B;QAKA,IAAIL,KAAK,GAAGkH,EAAE,CAACmP,WAAf;;QACA,IAAI8V,QAAQ,KAAKnsB,KAAjB,EAAwB;UACtBisB,kBAAkB,GAAG,IAArB;QACD;;QAED,IAAI5rB,MAAM,GAAG6G,EAAE,CAACoO,YAAhB;;QACA,IAAI,CAAC,KAAKjV,MAAL,IAAe,KAAKgsB,kBAArB,KAA4CD,SAAS,KAAK/rB,MAA9D,EAAsE;UACpE4rB,kBAAkB,GAAG,IAArB;QACD;;QAED,IAAIA,kBAAJ,EAAwB;UACtB,KAAKrU,WAAL,CAAiB5X,KAAjB,GAAyBA,KAAzB;UACA,KAAK4X,WAAL,CAAiBvX,MAAjB,GAA0BA,MAA1B;UACA,KAAKisB,QAAL;QACD;MACF,CA3IM;MA4IPA,QAAQ,EAAE,SAASA,QAAT,GAAoB;QAC5B,IAAI,KAAKD,kBAAT,EAA6B;UAC3B,KAAK1pB,MAAL,CAAY8S,eAAZ;QACD;;QACD,KAAK9S,MAAL,CAAYmU,kBAAZ;MACD,CAjJM;MAkJPrY,IAAI,EAAE,SAASA,IAAT,CAAcqS,IAAd,EAAoBpS,KAApB,EAA2B;QAC/B,KAAKoE,KAAL,CAAWmP,MAAX,CAAkB,MAAlB,EAA0B;UAAEnB,IAAI,EAAEA,IAAR;UAAcpS,KAAK,EAAEA;QAArB,CAA1B;MACD,CApJM;MAqJP0U,kBAAkB,EAAE,SAASA,kBAAT,GAA8B;QAChD,KAAKtQ,KAAL,CAAWmP,MAAX,CAAkB,oBAAlB;MACD;IAvJM,CAjHuD;IA2QhEpH,QAAQ,EAAEif,oCAAoC,CAAC;MAC7C5mB,SAAS,EAAE,SAASA,SAAT,GAAqB;QAC9B,OAAO,KAAKgnB,IAAL,IAAa,CAAC,KAAKqC,QAAL,IAAiB,EAAlB,EAAsBrC,IAA1C;MACD,CAH4C;MAI7CjV,WAAW,EAAE,SAASA,WAAT,GAAuB;QAClC,OAAO,KAAKxD,KAAL,CAAWwD,WAAlB;MACD,CAN4C;MAO7CoX,kBAAkB,EAAE,SAASA,kBAAT,GAA8B;QAChD,OAAO,KAAKhsB,MAAL,IAAe,KAAKqC,SAApB,IAAiC,KAAKwC,YAAL,CAAkBnH,MAAlB,GAA2B,CAA5D,IAAiE,KAAK4H,iBAAL,CAAuB5H,MAAvB,GAAgC,CAAxG;MACD,CAT4C;MAU7CgG,SAAS,EAAE,SAASA,SAAT,GAAqB;QAC9B,IAAIyoB,OAAO,GAAG,KAAK7pB,MAAnB;QAAA,IACIoB,SAAS,GAAGyoB,OAAO,CAACzoB,SADxB;QAAA,IAEIlB,OAAO,GAAG2pB,OAAO,CAAC3pB,OAFtB;QAAA,IAGIiD,WAAW,GAAG0mB,OAAO,CAAC1mB,WAH1B;QAKA,OAAO/B,SAAS,GAAGA,SAAS,IAAIlB,OAAO,GAAGiD,WAAH,GAAiB,CAA5B,CAAT,GAA0C,IAA7C,GAAoD,EAApE;MACD,CAjB4C;MAkB7C1B,UAAU,EAAE,SAASA,UAAT,GAAsB;QAChC,IAAIqoB,QAAQ,GAAG,KAAK9pB,MAApB;QAAA,IACI+pB,qBAAqB,GAAGD,QAAQ,CAACjnB,YADrC;QAAA,IAEIA,YAAY,GAAGknB,qBAAqB,KAAKzsB,SAA1B,GAAsC,CAAtC,GAA0CysB,qBAF7D;QAAA,IAGItoB,UAAU,GAAGqoB,QAAQ,CAACroB,UAH1B;QAAA,IAIIuoB,qBAAqB,GAAGF,QAAQ,CAAC1X,YAJrC;QAAA,IAKIA,YAAY,GAAG4X,qBAAqB,KAAK1sB,SAA1B,GAAsC,CAAtC,GAA0C0sB,qBAL7D;;QAOA,IAAI,KAAKtsB,MAAT,EAAiB;UACf,OAAO;YACLA,MAAM,EAAE+D,UAAU,GAAGA,UAAU,GAAG,IAAhB,GAAuB;UADpC,CAAP;QAGD,CAJD,MAIO,IAAI,KAAK1B,SAAT,EAAoB;UACzB,IAAIA,SAAS,GAAGvK,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAoC,KAAKlF,SAAzC,CAAhB;;UACA,IAAI,OAAOA,SAAP,KAAqB,QAAzB,EAAmC;YACjC,OAAO;cACL,cAAcA,SAAS,GAAGqS,YAAZ,IAA4B,KAAKtR,UAAL,GAAkB+B,YAAlB,GAAiC,CAA7D,IAAkE;YAD3E,CAAP;UAGD;QACF;;QACD,OAAO,EAAP;MACD,CAvC4C;MAwC7CC,eAAe,EAAE,SAASA,eAAT,GAA2B;QAC1C,IAAI,KAAKpF,MAAT,EAAiB;UACf,OAAO;YACLA,MAAM,EAAE,KAAKsC,MAAL,CAAY8C,eAAZ,GAA8B,KAAK9C,MAAL,CAAY8C,eAAZ,GAA8B,IAA5D,GAAmE;UADtE,CAAP;QAGD,CAJD,MAIO,IAAI,KAAK/C,SAAT,EAAoB;UACzB,IAAIA,SAAS,GAAGvK,MAAM,CAACyP,IAAI,CAAC;UAAI;UAAL,CAAL,CAAN,CAAoC,KAAKlF,SAAzC,CAAhB;;UACA,IAAI,OAAOA,SAAP,KAAqB,QAAzB,EAAmC;YACjCA,SAAS,GAAG,KAAKC,MAAL,CAAYC,OAAZ,GAAsBF,SAAS,GAAG,KAAKC,MAAL,CAAYmD,WAA9C,GAA4DpD,SAAxE;;YACA,IAAI,KAAKe,UAAT,EAAqB;cACnBf,SAAS,IAAI,KAAKC,MAAL,CAAY6C,YAAzB;YACD;;YACD9C,SAAS,IAAI,KAAKC,MAAL,CAAYoS,YAAzB;YACA,OAAO;cACL,cAAcrS,SAAS,GAAG;YADrB,CAAP;UAGD;QACF;;QACD,OAAO,EAAP;MACD,CA3D4C;MA4D7C2C,WAAW,EAAE,SAASA,WAAT,GAAuB;QAClC,IAAI,KAAK3C,SAAT,EAAoB;UAClB,IAAI,KAAKqC,WAAT,EAAsB;YACpB,OAAO;cACL6nB,MAAM,EAAE;YADH,CAAP;UAGD;;UACD,OAAO;YACLA,MAAM,EAAE,KAAKjqB,MAAL,CAAYC,OAAZ,IAAuB,KAAKK,IAAL,CAAUlF,MAAjC,GAA0C,KAAK4E,MAAL,CAAYmD,WAAZ,GAA0B,IAApE,GAA2E;UAD9E,CAAP;QAGD,CATD,MASO;UACL,IAAI,KAAKf,WAAT,EAAsB;YACpB,OAAO;cACL1E,MAAM,EAAE,KAAKsC,MAAL,CAAYmS,WAAZ,GAA0B,KAAKnS,MAAL,CAAYmS,WAAZ,GAA0B,IAApD,GAA2D;YAD9D,CAAP;UAGD;;UACD,OAAO;YACLzU,MAAM,EAAE,KAAKsC,MAAL,CAAYqS,cAAZ,GAA6B,KAAKrS,MAAL,CAAYqS,cAAZ,GAA6B,IAA1D,GAAiE;UADpE,CAAP;QAGD;MACF,CAhF4C;MAiF7CvQ,eAAe,EAAE,SAASA,eAAT,GAA2B;QAC1C,IAAI,KAAKxB,IAAL,IAAa,KAAKA,IAAL,CAAUlF,MAA3B,EAAmC,OAAO,IAAP;QACnC,IAAIsC,MAAM,GAAG,MAAb;;QACA,IAAI,KAAKsC,MAAL,CAAY+C,YAAhB,EAA8B;UAC5BrF,MAAM,GAAG,iBAAiB,KAAKsC,MAAL,CAAY+C,YAA7B,GAA4C,KAArD;QACD;;QACD,OAAO;UACL1F,KAAK,EAAE,KAAK+D,SADP;UAEL1D,MAAM,EAAEA;QAFH,CAAP;MAID;IA3F4C,CAAD,EA4F3C2T,SAAS,CAAC;MACXxF,SAAS,EAAE,WADA;MAEXzP,OAAO,EAAE,SAFE;MAGX8tB,SAAS,EAAE,MAHA;MAIX3nB,YAAY,EAAE,cAJH;MAKXS,iBAAiB,EAAE;IALR,CAAD,CA5FkC,CA3QkB;IA+WhEwF,KAAK,EAAE;MACL9K,MAAM,EAAE;QACNysB,SAAS,EAAE,IADL;QAENC,OAAO,EAAE,SAASA,OAAT,CAAiBr0B,KAAjB,EAAwB;UAC/B,KAAKiK,MAAL,CAAY4S,SAAZ,CAAsB7c,KAAtB;QACD;MAJK,CADH;MAQLgK,SAAS,EAAE;QACToqB,SAAS,EAAE,IADF;QAETC,OAAO,EAAE,SAASA,OAAT,CAAiBr0B,KAAjB,EAAwB;UAC/B,KAAKiK,MAAL,CAAY+S,YAAZ,CAAyBhd,KAAzB;QACD;MAJQ,CARN;MAeLuR,aAAa,EAAE;QACb6iB,SAAS,EAAE,IADE;QAEbC,OAAO,EAAE,SAASA,OAAT,CAAiBr0B,KAAjB,EAAwB;UAC/B,IAAI,CAAC,KAAK6G,MAAV,EAAkB;UAClB,KAAKuD,KAAL,CAAW6G,gBAAX,CAA4BjR,KAA5B;QACD;MALY,CAfV;MAuBLuK,IAAI,EAAE;QACJ6pB,SAAS,EAAE,IADP;QAEJC,OAAO,EAAE,SAASA,OAAT,CAAiBr0B,KAAjB,EAAwB;UAC/B,KAAKoK,KAAL,CAAWmP,MAAX,CAAkB,SAAlB,EAA6BvZ,KAA7B;QACD;MAJG,CAvBD;MA8BL4R,aAAa,EAAE;QACbwiB,SAAS,EAAE,IADE;QAEbC,OAAO,EAAE,SAASA,OAAT,CAAiBhsB,MAAjB,EAAyB;UAChC,IAAIA,MAAJ,EAAY;YACV,KAAK+B,KAAL,CAAWuP,uBAAX,CAAmCtR,MAAnC;UACD;QACF;MANY;IA9BV,CA/WyD;IAuZhEyX,OAAO,EAAE,SAASA,OAAT,GAAmB;MAC1B,IAAIxL,KAAK,GAAG,IAAZ;;MAEA,KAAKggB,OAAL,GAAe,cAAcjD,WAAW,EAAxC;MACA,KAAKza,qBAAL,GAA6BnX,MAAM,CAACgO,2BAA2B,CAAC,UAAD,CAA5B,CAAN,CAAgD,EAAhD,EAAoD,YAAY;QAC3F,OAAO6G,KAAK,CAACsf,QAAN,EAAP;MACD,CAF4B,CAA7B;IAGD,CA9Z+D;IA+ZhE3T,OAAO,EAAE,SAASA,OAAT,GAAmB;MAC1B,IAAI7C,MAAM,GAAG,IAAb;;MAEA,KAAK+V,UAAL;MACA,KAAK/oB,KAAL,CAAWkM,aAAX;MACA,KAAKsd,QAAL;MAEA,KAAK1U,WAAL,GAAmB;QACjB5X,KAAK,EAAE,KAAKkV,GAAL,CAASmB,WADC;QAEjBhW,MAAM,EAAE,KAAK6U,GAAL,CAASI;MAFA,CAAnB,CAP0B,CAY1B;;MACA,KAAKxS,KAAL,CAAWC,MAAX,CAAkBhE,OAAlB,CAA0BC,OAA1B,CAAkC,UAAUF,MAAV,EAAkB;QAClD,IAAIA,MAAM,CAACkT,aAAP,IAAwBlT,MAAM,CAACkT,aAAP,CAAqBjU,MAAjD,EAAyD;UACvD+X,MAAM,CAAChT,KAAP,CAAamP,MAAb,CAAoB,cAApB,EAAoC;YAClCnT,MAAM,EAAEA,MAD0B;YAElC6R,MAAM,EAAE7R,MAAM,CAACkT,aAFmB;YAGlCE,MAAM,EAAE;UAH0B,CAApC;QAKD;MACF,CARD;MAUA,KAAKW,MAAL,GAAc,IAAd;IACD,CAvb+D;IAwbhE6F,SAAS,EAAE,SAASA,SAAT,GAAqB;MAC9B,KAAKsT,YAAL;IACD,CA1b+D;IA2bhE/oB,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,IAAIgqB,UAAU,GAAG,KAAK9C,SAAtB;MAAA,IACI+C,qBAAqB,GAAGD,UAAU,CAAC7C,WADvC;MAAA,IAEIA,WAAW,GAAG8C,qBAAqB,KAAKjtB,SAA1B,GAAsC,aAAtC,GAAsDitB,qBAFxE;MAAA,IAGIC,mBAAmB,GAAGF,UAAU,CAACrrB,QAHrC;MAAA,IAIIA,QAAQ,GAAGurB,mBAAmB,KAAKltB,SAAxB,GAAoC,UAApC,GAAiDktB,mBAJhE;MAMA,KAAKrqB,KAAL,GAAagR,WAAW,CAAC,IAAD,EAAO;QAC7BvU,MAAM,EAAE,KAAKA,MADgB;QAE7BuI,gBAAgB,EAAE,KAAKA,gBAFM;QAG7B4G,qBAAqB,EAAE,KAAKA,qBAHC;QAI7B;QACAlE,MAAM,EAAE,KAAKA,MALgB;QAM7BC,IAAI,EAAE,KAAKA,IANkB;QAO7BE,oBAAoB,EAAEyf,WAPO;QAQ7Bxf,kBAAkB,EAAEhJ;MARS,CAAP,CAAxB;MAUA,IAAIe,MAAM,GAAG,IAAIwV,YAAJ,CAAiB;QAC5BrV,KAAK,EAAE,KAAKA,KADgB;QAE5BlE,KAAK,EAAE,IAFqB;QAG5ByD,GAAG,EAAE,KAAKA,GAHkB;QAI5BoB,UAAU,EAAE,KAAKA;MAJW,CAAjB,CAAb;MAMA,OAAO;QACLd,MAAM,EAAEA,MADH;QAELF,QAAQ,EAAE,KAFL;QAGL8d,cAAc,EAAE,IAHX;QAILxa,kBAAkB,EAAE,KAJf;QAKL6R,WAAW,EAAE;UACX5X,KAAK,EAAE,IADI;UAEXK,MAAM,EAAE;QAFG,CALR;QASL;QACAmC,OAAO,EAAE,KAVJ;QAWL2B,cAAc,EAAE;MAXX,CAAP;IAaD;EA/d+D,CAArC,CAjvHqC,CAktIlE;;EACC;;EAA6B,IAAIipB,iCAAiC,GAAIpD,6BAAzC,CAntIoC,CAotIlE;;EAMA;;EAEA,IAAIqD,eAAe,GAAGl1B,MAAM,CAAC4rB,mBAAmB,CAAC;EAAI;EAAL,CAApB,CAAN,CACpBqJ,iCADoB,EAEpBtzB,MAFoB,EAGpBC,eAHoB,EAIpB,KAJoB,EAKpB,IALoB,EAMpB,IANoB,EAOpB,IAPoB,CAAtB;EAWA;;EACA,IAAI,KAAJ,EAAW;IAAE,IAAIuzB,SAAJ;EAAgB;;EAC7BD,eAAe,CAAChzB,OAAhB,CAAwB6pB,MAAxB,GAAiC,8BAAjC;EACA;;EAA6B,IAAIqJ,SAAS,GAAIF,eAAe,CAACh2B,OAAjC,CA1uIqC,CA2uIlE;;EAGA;;EACAk2B,SAAS,CAACC,OAAV,GAAoB,UAAUC,GAAV,EAAe;IACjCA,GAAG,CAACzJ,SAAJ,CAAcuJ,SAAS,CAACv1B,IAAxB,EAA8Bu1B,SAA9B;EACD,CAFD;EAIA;;;EAA6B,IAAIG,cAAc,GAAG/zB,mBAAmB,CAAC,SAAD,CAAnB,GAAkC4zB,SAAvD;EAE7B;AAAO;AACP;AAtxJU,CAtFD,CADT"}]}