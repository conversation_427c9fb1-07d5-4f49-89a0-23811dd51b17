{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\CompilationColumn\\CompilationColumnNew\\CompilationColumnNew.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\CompilationColumn\\CompilationColumnNew\\CompilationColumnNew.vue", "mtime": 1752541697662}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAuDA;EACAA,4BADA;;EAEAC;IACA;MACAC,QADA;MAEAC;QACAH,QADA;QAEAI,YAFA;QAGAC,OAHA;QAIAC,UAJA;QAKAC,cALA;QAMAC;MANA,CAFA;MAUAC;QACAT,OACA;UAAAU;UAAAC;UAAAC;QAAA,CADA,CADA;QAIAP,OACA;UAAAK;UAAAC;UAAAC;QAAA,CADA,CAJA;QAOAJ,OACA;UAAAE;UAAAC;UAAAC;QAAA,CADA;MAPA;IAVA;EAsBA,CAzBA;;EA0BAC,wBA1BA;;EA2BAC;IACA;;IACA;MACA;IACA;EACA,CAhCA;;EAiCAC;IACA,+BACA;MACA;MACA;IACA,CALA;;IAMA;MACA;MACA;QAAAd;UAAAe;UAAAX;UAAAL;UAAAM;UAAAW;UAAAT;QAAA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CAfA;;IAgBAU;MACA;QACA;UACA;YACA;UACA;;UACA;;UACA;YACAC;UACA;;UACA;YACAC,WADA;YAEApB,oBAFA;YAGAK,oBAHA;YAIAC,sBAJA;YAKAE,4CALA;YAMAS;UANA,GAOAI,IAPA,CAOAC;YACA;cAAAC;cAAAC;YAAA;;YACA;cACA;gBACAb,eADA;gBAEAc;cAFA;cAIA;YACA;UACA,CAhBA;QAiBA,CAzBA,MAyBA;UACA;YACAd,iBADA;YAEAc;UAFA;UAIA;QACA;MACA,CAjCA;IAkCA,CAnDA;;IAoDAC;MACA;IACA;;EAtDA;AAjCA", "names": ["name", "data", "menu", "form", "superior", "sort", "isTop", "isPushApp", "year", "rules", "required", "message", "trigger", "props", "mounted", "methods", "parentId", "isApp", "submitForm", "url", "id", "then", "res", "<PERSON><PERSON><PERSON>", "errmsg", "type", "resetForm"], "sourceRoot": "src/views/wisdomWarehouse/CompilationColumn/CompilationColumnNew", "sources": ["CompilationColumnNew.vue"], "sourcesContent": ["<template>\r\n  <div class=\"CompilationColumnNew\">\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-position=\"top\"\r\n             class=\"newForm\">\r\n      <el-form-item label=\"栏目名称\"\r\n                    class=\"form-input\"\r\n                    prop=\"name\">\r\n        <el-input placeholder=\"请输入栏目名称\"\r\n                  v-model=\"form.name\"\r\n                  clearable>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"所属年份\" props=\"year\">\r\n            <el-date-picker\r\n      v-model=\"form.year\"\r\n      type=\"year\"\r\n      placeholder=\"选择年\">\r\n    </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"排序\"\r\n                    class=\"form-input\"\r\n                    prop=\"sort\">\r\n        <el-input-number placeholder=\"请输入排序\"\r\n                         :min=\"1\"\r\n                         style=\"width:296px;\"\r\n                         v-model=\"form.sort\"\r\n                         clearable></el-input-number>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否置顶\"\r\n                    class=\"form-input\">\r\n        <el-radio-group v-model=\"form.isTop\">\r\n          <el-radio label=\"1\">置顶</el-radio>\r\n          <el-radio label=\"0\">不置顶</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否APP显示\"\r\n                    class=\"form-input\">\r\n        <el-radio-group v-model=\"form.isPushApp\">\r\n          <el-radio label=\"1\">是</el-radio>\r\n          <el-radio label=\"0\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <div class=\"form-button\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'CompilationColumnNew',\r\n  data () {\r\n    return {\r\n      menu: [],\r\n      form: {\r\n        name: '',\r\n        superior: '',\r\n        sort: 0,\r\n        isTop: '1',\r\n        isPushApp: '1',\r\n        year: ''\r\n      },\r\n      rules: {\r\n        name: [\r\n          { required: true, message: '请输入栏目名称', trigger: 'blur' }\r\n        ],\r\n        sort: [\r\n          { required: true, message: '请输入排序', trigger: 'blur' }\r\n        ],\r\n        year: [\r\n          { required: true, message: '请输入排序', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  props: ['id', 'modules'],\r\n  mounted () {\r\n    this.informationColumnTree()\r\n    if (this.id) {\r\n      this.informationColumnInfo()\r\n    }\r\n  },\r\n  methods: {\r\n    async informationColumnTree () {\r\n      // const res = await this.$api.wisdomWarehouse.informationColumnTree({ module: this.modules })\r\n      // var { data } = res\r\n      // this.menu = data\r\n    },\r\n    async informationColumnInfo () {\r\n      const res = await this.$api.wisdomWarehouse.assemblycolumnInfo(this.id)\r\n      var { data: { parentId, sort, name, isTop, isApp, year } } = res\r\n      this.form.superior = parentId\r\n      this.form.name = name\r\n      this.form.sort = sort\r\n      this.form.isTop = isTop\r\n      this.form.isPushApp = isApp\r\n      this.form.year = year\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.superior === '') {\r\n            this.form.superior = 1\r\n          }\r\n          var url = '/assemblycolumn/add'\r\n          if (this.id) {\r\n            url = '/assemblycolumn/edit'\r\n          }\r\n          this.$api.wisdomWarehouse.assemblycolumn(url, {\r\n            id: this.id,\r\n            name: this.form.name,\r\n            sort: this.form.sort,\r\n            isTop: this.form.isTop,\r\n            year: new Date(this.form.year).getFullYear(),\r\n            isApp: this.form.isPushApp\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('newCallback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('newCallback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./CompilationColumnNew.scss\";\r\n</style>\r\n"]}]}