{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\general\\general.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\general\\general.vue", "mtime": 1752541695848}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["general.vue"], "names": [], "mappings": ";AA2GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "general.vue", "sourceRoot": "src/views/general", "sourcesContent": ["<template>\r\n  <el-container class=\"general\"\r\n                v-loading=\"loading\"\r\n                :element-loading-text=\"text\">\r\n    <el-header class=\"general-header\"\r\n               height=\"100px\">\r\n      <div class=\"general-header-log-box\">\r\n        <div class=\"general-header-log\"></div>\r\n        <div class=\"general-header-text-box\">\r\n          <!-- <div class=\"general-header-name\">{{moduleName}}</div> -->\r\n          <div class=\"general-header-name\"></div>\r\n          <div class=\"general-header-module-name\">{{name}}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"general-header-operation\">\r\n        <el-popover placement=\"bottom\"\r\n                    width=\"152\"\r\n                    trigger=\"click\">\r\n          <div class=\"sizeSwitch\"\r\n               slot=\"reference\">\r\n            <template v-if=\"fontSize == 1\">超大号字</template>\r\n            <template v-if=\"fontSize == 2\">大号字</template>\r\n            <template v-if=\"fontSize == 3\">标准字号</template>\r\n            <i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n          </div>\r\n          <div class=\"sizeSwitchItem\"\r\n               @click=\"fontSizeClick(1)\"\r\n               :class=\"{sizeSwitchItemA:fontSize == 1}\">超大号字</div>\r\n          <div class=\"sizeSwitchItem\"\r\n               @click=\"fontSizeClick(2)\"\r\n               :class=\"{sizeSwitchItemA:fontSize == 2}\">大号字</div>\r\n          <div class=\"sizeSwitchItem\"\r\n               @click=\"fontSizeClick(3)\"\r\n               :class=\"{sizeSwitchItemA:fontSize == 3}\">标准字号</div>\r\n        </el-popover>\r\n\r\n        <div class=\"general-header-home\"\r\n             @click=\"returnClick\"></div>\r\n        <div class=\"general-header-help\"\r\n             v-if=\"helpShow.length\"\r\n             @click=\"help\"></div>\r\n        <div class=\"general-header-set\"\r\n             v-if=\"systemShow.length\"\r\n             @click=\"system\"></div>\r\n        <div class=\"general-header-user\"\r\n             v-if=\"user\">\r\n          <!-- <div class=\"general-header-user-name\">{{user.userName}}</div> -->\r\n          <div class=\"general-header-user-img\">\r\n            <img :src=\"user.headImg\"\r\n                 alt=\"\">\r\n          </div>\r\n        </div>\r\n        <div class=\"general-exit\"\r\n             @click=\"exit()\">退出</div>\r\n      </div>\r\n    </el-header>\r\n    <el-container class=\"general-container\">\r\n      <el-aside width=\"248px\"\r\n                class=\"general-aside\">\r\n        <zy-menu v-model=\"menuId\"\r\n                 :menu=\"menuData\"\r\n                 @select=\"menuSelect\"\r\n                 :props=\"{ children: 'children', label: 'name', id: 'menuId' ,to: 'to',icon:'iconUrl', isShow: 'isShow', showValue: true}\"></zy-menu>\r\n      </el-aside>\r\n      <el-main class=\"general-main\">\r\n        <div class=\"general-main-breadcrumb\">\r\n          <el-breadcrumb separator=\"/\">\r\n            <el-breadcrumb-item v-for=\"(item,index) in crumbsData\"\r\n                                @click.native=\"crumbsClcik(item,index)\"\r\n                                :to=\"{ path: item.to, query: item.params }\"\r\n                                :key=\"item.id\">{{item.name}}</el-breadcrumb-item>\r\n          </el-breadcrumb>\r\n        </div>\r\n        <div class=\"general-main-content scrollBar\">\r\n\r\n          <zy-pop-up v-model=\"officeShow\"\r\n                     title=\"文件预览\">\r\n            <div class=\"btnbox\">\r\n              <el-button type=\"danger\"\r\n                         @click=\"dowload('word')\">无法打开，请点击按钮</el-button>\r\n            </div>\r\n            <div id=\"OfficeDiv\"\r\n                 ref=\"OfficeDiv\"\r\n                 class=\"OfficeDiv\"\r\n                 style=\"width:1160px; height: 700px;\">\r\n            </div>\r\n          </zy-pop-up>\r\n          <zy-pop-up v-model=\"pdfshow\"\r\n                     title=\"文件预览\">\r\n            <div class=\"btnbox\">\r\n              <el-button type=\"danger\"\r\n                         @click=\"dowload('pdf')\">无法打开，请点击按钮</el-button>\r\n            </div>\r\n            <div id=\"pdfDiv\"\r\n                 ref=\"pdfDiv\"\r\n                 style=\"width:1160px; height: 700px;\">\r\n            </div>\r\n          </zy-pop-up>\r\n          <keep-alive :include=\"includes\">\r\n            <router-view :key=\"$route.fullPath\" />\r\n          </keep-alive>\r\n        </div>\r\n      </el-main>\r\n    </el-container>\r\n  </el-container>\r\n</template>\r\n<script>\r\nimport mixins from '../../mixins'\r\nimport mixinsGeneral from '../../mixins/general'\r\nimport { mapActions } from 'vuex'\r\nimport zyMenu from '../../components/zy-menu/zy-menu.vue'\r\nimport woffice from './office.js'\r\nimport webOfficeTplPdf from '../../../public/static/js/iWebPDF2018.js'\r\n// import { WebOpenUrlPdf } from '../../../public/static/js/PDF2018.js'\r\nexport default {\r\n  components: { zyMenu },\r\n  name: 'general',\r\n  data () {\r\n    return {\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())),\r\n      moduleName: JSON.parse(sessionStorage.getItem('generalName' + this.$logo())),\r\n      name: JSON.parse(sessionStorage.getItem('name')),\r\n      loading: false,\r\n      text: '正在加载中',\r\n      officeShow: false,\r\n      pdfshow: false,\r\n      webOfficeTplPdf: webOfficeTplPdf,\r\n      fontSize: 3\r\n    }\r\n  },\r\n  mounted () {\r\n    this.fontSize = JSON.parse(localStorage.getItem('fontSize')) || 3\r\n    this.$nextTick(() => {\r\n      window.addEventListener('scroll', this.scrollMenu, true)\r\n    })\r\n  },\r\n  watch: {\r\n    officeShow () {\r\n      if (!this.officeShow) {\r\n        this.webOfficeObj.WebClose()\r\n        console.log(this.webOfficeObj)\r\n      }\r\n    }\r\n  },\r\n  destroyed () {\r\n    window.removeEventListener('scroll', this.scrollMenu)\r\n  },\r\n  provide () {\r\n    return {\r\n      newTab: this.newTab, // 打开新一级面包屑方法，以前使用newTab打开tab页的页面不用改\r\n      tabDelJump: this.tabDelJump, // 关闭当前面包屑跳转到上一级面包屑方法，以前调用tabDelJump关闭tab页的不用改  （注意：不用传参）\r\n      jumpMenu: this.jumpMenu, // 没啥用，为了不然页面报错\r\n      tabNameDelete: this.tabNameDelete, // 也是关闭当前面包屑跳转到上一级面包屑方法，为了兼容以前页面调用的方法 （注意：不用传参）\r\n      loadingprovide: this.loadingprovide,\r\n      loadingtext: this.loadingtext,\r\n      matchingMenu: this.matchingMenu,\r\n      openoffice: this.openoffice,\r\n      openPdf: this.openPdf\r\n\r\n    }\r\n  },\r\n  mixins: [mixins, mixinsGeneral, woffice],\r\n  methods: {\r\n    fontSizeClick (type) {\r\n      this.fontSize = type\r\n      this.$fontSize(type)\r\n    },\r\n    openoffice (openUrl) {\r\n      this.officeShow = true\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          const OfficeDiv = this.$refs.OfficeDiv\r\n          OfficeDiv.innerHTML = this.webOfficeTpl\r\n          this.webOfficeObj = new WebOffice2015() // eslint-disable-line \r\n          this.webOfficeObj.setObj(document.getElementById('WebOffice2015'))\r\n          setTimeout(() => {\r\n            this.initWebOfficeObject(openUrl)\r\n          }, 1000)\r\n        }, 1000)\r\n      })\r\n    },\r\n    openPdf (openUrl) {\r\n      this.pdfshow = true\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          const pdfDiv = this.$refs.pdfDiv\r\n          pdfDiv.innerHTML = this.webOfficeTplPdf\r\n          setTimeout(() => {\r\n            const addins = document.getElementById('iWebPDF2018').iWebPDFFun\r\n            if (addins != null) {\r\n              addins.WebOpenUrlFile(openUrl)\r\n            }\r\n          }, 1000)\r\n        }, 1000)\r\n      })\r\n    },\r\n    ...mapActions('position', ['GET_top']),\r\n    loadingprovide (loading) {\r\n      this.loading = loading\r\n    },\r\n    loadingtext (text) {\r\n      this.text = text\r\n    },\r\n    scrollMenu (e) {\r\n      var arr = ['/activityNew']\r\n      if (arr.includes(this.$route.path)) {\r\n        var top = this.$refs.scrollMenuRef.scrollTop\r\n        this.GET_top(top)\r\n      }\r\n    },\r\n    dowload (type) {\r\n      const elink = document.createElement('a')\r\n      elink.style.display = 'none'\r\n      elink.href = ''\r\n      if (type === 'word') {\r\n        elink.href = './static/office2015/iWebOffice2015.msi'\r\n      } else {\r\n        elink.href = './static/pdf2018/iWebPDF2018.exe'\r\n      }\r\n      document.body.appendChild(elink)\r\n      elink.click()\r\n      document.body.removeChild(elink)\r\n      // const elink = document.createElement('a')\r\n      // elink.style.display = 'none'\r\n      // elink.download = '../../../public/static/office2015/msiexec.exe'\r\n      // if (type === 'word') {\r\n      // } else {\r\n      //   elink.download = '../../../public/static/pdf2018/iWebPDF2018.exe'\r\n      // }\r\n      // document.body.appendChild(elink)\r\n      // elink.click()\r\n      // document.body.removeChild(elink)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"./general.scss\";\r\n.btnbox {\r\n  text-align: right;\r\n  margin: 10px;\r\n  margin-right: 20px;\r\n  // .el-button {\r\n  //     font-size: $textSize16;\r\n  //     color: #fff;\r\n  //     background: #f56c6c;\r\n  // }\r\n}\r\n</style>\r\n"]}]}