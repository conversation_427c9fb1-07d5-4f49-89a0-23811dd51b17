{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\ThreeActivities\\OrganReviewNew.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\ThreeActivities\\OrganReviewNew.vue", "mtime": 1752541693878}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA0CA;EACAA,sBADA;;EAEAC;IACA;MACAC,gBADA;MAGAC;QACAC,MADA;QAEAC,QAFA;QAGAC,YAHA;QAIAC,YAJA;QAKAC,cALA;QAMAC;MANA,CAHA;MAYAC;IAZA;EAcA,CAjBA;;EAkBAC,2BAlBA;;EAmBAC;IACA;IAEA;EACA,CAvBA;;EAwBAC;IACA;AACA;AACA;IACA;MACA;QACAC;MADA;MAGA;QAAAb;MAAA;MACA;IACA,CAVA;;IAWAc;MACA;IACA,CAbA;;IAeA;IACA;MACA;MACA;QAAAd;MAAA;MACA;MACAA;QACA;UACA;QACA;MACA,CAJA;IAKA,CAzBA;;IA2BA;MACA;MACA;QAAAA;MAAA;MACA;MACA;IACA,CAhCA;;IAiCAe;MACAC;IACA,CAnCA;;IAoCAC;MACA;QACA;UACA;YACAC,YADA;YAEAd;UAFA,GAIAe,IAJA,CAIAC;YACA;cAAAC;cAAAC;YAAA;;YACA;cACA;gBACAC,eADA;gBAEAnB;cAFA;cAIA;YACA;UACA,CAbA;QAcA,CAfA,MAeA;UACA;YACAmB,iBADA;YAEAnB;UAFA;UAIA;QACA;MACA,CAvBA;IAwBA,CA7DA;;IA8DAoB;MACA;IACA;;EAhEA;AAxBA", "names": ["name", "data", "classifyData", "form", "id", "type", "officeId", "sedateId", "officeName", "activityTypeName", "circlesStatus", "props", "mounted", "methods", "types", "select", "handleChange", "console", "submitForm", "ids", "then", "res", "<PERSON><PERSON><PERSON>", "errmsg", "message", "resetForm"], "sourceRoot": "src/views/AssessmentOrgan/ThreeActivities", "sources": ["OrganReviewNew.vue"], "sourcesContent": ["<template>\r\n\r\n  <div class=\"OrganReviewNew\">\r\n    <el-form :model=\"form\"\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"demo-form\">\r\n\r\n      <!-- <el-form-item label=\"三双活动类型\"\r\n                    class=\"form-title\">\r\n        <el-input-number v-model=\"form.meetTypeName\"\r\n                         controls-position=\"right\"\r\n                         @change=\"handleChange\"\r\n                         :min=\"0\"\r\n                         :max=\"100\"\r\n                         class=\"form-content\">\r\n        </el-input-number>\r\n      </el-form-item> -->\r\n\r\n      <el-form-item label=\"三双活动类型\"\r\n                    prop=\"activityTypeName\">\r\n        <el-select v-model=\"form.activityTypeName\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择类型\">\r\n          <el-option v-for=\"item in classifyData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'OrganReviewNew',\r\n  data () {\r\n    return {\r\n      classifyData: [],\r\n\r\n      form: {\r\n        id: '',\r\n        type: '',\r\n        officeId: '',\r\n        sedateId: '',\r\n        officeName: '',\r\n        activityTypeName: ''\r\n      },\r\n\r\n      circlesStatus: []\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  mounted () {\r\n    this.dictionaryPubkvs()\r\n\r\n    this.templatePageInfo()\r\n  },\r\n  methods: {\r\n    /**\r\n*字典\r\n*/\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluate_activiti_three'\r\n      })\r\n      var { data } = res\r\n      this.classifyData = data.evaluate_activiti_three\r\n    },\r\n    select (row) {\r\n      this.form.officeName = row ? row.label : ''\r\n    },\r\n\r\n    // 获取详情  (没有详情接口,接口从列表获取)\r\n    async templatePageInfo () {\r\n      const res = await this.$api.AssessmentOrgan.reqThreeActivitiesList(this.id)\r\n      var { data } = res\r\n      this.form.id = data.id\r\n      data.forEach(item => {\r\n        if (this.id === item.id) {\r\n          this.form.activityTypeName = item.activityTypeName\r\n        }\r\n      })\r\n    },\r\n\r\n    async historycirclesInfo () {\r\n      const res = await this.$api.memberInformation.historycirclesInfo(this.id)\r\n      var { data } = res\r\n      this.form.boutYear = data.boutYear\r\n      this.form.circlesStatus = data.circlesStatus\r\n    },\r\n    handleChange (value) {\r\n      console.log(value)\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          this.$api.AssessmentOrgan.reqEditThreeClass({\r\n            ids: this.id,\r\n            type: this.form.activityTypeName\r\n\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('newCallback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('newCallback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.OrganReviewNew {\r\n  width: 430px;\r\n  height: 100%;\r\n  padding: 24px 40px;\r\n  // overflow-y: scroll;\r\n  overflow-x: hidden;\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n"]}]}