{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\element-ui\\lib\\submenu.js", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\element-ui\\lib\\submenu.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["module", "exports", "modules", "installedModules", "__webpack_require__", "moduleId", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "getDefault", "getModuleExports", "object", "property", "prototype", "hasOwnProperty", "p", "s", "__webpack_exports__", "normalizeComponent", "scriptExports", "render", "staticRenderFns", "functionalTemplate", "injectStyles", "scopeId", "moduleIdentifier", "shadowMode", "options", "_compiled", "functional", "_scopeId", "hook", "context", "$vnode", "ssrContext", "parent", "__VUE_SSR_CONTEXT__", "_registeredComponents", "add", "_ssrRegister", "$root", "$options", "shadowRoot", "_injectStyles", "originalRender", "renderWithStyleInjection", "h", "existing", "beforeCreate", "concat", "collapse_transition_", "collapse_transition_default", "menu_mixin", "emitter_", "emitter_default", "vue_popper_", "vue_popper_default", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "transform<PERSON><PERSON>in", "type", "Boolean", "String", "default", "offset", "a", "boundariesPadding", "popperOptions", "data", "methods", "<PERSON><PERSON><PERSON><PERSON>", "deactivated", "submenuvue_type_script_lang_js_", "componentName", "mixins", "components", "ElCollapseTransition", "index", "required", "showTimeout", "Number", "hideTimeout", "popperClass", "disabled", "popperAppendToBody", "undefined", "popperJS", "timeout", "items", "submenus", "mouseInChild", "watch", "opened", "val", "_this", "isMenuPopup", "$nextTick", "_", "updatePopper", "computed", "appendToBody", "isFirstLevel", "menuTransitionName", "rootMenu", "collapse", "openedMenus", "indexOf", "active", "isActive", "keys", "for<PERSON>ach", "hoverBackground", "backgroundColor", "activeTextColor", "textColor", "titleStyle", "color", "borderBottomColor", "$parent", "handleCollapseToggle", "initPopper", "do<PERSON><PERSON>roy", "addItem", "item", "$set", "removeItem", "addSubmenu", "removeSubmenu", "handleClick", "menuTrigger", "dispatch", "handleMouseenter", "event", "_this2", "arguments", "length", "window", "relatedTarget", "clearTimeout", "setTimeout", "openMenu", "indexPath", "$el", "dispatchEvent", "MouseEvent", "handleMouseleave", "_this3", "deepDispatch", "closeMenu", "handleTitleMouseenter", "title", "$refs", "style", "handleTitleMouseleave", "updatePlacement", "currentPlacement", "referenceElm", "<PERSON><PERSON><PERSON><PERSON>", "menu", "created", "_this4", "$on", "mounted", "parentMenu", "_this5", "paddingStyle", "$slots", "popupMenu", "attrs", "ref", "directives", "on", "mouseenter", "$event", "mouseleave", "focus", "role", "inlineMenu", "submenuTitleIcon", "src_submenuvue_type_script_lang_js_", "componentNormalizer", "component", "api", "__file", "submenu", "install", "<PERSON><PERSON>", "packages_submenu", "require", "inject", "path", "unshift", "padding", "paddingLeft"], "sources": ["D:/zy/xm/pc/qdzx/product/node_modules/element-ui/lib/submenu.js"], "sourcesContent": ["module.exports =\n/******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__webpack_require__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__webpack_require__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __webpack_require__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__webpack_require__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"/dist/\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 129);\n/******/ })\n/************************************************************************/\n/******/ ({\n\n/***/ 0:\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"a\", function() { return normalizeComponent; });\n/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nfunction normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode /* vue-cli only */\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () { injectStyles.call(this, this.$root.$options.shadowRoot) }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functioal component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n\n\n/***/ }),\n\n/***/ 129:\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n\n// EXTERNAL MODULE: external \"element-ui/lib/transitions/collapse-transition\"\nvar collapse_transition_ = __webpack_require__(28);\nvar collapse_transition_default = /*#__PURE__*/__webpack_require__.n(collapse_transition_);\n\n// EXTERNAL MODULE: ./packages/menu/src/menu-mixin.js\nvar menu_mixin = __webpack_require__(36);\n\n// EXTERNAL MODULE: external \"element-ui/lib/mixins/emitter\"\nvar emitter_ = __webpack_require__(4);\nvar emitter_default = /*#__PURE__*/__webpack_require__.n(emitter_);\n\n// EXTERNAL MODULE: external \"element-ui/lib/utils/vue-popper\"\nvar vue_popper_ = __webpack_require__(5);\nvar vue_popper_default = /*#__PURE__*/__webpack_require__.n(vue_popper_);\n\n// CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/menu/src/submenu.vue?vue&type=script&lang=js&\n\n\n\n\n\n\nvar poperMixins = {\n  props: {\n    transformOrigin: {\n      type: [Boolean, String],\n      default: false\n    },\n    offset: vue_popper_default.a.props.offset,\n    boundariesPadding: vue_popper_default.a.props.boundariesPadding,\n    popperOptions: vue_popper_default.a.props.popperOptions\n  },\n  data: vue_popper_default.a.data,\n  methods: vue_popper_default.a.methods,\n  beforeDestroy: vue_popper_default.a.beforeDestroy,\n  deactivated: vue_popper_default.a.deactivated\n};\n\n/* harmony default export */ var submenuvue_type_script_lang_js_ = ({\n  name: 'ElSubmenu',\n\n  componentName: 'ElSubmenu',\n\n  mixins: [menu_mixin[\"a\" /* default */], emitter_default.a, poperMixins],\n\n  components: { ElCollapseTransition: collapse_transition_default.a },\n\n  props: {\n    index: {\n      type: String,\n      required: true\n    },\n    showTimeout: {\n      type: Number,\n      default: 300\n    },\n    hideTimeout: {\n      type: Number,\n      default: 300\n    },\n    popperClass: String,\n    disabled: Boolean,\n    popperAppendToBody: {\n      type: Boolean,\n      default: undefined\n    }\n  },\n\n  data: function data() {\n    return {\n      popperJS: null,\n      timeout: null,\n      items: {},\n      submenus: {},\n      mouseInChild: false\n    };\n  },\n\n  watch: {\n    opened: function opened(val) {\n      var _this = this;\n\n      if (this.isMenuPopup) {\n        this.$nextTick(function (_) {\n          _this.updatePopper();\n        });\n      }\n    }\n  },\n  computed: {\n    // popper option\n    appendToBody: function appendToBody() {\n      return this.popperAppendToBody === undefined ? this.isFirstLevel : this.popperAppendToBody;\n    },\n    menuTransitionName: function menuTransitionName() {\n      return this.rootMenu.collapse ? 'el-zoom-in-left' : 'el-zoom-in-top';\n    },\n    opened: function opened() {\n      return this.rootMenu.openedMenus.indexOf(this.index) > -1;\n    },\n    active: function active() {\n      var isActive = false;\n      var submenus = this.submenus;\n      var items = this.items;\n\n      Object.keys(items).forEach(function (index) {\n        if (items[index].active) {\n          isActive = true;\n        }\n      });\n\n      Object.keys(submenus).forEach(function (index) {\n        if (submenus[index].active) {\n          isActive = true;\n        }\n      });\n\n      return isActive;\n    },\n    hoverBackground: function hoverBackground() {\n      return this.rootMenu.hoverBackground;\n    },\n    backgroundColor: function backgroundColor() {\n      return this.rootMenu.backgroundColor || '';\n    },\n    activeTextColor: function activeTextColor() {\n      return this.rootMenu.activeTextColor || '';\n    },\n    textColor: function textColor() {\n      return this.rootMenu.textColor || '';\n    },\n    mode: function mode() {\n      return this.rootMenu.mode;\n    },\n    isMenuPopup: function isMenuPopup() {\n      return this.rootMenu.isMenuPopup;\n    },\n    titleStyle: function titleStyle() {\n      if (this.mode !== 'horizontal') {\n        return {\n          color: this.textColor\n        };\n      }\n      return {\n        borderBottomColor: this.active ? this.rootMenu.activeTextColor ? this.activeTextColor : '' : 'transparent',\n        color: this.active ? this.activeTextColor : this.textColor\n      };\n    },\n    isFirstLevel: function isFirstLevel() {\n      var isFirstLevel = true;\n      var parent = this.$parent;\n      while (parent && parent !== this.rootMenu) {\n        if (['ElSubmenu', 'ElMenuItemGroup'].indexOf(parent.$options.componentName) > -1) {\n          isFirstLevel = false;\n          break;\n        } else {\n          parent = parent.$parent;\n        }\n      }\n      return isFirstLevel;\n    }\n  },\n  methods: {\n    handleCollapseToggle: function handleCollapseToggle(value) {\n      if (value) {\n        this.initPopper();\n      } else {\n        this.doDestroy();\n      }\n    },\n    addItem: function addItem(item) {\n      this.$set(this.items, item.index, item);\n    },\n    removeItem: function removeItem(item) {\n      delete this.items[item.index];\n    },\n    addSubmenu: function addSubmenu(item) {\n      this.$set(this.submenus, item.index, item);\n    },\n    removeSubmenu: function removeSubmenu(item) {\n      delete this.submenus[item.index];\n    },\n    handleClick: function handleClick() {\n      var rootMenu = this.rootMenu,\n          disabled = this.disabled;\n\n      if (rootMenu.menuTrigger === 'hover' && rootMenu.mode === 'horizontal' || rootMenu.collapse && rootMenu.mode === 'vertical' || disabled) {\n        return;\n      }\n      this.dispatch('ElMenu', 'submenu-click', this);\n    },\n    handleMouseenter: function handleMouseenter(event) {\n      var _this2 = this;\n\n      var showTimeout = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.showTimeout;\n\n\n      if (!('ActiveXObject' in window) && event.type === 'focus' && !event.relatedTarget) {\n        return;\n      }\n      var rootMenu = this.rootMenu,\n          disabled = this.disabled;\n\n      if (rootMenu.menuTrigger === 'click' && rootMenu.mode === 'horizontal' || !rootMenu.collapse && rootMenu.mode === 'vertical' || disabled) {\n        return;\n      }\n      this.dispatch('ElSubmenu', 'mouse-enter-child');\n      clearTimeout(this.timeout);\n      this.timeout = setTimeout(function () {\n        _this2.rootMenu.openMenu(_this2.index, _this2.indexPath);\n      }, showTimeout);\n\n      if (this.appendToBody) {\n        this.$parent.$el.dispatchEvent(new MouseEvent('mouseenter'));\n      }\n    },\n    handleMouseleave: function handleMouseleave() {\n      var _this3 = this;\n\n      var deepDispatch = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      var rootMenu = this.rootMenu;\n\n      if (rootMenu.menuTrigger === 'click' && rootMenu.mode === 'horizontal' || !rootMenu.collapse && rootMenu.mode === 'vertical') {\n        return;\n      }\n      this.dispatch('ElSubmenu', 'mouse-leave-child');\n      clearTimeout(this.timeout);\n      this.timeout = setTimeout(function () {\n        !_this3.mouseInChild && _this3.rootMenu.closeMenu(_this3.index);\n      }, this.hideTimeout);\n\n      if (this.appendToBody && deepDispatch) {\n        if (this.$parent.$options.name === 'ElSubmenu') {\n          this.$parent.handleMouseleave(true);\n        }\n      }\n    },\n    handleTitleMouseenter: function handleTitleMouseenter() {\n      if (this.mode === 'horizontal' && !this.rootMenu.backgroundColor) return;\n      var title = this.$refs['submenu-title'];\n      title && (title.style.backgroundColor = this.rootMenu.hoverBackground);\n    },\n    handleTitleMouseleave: function handleTitleMouseleave() {\n      if (this.mode === 'horizontal' && !this.rootMenu.backgroundColor) return;\n      var title = this.$refs['submenu-title'];\n      title && (title.style.backgroundColor = this.rootMenu.backgroundColor || '');\n    },\n    updatePlacement: function updatePlacement() {\n      this.currentPlacement = this.mode === 'horizontal' && this.isFirstLevel ? 'bottom-start' : 'right-start';\n    },\n    initPopper: function initPopper() {\n      this.referenceElm = this.$el;\n      this.popperElm = this.$refs.menu;\n      this.updatePlacement();\n    }\n  },\n  created: function created() {\n    var _this4 = this;\n\n    this.$on('toggle-collapse', this.handleCollapseToggle);\n    this.$on('mouse-enter-child', function () {\n      _this4.mouseInChild = true;\n      clearTimeout(_this4.timeout);\n    });\n    this.$on('mouse-leave-child', function () {\n      _this4.mouseInChild = false;\n      clearTimeout(_this4.timeout);\n    });\n  },\n  mounted: function mounted() {\n    this.parentMenu.addSubmenu(this);\n    this.rootMenu.addSubmenu(this);\n    this.initPopper();\n  },\n  beforeDestroy: function beforeDestroy() {\n    this.parentMenu.removeSubmenu(this);\n    this.rootMenu.removeSubmenu(this);\n  },\n  render: function render(h) {\n    var _this5 = this;\n\n    var active = this.active,\n        opened = this.opened,\n        paddingStyle = this.paddingStyle,\n        titleStyle = this.titleStyle,\n        backgroundColor = this.backgroundColor,\n        rootMenu = this.rootMenu,\n        currentPlacement = this.currentPlacement,\n        menuTransitionName = this.menuTransitionName,\n        mode = this.mode,\n        disabled = this.disabled,\n        popperClass = this.popperClass,\n        $slots = this.$slots,\n        isFirstLevel = this.isFirstLevel;\n\n\n    var popupMenu = h(\n      'transition',\n      {\n        attrs: { name: menuTransitionName }\n      },\n      [h(\n        'div',\n        {\n          ref: 'menu',\n          directives: [{\n            name: 'show',\n            value: opened\n          }],\n\n          'class': ['el-menu--' + mode, popperClass],\n          on: {\n            'mouseenter': function mouseenter($event) {\n              return _this5.handleMouseenter($event, 100);\n            },\n            'mouseleave': function mouseleave() {\n              return _this5.handleMouseleave(true);\n            },\n            'focus': function focus($event) {\n              return _this5.handleMouseenter($event, 100);\n            }\n          }\n        },\n        [h(\n          'ul',\n          {\n            attrs: {\n              role: 'menu'\n            },\n            'class': ['el-menu el-menu--popup', 'el-menu--popup-' + currentPlacement],\n            style: { backgroundColor: rootMenu.backgroundColor || '' } },\n          [$slots.default]\n        )]\n      )]\n    );\n\n    var inlineMenu = h('el-collapse-transition', [h(\n      'ul',\n      {\n        attrs: {\n          role: 'menu'\n        },\n        'class': 'el-menu el-menu--inline',\n        directives: [{\n          name: 'show',\n          value: opened\n        }],\n\n        style: { backgroundColor: rootMenu.backgroundColor || '' } },\n      [$slots.default]\n    )]);\n\n    var submenuTitleIcon = rootMenu.mode === 'horizontal' && isFirstLevel || rootMenu.mode === 'vertical' && !rootMenu.collapse ? 'el-icon-arrow-down' : 'el-icon-arrow-right';\n\n    return h(\n      'li',\n      {\n        'class': {\n          'el-submenu': true,\n          'is-active': active,\n          'is-opened': opened,\n          'is-disabled': disabled\n        },\n        attrs: { role: 'menuitem',\n          'aria-haspopup': 'true',\n          'aria-expanded': opened\n        },\n        on: {\n          'mouseenter': this.handleMouseenter,\n          'mouseleave': function mouseleave() {\n            return _this5.handleMouseleave(false);\n          },\n          'focus': this.handleMouseenter\n        }\n      },\n      [h(\n        'div',\n        {\n          'class': 'el-submenu__title',\n          ref: 'submenu-title',\n          on: {\n            'click': this.handleClick,\n            'mouseenter': this.handleTitleMouseenter,\n            'mouseleave': this.handleTitleMouseleave\n          },\n\n          style: [paddingStyle, titleStyle, { backgroundColor: backgroundColor }]\n        },\n        [$slots.title, h('i', { 'class': ['el-submenu__icon-arrow', submenuTitleIcon] })]\n      ), this.isMenuPopup ? popupMenu : inlineMenu]\n    );\n  }\n});\n// CONCATENATED MODULE: ./packages/menu/src/submenu.vue?vue&type=script&lang=js&\n /* harmony default export */ var src_submenuvue_type_script_lang_js_ = (submenuvue_type_script_lang_js_); \n// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js\nvar componentNormalizer = __webpack_require__(0);\n\n// CONCATENATED MODULE: ./packages/menu/src/submenu.vue\nvar render, staticRenderFns\n\n\n\n\n/* normalize component */\n\nvar component = Object(componentNormalizer[\"a\" /* default */])(\n  src_submenuvue_type_script_lang_js_,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (false) { var api; }\ncomponent.options.__file = \"packages/menu/src/submenu.vue\"\n/* harmony default export */ var submenu = (component.exports);\n// CONCATENATED MODULE: ./packages/submenu/index.js\n\n\n/* istanbul ignore next */\nsubmenu.install = function (Vue) {\n  Vue.component(submenu.name, submenu);\n};\n\n/* harmony default export */ var packages_submenu = __webpack_exports__[\"default\"] = (submenu);\n\n/***/ }),\n\n/***/ 28:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/transitions/collapse-transition\");\n\n/***/ }),\n\n/***/ 36:\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n/* harmony default export */ __webpack_exports__[\"a\"] = ({\n  inject: ['rootMenu'],\n  computed: {\n    indexPath: function indexPath() {\n      var path = [this.index];\n      var parent = this.$parent;\n      while (parent.$options.componentName !== 'ElMenu') {\n        if (parent.index) {\n          path.unshift(parent.index);\n        }\n        parent = parent.$parent;\n      }\n      return path;\n    },\n    parentMenu: function parentMenu() {\n      var parent = this.$parent;\n      while (parent && ['ElMenu', 'ElSubmenu'].indexOf(parent.$options.componentName) === -1) {\n        parent = parent.$parent;\n      }\n      return parent;\n    },\n    paddingStyle: function paddingStyle() {\n      if (this.rootMenu.mode !== 'vertical') return {};\n\n      var padding = 20;\n      var parent = this.$parent;\n\n      if (this.rootMenu.collapse) {\n        padding = 20;\n      } else {\n        while (parent && parent.$options.componentName !== 'ElMenu') {\n          if (parent.$options.componentName === 'ElSubmenu') {\n            padding += 20;\n          }\n          parent = parent.$parent;\n        }\n      }\n      return { paddingLeft: padding + 'px' };\n    }\n  }\n});\n\n/***/ }),\n\n/***/ 4:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/mixins/emitter\");\n\n/***/ }),\n\n/***/ 5:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/vue-popper\");\n\n/***/ })\n\n/******/ });"], "mappings": "AAAAA,MAAM,CAACC,OAAP;AACA;AAAU,UAASC,OAAT,EAAkB;EAAE;;EAC9B;EAAU;;EACV;EAAU,IAAIC,gBAAgB,GAAG,EAAvB;EACV;;EACA;EAAU;;EACV;;EAAU,SAASC,mBAAT,CAA6BC,QAA7B,EAAuC;IACjD;;IACA;IAAW;;IACX;IAAW,IAAGF,gBAAgB,CAACE,QAAD,CAAnB,EAA+B;MAC1C;MAAY,OAAOF,gBAAgB,CAACE,QAAD,CAAhB,CAA2BJ,OAAlC;MACZ;IAAY;IACZ;IAAW;;IACX;;;IAAW,IAAID,MAAM,GAAGG,gBAAgB,CAACE,QAAD,CAAhB,GAA6B;MACrD;MAAYC,CAAC,EAAED,QADsC;;MAErD;MAAYE,CAAC,EAAE,KAFsC;;MAGrD;MAAYN,OAAO,EAAE;MACrB;;IAJqD,CAA1C;IAKX;;IACA;IAAW;;IACX;;IAAWC,OAAO,CAACG,QAAD,CAAP,CAAkBG,IAAlB,CAAuBR,MAAM,CAACC,OAA9B,EAAuCD,MAAvC,EAA+CA,MAAM,CAACC,OAAtD,EAA+DG,mBAA/D;IACX;;IACA;IAAW;;IACX;;IAAWJ,MAAM,CAACO,CAAP,GAAW,IAAX;IACX;;IACA;IAAW;;IACX;;IAAW,OAAOP,MAAM,CAACC,OAAd;IACX;EAAW;EACX;;EACA;;EACA;EAAU;;EACV;;;EAAUG,mBAAmB,CAACK,CAApB,GAAwBP,OAAxB;EACV;;EACA;EAAU;;EACV;;EAAUE,mBAAmB,CAACM,CAApB,GAAwBP,gBAAxB;EACV;;EACA;EAAU;;EACV;;EAAUC,mBAAmB,CAACO,CAApB,GAAwB,UAASV,OAAT,EAAkBW,IAAlB,EAAwBC,MAAxB,EAAgC;IAClE;IAAW,IAAG,CAACT,mBAAmB,CAACU,CAApB,CAAsBb,OAAtB,EAA+BW,IAA/B,CAAJ,EAA0C;MACrD;MAAYG,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+BW,IAA/B,EAAqC;QAAEK,UAAU,EAAE,IAAd;QAAoBC,GAAG,EAAEL;MAAzB,CAArC;MACZ;IAAY;IACZ;;EAAW,CAJD;EAKV;;EACA;EAAU;;EACV;;;EAAUT,mBAAmB,CAACe,CAApB,GAAwB,UAASlB,OAAT,EAAkB;IACpD;IAAW,IAAG,OAAOmB,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACC,WAA3C,EAAwD;MACnE;MAAYN,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+BmB,MAAM,CAACC,WAAtC,EAAmD;QAAEC,KAAK,EAAE;MAAT,CAAnD;MACZ;IAAY;IACZ;;;IAAWP,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+B,YAA/B,EAA6C;MAAEqB,KAAK,EAAE;IAAT,CAA7C;IACX;EAAW,CALD;EAMV;;EACA;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;;;EAAUlB,mBAAmB,CAACmB,CAApB,GAAwB,UAASD,KAAT,EAAgBE,IAAhB,EAAsB;IACxD;IAAW,IAAGA,IAAI,GAAG,CAAV,EAAaF,KAAK,GAAGlB,mBAAmB,CAACkB,KAAD,CAA3B;IACxB;;IAAW,IAAGE,IAAI,GAAG,CAAV,EAAa,OAAOF,KAAP;IACxB;;IAAW,IAAIE,IAAI,GAAG,CAAR,IAAc,OAAOF,KAAP,KAAiB,QAA/B,IAA2CA,KAA3C,IAAoDA,KAAK,CAACG,UAA7D,EAAyE,OAAOH,KAAP;IACpF;;IAAW,IAAII,EAAE,GAAGX,MAAM,CAACY,MAAP,CAAc,IAAd,CAAT;IACX;;IAAWvB,mBAAmB,CAACe,CAApB,CAAsBO,EAAtB;IACX;;;IAAWX,MAAM,CAACC,cAAP,CAAsBU,EAAtB,EAA0B,SAA1B,EAAqC;MAAET,UAAU,EAAE,IAAd;MAAoBK,KAAK,EAAEA;IAA3B,CAArC;IACX;;IAAW,IAAGE,IAAI,GAAG,CAAP,IAAY,OAAOF,KAAP,IAAgB,QAA/B,EAAyC,KAAI,IAAIM,GAAR,IAAeN,KAAf,EAAsBlB,mBAAmB,CAACO,CAApB,CAAsBe,EAAtB,EAA0BE,GAA1B,EAA+B,UAASA,GAAT,EAAc;MAAE,OAAON,KAAK,CAACM,GAAD,CAAZ;IAAoB,CAApC,CAAqCC,IAArC,CAA0C,IAA1C,EAAgDD,GAAhD,CAA/B;IAC1E;;IAAW,OAAOF,EAAP;IACX;EAAW,CATD;EAUV;;EACA;EAAU;;EACV;;;EAAUtB,mBAAmB,CAAC0B,CAApB,GAAwB,UAAS9B,MAAT,EAAiB;IACnD;IAAW,IAAIa,MAAM,GAAGb,MAAM,IAAIA,MAAM,CAACyB,UAAjB;IACxB;IAAY,SAASM,UAAT,GAAsB;MAAE,OAAO/B,MAAM,CAAC,SAAD,CAAb;IAA2B,CADvC;IAExB;IAAY,SAASgC,gBAAT,GAA4B;MAAE,OAAOhC,MAAP;IAAgB,CAF/C;IAGX;;IAAWI,mBAAmB,CAACO,CAApB,CAAsBE,MAAtB,EAA8B,GAA9B,EAAmCA,MAAnC;IACX;;;IAAW,OAAOA,MAAP;IACX;EAAW,CAND;EAOV;;EACA;EAAU;;EACV;;;EAAUT,mBAAmB,CAACU,CAApB,GAAwB,UAASmB,MAAT,EAAiBC,QAAjB,EAA2B;IAAE,OAAOnB,MAAM,CAACoB,SAAP,CAAiBC,cAAjB,CAAgC5B,IAAhC,CAAqCyB,MAArC,EAA6CC,QAA7C,CAAP;EAAgE,CAArH;EACV;;EACA;EAAU;;EACV;;;EAAU9B,mBAAmB,CAACiC,CAApB,GAAwB,QAAxB;EACV;;EACA;;EACA;EAAU;;EACV;;EAAU,OAAOjC,mBAAmB,CAACA,mBAAmB,CAACkC,CAApB,GAAwB,GAAzB,CAA1B;EACV;AAAU;AACV;;AACA;AAtFS,CAsFC;EAEV;EAAM;EACN;EAAO,UAAStC,MAAT,EAAiBuC,mBAAjB,EAAsCnC,mBAAtC,EAA2D;IAElE;IACA;;IAA+BA,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;MAAE,OAAOC,kBAAP;IAA4B,CAAzF;IAC/B;IAEA;IACA;IACA;;;IAEA,SAASA,kBAAT,CACEC,aADF,EAEEC,MAFF,EAGEC,eAHF,EAIEC,kBAJF,EAKEC,YALF,EAMEC,OANF,EAOEC,gBAPF;IAOoB;IAClBC;IAAW;IARb,EASE;MACA;MACA,IAAIC,OAAO,GAAG,OAAOR,aAAP,KAAyB,UAAzB,GACVA,aAAa,CAACQ,OADJ,GAEVR,aAFJ,CAFA,CAMA;;MACA,IAAIC,MAAJ,EAAY;QACVO,OAAO,CAACP,MAAR,GAAiBA,MAAjB;QACAO,OAAO,CAACN,eAAR,GAA0BA,eAA1B;QACAM,OAAO,CAACC,SAAR,GAAoB,IAApB;MACD,CAXD,CAaA;;;MACA,IAAIN,kBAAJ,EAAwB;QACtBK,OAAO,CAACE,UAAR,GAAqB,IAArB;MACD,CAhBD,CAkBA;;;MACA,IAAIL,OAAJ,EAAa;QACXG,OAAO,CAACG,QAAR,GAAmB,YAAYN,OAA/B;MACD;;MAED,IAAIO,IAAJ;;MACA,IAAIN,gBAAJ,EAAsB;QAAE;QACtBM,IAAI,GAAG,UAAUC,OAAV,EAAmB;UACxB;UACAA,OAAO,GACLA,OAAO,IAAI;UACV,KAAKC,MAAL,IAAe,KAAKA,MAAL,CAAYC,UAD5B,IAC2C;UAC1C,KAAKC,MAAL,IAAe,KAAKA,MAAL,CAAYF,MAA3B,IAAqC,KAAKE,MAAL,CAAYF,MAAZ,CAAmBC,UAH3D,CAFwB,CAK+C;UACvE;;UACA,IAAI,CAACF,OAAD,IAAY,OAAOI,mBAAP,KAA+B,WAA/C,EAA4D;YAC1DJ,OAAO,GAAGI,mBAAV;UACD,CATuB,CAUxB;;;UACA,IAAIb,YAAJ,EAAkB;YAChBA,YAAY,CAACrC,IAAb,CAAkB,IAAlB,EAAwB8C,OAAxB;UACD,CAbuB,CAcxB;;;UACA,IAAIA,OAAO,IAAIA,OAAO,CAACK,qBAAvB,EAA8C;YAC5CL,OAAO,CAACK,qBAAR,CAA8BC,GAA9B,CAAkCb,gBAAlC;UACD;QACF,CAlBD,CADoB,CAoBpB;QACA;;;QACAE,OAAO,CAACY,YAAR,GAAuBR,IAAvB;MACD,CAvBD,MAuBO,IAAIR,YAAJ,EAAkB;QACvBQ,IAAI,GAAGL,UAAU,GACb,YAAY;UAAEH,YAAY,CAACrC,IAAb,CAAkB,IAAlB,EAAwB,KAAKsD,KAAL,CAAWC,QAAX,CAAoBC,UAA5C;QAAyD,CAD1D,GAEbnB,YAFJ;MAGD;;MAED,IAAIQ,IAAJ,EAAU;QACR,IAAIJ,OAAO,CAACE,UAAZ,EAAwB;UACtB;UACA;UACAF,OAAO,CAACgB,aAAR,GAAwBZ,IAAxB,CAHsB,CAItB;;UACA,IAAIa,cAAc,GAAGjB,OAAO,CAACP,MAA7B;;UACAO,OAAO,CAACP,MAAR,GAAiB,SAASyB,wBAAT,CAAmCC,CAAnC,EAAsCd,OAAtC,EAA+C;YAC9DD,IAAI,CAAC7C,IAAL,CAAU8C,OAAV;YACA,OAAOY,cAAc,CAACE,CAAD,EAAId,OAAJ,CAArB;UACD,CAHD;QAID,CAVD,MAUO;UACL;UACA,IAAIe,QAAQ,GAAGpB,OAAO,CAACqB,YAAvB;UACArB,OAAO,CAACqB,YAAR,GAAuBD,QAAQ,GAC3B,GAAGE,MAAH,CAAUF,QAAV,EAAoBhB,IAApB,CAD2B,GAE3B,CAACA,IAAD,CAFJ;QAGD;MACF;;MAED,OAAO;QACLpD,OAAO,EAAEwC,aADJ;QAELQ,OAAO,EAAEA;MAFJ,CAAP;IAID;IAGD;;EAAO,CAtGG;;EAwGV;EAAM;EACN;EAAO,UAASjD,MAAT,EAAiBuC,mBAAjB,EAAsCnC,mBAAtC,EAA2D;IAElE;;IACAA,mBAAmB,CAACe,CAApB,CAAsBoB,mBAAtB,EAHkE,CAKlE;;;IACA,IAAIiC,oBAAoB,GAAGpE,mBAAmB,CAAC,EAAD,CAA9C;;IACA,IAAIqE,2BAA2B,GAAG,aAAarE,mBAAmB,CAAC0B,CAApB,CAAsB0C,oBAAtB,CAA/C,CAPkE,CASlE;;;IACA,IAAIE,UAAU,GAAGtE,mBAAmB,CAAC,EAAD,CAApC,CAVkE,CAYlE;;;IACA,IAAIuE,QAAQ,GAAGvE,mBAAmB,CAAC,CAAD,CAAlC;;IACA,IAAIwE,eAAe,GAAG,aAAaxE,mBAAmB,CAAC0B,CAApB,CAAsB6C,QAAtB,CAAnC,CAdkE,CAgBlE;;;IACA,IAAIE,WAAW,GAAGzE,mBAAmB,CAAC,CAAD,CAArC;;IACA,IAAI0E,kBAAkB,GAAG,aAAa1E,mBAAmB,CAAC0B,CAApB,CAAsB+C,WAAtB,CAAtC,CAlBkE,CAoBlE;;;IAOA,IAAIE,WAAW,GAAG;MAChBC,KAAK,EAAE;QACLC,eAAe,EAAE;UACfC,IAAI,EAAE,CAACC,OAAD,EAAUC,MAAV,CADS;UAEfC,OAAO,EAAE;QAFM,CADZ;QAKLC,MAAM,EAAER,kBAAkB,CAACS,CAAnB,CAAqBP,KAArB,CAA2BM,MAL9B;QAMLE,iBAAiB,EAAEV,kBAAkB,CAACS,CAAnB,CAAqBP,KAArB,CAA2BQ,iBANzC;QAOLC,aAAa,EAAEX,kBAAkB,CAACS,CAAnB,CAAqBP,KAArB,CAA2BS;MAPrC,CADS;MAUhBC,IAAI,EAAEZ,kBAAkB,CAACS,CAAnB,CAAqBG,IAVX;MAWhBC,OAAO,EAAEb,kBAAkB,CAACS,CAAnB,CAAqBI,OAXd;MAYhBC,aAAa,EAAEd,kBAAkB,CAACS,CAAnB,CAAqBK,aAZpB;MAahBC,WAAW,EAAEf,kBAAkB,CAACS,CAAnB,CAAqBM;IAblB,CAAlB;IAgBA;;IAA6B,IAAIC,+BAA+B,GAAI;MAClElF,IAAI,EAAE,WAD4D;MAGlEmF,aAAa,EAAE,WAHmD;MAKlEC,MAAM,EAAE,CAACtB,UAAU,CAAC;MAAI;MAAL,CAAX,EAAgCE,eAAe,CAACW,CAAhD,EAAmDR,WAAnD,CAL0D;MAOlEkB,UAAU,EAAE;QAAEC,oBAAoB,EAAEzB,2BAA2B,CAACc;MAApD,CAPsD;MASlEP,KAAK,EAAE;QACLmB,KAAK,EAAE;UACLjB,IAAI,EAAEE,MADD;UAELgB,QAAQ,EAAE;QAFL,CADF;QAKLC,WAAW,EAAE;UACXnB,IAAI,EAAEoB,MADK;UAEXjB,OAAO,EAAE;QAFE,CALR;QASLkB,WAAW,EAAE;UACXrB,IAAI,EAAEoB,MADK;UAEXjB,OAAO,EAAE;QAFE,CATR;QAaLmB,WAAW,EAAEpB,MAbR;QAcLqB,QAAQ,EAAEtB,OAdL;QAeLuB,kBAAkB,EAAE;UAClBxB,IAAI,EAAEC,OADY;UAElBE,OAAO,EAAEsB;QAFS;MAff,CAT2D;MA8BlEjB,IAAI,EAAE,SAASA,IAAT,GAAgB;QACpB,OAAO;UACLkB,QAAQ,EAAE,IADL;UAELC,OAAO,EAAE,IAFJ;UAGLC,KAAK,EAAE,EAHF;UAILC,QAAQ,EAAE,EAJL;UAKLC,YAAY,EAAE;QALT,CAAP;MAOD,CAtCiE;MAwClEC,KAAK,EAAE;QACLC,MAAM,EAAE,SAASA,MAAT,CAAgBC,GAAhB,EAAqB;UAC3B,IAAIC,KAAK,GAAG,IAAZ;;UAEA,IAAI,KAAKC,WAAT,EAAsB;YACpB,KAAKC,SAAL,CAAe,UAAUC,CAAV,EAAa;cAC1BH,KAAK,CAACI,YAAN;YACD,CAFD;UAGD;QACF;MATI,CAxC2D;MAmDlEC,QAAQ,EAAE;QACR;QACAC,YAAY,EAAE,SAASA,YAAT,GAAwB;UACpC,OAAO,KAAKhB,kBAAL,KAA4BC,SAA5B,GAAwC,KAAKgB,YAA7C,GAA4D,KAAKjB,kBAAxE;QACD,CAJO;QAKRkB,kBAAkB,EAAE,SAASA,kBAAT,GAA8B;UAChD,OAAO,KAAKC,QAAL,CAAcC,QAAd,GAAyB,iBAAzB,GAA6C,gBAApD;QACD,CAPO;QAQRZ,MAAM,EAAE,SAASA,MAAT,GAAkB;UACxB,OAAO,KAAKW,QAAL,CAAcE,WAAd,CAA0BC,OAA1B,CAAkC,KAAK7B,KAAvC,IAAgD,CAAC,CAAxD;QACD,CAVO;QAWR8B,MAAM,EAAE,SAASA,MAAT,GAAkB;UACxB,IAAIC,QAAQ,GAAG,KAAf;UACA,IAAInB,QAAQ,GAAG,KAAKA,QAApB;UACA,IAAID,KAAK,GAAG,KAAKA,KAAjB;UAEA/F,MAAM,CAACoH,IAAP,CAAYrB,KAAZ,EAAmBsB,OAAnB,CAA2B,UAAUjC,KAAV,EAAiB;YAC1C,IAAIW,KAAK,CAACX,KAAD,CAAL,CAAa8B,MAAjB,EAAyB;cACvBC,QAAQ,GAAG,IAAX;YACD;UACF,CAJD;UAMAnH,MAAM,CAACoH,IAAP,CAAYpB,QAAZ,EAAsBqB,OAAtB,CAA8B,UAAUjC,KAAV,EAAiB;YAC7C,IAAIY,QAAQ,CAACZ,KAAD,CAAR,CAAgB8B,MAApB,EAA4B;cAC1BC,QAAQ,GAAG,IAAX;YACD;UACF,CAJD;UAMA,OAAOA,QAAP;QACD,CA7BO;QA8BRG,eAAe,EAAE,SAASA,eAAT,GAA2B;UAC1C,OAAO,KAAKR,QAAL,CAAcQ,eAArB;QACD,CAhCO;QAiCRC,eAAe,EAAE,SAASA,eAAT,GAA2B;UAC1C,OAAO,KAAKT,QAAL,CAAcS,eAAd,IAAiC,EAAxC;QACD,CAnCO;QAoCRC,eAAe,EAAE,SAASA,eAAT,GAA2B;UAC1C,OAAO,KAAKV,QAAL,CAAcU,eAAd,IAAiC,EAAxC;QACD,CAtCO;QAuCRC,SAAS,EAAE,SAASA,SAAT,GAAqB;UAC9B,OAAO,KAAKX,QAAL,CAAcW,SAAd,IAA2B,EAAlC;QACD,CAzCO;QA0CRhH,IAAI,EAAE,SAASA,IAAT,GAAgB;UACpB,OAAO,KAAKqG,QAAL,CAAcrG,IAArB;QACD,CA5CO;QA6CR6F,WAAW,EAAE,SAASA,WAAT,GAAuB;UAClC,OAAO,KAAKQ,QAAL,CAAcR,WAArB;QACD,CA/CO;QAgDRoB,UAAU,EAAE,SAASA,UAAT,GAAsB;UAChC,IAAI,KAAKjH,IAAL,KAAc,YAAlB,EAAgC;YAC9B,OAAO;cACLkH,KAAK,EAAE,KAAKF;YADP,CAAP;UAGD;;UACD,OAAO;YACLG,iBAAiB,EAAE,KAAKV,MAAL,GAAc,KAAKJ,QAAL,CAAcU,eAAd,GAAgC,KAAKA,eAArC,GAAuD,EAArE,GAA0E,aADxF;YAELG,KAAK,EAAE,KAAKT,MAAL,GAAc,KAAKM,eAAnB,GAAqC,KAAKC;UAF5C,CAAP;QAID,CA1DO;QA2DRb,YAAY,EAAE,SAASA,YAAT,GAAwB;UACpC,IAAIA,YAAY,GAAG,IAAnB;UACA,IAAIlE,MAAM,GAAG,KAAKmF,OAAlB;;UACA,OAAOnF,MAAM,IAAIA,MAAM,KAAK,KAAKoE,QAAjC,EAA2C;YACzC,IAAI,CAAC,WAAD,EAAc,iBAAd,EAAiCG,OAAjC,CAAyCvE,MAAM,CAACM,QAAP,CAAgBgC,aAAzD,IAA0E,CAAC,CAA/E,EAAkF;cAChF4B,YAAY,GAAG,KAAf;cACA;YACD,CAHD,MAGO;cACLlE,MAAM,GAAGA,MAAM,CAACmF,OAAhB;YACD;UACF;;UACD,OAAOjB,YAAP;QACD;MAvEO,CAnDwD;MA4HlEhC,OAAO,EAAE;QACPkD,oBAAoB,EAAE,SAASA,oBAAT,CAA8BvH,KAA9B,EAAqC;UACzD,IAAIA,KAAJ,EAAW;YACT,KAAKwH,UAAL;UACD,CAFD,MAEO;YACL,KAAKC,SAAL;UACD;QACF,CAPM;QAQPC,OAAO,EAAE,SAASA,OAAT,CAAiBC,IAAjB,EAAuB;UAC9B,KAAKC,IAAL,CAAU,KAAKpC,KAAf,EAAsBmC,IAAI,CAAC9C,KAA3B,EAAkC8C,IAAlC;QACD,CAVM;QAWPE,UAAU,EAAE,SAASA,UAAT,CAAoBF,IAApB,EAA0B;UACpC,OAAO,KAAKnC,KAAL,CAAWmC,IAAI,CAAC9C,KAAhB,CAAP;QACD,CAbM;QAcPiD,UAAU,EAAE,SAASA,UAAT,CAAoBH,IAApB,EAA0B;UACpC,KAAKC,IAAL,CAAU,KAAKnC,QAAf,EAAyBkC,IAAI,CAAC9C,KAA9B,EAAqC8C,IAArC;QACD,CAhBM;QAiBPI,aAAa,EAAE,SAASA,aAAT,CAAuBJ,IAAvB,EAA6B;UAC1C,OAAO,KAAKlC,QAAL,CAAckC,IAAI,CAAC9C,KAAnB,CAAP;QACD,CAnBM;QAoBPmD,WAAW,EAAE,SAASA,WAAT,GAAuB;UAClC,IAAIzB,QAAQ,GAAG,KAAKA,QAApB;UAAA,IACIpB,QAAQ,GAAG,KAAKA,QADpB;;UAGA,IAAIoB,QAAQ,CAAC0B,WAAT,KAAyB,OAAzB,IAAoC1B,QAAQ,CAACrG,IAAT,KAAkB,YAAtD,IAAsEqG,QAAQ,CAACC,QAAT,IAAqBD,QAAQ,CAACrG,IAAT,KAAkB,UAA7G,IAA2HiF,QAA/H,EAAyI;YACvI;UACD;;UACD,KAAK+C,QAAL,CAAc,QAAd,EAAwB,eAAxB,EAAyC,IAAzC;QACD,CA5BM;QA6BPC,gBAAgB,EAAE,SAASA,gBAAT,CAA0BC,KAA1B,EAAiC;UACjD,IAAIC,MAAM,GAAG,IAAb;;UAEA,IAAItD,WAAW,GAAGuD,SAAS,CAACC,MAAV,GAAmB,CAAnB,IAAwBD,SAAS,CAAC,CAAD,CAAT,KAAiBjD,SAAzC,GAAqDiD,SAAS,CAAC,CAAD,CAA9D,GAAoE,KAAKvD,WAA3F;;UAGA,IAAI,EAAE,mBAAmByD,MAArB,KAAgCJ,KAAK,CAACxE,IAAN,KAAe,OAA/C,IAA0D,CAACwE,KAAK,CAACK,aAArE,EAAoF;YAClF;UACD;;UACD,IAAIlC,QAAQ,GAAG,KAAKA,QAApB;UAAA,IACIpB,QAAQ,GAAG,KAAKA,QADpB;;UAGA,IAAIoB,QAAQ,CAAC0B,WAAT,KAAyB,OAAzB,IAAoC1B,QAAQ,CAACrG,IAAT,KAAkB,YAAtD,IAAsE,CAACqG,QAAQ,CAACC,QAAV,IAAsBD,QAAQ,CAACrG,IAAT,KAAkB,UAA9G,IAA4HiF,QAAhI,EAA0I;YACxI;UACD;;UACD,KAAK+C,QAAL,CAAc,WAAd,EAA2B,mBAA3B;UACAQ,YAAY,CAAC,KAAKnD,OAAN,CAAZ;UACA,KAAKA,OAAL,GAAeoD,UAAU,CAAC,YAAY;YACpCN,MAAM,CAAC9B,QAAP,CAAgBqC,QAAhB,CAAyBP,MAAM,CAACxD,KAAhC,EAAuCwD,MAAM,CAACQ,SAA9C;UACD,CAFwB,EAEtB9D,WAFsB,CAAzB;;UAIA,IAAI,KAAKqB,YAAT,EAAuB;YACrB,KAAKkB,OAAL,CAAawB,GAAb,CAAiBC,aAAjB,CAA+B,IAAIC,UAAJ,CAAe,YAAf,CAA/B;UACD;QACF,CArDM;QAsDPC,gBAAgB,EAAE,SAASA,gBAAT,GAA4B;UAC5C,IAAIC,MAAM,GAAG,IAAb;;UAEA,IAAIC,YAAY,GAAGb,SAAS,CAACC,MAAV,GAAmB,CAAnB,IAAwBD,SAAS,CAAC,CAAD,CAAT,KAAiBjD,SAAzC,GAAqDiD,SAAS,CAAC,CAAD,CAA9D,GAAoE,KAAvF;UACA,IAAI/B,QAAQ,GAAG,KAAKA,QAApB;;UAEA,IAAIA,QAAQ,CAAC0B,WAAT,KAAyB,OAAzB,IAAoC1B,QAAQ,CAACrG,IAAT,KAAkB,YAAtD,IAAsE,CAACqG,QAAQ,CAACC,QAAV,IAAsBD,QAAQ,CAACrG,IAAT,KAAkB,UAAlH,EAA8H;YAC5H;UACD;;UACD,KAAKgI,QAAL,CAAc,WAAd,EAA2B,mBAA3B;UACAQ,YAAY,CAAC,KAAKnD,OAAN,CAAZ;UACA,KAAKA,OAAL,GAAeoD,UAAU,CAAC,YAAY;YACpC,CAACO,MAAM,CAACxD,YAAR,IAAwBwD,MAAM,CAAC3C,QAAP,CAAgB6C,SAAhB,CAA0BF,MAAM,CAACrE,KAAjC,CAAxB;UACD,CAFwB,EAEtB,KAAKI,WAFiB,CAAzB;;UAIA,IAAI,KAAKmB,YAAL,IAAqB+C,YAAzB,EAAuC;YACrC,IAAI,KAAK7B,OAAL,CAAa7E,QAAb,CAAsBnD,IAAtB,KAA+B,WAAnC,EAAgD;cAC9C,KAAKgI,OAAL,CAAa2B,gBAAb,CAA8B,IAA9B;YACD;UACF;QACF,CA1EM;QA2EPI,qBAAqB,EAAE,SAASA,qBAAT,GAAiC;UACtD,IAAI,KAAKnJ,IAAL,KAAc,YAAd,IAA8B,CAAC,KAAKqG,QAAL,CAAcS,eAAjD,EAAkE;UAClE,IAAIsC,KAAK,GAAG,KAAKC,KAAL,CAAW,eAAX,CAAZ;UACAD,KAAK,KAAKA,KAAK,CAACE,KAAN,CAAYxC,eAAZ,GAA8B,KAAKT,QAAL,CAAcQ,eAAjD,CAAL;QACD,CA/EM;QAgFP0C,qBAAqB,EAAE,SAASA,qBAAT,GAAiC;UACtD,IAAI,KAAKvJ,IAAL,KAAc,YAAd,IAA8B,CAAC,KAAKqG,QAAL,CAAcS,eAAjD,EAAkE;UAClE,IAAIsC,KAAK,GAAG,KAAKC,KAAL,CAAW,eAAX,CAAZ;UACAD,KAAK,KAAKA,KAAK,CAACE,KAAN,CAAYxC,eAAZ,GAA8B,KAAKT,QAAL,CAAcS,eAAd,IAAiC,EAApE,CAAL;QACD,CApFM;QAqFP0C,eAAe,EAAE,SAASA,eAAT,GAA2B;UAC1C,KAAKC,gBAAL,GAAwB,KAAKzJ,IAAL,KAAc,YAAd,IAA8B,KAAKmG,YAAnC,GAAkD,cAAlD,GAAmE,aAA3F;QACD,CAvFM;QAwFPmB,UAAU,EAAE,SAASA,UAAT,GAAsB;UAChC,KAAKoC,YAAL,GAAoB,KAAKd,GAAzB;UACA,KAAKe,SAAL,GAAiB,KAAKN,KAAL,CAAWO,IAA5B;UACA,KAAKJ,eAAL;QACD;MA5FM,CA5HyD;MA0NlEK,OAAO,EAAE,SAASA,OAAT,GAAmB;QAC1B,IAAIC,MAAM,GAAG,IAAb;;QAEA,KAAKC,GAAL,CAAS,iBAAT,EAA4B,KAAK1C,oBAAjC;QACA,KAAK0C,GAAL,CAAS,mBAAT,EAA8B,YAAY;UACxCD,MAAM,CAACtE,YAAP,GAAsB,IAAtB;UACAgD,YAAY,CAACsB,MAAM,CAACzE,OAAR,CAAZ;QACD,CAHD;QAIA,KAAK0E,GAAL,CAAS,mBAAT,EAA8B,YAAY;UACxCD,MAAM,CAACtE,YAAP,GAAsB,KAAtB;UACAgD,YAAY,CAACsB,MAAM,CAACzE,OAAR,CAAZ;QACD,CAHD;MAID,CAtOiE;MAuOlE2E,OAAO,EAAE,SAASA,OAAT,GAAmB;QAC1B,KAAKC,UAAL,CAAgBrC,UAAhB,CAA2B,IAA3B;QACA,KAAKvB,QAAL,CAAcuB,UAAd,CAAyB,IAAzB;QACA,KAAKN,UAAL;MACD,CA3OiE;MA4OlElD,aAAa,EAAE,SAASA,aAAT,GAAyB;QACtC,KAAK6F,UAAL,CAAgBpC,aAAhB,CAA8B,IAA9B;QACA,KAAKxB,QAAL,CAAcwB,aAAd,CAA4B,IAA5B;MACD,CA/OiE;MAgPlE3G,MAAM,EAAE,SAASA,MAAT,CAAgB0B,CAAhB,EAAmB;QACzB,IAAIsH,MAAM,GAAG,IAAb;;QAEA,IAAIzD,MAAM,GAAG,KAAKA,MAAlB;QAAA,IACIf,MAAM,GAAG,KAAKA,MADlB;QAAA,IAEIyE,YAAY,GAAG,KAAKA,YAFxB;QAAA,IAGIlD,UAAU,GAAG,KAAKA,UAHtB;QAAA,IAIIH,eAAe,GAAG,KAAKA,eAJ3B;QAAA,IAKIT,QAAQ,GAAG,KAAKA,QALpB;QAAA,IAMIoD,gBAAgB,GAAG,KAAKA,gBAN5B;QAAA,IAOIrD,kBAAkB,GAAG,KAAKA,kBAP9B;QAAA,IAQIpG,IAAI,GAAG,KAAKA,IARhB;QAAA,IASIiF,QAAQ,GAAG,KAAKA,QATpB;QAAA,IAUID,WAAW,GAAG,KAAKA,WAVvB;QAAA,IAWIoF,MAAM,GAAG,KAAKA,MAXlB;QAAA,IAYIjE,YAAY,GAAG,KAAKA,YAZxB;QAeA,IAAIkE,SAAS,GAAGzH,CAAC,CACf,YADe,EAEf;UACE0H,KAAK,EAAE;YAAElL,IAAI,EAAEgH;UAAR;QADT,CAFe,EAKf,CAACxD,CAAC,CACA,KADA,EAEA;UACE2H,GAAG,EAAE,MADP;UAEEC,UAAU,EAAE,CAAC;YACXpL,IAAI,EAAE,MADK;YAEXU,KAAK,EAAE4F;UAFI,CAAD,CAFd;UAOE,SAAS,CAAC,cAAc1F,IAAf,EAAqBgF,WAArB,CAPX;UAQEyF,EAAE,EAAE;YACF,cAAc,SAASC,UAAT,CAAoBC,MAApB,EAA4B;cACxC,OAAOT,MAAM,CAACjC,gBAAP,CAAwB0C,MAAxB,EAAgC,GAAhC,CAAP;YACD,CAHC;YAIF,cAAc,SAASC,UAAT,GAAsB;cAClC,OAAOV,MAAM,CAACnB,gBAAP,CAAwB,IAAxB,CAAP;YACD,CANC;YAOF,SAAS,SAAS8B,KAAT,CAAeF,MAAf,EAAuB;cAC9B,OAAOT,MAAM,CAACjC,gBAAP,CAAwB0C,MAAxB,EAAgC,GAAhC,CAAP;YACD;UATC;QARN,CAFA,EAsBA,CAAC/H,CAAC,CACA,IADA,EAEA;UACE0H,KAAK,EAAE;YACLQ,IAAI,EAAE;UADD,CADT;UAIE,SAAS,CAAC,wBAAD,EAA2B,oBAAoBrB,gBAA/C,CAJX;UAKEH,KAAK,EAAE;YAAExC,eAAe,EAAET,QAAQ,CAACS,eAAT,IAA4B;UAA/C;QALT,CAFA,EAQA,CAACsD,MAAM,CAACvG,OAAR,CARA,CAAF,CAtBA,CAAF,CALe,CAAjB;QAwCA,IAAIkH,UAAU,GAAGnI,CAAC,CAAC,wBAAD,EAA2B,CAACA,CAAC,CAC7C,IAD6C,EAE7C;UACE0H,KAAK,EAAE;YACLQ,IAAI,EAAE;UADD,CADT;UAIE,SAAS,yBAJX;UAKEN,UAAU,EAAE,CAAC;YACXpL,IAAI,EAAE,MADK;YAEXU,KAAK,EAAE4F;UAFI,CAAD,CALd;UAUE4D,KAAK,EAAE;YAAExC,eAAe,EAAET,QAAQ,CAACS,eAAT,IAA4B;UAA/C;QAVT,CAF6C,EAa7C,CAACsD,MAAM,CAACvG,OAAR,CAb6C,CAAF,CAA3B,CAAlB;QAgBA,IAAImH,gBAAgB,GAAG3E,QAAQ,CAACrG,IAAT,KAAkB,YAAlB,IAAkCmG,YAAlC,IAAkDE,QAAQ,CAACrG,IAAT,KAAkB,UAAlB,IAAgC,CAACqG,QAAQ,CAACC,QAA5F,GAAuG,oBAAvG,GAA8H,qBAArJ;QAEA,OAAO1D,CAAC,CACN,IADM,EAEN;UACE,SAAS;YACP,cAAc,IADP;YAEP,aAAa6D,MAFN;YAGP,aAAaf,MAHN;YAIP,eAAeT;UAJR,CADX;UAOEqF,KAAK,EAAE;YAAEQ,IAAI,EAAE,UAAR;YACL,iBAAiB,MADZ;YAEL,iBAAiBpF;UAFZ,CAPT;UAWE+E,EAAE,EAAE;YACF,cAAc,KAAKxC,gBADjB;YAEF,cAAc,SAAS2C,UAAT,GAAsB;cAClC,OAAOV,MAAM,CAACnB,gBAAP,CAAwB,KAAxB,CAAP;YACD,CAJC;YAKF,SAAS,KAAKd;UALZ;QAXN,CAFM,EAqBN,CAACrF,CAAC,CACA,KADA,EAEA;UACE,SAAS,mBADX;UAEE2H,GAAG,EAAE,eAFP;UAGEE,EAAE,EAAE;YACF,SAAS,KAAK3C,WADZ;YAEF,cAAc,KAAKqB,qBAFjB;YAGF,cAAc,KAAKI;UAHjB,CAHN;UASED,KAAK,EAAE,CAACa,YAAD,EAAelD,UAAf,EAA2B;YAAEH,eAAe,EAAEA;UAAnB,CAA3B;QATT,CAFA,EAaA,CAACsD,MAAM,CAAChB,KAAR,EAAexG,CAAC,CAAC,GAAD,EAAM;UAAE,SAAS,CAAC,wBAAD,EAA2BoI,gBAA3B;QAAX,CAAN,CAAhB,CAbA,CAAF,EAcG,KAAKnF,WAAL,GAAmBwE,SAAnB,GAA+BU,UAdlC,CArBM,CAAR;MAqCD;IAjWiE,CAAvC,CA3CqC,CA8YlE;;IACC;;IAA6B,IAAIE,mCAAmC,GAAI3G,+BAA3C,CA/YoC,CAgZlE;;IACA,IAAI4G,mBAAmB,GAAGtM,mBAAmB,CAAC,CAAD,CAA7C,CAjZkE,CAmZlE;;;IACA,IAAIsC,MAAJ,EAAYC,eAAZ;IAKA;;IAEA,IAAIgK,SAAS,GAAG5L,MAAM,CAAC2L,mBAAmB,CAAC;IAAI;IAAL,CAApB,CAAN,CACdD,mCADc,EAEd/J,MAFc,EAGdC,eAHc,EAId,KAJc,EAKd,IALc,EAMd,IANc,EAOd,IAPc,CAAhB;IAWA;;IACA,IAAI,KAAJ,EAAW;MAAE,IAAIiK,GAAJ;IAAU;;IACvBD,SAAS,CAAC1J,OAAV,CAAkB4J,MAAlB,GAA2B,+BAA3B;IACA;;IAA6B,IAAIC,OAAO,GAAIH,SAAS,CAAC1M,OAAzB,CAzaqC,CA0alE;;IAGA;;IACA6M,OAAO,CAACC,OAAR,GAAkB,UAAUC,GAAV,EAAe;MAC/BA,GAAG,CAACL,SAAJ,CAAcG,OAAO,CAAClM,IAAtB,EAA4BkM,OAA5B;IACD,CAFD;IAIA;;;IAA6B,IAAIG,gBAAgB,GAAG1K,mBAAmB,CAAC,SAAD,CAAnB,GAAkCuK,OAAzD;IAE7B;EAAO,CA7hBG;;EA+hBV;EAAM;EACN;EAAO,UAAS9M,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBiN,OAAO,CAAC,gDAAD,CAAxB;IAEA;EAAO,CApiBG;;EAsiBV;EAAM;EACN;EAAO,UAASlN,MAAT,EAAiBuC,mBAAjB,EAAsCnC,mBAAtC,EAA2D;IAElE;IACA;;IAA6BmC,mBAAmB,CAAC,GAAD,CAAnB,GAA4B;MACvD4K,MAAM,EAAE,CAAC,UAAD,CAD+C;MAEvD1F,QAAQ,EAAE;QACR0C,SAAS,EAAE,SAASA,SAAT,GAAqB;UAC9B,IAAIiD,IAAI,GAAG,CAAC,KAAKjH,KAAN,CAAX;UACA,IAAI1C,MAAM,GAAG,KAAKmF,OAAlB;;UACA,OAAOnF,MAAM,CAACM,QAAP,CAAgBgC,aAAhB,KAAkC,QAAzC,EAAmD;YACjD,IAAItC,MAAM,CAAC0C,KAAX,EAAkB;cAChBiH,IAAI,CAACC,OAAL,CAAa5J,MAAM,CAAC0C,KAApB;YACD;;YACD1C,MAAM,GAAGA,MAAM,CAACmF,OAAhB;UACD;;UACD,OAAOwE,IAAP;QACD,CAXO;QAYR3B,UAAU,EAAE,SAASA,UAAT,GAAsB;UAChC,IAAIhI,MAAM,GAAG,KAAKmF,OAAlB;;UACA,OAAOnF,MAAM,IAAI,CAAC,QAAD,EAAW,WAAX,EAAwBuE,OAAxB,CAAgCvE,MAAM,CAACM,QAAP,CAAgBgC,aAAhD,MAAmE,CAAC,CAArF,EAAwF;YACtFtC,MAAM,GAAGA,MAAM,CAACmF,OAAhB;UACD;;UACD,OAAOnF,MAAP;QACD,CAlBO;QAmBRkI,YAAY,EAAE,SAASA,YAAT,GAAwB;UACpC,IAAI,KAAK9D,QAAL,CAAcrG,IAAd,KAAuB,UAA3B,EAAuC,OAAO,EAAP;UAEvC,IAAI8L,OAAO,GAAG,EAAd;UACA,IAAI7J,MAAM,GAAG,KAAKmF,OAAlB;;UAEA,IAAI,KAAKf,QAAL,CAAcC,QAAlB,EAA4B;YAC1BwF,OAAO,GAAG,EAAV;UACD,CAFD,MAEO;YACL,OAAO7J,MAAM,IAAIA,MAAM,CAACM,QAAP,CAAgBgC,aAAhB,KAAkC,QAAnD,EAA6D;cAC3D,IAAItC,MAAM,CAACM,QAAP,CAAgBgC,aAAhB,KAAkC,WAAtC,EAAmD;gBACjDuH,OAAO,IAAI,EAAX;cACD;;cACD7J,MAAM,GAAGA,MAAM,CAACmF,OAAhB;YACD;UACF;;UACD,OAAO;YAAE2E,WAAW,EAAED,OAAO,GAAG;UAAzB,CAAP;QACD;MApCO;IAF6C,CAA5B;IA0C7B;EAAO,CAplBG;;EAslBV;EAAM;EACN;EAAO,UAAStN,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBiN,OAAO,CAAC,+BAAD,CAAxB;IAEA;EAAO,CA3lBG;;EA6lBV;EAAM;EACN;EAAO,UAASlN,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBiN,OAAO,CAAC,iCAAD,CAAxB;IAEA;EAAO;EAEP;;AApmBU,CAtFD,CADT"}]}