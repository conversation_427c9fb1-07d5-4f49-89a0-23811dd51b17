{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\element-ui\\lib\\menu.js", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\element-ui\\lib\\menu.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["module", "exports", "modules", "installedModules", "__webpack_require__", "moduleId", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "getDefault", "getModuleExports", "object", "property", "prototype", "hasOwnProperty", "p", "s", "__webpack_exports__", "normalizeComponent", "scriptExports", "render", "staticRenderFns", "functionalTemplate", "injectStyles", "scopeId", "moduleIdentifier", "shadowMode", "options", "_compiled", "functional", "_scopeId", "hook", "context", "$vnode", "ssrContext", "parent", "__VUE_SSR_CONTEXT__", "_registeredComponents", "add", "_ssrRegister", "$root", "$options", "shadowRoot", "_injectStyles", "originalRender", "renderWithStyleInjection", "h", "existing", "beforeCreate", "concat", "require", "emitter_", "emitter_default", "migrating_", "migrating_default", "aria", "Utils", "focusFirstDescendant", "element", "childNodes", "length", "child", "attemptFocus", "focusLastDescendant", "isFocusable", "IgnoreUtilFocusChanges", "focus", "e", "document", "activeElement", "tabIndex", "getAttribute", "disabled", "nodeName", "href", "rel", "type", "triggerEvent", "elm", "eventName", "test", "evt", "createEvent", "_len", "arguments", "opts", "Array", "_key", "initEvent", "apply", "dispatchEvent", "fireEvent", "keys", "tab", "enter", "space", "left", "up", "right", "down", "esc", "aria_utils", "SubMenu", "domNode", "subMenuItems", "subIndex", "init", "querySelectorAll", "addListeners", "gotoSubIndex", "idx", "_this", "parentNode", "for<PERSON>ach", "el", "addEventListener", "event", "prevDef", "keyCode", "currentTarget", "click", "preventDefault", "stopPropagation", "aria_submenu", "MenuItem", "submenu", "setAttribute", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "aria_menuitem", "<PERSON><PERSON>", "menu<PERSON><PERSON><PERSON>n", "filter", "nodeType", "aria_menubar", "dom_", "menuvue_type_script_lang_js_", "component", "attrs", "role", "collapse", "style", "backgroundColor", "$slots", "default", "collapseTransition", "componentName", "mixins", "a", "provide", "rootMenu", "components", "createElement", "data", "props", "on", "beforeEnter", "opacity", "afterEnter", "beforeLeave", "dataset", "oldOverflow", "overflow", "scrollWidth", "clientWidth", "width", "leave", "children", "String", "defaultActive", "defaultOpeneds", "uniqueOpened", "Boolean", "router", "menuTrigger", "textColor", "activeTextColor", "activeIndex", "openedMenus", "slice", "items", "submenus", "computed", "hoverBackground", "mixColor", "isMenuPopup", "watch", "updateActiveIndex", "broadcast", "methods", "val", "item", "index", "initOpenedMenu", "getMigratingConfig", "getColorChannels", "color", "replace", "split", "splice", "join", "red", "parseInt", "green", "blue", "percent", "_getColorChannels", "Math", "round", "addItem", "$set", "removeItem", "addSubmenu", "removeSubmenu", "openMenu", "indexPath", "indexOf", "push", "closeMenu", "handleSubmenuClick", "isOpened", "$emit", "handleItemClick", "oldActiveIndex", "hasIndex", "routeToItem", "error", "console", "_this2", "activeItem", "onError", "route", "$router", "open", "_this3", "toString", "close", "mounted", "$on", "$el", "$watch", "src_menuvue_type_script_lang_js_", "componentNormalizer", "menu_render", "api", "__file", "menu", "install", "<PERSON><PERSON>", "packages_menu"], "sources": ["D:/zy/xm/pc/qdzx/product/node_modules/element-ui/lib/menu.js"], "sourcesContent": ["module.exports =\n/******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__webpack_require__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__webpack_require__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __webpack_require__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__webpack_require__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"/dist/\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 71);\n/******/ })\n/************************************************************************/\n/******/ ({\n\n/***/ 0:\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"a\", function() { return normalizeComponent; });\n/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nfunction normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode /* vue-cli only */\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () { injectStyles.call(this, this.$root.$options.shadowRoot) }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functioal component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n\n\n/***/ }),\n\n/***/ 11:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/mixins/migrating\");\n\n/***/ }),\n\n/***/ 2:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/utils/dom\");\n\n/***/ }),\n\n/***/ 4:\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"element-ui/lib/mixins/emitter\");\n\n/***/ }),\n\n/***/ 71:\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n\n// EXTERNAL MODULE: external \"element-ui/lib/mixins/emitter\"\nvar emitter_ = __webpack_require__(4);\nvar emitter_default = /*#__PURE__*/__webpack_require__.n(emitter_);\n\n// EXTERNAL MODULE: external \"element-ui/lib/mixins/migrating\"\nvar migrating_ = __webpack_require__(11);\nvar migrating_default = /*#__PURE__*/__webpack_require__.n(migrating_);\n\n// CONCATENATED MODULE: ./src/utils/aria-utils.js\nvar aria = aria || {};\n\naria.Utils = aria.Utils || {};\n\n/**\n * @desc Set focus on descendant nodes until the first focusable element is\n *       found.\n * @param element\n *          DOM node for which to find the first focusable descendant.\n * @returns\n *  true if a focusable element is found and focus is set.\n */\naria.Utils.focusFirstDescendant = function (element) {\n  for (var i = 0; i < element.childNodes.length; i++) {\n    var child = element.childNodes[i];\n    if (aria.Utils.attemptFocus(child) || aria.Utils.focusFirstDescendant(child)) {\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * @desc Find the last descendant node that is focusable.\n * @param element\n *          DOM node for which to find the last focusable descendant.\n * @returns\n *  true if a focusable element is found and focus is set.\n */\n\naria.Utils.focusLastDescendant = function (element) {\n  for (var i = element.childNodes.length - 1; i >= 0; i--) {\n    var child = element.childNodes[i];\n    if (aria.Utils.attemptFocus(child) || aria.Utils.focusLastDescendant(child)) {\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * @desc Set Attempt to set focus on the current node.\n * @param element\n *          The node to attempt to focus on.\n * @returns\n *  true if element is focused.\n */\naria.Utils.attemptFocus = function (element) {\n  if (!aria.Utils.isFocusable(element)) {\n    return false;\n  }\n  aria.Utils.IgnoreUtilFocusChanges = true;\n  try {\n    element.focus();\n  } catch (e) {}\n  aria.Utils.IgnoreUtilFocusChanges = false;\n  return document.activeElement === element;\n};\n\naria.Utils.isFocusable = function (element) {\n  if (element.tabIndex > 0 || element.tabIndex === 0 && element.getAttribute('tabIndex') !== null) {\n    return true;\n  }\n\n  if (element.disabled) {\n    return false;\n  }\n\n  switch (element.nodeName) {\n    case 'A':\n      return !!element.href && element.rel !== 'ignore';\n    case 'INPUT':\n      return element.type !== 'hidden' && element.type !== 'file';\n    case 'BUTTON':\n    case 'SELECT':\n    case 'TEXTAREA':\n      return true;\n    default:\n      return false;\n  }\n};\n\n/**\n * 触发一个事件\n * mouseenter, mouseleave, mouseover, keyup, change, click 等\n * @param  {Element} elm\n * @param  {String} name\n * @param  {*} opts\n */\naria.Utils.triggerEvent = function (elm, name) {\n  var eventName = void 0;\n\n  if (/^mouse|click/.test(name)) {\n    eventName = 'MouseEvents';\n  } else if (/^key/.test(name)) {\n    eventName = 'KeyboardEvent';\n  } else {\n    eventName = 'HTMLEvents';\n  }\n  var evt = document.createEvent(eventName);\n\n  for (var _len = arguments.length, opts = Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    opts[_key - 2] = arguments[_key];\n  }\n\n  evt.initEvent.apply(evt, [name].concat(opts));\n  elm.dispatchEvent ? elm.dispatchEvent(evt) : elm.fireEvent('on' + name, evt);\n\n  return elm;\n};\n\naria.Utils.keys = {\n  tab: 9,\n  enter: 13,\n  space: 32,\n  left: 37,\n  up: 38,\n  right: 39,\n  down: 40,\n  esc: 27\n};\n\n/* harmony default export */ var aria_utils = (aria.Utils);\n// CONCATENATED MODULE: ./src/utils/menu/aria-submenu.js\n\n\nvar SubMenu = function SubMenu(parent, domNode) {\n  this.domNode = domNode;\n  this.parent = parent;\n  this.subMenuItems = [];\n  this.subIndex = 0;\n  this.init();\n};\n\nSubMenu.prototype.init = function () {\n  this.subMenuItems = this.domNode.querySelectorAll('li');\n  this.addListeners();\n};\n\nSubMenu.prototype.gotoSubIndex = function (idx) {\n  if (idx === this.subMenuItems.length) {\n    idx = 0;\n  } else if (idx < 0) {\n    idx = this.subMenuItems.length - 1;\n  }\n  this.subMenuItems[idx].focus();\n  this.subIndex = idx;\n};\n\nSubMenu.prototype.addListeners = function () {\n  var _this = this;\n\n  var keys = aria_utils.keys;\n  var parentNode = this.parent.domNode;\n  Array.prototype.forEach.call(this.subMenuItems, function (el) {\n    el.addEventListener('keydown', function (event) {\n      var prevDef = false;\n      switch (event.keyCode) {\n        case keys.down:\n          _this.gotoSubIndex(_this.subIndex + 1);\n          prevDef = true;\n          break;\n        case keys.up:\n          _this.gotoSubIndex(_this.subIndex - 1);\n          prevDef = true;\n          break;\n        case keys.tab:\n          aria_utils.triggerEvent(parentNode, 'mouseleave');\n          break;\n        case keys.enter:\n        case keys.space:\n          prevDef = true;\n          event.currentTarget.click();\n          break;\n      }\n      if (prevDef) {\n        event.preventDefault();\n        event.stopPropagation();\n      }\n      return false;\n    });\n  });\n};\n\n/* harmony default export */ var aria_submenu = (SubMenu);\n// CONCATENATED MODULE: ./src/utils/menu/aria-menuitem.js\n\n\n\nvar MenuItem = function MenuItem(domNode) {\n  this.domNode = domNode;\n  this.submenu = null;\n  this.init();\n};\n\nMenuItem.prototype.init = function () {\n  this.domNode.setAttribute('tabindex', '0');\n  var menuChild = this.domNode.querySelector('.el-menu');\n  if (menuChild) {\n    this.submenu = new aria_submenu(this, menuChild);\n  }\n  this.addListeners();\n};\n\nMenuItem.prototype.addListeners = function () {\n  var _this = this;\n\n  var keys = aria_utils.keys;\n  this.domNode.addEventListener('keydown', function (event) {\n    var prevDef = false;\n    switch (event.keyCode) {\n      case keys.down:\n        aria_utils.triggerEvent(event.currentTarget, 'mouseenter');\n        _this.submenu && _this.submenu.gotoSubIndex(0);\n        prevDef = true;\n        break;\n      case keys.up:\n        aria_utils.triggerEvent(event.currentTarget, 'mouseenter');\n        _this.submenu && _this.submenu.gotoSubIndex(_this.submenu.subMenuItems.length - 1);\n        prevDef = true;\n        break;\n      case keys.tab:\n        aria_utils.triggerEvent(event.currentTarget, 'mouseleave');\n        break;\n      case keys.enter:\n      case keys.space:\n        prevDef = true;\n        event.currentTarget.click();\n        break;\n    }\n    if (prevDef) {\n      event.preventDefault();\n    }\n  });\n};\n\n/* harmony default export */ var aria_menuitem = (MenuItem);\n// CONCATENATED MODULE: ./src/utils/menu/aria-menubar.js\n\n\nvar Menu = function Menu(domNode) {\n  this.domNode = domNode;\n  this.init();\n};\n\nMenu.prototype.init = function () {\n  var menuChildren = this.domNode.childNodes;\n  [].filter.call(menuChildren, function (child) {\n    return child.nodeType === 1;\n  }).forEach(function (child) {\n    new aria_menuitem(child); // eslint-disable-line\n  });\n};\n/* harmony default export */ var aria_menubar = (Menu);\n// EXTERNAL MODULE: external \"element-ui/lib/utils/dom\"\nvar dom_ = __webpack_require__(2);\n\n// CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/menu/src/menu.vue?vue&type=script&lang=js&\n\n\n\n\n\n\n/* harmony default export */ var menuvue_type_script_lang_js_ = ({\n  name: 'ElMenu',\n\n  render: function render(h) {\n    var component = h(\n      'ul',\n      {\n        attrs: {\n          role: 'menubar'\n        },\n        key: +this.collapse,\n        style: { backgroundColor: this.backgroundColor || '' },\n        'class': {\n          'el-menu--horizontal': this.mode === 'horizontal',\n          'el-menu--collapse': this.collapse,\n          \"el-menu\": true\n        }\n      },\n      [this.$slots.default]\n    );\n\n    if (this.collapseTransition) {\n      return h('el-menu-collapse-transition', [component]);\n    } else {\n      return component;\n    }\n  },\n\n\n  componentName: 'ElMenu',\n\n  mixins: [emitter_default.a, migrating_default.a],\n\n  provide: function provide() {\n    return {\n      rootMenu: this\n    };\n  },\n\n\n  components: {\n    'el-menu-collapse-transition': {\n      functional: true,\n      render: function render(createElement, context) {\n        var data = {\n          props: {\n            mode: 'out-in'\n          },\n          on: {\n            beforeEnter: function beforeEnter(el) {\n              el.style.opacity = 0.2;\n            },\n            enter: function enter(el) {\n              Object(dom_[\"addClass\"])(el, 'el-opacity-transition');\n              el.style.opacity = 1;\n            },\n            afterEnter: function afterEnter(el) {\n              Object(dom_[\"removeClass\"])(el, 'el-opacity-transition');\n              el.style.opacity = '';\n            },\n            beforeLeave: function beforeLeave(el) {\n              if (!el.dataset) el.dataset = {};\n\n              if (Object(dom_[\"hasClass\"])(el, 'el-menu--collapse')) {\n                Object(dom_[\"removeClass\"])(el, 'el-menu--collapse');\n                el.dataset.oldOverflow = el.style.overflow;\n                el.dataset.scrollWidth = el.clientWidth;\n                Object(dom_[\"addClass\"])(el, 'el-menu--collapse');\n              } else {\n                Object(dom_[\"addClass\"])(el, 'el-menu--collapse');\n                el.dataset.oldOverflow = el.style.overflow;\n                el.dataset.scrollWidth = el.clientWidth;\n                Object(dom_[\"removeClass\"])(el, 'el-menu--collapse');\n              }\n\n              el.style.width = el.scrollWidth + 'px';\n              el.style.overflow = 'hidden';\n            },\n            leave: function leave(el) {\n              Object(dom_[\"addClass\"])(el, 'horizontal-collapse-transition');\n              el.style.width = el.dataset.scrollWidth + 'px';\n            }\n          }\n        };\n        return createElement('transition', data, context.children);\n      }\n    }\n  },\n\n  props: {\n    mode: {\n      type: String,\n      default: 'vertical'\n    },\n    defaultActive: {\n      type: String,\n      default: ''\n    },\n    defaultOpeneds: Array,\n    uniqueOpened: Boolean,\n    router: Boolean,\n    menuTrigger: {\n      type: String,\n      default: 'hover'\n    },\n    collapse: Boolean,\n    backgroundColor: String,\n    textColor: String,\n    activeTextColor: String,\n    collapseTransition: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data: function data() {\n    return {\n      activeIndex: this.defaultActive,\n      openedMenus: this.defaultOpeneds && !this.collapse ? this.defaultOpeneds.slice(0) : [],\n      items: {},\n      submenus: {}\n    };\n  },\n\n  computed: {\n    hoverBackground: function hoverBackground() {\n      return this.backgroundColor ? this.mixColor(this.backgroundColor, 0.2) : '';\n    },\n    isMenuPopup: function isMenuPopup() {\n      return this.mode === 'horizontal' || this.mode === 'vertical' && this.collapse;\n    }\n  },\n  watch: {\n    defaultActive: function defaultActive(value) {\n      if (!this.items[value]) {\n        this.activeIndex = null;\n      }\n      this.updateActiveIndex(value);\n    },\n    defaultOpeneds: function defaultOpeneds(value) {\n      if (!this.collapse) {\n        this.openedMenus = value;\n      }\n    },\n    collapse: function collapse(value) {\n      if (value) this.openedMenus = [];\n      this.broadcast('ElSubmenu', 'toggle-collapse', value);\n    }\n  },\n  methods: {\n    updateActiveIndex: function updateActiveIndex(val) {\n      var item = this.items[val] || this.items[this.activeIndex] || this.items[this.defaultActive];\n      if (item) {\n        this.activeIndex = item.index;\n        this.initOpenedMenu();\n      } else {\n        this.activeIndex = null;\n      }\n    },\n    getMigratingConfig: function getMigratingConfig() {\n      return {\n        props: {\n          'theme': 'theme is removed.'\n        }\n      };\n    },\n    getColorChannels: function getColorChannels(color) {\n      color = color.replace('#', '');\n      if (/^[0-9a-fA-F]{3}$/.test(color)) {\n        color = color.split('');\n        for (var i = 2; i >= 0; i--) {\n          color.splice(i, 0, color[i]);\n        }\n        color = color.join('');\n      }\n      if (/^[0-9a-fA-F]{6}$/.test(color)) {\n        return {\n          red: parseInt(color.slice(0, 2), 16),\n          green: parseInt(color.slice(2, 4), 16),\n          blue: parseInt(color.slice(4, 6), 16)\n        };\n      } else {\n        return {\n          red: 255,\n          green: 255,\n          blue: 255\n        };\n      }\n    },\n    mixColor: function mixColor(color, percent) {\n      var _getColorChannels = this.getColorChannels(color),\n          red = _getColorChannels.red,\n          green = _getColorChannels.green,\n          blue = _getColorChannels.blue;\n\n      if (percent > 0) {\n        // shade given color\n        red *= 1 - percent;\n        green *= 1 - percent;\n        blue *= 1 - percent;\n      } else {\n        // tint given color\n        red += (255 - red) * percent;\n        green += (255 - green) * percent;\n        blue += (255 - blue) * percent;\n      }\n      return 'rgb(' + Math.round(red) + ', ' + Math.round(green) + ', ' + Math.round(blue) + ')';\n    },\n    addItem: function addItem(item) {\n      this.$set(this.items, item.index, item);\n    },\n    removeItem: function removeItem(item) {\n      delete this.items[item.index];\n    },\n    addSubmenu: function addSubmenu(item) {\n      this.$set(this.submenus, item.index, item);\n    },\n    removeSubmenu: function removeSubmenu(item) {\n      delete this.submenus[item.index];\n    },\n    openMenu: function openMenu(index, indexPath) {\n      var openedMenus = this.openedMenus;\n      if (openedMenus.indexOf(index) !== -1) return;\n      // 将不在该菜单路径下的其余菜单收起\n      // collapse all menu that are not under current menu item\n      if (this.uniqueOpened) {\n        this.openedMenus = openedMenus.filter(function (index) {\n          return indexPath.indexOf(index) !== -1;\n        });\n      }\n      this.openedMenus.push(index);\n    },\n    closeMenu: function closeMenu(index) {\n      var i = this.openedMenus.indexOf(index);\n      if (i !== -1) {\n        this.openedMenus.splice(i, 1);\n      }\n    },\n    handleSubmenuClick: function handleSubmenuClick(submenu) {\n      var index = submenu.index,\n          indexPath = submenu.indexPath;\n\n      var isOpened = this.openedMenus.indexOf(index) !== -1;\n\n      if (isOpened) {\n        this.closeMenu(index);\n        this.$emit('close', index, indexPath);\n      } else {\n        this.openMenu(index, indexPath);\n        this.$emit('open', index, indexPath);\n      }\n    },\n    handleItemClick: function handleItemClick(item) {\n      var _this = this;\n\n      var index = item.index,\n          indexPath = item.indexPath;\n\n      var oldActiveIndex = this.activeIndex;\n      var hasIndex = item.index !== null;\n\n      if (hasIndex) {\n        this.activeIndex = item.index;\n      }\n\n      this.$emit('select', index, indexPath, item);\n\n      if (this.mode === 'horizontal' || this.collapse) {\n        this.openedMenus = [];\n      }\n\n      if (this.router && hasIndex) {\n        this.routeToItem(item, function (error) {\n          _this.activeIndex = oldActiveIndex;\n          if (error) {\n            // vue-router 3.1.0+ push/replace cause NavigationDuplicated error \n            // https://github.com/ElemeFE/element/issues/17044\n            if (error.name === 'NavigationDuplicated') return;\n            console.error(error);\n          }\n        });\n      }\n    },\n\n    // 初始化展开菜单\n    // initialize opened menu\n    initOpenedMenu: function initOpenedMenu() {\n      var _this2 = this;\n\n      var index = this.activeIndex;\n      var activeItem = this.items[index];\n      if (!activeItem || this.mode === 'horizontal' || this.collapse) return;\n\n      var indexPath = activeItem.indexPath;\n\n      // 展开该菜单项的路径上所有子菜单\n      // expand all submenus of the menu item\n      indexPath.forEach(function (index) {\n        var submenu = _this2.submenus[index];\n        submenu && _this2.openMenu(index, submenu.indexPath);\n      });\n    },\n    routeToItem: function routeToItem(item, onError) {\n      var route = item.route || item.index;\n      try {\n        this.$router.push(route, function () {}, onError);\n      } catch (e) {\n        console.error(e);\n      }\n    },\n    open: function open(index) {\n      var _this3 = this;\n\n      var indexPath = this.submenus[index.toString()].indexPath;\n\n      indexPath.forEach(function (i) {\n        return _this3.openMenu(i, indexPath);\n      });\n    },\n    close: function close(index) {\n      this.closeMenu(index);\n    }\n  },\n  mounted: function mounted() {\n    this.initOpenedMenu();\n    this.$on('item-click', this.handleItemClick);\n    this.$on('submenu-click', this.handleSubmenuClick);\n    if (this.mode === 'horizontal') {\n      new aria_menubar(this.$el); // eslint-disable-line\n    }\n    this.$watch('items', this.updateActiveIndex);\n  }\n});\n// CONCATENATED MODULE: ./packages/menu/src/menu.vue?vue&type=script&lang=js&\n /* harmony default export */ var src_menuvue_type_script_lang_js_ = (menuvue_type_script_lang_js_); \n// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js\nvar componentNormalizer = __webpack_require__(0);\n\n// CONCATENATED MODULE: ./packages/menu/src/menu.vue\nvar menu_render, staticRenderFns\n\n\n\n\n/* normalize component */\n\nvar component = Object(componentNormalizer[\"a\" /* default */])(\n  src_menuvue_type_script_lang_js_,\n  menu_render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (false) { var api; }\ncomponent.options.__file = \"packages/menu/src/menu.vue\"\n/* harmony default export */ var menu = (component.exports);\n// CONCATENATED MODULE: ./packages/menu/index.js\n\n\n/* istanbul ignore next */\nmenu.install = function (Vue) {\n  Vue.component(menu.name, menu);\n};\n\n/* harmony default export */ var packages_menu = __webpack_exports__[\"default\"] = (menu);\n\n/***/ })\n\n/******/ });"], "mappings": "AAAAA,MAAM,CAACC,OAAP;AACA;AAAU,UAASC,OAAT,EAAkB;EAAE;;EAC9B;EAAU;;EACV;EAAU,IAAIC,gBAAgB,GAAG,EAAvB;EACV;;EACA;EAAU;;EACV;;EAAU,SAASC,mBAAT,CAA6BC,QAA7B,EAAuC;IACjD;;IACA;IAAW;;IACX;IAAW,IAAGF,gBAAgB,CAACE,QAAD,CAAnB,EAA+B;MAC1C;MAAY,OAAOF,gBAAgB,CAACE,QAAD,CAAhB,CAA2BJ,OAAlC;MACZ;IAAY;IACZ;IAAW;;IACX;;;IAAW,IAAID,MAAM,GAAGG,gBAAgB,CAACE,QAAD,CAAhB,GAA6B;MACrD;MAAYC,CAAC,EAAED,QADsC;;MAErD;MAAYE,CAAC,EAAE,KAFsC;;MAGrD;MAAYN,OAAO,EAAE;MACrB;;IAJqD,CAA1C;IAKX;;IACA;IAAW;;IACX;;IAAWC,OAAO,CAACG,QAAD,CAAP,CAAkBG,IAAlB,CAAuBR,MAAM,CAACC,OAA9B,EAAuCD,MAAvC,EAA+CA,MAAM,CAACC,OAAtD,EAA+DG,mBAA/D;IACX;;IACA;IAAW;;IACX;;IAAWJ,MAAM,CAACO,CAAP,GAAW,IAAX;IACX;;IACA;IAAW;;IACX;;IAAW,OAAOP,MAAM,CAACC,OAAd;IACX;EAAW;EACX;;EACA;;EACA;EAAU;;EACV;;;EAAUG,mBAAmB,CAACK,CAApB,GAAwBP,OAAxB;EACV;;EACA;EAAU;;EACV;;EAAUE,mBAAmB,CAACM,CAApB,GAAwBP,gBAAxB;EACV;;EACA;EAAU;;EACV;;EAAUC,mBAAmB,CAACO,CAApB,GAAwB,UAASV,OAAT,EAAkBW,IAAlB,EAAwBC,MAAxB,EAAgC;IAClE;IAAW,IAAG,CAACT,mBAAmB,CAACU,CAApB,CAAsBb,OAAtB,EAA+BW,IAA/B,CAAJ,EAA0C;MACrD;MAAYG,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+BW,IAA/B,EAAqC;QAAEK,UAAU,EAAE,IAAd;QAAoBC,GAAG,EAAEL;MAAzB,CAArC;MACZ;IAAY;IACZ;;EAAW,CAJD;EAKV;;EACA;EAAU;;EACV;;;EAAUT,mBAAmB,CAACe,CAApB,GAAwB,UAASlB,OAAT,EAAkB;IACpD;IAAW,IAAG,OAAOmB,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACC,WAA3C,EAAwD;MACnE;MAAYN,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+BmB,MAAM,CAACC,WAAtC,EAAmD;QAAEC,KAAK,EAAE;MAAT,CAAnD;MACZ;IAAY;IACZ;;;IAAWP,MAAM,CAACC,cAAP,CAAsBf,OAAtB,EAA+B,YAA/B,EAA6C;MAAEqB,KAAK,EAAE;IAAT,CAA7C;IACX;EAAW,CALD;EAMV;;EACA;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;EAAU;;EACV;;;EAAUlB,mBAAmB,CAACmB,CAApB,GAAwB,UAASD,KAAT,EAAgBE,IAAhB,EAAsB;IACxD;IAAW,IAAGA,IAAI,GAAG,CAAV,EAAaF,KAAK,GAAGlB,mBAAmB,CAACkB,KAAD,CAA3B;IACxB;;IAAW,IAAGE,IAAI,GAAG,CAAV,EAAa,OAAOF,KAAP;IACxB;;IAAW,IAAIE,IAAI,GAAG,CAAR,IAAc,OAAOF,KAAP,KAAiB,QAA/B,IAA2CA,KAA3C,IAAoDA,KAAK,CAACG,UAA7D,EAAyE,OAAOH,KAAP;IACpF;;IAAW,IAAII,EAAE,GAAGX,MAAM,CAACY,MAAP,CAAc,IAAd,CAAT;IACX;;IAAWvB,mBAAmB,CAACe,CAApB,CAAsBO,EAAtB;IACX;;;IAAWX,MAAM,CAACC,cAAP,CAAsBU,EAAtB,EAA0B,SAA1B,EAAqC;MAAET,UAAU,EAAE,IAAd;MAAoBK,KAAK,EAAEA;IAA3B,CAArC;IACX;;IAAW,IAAGE,IAAI,GAAG,CAAP,IAAY,OAAOF,KAAP,IAAgB,QAA/B,EAAyC,KAAI,IAAIM,GAAR,IAAeN,KAAf,EAAsBlB,mBAAmB,CAACO,CAApB,CAAsBe,EAAtB,EAA0BE,GAA1B,EAA+B,UAASA,GAAT,EAAc;MAAE,OAAON,KAAK,CAACM,GAAD,CAAZ;IAAoB,CAApC,CAAqCC,IAArC,CAA0C,IAA1C,EAAgDD,GAAhD,CAA/B;IAC1E;;IAAW,OAAOF,EAAP;IACX;EAAW,CATD;EAUV;;EACA;EAAU;;EACV;;;EAAUtB,mBAAmB,CAAC0B,CAApB,GAAwB,UAAS9B,MAAT,EAAiB;IACnD;IAAW,IAAIa,MAAM,GAAGb,MAAM,IAAIA,MAAM,CAACyB,UAAjB;IACxB;IAAY,SAASM,UAAT,GAAsB;MAAE,OAAO/B,MAAM,CAAC,SAAD,CAAb;IAA2B,CADvC;IAExB;IAAY,SAASgC,gBAAT,GAA4B;MAAE,OAAOhC,MAAP;IAAgB,CAF/C;IAGX;;IAAWI,mBAAmB,CAACO,CAApB,CAAsBE,MAAtB,EAA8B,GAA9B,EAAmCA,MAAnC;IACX;;;IAAW,OAAOA,MAAP;IACX;EAAW,CAND;EAOV;;EACA;EAAU;;EACV;;;EAAUT,mBAAmB,CAACU,CAApB,GAAwB,UAASmB,MAAT,EAAiBC,QAAjB,EAA2B;IAAE,OAAOnB,MAAM,CAACoB,SAAP,CAAiBC,cAAjB,CAAgC5B,IAAhC,CAAqCyB,MAArC,EAA6CC,QAA7C,CAAP;EAAgE,CAArH;EACV;;EACA;EAAU;;EACV;;;EAAU9B,mBAAmB,CAACiC,CAApB,GAAwB,QAAxB;EACV;;EACA;;EACA;EAAU;;EACV;;EAAU,OAAOjC,mBAAmB,CAACA,mBAAmB,CAACkC,CAApB,GAAwB,EAAzB,CAA1B;EACV;AAAU;AACV;;AACA;AAtFS,CAsFC;EAEV;EAAM;EACN;EAAO,UAAStC,MAAT,EAAiBuC,mBAAjB,EAAsCnC,mBAAtC,EAA2D;IAElE;IACA;;IAA+BA,mBAAmB,CAACO,CAApB,CAAsB4B,mBAAtB,EAA2C,GAA3C,EAAgD,YAAW;MAAE,OAAOC,kBAAP;IAA4B,CAAzF;IAC/B;IAEA;IACA;IACA;;;IAEA,SAASA,kBAAT,CACEC,aADF,EAEEC,MAFF,EAGEC,eAHF,EAIEC,kBAJF,EAKEC,YALF,EAMEC,OANF,EAOEC,gBAPF;IAOoB;IAClBC;IAAW;IARb,EASE;MACA;MACA,IAAIC,OAAO,GAAG,OAAOR,aAAP,KAAyB,UAAzB,GACVA,aAAa,CAACQ,OADJ,GAEVR,aAFJ,CAFA,CAMA;;MACA,IAAIC,MAAJ,EAAY;QACVO,OAAO,CAACP,MAAR,GAAiBA,MAAjB;QACAO,OAAO,CAACN,eAAR,GAA0BA,eAA1B;QACAM,OAAO,CAACC,SAAR,GAAoB,IAApB;MACD,CAXD,CAaA;;;MACA,IAAIN,kBAAJ,EAAwB;QACtBK,OAAO,CAACE,UAAR,GAAqB,IAArB;MACD,CAhBD,CAkBA;;;MACA,IAAIL,OAAJ,EAAa;QACXG,OAAO,CAACG,QAAR,GAAmB,YAAYN,OAA/B;MACD;;MAED,IAAIO,IAAJ;;MACA,IAAIN,gBAAJ,EAAsB;QAAE;QACtBM,IAAI,GAAG,UAAUC,OAAV,EAAmB;UACxB;UACAA,OAAO,GACLA,OAAO,IAAI;UACV,KAAKC,MAAL,IAAe,KAAKA,MAAL,CAAYC,UAD5B,IAC2C;UAC1C,KAAKC,MAAL,IAAe,KAAKA,MAAL,CAAYF,MAA3B,IAAqC,KAAKE,MAAL,CAAYF,MAAZ,CAAmBC,UAH3D,CAFwB,CAK+C;UACvE;;UACA,IAAI,CAACF,OAAD,IAAY,OAAOI,mBAAP,KAA+B,WAA/C,EAA4D;YAC1DJ,OAAO,GAAGI,mBAAV;UACD,CATuB,CAUxB;;;UACA,IAAIb,YAAJ,EAAkB;YAChBA,YAAY,CAACrC,IAAb,CAAkB,IAAlB,EAAwB8C,OAAxB;UACD,CAbuB,CAcxB;;;UACA,IAAIA,OAAO,IAAIA,OAAO,CAACK,qBAAvB,EAA8C;YAC5CL,OAAO,CAACK,qBAAR,CAA8BC,GAA9B,CAAkCb,gBAAlC;UACD;QACF,CAlBD,CADoB,CAoBpB;QACA;;;QACAE,OAAO,CAACY,YAAR,GAAuBR,IAAvB;MACD,CAvBD,MAuBO,IAAIR,YAAJ,EAAkB;QACvBQ,IAAI,GAAGL,UAAU,GACb,YAAY;UAAEH,YAAY,CAACrC,IAAb,CAAkB,IAAlB,EAAwB,KAAKsD,KAAL,CAAWC,QAAX,CAAoBC,UAA5C;QAAyD,CAD1D,GAEbnB,YAFJ;MAGD;;MAED,IAAIQ,IAAJ,EAAU;QACR,IAAIJ,OAAO,CAACE,UAAZ,EAAwB;UACtB;UACA;UACAF,OAAO,CAACgB,aAAR,GAAwBZ,IAAxB,CAHsB,CAItB;;UACA,IAAIa,cAAc,GAAGjB,OAAO,CAACP,MAA7B;;UACAO,OAAO,CAACP,MAAR,GAAiB,SAASyB,wBAAT,CAAmCC,CAAnC,EAAsCd,OAAtC,EAA+C;YAC9DD,IAAI,CAAC7C,IAAL,CAAU8C,OAAV;YACA,OAAOY,cAAc,CAACE,CAAD,EAAId,OAAJ,CAArB;UACD,CAHD;QAID,CAVD,MAUO;UACL;UACA,IAAIe,QAAQ,GAAGpB,OAAO,CAACqB,YAAvB;UACArB,OAAO,CAACqB,YAAR,GAAuBD,QAAQ,GAC3B,GAAGE,MAAH,CAAUF,QAAV,EAAoBhB,IAApB,CAD2B,GAE3B,CAACA,IAAD,CAFJ;QAGD;MACF;;MAED,OAAO;QACLpD,OAAO,EAAEwC,aADJ;QAELQ,OAAO,EAAEA;MAFJ,CAAP;IAID;IAGD;;EAAO,CAtGG;;EAwGV;EAAM;EACN;EAAO,UAASjD,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,iCAAD,CAAxB;IAEA;EAAO,CA7GG;;EA+GV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,0BAAD,CAAxB;IAEA;EAAO,CApHG;;EAsHV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBC,OAAjB,EAA0B;IAEjCD,MAAM,CAACC,OAAP,GAAiBuE,OAAO,CAAC,+BAAD,CAAxB;IAEA;EAAO,CA3HG;;EA6HV;EAAM;EACN;EAAO,UAASxE,MAAT,EAAiBuC,mBAAjB,EAAsCnC,mBAAtC,EAA2D;IAElE;;IACAA,mBAAmB,CAACe,CAApB,CAAsBoB,mBAAtB,EAHkE,CAKlE;;;IACA,IAAIkC,QAAQ,GAAGrE,mBAAmB,CAAC,CAAD,CAAlC;;IACA,IAAIsE,eAAe,GAAG,aAAatE,mBAAmB,CAAC0B,CAApB,CAAsB2C,QAAtB,CAAnC,CAPkE,CASlE;;;IACA,IAAIE,UAAU,GAAGvE,mBAAmB,CAAC,EAAD,CAApC;;IACA,IAAIwE,iBAAiB,GAAG,aAAaxE,mBAAmB,CAAC0B,CAApB,CAAsB6C,UAAtB,CAArC,CAXkE,CAalE;;;IACA,IAAIE,IAAI,GAAGA,IAAI,IAAI,EAAnB;IAEAA,IAAI,CAACC,KAAL,GAAaD,IAAI,CAACC,KAAL,IAAc,EAA3B;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IACAD,IAAI,CAACC,KAAL,CAAWC,oBAAX,GAAkC,UAAUC,OAAV,EAAmB;MACnD,KAAK,IAAI1E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0E,OAAO,CAACC,UAAR,CAAmBC,MAAvC,EAA+C5E,CAAC,EAAhD,EAAoD;QAClD,IAAI6E,KAAK,GAAGH,OAAO,CAACC,UAAR,CAAmB3E,CAAnB,CAAZ;;QACA,IAAIuE,IAAI,CAACC,KAAL,CAAWM,YAAX,CAAwBD,KAAxB,KAAkCN,IAAI,CAACC,KAAL,CAAWC,oBAAX,CAAgCI,KAAhC,CAAtC,EAA8E;UAC5E,OAAO,IAAP;QACD;MACF;;MACD,OAAO,KAAP;IACD,CARD;IAUA;AACA;AACA;AACA;AACA;AACA;AACA;;;IAEAN,IAAI,CAACC,KAAL,CAAWO,mBAAX,GAAiC,UAAUL,OAAV,EAAmB;MAClD,KAAK,IAAI1E,CAAC,GAAG0E,OAAO,CAACC,UAAR,CAAmBC,MAAnB,GAA4B,CAAzC,EAA4C5E,CAAC,IAAI,CAAjD,EAAoDA,CAAC,EAArD,EAAyD;QACvD,IAAI6E,KAAK,GAAGH,OAAO,CAACC,UAAR,CAAmB3E,CAAnB,CAAZ;;QACA,IAAIuE,IAAI,CAACC,KAAL,CAAWM,YAAX,CAAwBD,KAAxB,KAAkCN,IAAI,CAACC,KAAL,CAAWO,mBAAX,CAA+BF,KAA/B,CAAtC,EAA6E;UAC3E,OAAO,IAAP;QACD;MACF;;MACD,OAAO,KAAP;IACD,CARD;IAUA;AACA;AACA;AACA;AACA;AACA;AACA;;;IACAN,IAAI,CAACC,KAAL,CAAWM,YAAX,GAA0B,UAAUJ,OAAV,EAAmB;MAC3C,IAAI,CAACH,IAAI,CAACC,KAAL,CAAWQ,WAAX,CAAuBN,OAAvB,CAAL,EAAsC;QACpC,OAAO,KAAP;MACD;;MACDH,IAAI,CAACC,KAAL,CAAWS,sBAAX,GAAoC,IAApC;;MACA,IAAI;QACFP,OAAO,CAACQ,KAAR;MACD,CAFD,CAEE,OAAOC,CAAP,EAAU,CAAE;;MACdZ,IAAI,CAACC,KAAL,CAAWS,sBAAX,GAAoC,KAApC;MACA,OAAOG,QAAQ,CAACC,aAAT,KAA2BX,OAAlC;IACD,CAVD;;IAYAH,IAAI,CAACC,KAAL,CAAWQ,WAAX,GAAyB,UAAUN,OAAV,EAAmB;MAC1C,IAAIA,OAAO,CAACY,QAAR,GAAmB,CAAnB,IAAwBZ,OAAO,CAACY,QAAR,KAAqB,CAArB,IAA0BZ,OAAO,CAACa,YAAR,CAAqB,UAArB,MAAqC,IAA3F,EAAiG;QAC/F,OAAO,IAAP;MACD;;MAED,IAAIb,OAAO,CAACc,QAAZ,EAAsB;QACpB,OAAO,KAAP;MACD;;MAED,QAAQd,OAAO,CAACe,QAAhB;QACE,KAAK,GAAL;UACE,OAAO,CAAC,CAACf,OAAO,CAACgB,IAAV,IAAkBhB,OAAO,CAACiB,GAAR,KAAgB,QAAzC;;QACF,KAAK,OAAL;UACE,OAAOjB,OAAO,CAACkB,IAAR,KAAiB,QAAjB,IAA6BlB,OAAO,CAACkB,IAAR,KAAiB,MAArD;;QACF,KAAK,QAAL;QACA,KAAK,QAAL;QACA,KAAK,UAAL;UACE,OAAO,IAAP;;QACF;UACE,OAAO,KAAP;MAVJ;IAYD,CArBD;IAuBA;AACA;AACA;AACA;AACA;AACA;AACA;;;IACArB,IAAI,CAACC,KAAL,CAAWqB,YAAX,GAA0B,UAAUC,GAAV,EAAexF,IAAf,EAAqB;MAC7C,IAAIyF,SAAS,GAAG,KAAK,CAArB;;MAEA,IAAI,eAAeC,IAAf,CAAoB1F,IAApB,CAAJ,EAA+B;QAC7ByF,SAAS,GAAG,aAAZ;MACD,CAFD,MAEO,IAAI,OAAOC,IAAP,CAAY1F,IAAZ,CAAJ,EAAuB;QAC5ByF,SAAS,GAAG,eAAZ;MACD,CAFM,MAEA;QACLA,SAAS,GAAG,YAAZ;MACD;;MACD,IAAIE,GAAG,GAAGb,QAAQ,CAACc,WAAT,CAAqBH,SAArB,CAAV;;MAEA,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACxB,MAArB,EAA6ByB,IAAI,GAAGC,KAAK,CAACH,IAAI,GAAG,CAAP,GAAWA,IAAI,GAAG,CAAlB,GAAsB,CAAvB,CAAzC,EAAoEI,IAAI,GAAG,CAAhF,EAAmFA,IAAI,GAAGJ,IAA1F,EAAgGI,IAAI,EAApG,EAAwG;QACtGF,IAAI,CAACE,IAAI,GAAG,CAAR,CAAJ,GAAiBH,SAAS,CAACG,IAAD,CAA1B;MACD;;MAEDN,GAAG,CAACO,SAAJ,CAAcC,KAAd,CAAoBR,GAApB,EAAyB,CAAC3F,IAAD,EAAO2D,MAAP,CAAcoC,IAAd,CAAzB;MACAP,GAAG,CAACY,aAAJ,GAAoBZ,GAAG,CAACY,aAAJ,CAAkBT,GAAlB,CAApB,GAA6CH,GAAG,CAACa,SAAJ,CAAc,OAAOrG,IAArB,EAA2B2F,GAA3B,CAA7C;MAEA,OAAOH,GAAP;IACD,CApBD;;IAsBAvB,IAAI,CAACC,KAAL,CAAWoC,IAAX,GAAkB;MAChBC,GAAG,EAAE,CADW;MAEhBC,KAAK,EAAE,EAFS;MAGhBC,KAAK,EAAE,EAHS;MAIhBC,IAAI,EAAE,EAJU;MAKhBC,EAAE,EAAE,EALY;MAMhBC,KAAK,EAAE,EANS;MAOhBC,IAAI,EAAE,EAPU;MAQhBC,GAAG,EAAE;IARW,CAAlB;IAWA;;IAA6B,IAAIC,UAAU,GAAI9C,IAAI,CAACC,KAAvB,CAxIqC,CAyIlE;;IAGA,IAAI8C,OAAO,GAAG,SAASA,OAAT,CAAiBnE,MAAjB,EAAyBoE,OAAzB,EAAkC;MAC9C,KAAKA,OAAL,GAAeA,OAAf;MACA,KAAKpE,MAAL,GAAcA,MAAd;MACA,KAAKqE,YAAL,GAAoB,EAApB;MACA,KAAKC,QAAL,GAAgB,CAAhB;MACA,KAAKC,IAAL;IACD,CAND;;IAQAJ,OAAO,CAACzF,SAAR,CAAkB6F,IAAlB,GAAyB,YAAY;MACnC,KAAKF,YAAL,GAAoB,KAAKD,OAAL,CAAaI,gBAAb,CAA8B,IAA9B,CAApB;MACA,KAAKC,YAAL;IACD,CAHD;;IAKAN,OAAO,CAACzF,SAAR,CAAkBgG,YAAlB,GAAiC,UAAUC,GAAV,EAAe;MAC9C,IAAIA,GAAG,KAAK,KAAKN,YAAL,CAAkB5C,MAA9B,EAAsC;QACpCkD,GAAG,GAAG,CAAN;MACD,CAFD,MAEO,IAAIA,GAAG,GAAG,CAAV,EAAa;QAClBA,GAAG,GAAG,KAAKN,YAAL,CAAkB5C,MAAlB,GAA2B,CAAjC;MACD;;MACD,KAAK4C,YAAL,CAAkBM,GAAlB,EAAuB5C,KAAvB;MACA,KAAKuC,QAAL,GAAgBK,GAAhB;IACD,CARD;;IAUAR,OAAO,CAACzF,SAAR,CAAkB+F,YAAlB,GAAiC,YAAY;MAC3C,IAAIG,KAAK,GAAG,IAAZ;;MAEA,IAAInB,IAAI,GAAGS,UAAU,CAACT,IAAtB;MACA,IAAIoB,UAAU,GAAG,KAAK7E,MAAL,CAAYoE,OAA7B;MACAjB,KAAK,CAACzE,SAAN,CAAgBoG,OAAhB,CAAwB/H,IAAxB,CAA6B,KAAKsH,YAAlC,EAAgD,UAAUU,EAAV,EAAc;QAC5DA,EAAE,CAACC,gBAAH,CAAoB,SAApB,EAA+B,UAAUC,KAAV,EAAiB;UAC9C,IAAIC,OAAO,GAAG,KAAd;;UACA,QAAQD,KAAK,CAACE,OAAd;YACE,KAAK1B,IAAI,CAACO,IAAV;cACEY,KAAK,CAACF,YAAN,CAAmBE,KAAK,CAACN,QAAN,GAAiB,CAApC;;cACAY,OAAO,GAAG,IAAV;cACA;;YACF,KAAKzB,IAAI,CAACK,EAAV;cACEc,KAAK,CAACF,YAAN,CAAmBE,KAAK,CAACN,QAAN,GAAiB,CAApC;;cACAY,OAAO,GAAG,IAAV;cACA;;YACF,KAAKzB,IAAI,CAACC,GAAV;cACEQ,UAAU,CAACxB,YAAX,CAAwBmC,UAAxB,EAAoC,YAApC;cACA;;YACF,KAAKpB,IAAI,CAACE,KAAV;YACA,KAAKF,IAAI,CAACG,KAAV;cACEsB,OAAO,GAAG,IAAV;cACAD,KAAK,CAACG,aAAN,CAAoBC,KAApB;cACA;UAhBJ;;UAkBA,IAAIH,OAAJ,EAAa;YACXD,KAAK,CAACK,cAAN;YACAL,KAAK,CAACM,eAAN;UACD;;UACD,OAAO,KAAP;QACD,CAzBD;MA0BD,CA3BD;IA4BD,CAjCD;IAmCA;;;IAA6B,IAAIC,YAAY,GAAIrB,OAApB,CAtMqC,CAuMlE;;IAIA,IAAIsB,QAAQ,GAAG,SAASA,QAAT,CAAkBrB,OAAlB,EAA2B;MACxC,KAAKA,OAAL,GAAeA,OAAf;MACA,KAAKsB,OAAL,GAAe,IAAf;MACA,KAAKnB,IAAL;IACD,CAJD;;IAMAkB,QAAQ,CAAC/G,SAAT,CAAmB6F,IAAnB,GAA0B,YAAY;MACpC,KAAKH,OAAL,CAAauB,YAAb,CAA0B,UAA1B,EAAsC,GAAtC;MACA,IAAIC,SAAS,GAAG,KAAKxB,OAAL,CAAayB,aAAb,CAA2B,UAA3B,CAAhB;;MACA,IAAID,SAAJ,EAAe;QACb,KAAKF,OAAL,GAAe,IAAIF,YAAJ,CAAiB,IAAjB,EAAuBI,SAAvB,CAAf;MACD;;MACD,KAAKnB,YAAL;IACD,CAPD;;IASAgB,QAAQ,CAAC/G,SAAT,CAAmB+F,YAAnB,GAAkC,YAAY;MAC5C,IAAIG,KAAK,GAAG,IAAZ;;MAEA,IAAInB,IAAI,GAAGS,UAAU,CAACT,IAAtB;MACA,KAAKW,OAAL,CAAaY,gBAAb,CAA8B,SAA9B,EAAyC,UAAUC,KAAV,EAAiB;QACxD,IAAIC,OAAO,GAAG,KAAd;;QACA,QAAQD,KAAK,CAACE,OAAd;UACE,KAAK1B,IAAI,CAACO,IAAV;YACEE,UAAU,CAACxB,YAAX,CAAwBuC,KAAK,CAACG,aAA9B,EAA6C,YAA7C;YACAR,KAAK,CAACc,OAAN,IAAiBd,KAAK,CAACc,OAAN,CAAchB,YAAd,CAA2B,CAA3B,CAAjB;YACAQ,OAAO,GAAG,IAAV;YACA;;UACF,KAAKzB,IAAI,CAACK,EAAV;YACEI,UAAU,CAACxB,YAAX,CAAwBuC,KAAK,CAACG,aAA9B,EAA6C,YAA7C;YACAR,KAAK,CAACc,OAAN,IAAiBd,KAAK,CAACc,OAAN,CAAchB,YAAd,CAA2BE,KAAK,CAACc,OAAN,CAAcrB,YAAd,CAA2B5C,MAA3B,GAAoC,CAA/D,CAAjB;YACAyD,OAAO,GAAG,IAAV;YACA;;UACF,KAAKzB,IAAI,CAACC,GAAV;YACEQ,UAAU,CAACxB,YAAX,CAAwBuC,KAAK,CAACG,aAA9B,EAA6C,YAA7C;YACA;;UACF,KAAK3B,IAAI,CAACE,KAAV;UACA,KAAKF,IAAI,CAACG,KAAV;YACEsB,OAAO,GAAG,IAAV;YACAD,KAAK,CAACG,aAAN,CAAoBC,KAApB;YACA;QAlBJ;;QAoBA,IAAIH,OAAJ,EAAa;UACXD,KAAK,CAACK,cAAN;QACD;MACF,CAzBD;IA0BD,CA9BD;IAgCA;;;IAA6B,IAAIQ,aAAa,GAAIL,QAArB,CA1PqC,CA2PlE;;IAGA,IAAIM,IAAI,GAAG,SAASA,IAAT,CAAc3B,OAAd,EAAuB;MAChC,KAAKA,OAAL,GAAeA,OAAf;MACA,KAAKG,IAAL;IACD,CAHD;;IAKAwB,IAAI,CAACrH,SAAL,CAAe6F,IAAf,GAAsB,YAAY;MAChC,IAAIyB,YAAY,GAAG,KAAK5B,OAAL,CAAa5C,UAAhC;MACA,GAAGyE,MAAH,CAAUlJ,IAAV,CAAeiJ,YAAf,EAA6B,UAAUtE,KAAV,EAAiB;QAC5C,OAAOA,KAAK,CAACwE,QAAN,KAAmB,CAA1B;MACD,CAFD,EAEGpB,OAFH,CAEW,UAAUpD,KAAV,EAAiB;QAC1B,IAAIoE,aAAJ,CAAkBpE,KAAlB,EAD0B,CACA;MAC3B,CAJD;IAKD,CAPD;IAQA;;;IAA6B,IAAIyE,YAAY,GAAIJ,IAApB,CA3QqC,CA4QlE;;IACA,IAAIK,IAAI,GAAGzJ,mBAAmB,CAAC,CAAD,CAA9B,CA7QkE,CA+QlE;;IAOA;;;IAA6B,IAAI0J,4BAA4B,GAAI;MAC/DlJ,IAAI,EAAE,QADyD;MAG/D8B,MAAM,EAAE,SAASA,MAAT,CAAgB0B,CAAhB,EAAmB;QACzB,IAAI2F,SAAS,GAAG3F,CAAC,CACf,IADe,EAEf;UACE4F,KAAK,EAAE;YACLC,IAAI,EAAE;UADD,CADT;UAIErI,GAAG,EAAE,CAAC,KAAKsI,QAJb;UAKEC,KAAK,EAAE;YAAEC,eAAe,EAAE,KAAKA,eAAL,IAAwB;UAA3C,CALT;UAME,SAAS;YACP,uBAAuB,KAAK5I,IAAL,KAAc,YAD9B;YAEP,qBAAqB,KAAK0I,QAFnB;YAGP,WAAW;UAHJ;QANX,CAFe,EAcf,CAAC,KAAKG,MAAL,CAAYC,OAAb,CAde,CAAjB;;QAiBA,IAAI,KAAKC,kBAAT,EAA6B;UAC3B,OAAOnG,CAAC,CAAC,6BAAD,EAAgC,CAAC2F,SAAD,CAAhC,CAAR;QACD,CAFD,MAEO;UACL,OAAOA,SAAP;QACD;MACF,CA1B8D;MA6B/DS,aAAa,EAAE,QA7BgD;MA+B/DC,MAAM,EAAE,CAAC/F,eAAe,CAACgG,CAAjB,EAAoB9F,iBAAiB,CAAC8F,CAAtC,CA/BuD;MAiC/DC,OAAO,EAAE,SAASA,OAAT,GAAmB;QAC1B,OAAO;UACLC,QAAQ,EAAE;QADL,CAAP;MAGD,CArC8D;MAwC/DC,UAAU,EAAE;QACV,+BAA+B;UAC7B1H,UAAU,EAAE,IADiB;UAE7BT,MAAM,EAAE,SAASA,MAAT,CAAgBoI,aAAhB,EAA+BxH,OAA/B,EAAwC;YAC9C,IAAIyH,IAAI,GAAG;cACTC,KAAK,EAAE;gBACLxJ,IAAI,EAAE;cADD,CADE;cAITyJ,EAAE,EAAE;gBACFC,WAAW,EAAE,SAASA,WAAT,CAAqB1C,EAArB,EAAyB;kBACpCA,EAAE,CAAC2B,KAAH,CAASgB,OAAT,GAAmB,GAAnB;gBACD,CAHC;gBAIF/D,KAAK,EAAE,SAASA,KAAT,CAAeoB,EAAf,EAAmB;kBACxBzH,MAAM,CAAC8I,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBrB,EAAzB,EAA6B,uBAA7B;kBACAA,EAAE,CAAC2B,KAAH,CAASgB,OAAT,GAAmB,CAAnB;gBACD,CAPC;gBAQFC,UAAU,EAAE,SAASA,UAAT,CAAoB5C,EAApB,EAAwB;kBAClCzH,MAAM,CAAC8I,IAAI,CAAC,aAAD,CAAL,CAAN,CAA4BrB,EAA5B,EAAgC,uBAAhC;kBACAA,EAAE,CAAC2B,KAAH,CAASgB,OAAT,GAAmB,EAAnB;gBACD,CAXC;gBAYFE,WAAW,EAAE,SAASA,WAAT,CAAqB7C,EAArB,EAAyB;kBACpC,IAAI,CAACA,EAAE,CAAC8C,OAAR,EAAiB9C,EAAE,CAAC8C,OAAH,GAAa,EAAb;;kBAEjB,IAAIvK,MAAM,CAAC8I,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBrB,EAAzB,EAA6B,mBAA7B,CAAJ,EAAuD;oBACrDzH,MAAM,CAAC8I,IAAI,CAAC,aAAD,CAAL,CAAN,CAA4BrB,EAA5B,EAAgC,mBAAhC;oBACAA,EAAE,CAAC8C,OAAH,CAAWC,WAAX,GAAyB/C,EAAE,CAAC2B,KAAH,CAASqB,QAAlC;oBACAhD,EAAE,CAAC8C,OAAH,CAAWG,WAAX,GAAyBjD,EAAE,CAACkD,WAA5B;oBACA3K,MAAM,CAAC8I,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBrB,EAAzB,EAA6B,mBAA7B;kBACD,CALD,MAKO;oBACLzH,MAAM,CAAC8I,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBrB,EAAzB,EAA6B,mBAA7B;oBACAA,EAAE,CAAC8C,OAAH,CAAWC,WAAX,GAAyB/C,EAAE,CAAC2B,KAAH,CAASqB,QAAlC;oBACAhD,EAAE,CAAC8C,OAAH,CAAWG,WAAX,GAAyBjD,EAAE,CAACkD,WAA5B;oBACA3K,MAAM,CAAC8I,IAAI,CAAC,aAAD,CAAL,CAAN,CAA4BrB,EAA5B,EAAgC,mBAAhC;kBACD;;kBAEDA,EAAE,CAAC2B,KAAH,CAASwB,KAAT,GAAiBnD,EAAE,CAACiD,WAAH,GAAiB,IAAlC;kBACAjD,EAAE,CAAC2B,KAAH,CAASqB,QAAT,GAAoB,QAApB;gBACD,CA7BC;gBA8BFI,KAAK,EAAE,SAASA,KAAT,CAAepD,EAAf,EAAmB;kBACxBzH,MAAM,CAAC8I,IAAI,CAAC,UAAD,CAAL,CAAN,CAAyBrB,EAAzB,EAA6B,gCAA7B;kBACAA,EAAE,CAAC2B,KAAH,CAASwB,KAAT,GAAiBnD,EAAE,CAAC8C,OAAH,CAAWG,WAAX,GAAyB,IAA1C;gBACD;cAjCC;YAJK,CAAX;YAwCA,OAAOX,aAAa,CAAC,YAAD,EAAeC,IAAf,EAAqBzH,OAAO,CAACuI,QAA7B,CAApB;UACD;QA5C4B;MADrB,CAxCmD;MAyF/Db,KAAK,EAAE;QACLxJ,IAAI,EAAE;UACJ0E,IAAI,EAAE4F,MADF;UAEJxB,OAAO,EAAE;QAFL,CADD;QAKLyB,aAAa,EAAE;UACb7F,IAAI,EAAE4F,MADO;UAEbxB,OAAO,EAAE;QAFI,CALV;QASL0B,cAAc,EAAEpF,KATX;QAULqF,YAAY,EAAEC,OAVT;QAWLC,MAAM,EAAED,OAXH;QAYLE,WAAW,EAAE;UACXlG,IAAI,EAAE4F,MADK;UAEXxB,OAAO,EAAE;QAFE,CAZR;QAgBLJ,QAAQ,EAAEgC,OAhBL;QAiBL9B,eAAe,EAAE0B,MAjBZ;QAkBLO,SAAS,EAAEP,MAlBN;QAmBLQ,eAAe,EAAER,MAnBZ;QAoBLvB,kBAAkB,EAAE;UAClBrE,IAAI,EAAEgG,OADY;UAElB5B,OAAO,EAAE;QAFS;MApBf,CAzFwD;MAkH/DS,IAAI,EAAE,SAASA,IAAT,GAAgB;QACpB,OAAO;UACLwB,WAAW,EAAE,KAAKR,aADb;UAELS,WAAW,EAAE,KAAKR,cAAL,IAAuB,CAAC,KAAK9B,QAA7B,GAAwC,KAAK8B,cAAL,CAAoBS,KAApB,CAA0B,CAA1B,CAAxC,GAAuE,EAF/E;UAGLC,KAAK,EAAE,EAHF;UAILC,QAAQ,EAAE;QAJL,CAAP;MAMD,CAzH8D;MA2H/DC,QAAQ,EAAE;QACRC,eAAe,EAAE,SAASA,eAAT,GAA2B;UAC1C,OAAO,KAAKzC,eAAL,GAAuB,KAAK0C,QAAL,CAAc,KAAK1C,eAAnB,EAAoC,GAApC,CAAvB,GAAkE,EAAzE;QACD,CAHO;QAIR2C,WAAW,EAAE,SAASA,WAAT,GAAuB;UAClC,OAAO,KAAKvL,IAAL,KAAc,YAAd,IAA8B,KAAKA,IAAL,KAAc,UAAd,IAA4B,KAAK0I,QAAtE;QACD;MANO,CA3HqD;MAmI/D8C,KAAK,EAAE;QACLjB,aAAa,EAAE,SAASA,aAAT,CAAuBzK,KAAvB,EAA8B;UAC3C,IAAI,CAAC,KAAKoL,KAAL,CAAWpL,KAAX,CAAL,EAAwB;YACtB,KAAKiL,WAAL,GAAmB,IAAnB;UACD;;UACD,KAAKU,iBAAL,CAAuB3L,KAAvB;QACD,CANI;QAOL0K,cAAc,EAAE,SAASA,cAAT,CAAwB1K,KAAxB,EAA+B;UAC7C,IAAI,CAAC,KAAK4I,QAAV,EAAoB;YAClB,KAAKsC,WAAL,GAAmBlL,KAAnB;UACD;QACF,CAXI;QAYL4I,QAAQ,EAAE,SAASA,QAAT,CAAkB5I,KAAlB,EAAyB;UACjC,IAAIA,KAAJ,EAAW,KAAKkL,WAAL,GAAmB,EAAnB;UACX,KAAKU,SAAL,CAAe,WAAf,EAA4B,iBAA5B,EAA+C5L,KAA/C;QACD;MAfI,CAnIwD;MAoJ/D6L,OAAO,EAAE;QACPF,iBAAiB,EAAE,SAASA,iBAAT,CAA2BG,GAA3B,EAAgC;UACjD,IAAIC,IAAI,GAAG,KAAKX,KAAL,CAAWU,GAAX,KAAmB,KAAKV,KAAL,CAAW,KAAKH,WAAhB,CAAnB,IAAmD,KAAKG,KAAL,CAAW,KAAKX,aAAhB,CAA9D;;UACA,IAAIsB,IAAJ,EAAU;YACR,KAAKd,WAAL,GAAmBc,IAAI,CAACC,KAAxB;YACA,KAAKC,cAAL;UACD,CAHD,MAGO;YACL,KAAKhB,WAAL,GAAmB,IAAnB;UACD;QACF,CATM;QAUPiB,kBAAkB,EAAE,SAASA,kBAAT,GAA8B;UAChD,OAAO;YACLxC,KAAK,EAAE;cACL,SAAS;YADJ;UADF,CAAP;QAKD,CAhBM;QAiBPyC,gBAAgB,EAAE,SAASA,gBAAT,CAA0BC,KAA1B,EAAiC;UACjDA,KAAK,GAAGA,KAAK,CAACC,OAAN,CAAc,GAAd,EAAmB,EAAnB,CAAR;;UACA,IAAI,mBAAmBrH,IAAnB,CAAwBoH,KAAxB,CAAJ,EAAoC;YAClCA,KAAK,GAAGA,KAAK,CAACE,KAAN,CAAY,EAAZ,CAAR;;YACA,KAAK,IAAItN,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,CAArB,EAAwBA,CAAC,EAAzB,EAA6B;cAC3BoN,KAAK,CAACG,MAAN,CAAavN,CAAb,EAAgB,CAAhB,EAAmBoN,KAAK,CAACpN,CAAD,CAAxB;YACD;;YACDoN,KAAK,GAAGA,KAAK,CAACI,IAAN,CAAW,EAAX,CAAR;UACD;;UACD,IAAI,mBAAmBxH,IAAnB,CAAwBoH,KAAxB,CAAJ,EAAoC;YAClC,OAAO;cACLK,GAAG,EAAEC,QAAQ,CAACN,KAAK,CAACjB,KAAN,CAAY,CAAZ,EAAe,CAAf,CAAD,EAAoB,EAApB,CADR;cAELwB,KAAK,EAAED,QAAQ,CAACN,KAAK,CAACjB,KAAN,CAAY,CAAZ,EAAe,CAAf,CAAD,EAAoB,EAApB,CAFV;cAGLyB,IAAI,EAAEF,QAAQ,CAACN,KAAK,CAACjB,KAAN,CAAY,CAAZ,EAAe,CAAf,CAAD,EAAoB,EAApB;YAHT,CAAP;UAKD,CAND,MAMO;YACL,OAAO;cACLsB,GAAG,EAAE,GADA;cAELE,KAAK,EAAE,GAFF;cAGLC,IAAI,EAAE;YAHD,CAAP;UAKD;QACF,CAvCM;QAwCPpB,QAAQ,EAAE,SAASA,QAAT,CAAkBY,KAAlB,EAAyBS,OAAzB,EAAkC;UAC1C,IAAIC,iBAAiB,GAAG,KAAKX,gBAAL,CAAsBC,KAAtB,CAAxB;UAAA,IACIK,GAAG,GAAGK,iBAAiB,CAACL,GAD5B;UAAA,IAEIE,KAAK,GAAGG,iBAAiB,CAACH,KAF9B;UAAA,IAGIC,IAAI,GAAGE,iBAAiB,CAACF,IAH7B;;UAKA,IAAIC,OAAO,GAAG,CAAd,EAAiB;YACf;YACAJ,GAAG,IAAI,IAAII,OAAX;YACAF,KAAK,IAAI,IAAIE,OAAb;YACAD,IAAI,IAAI,IAAIC,OAAZ;UACD,CALD,MAKO;YACL;YACAJ,GAAG,IAAI,CAAC,MAAMA,GAAP,IAAcI,OAArB;YACAF,KAAK,IAAI,CAAC,MAAMA,KAAP,IAAgBE,OAAzB;YACAD,IAAI,IAAI,CAAC,MAAMA,IAAP,IAAeC,OAAvB;UACD;;UACD,OAAO,SAASE,IAAI,CAACC,KAAL,CAAWP,GAAX,CAAT,GAA2B,IAA3B,GAAkCM,IAAI,CAACC,KAAL,CAAWL,KAAX,CAAlC,GAAsD,IAAtD,GAA6DI,IAAI,CAACC,KAAL,CAAWJ,IAAX,CAA7D,GAAgF,GAAvF;QACD,CA1DM;QA2DPK,OAAO,EAAE,SAASA,OAAT,CAAiBlB,IAAjB,EAAuB;UAC9B,KAAKmB,IAAL,CAAU,KAAK9B,KAAf,EAAsBW,IAAI,CAACC,KAA3B,EAAkCD,IAAlC;QACD,CA7DM;QA8DPoB,UAAU,EAAE,SAASA,UAAT,CAAoBpB,IAApB,EAA0B;UACpC,OAAO,KAAKX,KAAL,CAAWW,IAAI,CAACC,KAAhB,CAAP;QACD,CAhEM;QAiEPoB,UAAU,EAAE,SAASA,UAAT,CAAoBrB,IAApB,EAA0B;UACpC,KAAKmB,IAAL,CAAU,KAAK7B,QAAf,EAAyBU,IAAI,CAACC,KAA9B,EAAqCD,IAArC;QACD,CAnEM;QAoEPsB,aAAa,EAAE,SAASA,aAAT,CAAuBtB,IAAvB,EAA6B;UAC1C,OAAO,KAAKV,QAAL,CAAcU,IAAI,CAACC,KAAnB,CAAP;QACD,CAtEM;QAuEPsB,QAAQ,EAAE,SAASA,QAAT,CAAkBtB,KAAlB,EAAyBuB,SAAzB,EAAoC;UAC5C,IAAIrC,WAAW,GAAG,KAAKA,WAAvB;UACA,IAAIA,WAAW,CAACsC,OAAZ,CAAoBxB,KAApB,MAA+B,CAAC,CAApC,EAAuC,OAFK,CAG5C;UACA;;UACA,IAAI,KAAKrB,YAAT,EAAuB;YACrB,KAAKO,WAAL,GAAmBA,WAAW,CAAC9C,MAAZ,CAAmB,UAAU4D,KAAV,EAAiB;cACrD,OAAOuB,SAAS,CAACC,OAAV,CAAkBxB,KAAlB,MAA6B,CAAC,CAArC;YACD,CAFkB,CAAnB;UAGD;;UACD,KAAKd,WAAL,CAAiBuC,IAAjB,CAAsBzB,KAAtB;QACD,CAlFM;QAmFP0B,SAAS,EAAE,SAASA,SAAT,CAAmB1B,KAAnB,EAA0B;UACnC,IAAIhN,CAAC,GAAG,KAAKkM,WAAL,CAAiBsC,OAAjB,CAAyBxB,KAAzB,CAAR;;UACA,IAAIhN,CAAC,KAAK,CAAC,CAAX,EAAc;YACZ,KAAKkM,WAAL,CAAiBqB,MAAjB,CAAwBvN,CAAxB,EAA2B,CAA3B;UACD;QACF,CAxFM;QAyFP2O,kBAAkB,EAAE,SAASA,kBAAT,CAA4B9F,OAA5B,EAAqC;UACvD,IAAImE,KAAK,GAAGnE,OAAO,CAACmE,KAApB;UAAA,IACIuB,SAAS,GAAG1F,OAAO,CAAC0F,SADxB;UAGA,IAAIK,QAAQ,GAAG,KAAK1C,WAAL,CAAiBsC,OAAjB,CAAyBxB,KAAzB,MAAoC,CAAC,CAApD;;UAEA,IAAI4B,QAAJ,EAAc;YACZ,KAAKF,SAAL,CAAe1B,KAAf;YACA,KAAK6B,KAAL,CAAW,OAAX,EAAoB7B,KAApB,EAA2BuB,SAA3B;UACD,CAHD,MAGO;YACL,KAAKD,QAAL,CAActB,KAAd,EAAqBuB,SAArB;YACA,KAAKM,KAAL,CAAW,MAAX,EAAmB7B,KAAnB,EAA0BuB,SAA1B;UACD;QACF,CAtGM;QAuGPO,eAAe,EAAE,SAASA,eAAT,CAAyB/B,IAAzB,EAA+B;UAC9C,IAAIhF,KAAK,GAAG,IAAZ;;UAEA,IAAIiF,KAAK,GAAGD,IAAI,CAACC,KAAjB;UAAA,IACIuB,SAAS,GAAGxB,IAAI,CAACwB,SADrB;UAGA,IAAIQ,cAAc,GAAG,KAAK9C,WAA1B;UACA,IAAI+C,QAAQ,GAAGjC,IAAI,CAACC,KAAL,KAAe,IAA9B;;UAEA,IAAIgC,QAAJ,EAAc;YACZ,KAAK/C,WAAL,GAAmBc,IAAI,CAACC,KAAxB;UACD;;UAED,KAAK6B,KAAL,CAAW,QAAX,EAAqB7B,KAArB,EAA4BuB,SAA5B,EAAuCxB,IAAvC;;UAEA,IAAI,KAAK7L,IAAL,KAAc,YAAd,IAA8B,KAAK0I,QAAvC,EAAiD;YAC/C,KAAKsC,WAAL,GAAmB,EAAnB;UACD;;UAED,IAAI,KAAKL,MAAL,IAAemD,QAAnB,EAA6B;YAC3B,KAAKC,WAAL,CAAiBlC,IAAjB,EAAuB,UAAUmC,KAAV,EAAiB;cACtCnH,KAAK,CAACkE,WAAN,GAAoB8C,cAApB;;cACA,IAAIG,KAAJ,EAAW;gBACT;gBACA;gBACA,IAAIA,KAAK,CAAC5O,IAAN,KAAe,sBAAnB,EAA2C;gBAC3C6O,OAAO,CAACD,KAAR,CAAcA,KAAd;cACD;YACF,CARD;UASD;QACF,CArIM;QAuIP;QACA;QACAjC,cAAc,EAAE,SAASA,cAAT,GAA0B;UACxC,IAAImC,MAAM,GAAG,IAAb;;UAEA,IAAIpC,KAAK,GAAG,KAAKf,WAAjB;UACA,IAAIoD,UAAU,GAAG,KAAKjD,KAAL,CAAWY,KAAX,CAAjB;UACA,IAAI,CAACqC,UAAD,IAAe,KAAKnO,IAAL,KAAc,YAA7B,IAA6C,KAAK0I,QAAtD,EAAgE;UAEhE,IAAI2E,SAAS,GAAGc,UAAU,CAACd,SAA3B,CAPwC,CASxC;UACA;;UACAA,SAAS,CAACtG,OAAV,CAAkB,UAAU+E,KAAV,EAAiB;YACjC,IAAInE,OAAO,GAAGuG,MAAM,CAAC/C,QAAP,CAAgBW,KAAhB,CAAd;YACAnE,OAAO,IAAIuG,MAAM,CAACd,QAAP,CAAgBtB,KAAhB,EAAuBnE,OAAO,CAAC0F,SAA/B,CAAX;UACD,CAHD;QAID,CAxJM;QAyJPU,WAAW,EAAE,SAASA,WAAT,CAAqBlC,IAArB,EAA2BuC,OAA3B,EAAoC;UAC/C,IAAIC,KAAK,GAAGxC,IAAI,CAACwC,KAAL,IAAcxC,IAAI,CAACC,KAA/B;;UACA,IAAI;YACF,KAAKwC,OAAL,CAAaf,IAAb,CAAkBc,KAAlB,EAAyB,YAAY,CAAE,CAAvC,EAAyCD,OAAzC;UACD,CAFD,CAEE,OAAOnK,CAAP,EAAU;YACVgK,OAAO,CAACD,KAAR,CAAc/J,CAAd;UACD;QACF,CAhKM;QAiKPsK,IAAI,EAAE,SAASA,IAAT,CAAczC,KAAd,EAAqB;UACzB,IAAI0C,MAAM,GAAG,IAAb;;UAEA,IAAInB,SAAS,GAAG,KAAKlC,QAAL,CAAcW,KAAK,CAAC2C,QAAN,EAAd,EAAgCpB,SAAhD;UAEAA,SAAS,CAACtG,OAAV,CAAkB,UAAUjI,CAAV,EAAa;YAC7B,OAAO0P,MAAM,CAACpB,QAAP,CAAgBtO,CAAhB,EAAmBuO,SAAnB,CAAP;UACD,CAFD;QAGD,CAzKM;QA0KPqB,KAAK,EAAE,SAASA,KAAT,CAAe5C,KAAf,EAAsB;UAC3B,KAAK0B,SAAL,CAAe1B,KAAf;QACD;MA5KM,CApJsD;MAkU/D6C,OAAO,EAAE,SAASA,OAAT,GAAmB;QAC1B,KAAK5C,cAAL;QACA,KAAK6C,GAAL,CAAS,YAAT,EAAuB,KAAKhB,eAA5B;QACA,KAAKgB,GAAL,CAAS,eAAT,EAA0B,KAAKnB,kBAA/B;;QACA,IAAI,KAAKzN,IAAL,KAAc,YAAlB,EAAgC;UAC9B,IAAIoI,YAAJ,CAAiB,KAAKyG,GAAtB,EAD8B,CACF;QAC7B;;QACD,KAAKC,MAAL,CAAY,OAAZ,EAAqB,KAAKrD,iBAA1B;MACD;IA1U8D,CAApC,CAtRqC,CAkmBlE;;IACC;;IAA6B,IAAIsD,gCAAgC,GAAIzG,4BAAxC,CAnmBoC,CAomBlE;;IACA,IAAI0G,mBAAmB,GAAGpQ,mBAAmB,CAAC,CAAD,CAA7C,CArmBkE,CAumBlE;;;IACA,IAAIqQ,WAAJ,EAAiB9N,eAAjB;IAKA;;IAEA,IAAIoH,SAAS,GAAGhJ,MAAM,CAACyP,mBAAmB,CAAC;IAAI;IAAL,CAApB,CAAN,CACdD,gCADc,EAEdE,WAFc,EAGd9N,eAHc,EAId,KAJc,EAKd,IALc,EAMd,IANc,EAOd,IAPc,CAAhB;IAWA;;IACA,IAAI,KAAJ,EAAW;MAAE,IAAI+N,GAAJ;IAAU;;IACvB3G,SAAS,CAAC9G,OAAV,CAAkB0N,MAAlB,GAA2B,4BAA3B;IACA;;IAA6B,IAAIC,IAAI,GAAI7G,SAAS,CAAC9J,OAAtB,CA7nBqC,CA8nBlE;;IAGA;;IACA2Q,IAAI,CAACC,OAAL,GAAe,UAAUC,GAAV,EAAe;MAC5BA,GAAG,CAAC/G,SAAJ,CAAc6G,IAAI,CAAChQ,IAAnB,EAAyBgQ,IAAzB;IACD,CAFD;IAIA;;;IAA6B,IAAIG,aAAa,GAAGxO,mBAAmB,CAAC,SAAD,CAAnB,GAAkCqO,IAAtD;IAE7B;EAAO;EAEP;;AAxwBU,CAtFD,CADT"}]}