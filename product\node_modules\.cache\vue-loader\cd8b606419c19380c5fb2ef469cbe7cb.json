{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\candidates-user\\candidates-user.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\candidates-user\\candidates-user.vue", "mtime": 1752541693445}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["candidates-user.vue"], "names": [], "mappings": ";AA8FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "candidates-user.vue", "sourceRoot": "src/components/candidates-user", "sourcesContent": ["<template>\r\n  <div class=\"candidates-user\"\r\n       v-loading=\"loading\"\r\n       element-loading-text=\"拼命加载中\">\r\n    <div class=\"candidates-user-box\">\r\n      <div class=\"candidates-user-content\">\r\n        <div class=\"search-box\">\r\n          <el-input placeholder=\"搜索人员名字\"\r\n                    v-model=\"name\"\r\n                    clearable\r\n                    @keyup.enter.native=\"search\">\r\n            <div slot=\"prefix\"\r\n                 class=\"input-search\"></div>\r\n          </el-input>\r\n        </div>\r\n        <div class=\"user-box\">\r\n          <div class=\"user-tree-box\">\r\n            <div class=\"institutions-text\">选择机构</div>\r\n            <div class=\"user-tree\">\r\n              <!-- <zy-tree :tree=\"tree\"\r\n                       :choiceId.sync=\"choiceval\"\r\n                       @on-choice-click=\"choiceClick\"></zy-tree> -->\r\n              <zy-tree :tree=\"tree\"\r\n                       v-model=\"choiceval\"\r\n                       :props=\"{ children: 'children', label: 'name' }\"\r\n                       @on-tree-click=\"choiceClick\"\r\n                       :anykey=\"defaultUnitShowIds\"></zy-tree>\r\n            </div>\r\n          </div>\r\n          <div class=\"user-personnel-box\">\r\n            <div class=\"personnel-checkbox\">\r\n              <div class=\"personnel-checkbox-text\">人员列表</div>\r\n              <el-checkbox :indeterminate=\"isIndeterminate\"\r\n                           v-model=\"checkAll\"\r\n                           @change=\"handleCheckAllChange\"></el-checkbox>\r\n            </div>\r\n            <div class=\"user-content-box scrollBar\">\r\n              <el-checkbox-group v-model=\"checkedCities\"\r\n                                 @change=\"handleCheckedCitiesChange\">\r\n                <div class=\"user-content\"\r\n                     v-for=\"city in cities\"\r\n                     :key=\"city.userId\">\r\n                  <div class=\"user-content-icon-name\">\r\n                    <div class=\"user-content-icon\"></div>\r\n                    <div class=\"user-content-name el-checkbox__label ellipsis\">\r\n                      {{ city.userName }}\r\n                    </div>\r\n                  </div>\r\n                  <el-checkbox :value=\"city.userId\"\r\n                               :label=\"city.userId\"\r\n                               :disabled=\"maxUser(city.userId)\"></el-checkbox>\r\n                </div>\r\n              </el-checkbox-group>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"selected-user-box\">\r\n        <div class=\"selected-user-number\">\r\n          <div class=\"selected-user-number-text\">\r\n            {{ point }}已选择({{ storageData.length }}人)\r\n          </div>\r\n          <div class=\"selected-user-icon-delete\"\r\n               @click=\"deleteAll\"></div>\r\n        </div>\r\n        <div class=\"selected-user scrollBar\">\r\n          <div class=\"selected-user-content\"\r\n               v-for=\"(item, index) in storageData\"\r\n               :key=\"index\">\r\n            <div class=\"selected-user-icon\">\r\n              <div class=\"selected-user-icon-name\"></div>\r\n            </div>\r\n            <div class=\"selected-user-information\">\r\n              <div class=\"selected-user-name\">\r\n                {{ item.userName || item.name }}\r\n              </div>\r\n              <div class=\"selected-user-text ellipsis\">{{ item.position }}</div>\r\n            </div>\r\n            <div class=\"selected-user-delete\">\r\n              <div class=\"selected-user-icon-delete\"\r\n                   @click=\"deleteclick(item)\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"candidates-user-button\">\r\n      <el-button type=\"primary\"\r\n                 @click=\"submitForm\">确定</el-button>\r\n      <el-button @click=\"resetForm\">取消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport _ from 'lodash'\r\nexport default {\r\n  name: 'candidatesUser',\r\n  data () {\r\n    return {\r\n      choiceval: '',\r\n      tree: [],\r\n      name: '',\r\n      checkAll: false,\r\n      checkedCities: [],\r\n      cities: [],\r\n      isIndeterminate: true,\r\n      storage: {},\r\n      storageData: [],\r\n      selectObj: [],\r\n      loading: false,\r\n      defaultUnitIds: [],\r\n      defaultUnitShowIds: []\r\n    }\r\n  },\r\n  props: {\r\n    point: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    max: {\r\n      type: Number,\r\n      default: 10000\r\n    },\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    flag: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    groupId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    disabled: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    defualtUser: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    defaultUnit: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  created () {\r\n    if (this.data.length) {\r\n      this.default()\r\n    }\r\n    this.pointrees()\r\n  },\r\n  watch: {\r\n    defualtUser (val) {\r\n      if (val.length) {\r\n        this.cities = val\r\n        val.forEach(item => {\r\n          this.checkedCities = []\r\n          this.storageData = []\r\n          this.checkedCities.push(item.userId)\r\n          this.storage[this.choiceval] = item\r\n          this.storageData.push(item)\r\n        })\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    maxUser (id) {\r\n      var show = false\r\n      if (this.storageData.length >= this.max) {\r\n        show = true\r\n      }\r\n      this.storageData.forEach(item => {\r\n        if (item.userId === id) {\r\n          show = false\r\n        }\r\n      })\r\n      this.storageData.forEach(item => {\r\n        if (item.disabled && item.userId === id) {\r\n          show = true\r\n        }\r\n      })\r\n      // this.disabled.forEach(item => {\r\n      //   if (item.userId === id) {\r\n      //     show = true\r\n      //   }\r\n      // })\r\n      return show\r\n    },\r\n    search () {\r\n      this.loading = true\r\n      this.roleChooseusers()\r\n    },\r\n    default () {\r\n      // this.storageData = this.data\r\n      this.data.forEach(item => {\r\n        this.storageData.push(item)\r\n        this.selectObj[item.userId] = item.userId\r\n      })\r\n    },\r\n    async pointrees () {\r\n      const res = await this.$api.general.pointrees(this.point)\r\n      var { data } = res\r\n      this.tree = data\r\n      var that = this\r\n      const treeIdArr = []\r\n      function getArr (arr) {\r\n        that.defaultUnit.forEach(v => {\r\n          arr.forEach(v2 => {\r\n            if (v2.id.indexOf(v) !== -1) {\r\n              treeIdArr.push(v2.id)\r\n            } else {\r\n              getArr(v2.children)\r\n            }\r\n          })\r\n        })\r\n      }\r\n      if (this.defaultUnit.length) {\r\n        this.loading = true\r\n        getArr(this.tree)\r\n        this.defaultUnitIds = _.uniq(treeIdArr)\r\n        this.defaultUnitIds.forEach((v, index) => { that.forRoleChooseusers(v, index) })\r\n        this.getPidList()\r\n      }\r\n    },\r\n    getPidList () {\r\n      var arr = []\r\n      this.defaultUnitIds.forEach(v => {\r\n        arr = arr.concat(this.filterTree(this.tree, v))\r\n      })\r\n      this.getPid(arr)\r\n    },\r\n    getPid (item) {\r\n      item.forEach(v => {\r\n        this.defaultUnitShowIds.push(v.id)\r\n        if (v.children) {\r\n          this.getPid(v.children)\r\n        }\r\n      })\r\n    },\r\n    filterTree (nodes, id) {\r\n      if (!nodes || !nodes.length) return void 0 // eslint-disable-line\r\n      const children = []\r\n      for (let node of nodes) {\r\n        node = Object.assign({}, node)\r\n        const sub = this.filterTree(node.children, id)\r\n        if ((sub && sub.length) || node.id === id) {\r\n          sub && (node.children = sub)\r\n          children.push(node)\r\n        }\r\n      }\r\n      return children.length ? children : void 0 // eslint-disable-line\r\n    },\r\n    choiceClick (item) {\r\n      this.loading = true\r\n      this.roleChooseusers()\r\n    },\r\n    async forRoleChooseusers (val, index) {\r\n      var datas = {\r\n        pointCode: this.point,\r\n        treeId: val,\r\n        keyword: this.name\r\n      }\r\n      const res = await this.$api.general.pointreeUsers(datas)\r\n      var { data } = res\r\n      this.cities = this.cities.concat(data)\r\n      const labeluserUsersData = await this.$api.systemSettings.labeluserUsers({ labelCode: ' 202104', pageNo: 1, pageSize: 99999 })\r\n      data.forEach((item, index) => { // 匹配标签用户与 所展示的列表数据\r\n        labeluserUsersData.data.forEach((v) => {\r\n          if (v.userId === item.userId) {\r\n            this.checkedCities.push(item.userId)\r\n            this.storageData.push(item)\r\n          }\r\n        })\r\n      })\r\n      this.loading = false\r\n      if (index + 1 === this.defaultUnitIds.length) {\r\n        this.choiceval = this.defaultUnitIds[index]\r\n      }\r\n    },\r\n    async roleChooseusers () {\r\n      var datas = {\r\n        pointCode: this.point,\r\n        treeId: this.choiceval,\r\n        keyword: this.name\r\n      }\r\n      const res = await this.$api.general.pointreeUsers(datas)\r\n      var { data } = res\r\n      this.disabled.forEach(item => {\r\n        data = data.filter(tab => tab.userId !== item.userId)\r\n      })\r\n      this.cities = data\r\n      this.loading = false\r\n      if (!this.defaultUnit.length && !this.defualtUser.length) {\r\n        this.memoryChecked()\r\n      }\r\n    },\r\n    handleCheckAllChange (val) {\r\n      var arr = []\r\n      this.cities.forEach(item => {\r\n        arr.push(item.userId)\r\n        if (Object.prototype.hasOwnProperty.call(this.selectObj, item.userId)) {\r\n          if (!val) {\r\n            this.deleteData(item)\r\n          }\r\n          val ? null : delete this.selectObj[item.userId] // eslint-disable-line\r\n        } else {\r\n          this.selectObj[item.userId] = item.userId\r\n          this.pushData(item.userId)\r\n        }\r\n      })\r\n      this.checkedCities = val ? arr : []\r\n      this.storage[this.choiceval] = val ? arr : []\r\n      this.isIndeterminate = false\r\n    },\r\n    handleCheckedCitiesChange (value) {\r\n      if (!this.defaultUnit.length || !this.defualtUser.length) {\r\n        this.storage[this.choiceval] = value\r\n      }\r\n      const checkedCount = value.length\r\n      this.checkAll = checkedCount > this.cities.length || checkedCount === this.cities.length\r\n      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length\r\n      var values = []\r\n      value.forEach(item => {\r\n        values[item] = item\r\n      })\r\n      this.cities.forEach((item) => {\r\n        if (Object.prototype.hasOwnProperty.call(values, item.userId)) {\r\n        } else {\r\n          delete this.selectObj[item.userId]\r\n          this.deleteData(item)\r\n        }\r\n      })\r\n      value.forEach(item => {\r\n        if (Object.prototype.hasOwnProperty.call(this.selectObj, item)) {\r\n        } else {\r\n          this.selectObj[item] = item\r\n          if (!this.defaultUnit.length || !this.defualtUser.length) {\r\n            this.pushData(item)\r\n          }\r\n        }\r\n      })\r\n    },\r\n    deleteAll () {\r\n      this.$confirm('此操作将删除当前已选的所有用户, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        var arr = []\r\n        this.storageData.forEach(item => {\r\n          if (item.disabled) {\r\n            arr.push(item)\r\n          }\r\n        })\r\n        this.storageData = arr\r\n        this.selectObj = []\r\n        arr.forEach(item => {\r\n          this.selectObj[item.userId] = item.userId\r\n        })\r\n        if (arr.length) {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '当前选中用户有部分不能移除'\r\n          })\r\n        }\r\n        this.memoryChecked()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    deleteclick (data) {\r\n      if (data.disabled) {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '当前选中用户不能移除'\r\n        })\r\n        return\r\n      }\r\n      this.deleteData(data)\r\n      delete this.selectObj[data.userId]\r\n      this.memoryChecked()\r\n    },\r\n    deleteData (data) {\r\n      var found = this.data.some(function (item) {\r\n        return item.userId.includes(data.userId)\r\n      })\r\n      if (found) {\r\n        this.deleteApi(data)\r\n      }\r\n      const arr = this.storageData\r\n      arr.forEach((item, index) => {\r\n        if (item.userId === data.userId) {\r\n          arr.splice(index, 1)\r\n        }\r\n      })\r\n      this.storageData = arr\r\n    },\r\n    async deleteApi (a) {\r\n      var params = {\r\n        userId: a.userId,\r\n        groupId: this.groupId\r\n      }\r\n      const res = await this.$api.general.deleteApi(params)\r\n      this.$message({\r\n        message: res.errmsg,\r\n        type: 'success'\r\n      })\r\n    },\r\n    pushData (id) {\r\n      this.cities.forEach((item, index) => {\r\n        if (item.userId === id) {\r\n          this.storageData.push(item)\r\n        }\r\n      })\r\n    },\r\n    memoryChecked () {\r\n      var add = []\r\n      this.cities.forEach((row, index) => {\r\n        if (Object.prototype.hasOwnProperty.call(this.selectObj, row.userId)) {\r\n          add.push(row.userId)\r\n        }\r\n      })\r\n      this.checkedCities = add\r\n      const checkedCount = add.length\r\n      this.checkAll = checkedCount > this.cities.length || checkedCount === this.cities.length\r\n      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length\r\n    },\r\n    submitForm () {\r\n      this.$emit('userCallback', this.storageData, true)\r\n    },\r\n    resetForm () {\r\n      this.$emit('userCallback', this.data, false)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./candidates-user.scss\";\r\n</style>\r\n"]}]}