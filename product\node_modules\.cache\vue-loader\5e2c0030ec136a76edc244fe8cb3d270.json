{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tab\\zy-tab.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tab\\zy-tab.vue", "mtime": 1752541693583}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgZWxlbWVudFJlc2l6ZURldGVjdG9yTWFrZXIgZnJvbSAnZWxlbWVudC1yZXNpemUtZGV0ZWN0b3InDQpjb25zdCBlcmQgPSBlbGVtZW50UmVzaXplRGV0ZWN0b3JNYWtlcigpDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICd6eVRhYicsDQogIGRhdGEgKCkgew0KICAgIHJldHVybiB7DQogICAgICB0YWJJZDogdGhpcy52YWx1ZSwNCiAgICAgIHRhYkRhdGE6IFtdLA0KICAgICAgc2hvdzogZmFsc2UsDQogICAgICBvZmZzZXQ6IDAsDQogICAgICBiaWdnZXN0OiAwDQogICAgfQ0KICB9LA0KICBwcm9wczogew0KICAgIHZhbHVlOiB7DQogICAgICB0eXBlOiBTdHJpbmcNCiAgICB9LA0KICAgIHRhYkxpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogKCkgPT4gW10NCiAgICB9LA0KICAgIHNoaWZ0OiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAxNjgNCiAgICB9LA0KICAgIHByb3BzOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgaWQ6ICdpZCcsDQogICAgICAgICAgbGFiZWw6ICdsYWJlbCcNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCAoKSB7DQogICAgdGhpcy50YWJDb3B5RGF0YSh0aGlzLmRlZXBDb3B5KHRoaXMudGFiTGlzdCkpDQogIH0sDQogIHdhdGNoOiB7DQogICAgdmFsdWUgKHZhbCkgew0KICAgICAgdGhpcy50YWJJZCA9IHZhbA0KICAgICAgdGhpcy5zZWxlY3RlZCgpDQogICAgfSwNCiAgICB0YWJJZCAodmFsKSB7DQogICAgICB0aGlzLiRlbWl0KCdpbnB1dCcsIHZhbCkNCiAgICB9LA0KICAgIHRhYkxpc3QgKHZhbCkgew0KICAgICAgdGhpcy50YWJDb3B5RGF0YSh0aGlzLmRlZXBDb3B5KHRoaXMudGFiTGlzdCkpDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuYmlnZ2VzdENsaWNrKCkNCiAgICAgIH0pDQogICAgfQ0KICB9LA0KICBtb3VudGVkICgpIHsNCiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICBpZiAodGhpcy50YWJJZCkgew0KICAgICAgICB0aGlzLnRhYkJveCgpDQogICAgICB9DQogICAgICBjb25zdCB0aGF0ID0gdGhpcw0KICAgICAgZXJkLmxpc3RlblRvKHRoaXMuJHJlZnNbJ3p5LXRhYiddLCAoZWxlbWVudCkgPT4gew0KICAgICAgICB0aGF0LiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgdGhpcy5iaWdnZXN0Q2xpY2soKQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9KQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgc2VsZWN0ZWRNZXRob2RzIChpdGVtKSB7DQogICAgICB0aGlzLnRhYklkID0gaXRlbVt0aGlzLnByb3BzLmlkXQ0KICAgICAgdGhpcy5zZWxlY3RlZCgpDQogICAgICBpZiAoaXRlbS50eXBlKSB7DQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgICAgIHBhdGg6IGl0ZW0udG8sDQogICAgICAgICAgICBxdWVyeTogaXRlbS5wYXJhbXMNCiAgICAgICAgICB9KQ0KICAgICAgICB9LCAyMDApDQogICAgICB9DQogICAgfSwNCiAgICBzZWxlY3RlZCAoaWQpIHsNCiAgICAgIHZhciBhcnIgPSB0aGlzLnRhYkRhdGENCiAgICAgIGFyci5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLmNsYXNzID0gZmFsc2UNCiAgICAgICAgaWYgKGl0ZW1bdGhpcy5wcm9wcy5pZF0gPT09IHRoaXMudGFiSWQpIHsNCiAgICAgICAgICBpdGVtLmNsYXNzID0gdHJ1ZQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgdGhpcy50YWJEYXRhID0gYXJyDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMudGFiQm94KCkNCiAgICAgIH0pDQogICAgfSwNCiAgICByZWZyZXNoY2xpY2sgKGRhdGEpIHsNCiAgICAgIHRoaXMuJGVtaXQoJ3RhYi1yZWZyZXNoJywgZGF0YSkNCiAgICB9LA0KICAgIGRlbGV0ZWNsaWNrIChkYXRhLCBpbmRleCkgew0KICAgICAgaWYgKHRoaXMudGFiSWQgPT09IGRhdGFbdGhpcy5wcm9wcy5pZF0pIHsNCiAgICAgICAgdGhpcy5CZWZvcmUoaW5kZXgpDQogICAgICB9DQogICAgICB0aGlzLiRlbWl0KCd0YWItY2xpY2snLCBkYXRhKQ0KICAgIH0sDQogICAgQmVmb3JlIChpKSB7DQogICAgICB0aGlzLnRhYkxpc3QuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgaWYgKGkgPT09IDApIHsNCiAgICAgICAgICBpZiAoaW5kZXggPT09IGkgKyAxKSB7DQogICAgICAgICAgICB0aGlzLnRhYklkID0gaXRlbVt0aGlzLnByb3BzLmlkXQ0KICAgICAgICAgICAgaWYgKGl0ZW0udHlwZSkgew0KICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgICAgICAgICAgICBwYXRoOiBpdGVtLnRvLA0KICAgICAgICAgICAgICAgICAgcXVlcnk6IGl0ZW0ucGFyYW1zDQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgfSwgMjAwKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBpZiAoaW5kZXggPT09IGkgLSAxKSB7DQogICAgICAgICAgICB0aGlzLnRhYklkID0gaXRlbVt0aGlzLnByb3BzLmlkXQ0KICAgICAgICAgICAgaWYgKGl0ZW0udHlwZSkgew0KICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgICAgICAgICAgICBwYXRoOiBpdGVtLnRvLA0KICAgICAgICAgICAgICAgICAgcXVlcnk6IGl0ZW0ucGFyYW1zDQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgfSwgMjAwKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGJpZ2dlc3RDbGljayAoKSB7DQogICAgICB2YXIgdGFiQm94ID0gdGhpcy4kcmVmc1snenktdGFiLWJveCddDQogICAgICB2YXIgaXRlbUJveCA9IHRoaXMuJHJlZnNbJ3p5LXRhYi1pdGVtLWxpc3QnXQ0KICAgICAgaWYgKGl0ZW1Cb3gub2Zmc2V0V2lkdGggPiB0YWJCb3gub2Zmc2V0V2lkdGgpIHsNCiAgICAgICAgdGhpcy5zaG93ID0gdHJ1ZQ0KICAgICAgICB0aGlzLmJpZ2dlc3QgPSBpdGVtQm94Lm9mZnNldFdpZHRoIC0gdGFiQm94Lm9mZnNldFdpZHRoDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNob3cgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQogICAgdGFic0xlZnQgKCkgew0KICAgICAgdmFyIGl0ZW1Cb3ggPSB0aGlzLiRyZWZzWyd6eS10YWItaXRlbS1saXN0J10NCiAgICAgIHZhciBvZmZzZXQgPSB0aGlzLm9mZnNldCAtIHRoaXMuc2hpZnQNCiAgICAgIGlmICh0aGlzLm9mZnNldCAtIHRoaXMuc2hpZnQgPD0gMCkgew0KICAgICAgICBvZmZzZXQgPSAwDQogICAgICB9DQogICAgICBpdGVtQm94LnN0eWxlLnRyYW5zZm9ybSA9IGB0cmFuc2xhdGVYKC0ke29mZnNldH1weClgDQogICAgICBpdGVtQm94LnN0eWxlLnRyYW5zaXRpb25EdXJhdGlvbiA9ICcuNHMnDQogICAgICB0aGlzLm9mZnNldCA9IG9mZnNldA0KICAgIH0sDQogICAgdGFic1JpZ2h0ICgpIHsNCiAgICAgIHZhciBpdGVtQm94ID0gdGhpcy4kcmVmc1snenktdGFiLWl0ZW0tbGlzdCddDQogICAgICB2YXIgb2Zmc2V0ID0gdGhpcy5vZmZzZXQgKyB0aGlzLnNoaWZ0DQogICAgICBpZiAodGhpcy5iaWdnZXN0IDwgdGhpcy5vZmZzZXQgKyB0aGlzLnNoaWZ0KSB7DQogICAgICAgIG9mZnNldCA9IHRoaXMuYmlnZ2VzdA0KICAgICAgfQ0KICAgICAgaXRlbUJveC5zdHlsZS50cmFuc2Zvcm0gPSBgdHJhbnNsYXRlWCgtJHtvZmZzZXR9cHgpYA0KICAgICAgaXRlbUJveC5zdHlsZS50cmFuc2l0aW9uRHVyYXRpb24gPSAnLjRzJw0KICAgICAgdGhpcy5vZmZzZXQgPSBvZmZzZXQNCiAgICB9LA0KICAgIHRhYkJveCAoKSB7DQogICAgICB2YXIgdGFiQm94ID0gdGhpcy4kcmVmc1snenktdGFiLWJveCddDQogICAgICB2YXIgaXRlbUJveCA9IHRoaXMuJHJlZnNbJ3p5LXRhYi1pdGVtLWxpc3QnXQ0KICAgICAgdmFyIGl0ZW1BY3RpdmUgPSBpdGVtQm94LnF1ZXJ5U2VsZWN0b3IoJy56eS10YWItaXRlbS1hY3RpdmUnKQ0KICAgICAgaWYgKHRhYkJveC5vZmZzZXRXaWR0aCA8IGl0ZW1Cb3gub2Zmc2V0V2lkdGgpIHsNCiAgICAgICAgaXRlbUJveC5zdHlsZS50cmFuc2Zvcm0gPSAndHJhbnNsYXRlWCgwcHgpJw0KICAgICAgICBpdGVtQm94LnN0eWxlLnRyYW5zaXRpb25EdXJhdGlvbiA9ICcuNHMnDQogICAgICAgIGlmIChpdGVtQm94Lm9mZnNldFdpZHRoID09PSBpdGVtQWN0aXZlLm9mZnNldExlZnQgKyBpdGVtQWN0aXZlLm9mZnNldFdpZHRoKSB7DQogICAgICAgICAgdGhpcy5vZmZzZXQgPSBpdGVtQm94Lm9mZnNldFdpZHRoIC0gdGFiQm94Lm9mZnNldFdpZHRoDQogICAgICAgICAgaXRlbUJveC5zdHlsZS50cmFuc2Zvcm0gPSBgdHJhbnNsYXRlWCgtJHtpdGVtQm94Lm9mZnNldFdpZHRoIC0gdGFiQm94Lm9mZnNldFdpZHRofXB4KWANCiAgICAgICAgfSBlbHNlIGlmICh0YWJCb3gub2Zmc2V0V2lkdGggLyAyID4gaXRlbUJveC5vZmZzZXRXaWR0aCAtIGl0ZW1BY3RpdmUub2Zmc2V0TGVmdCkgew0KICAgICAgICAgIHRoaXMub2Zmc2V0ID0gaXRlbUJveC5vZmZzZXRXaWR0aCAtIHRhYkJveC5vZmZzZXRXaWR0aA0KICAgICAgICAgIGl0ZW1Cb3guc3R5bGUudHJhbnNmb3JtID0gYHRyYW5zbGF0ZVgoLSR7aXRlbUJveC5vZmZzZXRXaWR0aCAtIHRhYkJveC5vZmZzZXRXaWR0aH1weClgDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgaWYgKGl0ZW1BY3RpdmUub2Zmc2V0TGVmdCAtIHRhYkJveC5vZmZzZXRXaWR0aCAvIDIgPCAwKSB7DQogICAgICAgICAgICB0aGlzLm9mZnNldCA9IDANCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5vZmZzZXQgPSBpdGVtQWN0aXZlLm9mZnNldExlZnQgLSB0YWJCb3gub2Zmc2V0V2lkdGggLyAyDQogICAgICAgICAgfQ0KICAgICAgICAgIGl0ZW1Cb3guc3R5bGUudHJhbnNmb3JtID0gYHRyYW5zbGF0ZVgoLSR7aXRlbUFjdGl2ZS5vZmZzZXRMZWZ0IC0gdGFiQm94Lm9mZnNldFdpZHRoIC8gMn1weClgDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIHRhYkNvcHlEYXRhIChkYXRhKSB7DQogICAgICB0aGlzLnRhYkRhdGEgPSB0aGlzLmluaXREYXRhKGRhdGEpDQogICAgfSwNCiAgICBpbml0RGF0YSAoaXRlbXMpIHsNCiAgICAgIGl0ZW1zLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgIGlmICgodHlwZW9mIGl0ZW0uY2xhc3MpID09PSAndW5kZWZpbmVkJykgeyAvLyBlc2xpbnQtZGlzYWJsZS1saW5lDQogICAgICAgICAgaXRlbS5jbGFzcyA9IGZhbHNlDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMudGFiSWQgPT09IGl0ZW1bdGhpcy5wcm9wcy5pZF0pIHsNCiAgICAgICAgICBpdGVtLmNsYXNzID0gdHJ1ZQ0KICAgICAgICAgIGlmIChpdGVtLnR5cGUpIHsNCiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgICAgICAgICAgcGF0aDogaXRlbS50bywNCiAgICAgICAgICAgICAgICBxdWVyeTogaXRlbS5wYXJhbXMNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0sIDIwMCkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICByZXR1cm4gaXRlbXMNCiAgICB9LA0KICAgIGRlZXBDb3B5IChkYXRhKSB7DQogICAgICB2YXIgdCA9IHRoaXMudHlwZShkYXRhKQ0KICAgICAgdmFyIG8NCiAgICAgIHZhciBpDQogICAgICB2YXIgbmkNCiAgICAgIGlmICh0ID09PSAnYXJyYXknKSB7DQogICAgICAgIG8gPSBbXQ0KICAgICAgfSBlbHNlIGlmICh0ID09PSAnb2JqZWN0Jykgew0KICAgICAgICBvID0ge30NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiBkYXRhDQogICAgICB9DQogICAgICBpZiAodCA9PT0gJ2FycmF5Jykgew0KICAgICAgICBmb3IgKGkgPSAwLCBuaSA9IGRhdGEubGVuZ3RoOyBpIDwgbmk7IGkrKykgew0KICAgICAgICAgIG8ucHVzaCh0aGlzLmRlZXBDb3B5KGRhdGFbaV0pKQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBvDQogICAgICB9IGVsc2UgaWYgKHQgPT09ICdvYmplY3QnKSB7DQogICAgICAgIGZvciAoaSBpbiBkYXRhKSB7DQogICAgICAgICAgb1tpXSA9IHRoaXMuZGVlcENvcHkoZGF0YVtpXSkNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gbw0KICAgICAgfQ0KICAgIH0sDQogICAgdHlwZSAob2JqKSB7DQogICAgICB2YXIgdG9TdHJpbmcgPSBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nDQogICAgICB2YXIgbWFwID0gew0KICAgICAgICAnW29iamVjdCBCb29sZWFuXSc6ICdib29sZWFuJywNCiAgICAgICAgJ1tvYmplY3QgTnVtYmVyXSc6ICdudW1iZXInLA0KICAgICAgICAnW29iamVjdCBTdHJpbmddJzogJ3N0cmluZycsDQogICAgICAgICdbb2JqZWN0IEZ1bmN0aW9uXSc6ICdmdW5jdGlvbicsDQogICAgICAgICdbb2JqZWN0IEFycmF5XSc6ICdhcnJheScsDQogICAgICAgICdbb2JqZWN0IERhdGVdJzogJ2RhdGUnLA0KICAgICAgICAnW29iamVjdCBSZWdFeHBdJzogJ3JlZ0V4cCcsDQogICAgICAgICdbb2JqZWN0IFVuZGVmaW5lZF0nOiAndW5kZWZpbmVkJywNCiAgICAgICAgJ1tvYmplY3QgTnVsbF0nOiAnbnVsbCcsDQogICAgICAgICdbb2JqZWN0IE9iamVjdF0nOiAnb2JqZWN0Jw0KICAgICAgfQ0KICAgICAgcmV0dXJuIG1hcFt0b1N0cmluZy5jYWxsKG9iaildDQogICAgfQ0KICB9LA0KICBiZWZvcmVEZXN0cm95ICgpIHsNCiAgICBlcmQudW5pbnN0YWxsKHRoaXMuJHJlZnNbJ3p5LXRhYiddKQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["zy-tab.vue"], "names": [], "mappings": ";AAmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-tab.vue", "sourceRoot": "src/components/zy-tab", "sourcesContent": ["<template>\r\n  <div class=\"zy-tab\"\r\n       ref=\"zy-tab\">\r\n    <div class=\"zy-tab-left\"\r\n         v-if=\"show&&offset>0||offset!=0\"\r\n         @click.stop=\"tabsLeft\"><i class=\"el-icon-d-arrow-left\"></i></div>\r\n    <div class=\"zy-tab-right\"\r\n         v-if=\"show&&offset<biggest\"\r\n         @click.stop=\"tabsRight\"><i class=\"el-icon-d-arrow-right\"></i></div>\r\n    <div class=\"zy-tab-box\"\r\n         ref=\"zy-tab-box\">\r\n      <div class=\"zy-tab-item-list\"\r\n           ref=\"zy-tab-item-list\">\r\n        <div v-for=\"(item, index) in tabData\"\r\n             :key=\"index\"\r\n             @click.prevent=\"selectedMethods(item)\"\r\n             :ref=\"item.class?'zy-tab-item-active':'zy-tab-item'\"\r\n             :class=\"['zy-tab-item',item.class?'zy-tab-item-active':'']\">\r\n          <div class=\"zy-tab-item-label\">{{item[props.label]}}</div>\r\n          <span class=\"zy-tab-item-del-box\"\r\n                v-if=\"item.class\">\r\n            <span class=\"zy-tab-item-refresh\"\r\n                  @click.stop=\"refreshclick(item,index)\"><i class=\"el-icon-refresh\"></i></span>\r\n          </span>\r\n          <span class=\"zy-tab-item-del-box\"\r\n                v-if=\"tabData.length!=1\">\r\n            <span class=\"zy-tab-item-del\"\r\n                  @click.stop=\"deleteclick(item,index)\"><i class=\"el-icon-close\"></i></span>\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nexport default {\r\n  name: 'zyTab',\r\n  data () {\r\n    return {\r\n      tabId: this.value,\r\n      tabData: [],\r\n      show: false,\r\n      offset: 0,\r\n      biggest: 0\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String\r\n    },\r\n    tabList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    shift: {\r\n      type: Number,\r\n      default: 168\r\n    },\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          id: 'id',\r\n          label: 'label'\r\n        }\r\n      }\r\n    }\r\n  },\r\n  created () {\r\n    this.tabCopyData(this.deepCopy(this.tabList))\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      this.tabId = val\r\n      this.selected()\r\n    },\r\n    tabId (val) {\r\n      this.$emit('input', val)\r\n    },\r\n    tabList (val) {\r\n      this.tabCopyData(this.deepCopy(this.tabList))\r\n      this.$nextTick(() => {\r\n        this.biggestClick()\r\n      })\r\n    }\r\n  },\r\n  mounted () {\r\n    this.$nextTick(() => {\r\n      if (this.tabId) {\r\n        this.tabBox()\r\n      }\r\n      const that = this\r\n      erd.listenTo(this.$refs['zy-tab'], (element) => {\r\n        that.$nextTick(() => {\r\n          this.biggestClick()\r\n        })\r\n      })\r\n    })\r\n  },\r\n  methods: {\r\n    selectedMethods (item) {\r\n      this.tabId = item[this.props.id]\r\n      this.selected()\r\n      if (item.type) {\r\n        setTimeout(() => {\r\n          this.$router.push({\r\n            path: item.to,\r\n            query: item.params\r\n          })\r\n        }, 200)\r\n      }\r\n    },\r\n    selected (id) {\r\n      var arr = this.tabData\r\n      arr.forEach(item => {\r\n        item.class = false\r\n        if (item[this.props.id] === this.tabId) {\r\n          item.class = true\r\n        }\r\n      })\r\n      this.tabData = arr\r\n      this.$nextTick(() => {\r\n        this.tabBox()\r\n      })\r\n    },\r\n    refreshclick (data) {\r\n      this.$emit('tab-refresh', data)\r\n    },\r\n    deleteclick (data, index) {\r\n      if (this.tabId === data[this.props.id]) {\r\n        this.Before(index)\r\n      }\r\n      this.$emit('tab-click', data)\r\n    },\r\n    Before (i) {\r\n      this.tabList.forEach((item, index) => {\r\n        if (i === 0) {\r\n          if (index === i + 1) {\r\n            this.tabId = item[this.props.id]\r\n            if (item.type) {\r\n              setTimeout(() => {\r\n                this.$router.push({\r\n                  path: item.to,\r\n                  query: item.params\r\n                })\r\n              }, 200)\r\n            }\r\n          }\r\n        } else {\r\n          if (index === i - 1) {\r\n            this.tabId = item[this.props.id]\r\n            if (item.type) {\r\n              setTimeout(() => {\r\n                this.$router.push({\r\n                  path: item.to,\r\n                  query: item.params\r\n                })\r\n              }, 200)\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    biggestClick () {\r\n      var tabBox = this.$refs['zy-tab-box']\r\n      var itemBox = this.$refs['zy-tab-item-list']\r\n      if (itemBox.offsetWidth > tabBox.offsetWidth) {\r\n        this.show = true\r\n        this.biggest = itemBox.offsetWidth - tabBox.offsetWidth\r\n      } else {\r\n        this.show = false\r\n      }\r\n    },\r\n    tabsLeft () {\r\n      var itemBox = this.$refs['zy-tab-item-list']\r\n      var offset = this.offset - this.shift\r\n      if (this.offset - this.shift <= 0) {\r\n        offset = 0\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      itemBox.style.transitionDuration = '.4s'\r\n      this.offset = offset\r\n    },\r\n    tabsRight () {\r\n      var itemBox = this.$refs['zy-tab-item-list']\r\n      var offset = this.offset + this.shift\r\n      if (this.biggest < this.offset + this.shift) {\r\n        offset = this.biggest\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      itemBox.style.transitionDuration = '.4s'\r\n      this.offset = offset\r\n    },\r\n    tabBox () {\r\n      var tabBox = this.$refs['zy-tab-box']\r\n      var itemBox = this.$refs['zy-tab-item-list']\r\n      var itemActive = itemBox.querySelector('.zy-tab-item-active')\r\n      if (tabBox.offsetWidth < itemBox.offsetWidth) {\r\n        itemBox.style.transform = 'translateX(0px)'\r\n        itemBox.style.transitionDuration = '.4s'\r\n        if (itemBox.offsetWidth === itemActive.offsetLeft + itemActive.offsetWidth) {\r\n          this.offset = itemBox.offsetWidth - tabBox.offsetWidth\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`\r\n        } else if (tabBox.offsetWidth / 2 > itemBox.offsetWidth - itemActive.offsetLeft) {\r\n          this.offset = itemBox.offsetWidth - tabBox.offsetWidth\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`\r\n        } else {\r\n          if (itemActive.offsetLeft - tabBox.offsetWidth / 2 < 0) {\r\n            this.offset = 0\r\n          } else {\r\n            this.offset = itemActive.offsetLeft - tabBox.offsetWidth / 2\r\n          }\r\n          itemBox.style.transform = `translateX(-${itemActive.offsetLeft - tabBox.offsetWidth / 2}px)`\r\n        }\r\n      }\r\n    },\r\n    tabCopyData (data) {\r\n      this.tabData = this.initData(data)\r\n    },\r\n    initData (items) {\r\n      items.forEach((item, index) => {\r\n        if ((typeof item.class) === 'undefined') { // eslint-disable-line\r\n          item.class = false\r\n        }\r\n        if (this.tabId === item[this.props.id]) {\r\n          item.class = true\r\n          if (item.type) {\r\n            setTimeout(() => {\r\n              this.$router.push({\r\n                path: item.to,\r\n                query: item.params\r\n              })\r\n            }, 200)\r\n          }\r\n        }\r\n      })\r\n      return items\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    }\r\n  },\r\n  beforeDestroy () {\r\n    erd.uninstall(this.$refs['zy-tab'])\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-tab.scss\";\r\n</style>\r\n"]}]}