{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\finishDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\finishDetail.vue", "mtime": 1752541693830}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["finishDetail.vue"], "names": [], "mappings": ";AAsIA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "finishDetail.vue", "sourceRoot": "src/views/AssessmentOrgan/InnovationExcellence", "sourcesContent": ["<template>\r\n  <!-- 点击完成情况 -->\r\n  <div class=\"finishDetail\">\r\n    <div class=\"buttonColumn\">\r\n      <!-- 新增/删除 按钮 -->\r\n      <div class=\"qd-btn-box\">\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-plus\"\r\n                   @click=\"finishStatus\">新增\r\n        </el-button>\r\n        <el-button type=\"danger\"\r\n                   icon=\"el-icon-delete\"\r\n                   plain\r\n                   @click=\"handleBatchDelete\">删除\r\n        </el-button>\r\n\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   v-permissions=\"'auth:innovation:checkPass'\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n        <el-button type=\"danger\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   v-permissions=\"'auth:innovation:checkNotPass'\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"listView\">\r\n\r\n      <el-table :data=\"tableData\"\r\n                row-key=\"id\"\r\n                ref=\"multipleTable\"\r\n                @select=\"selected\"\r\n                @select-all=\"selectedAll\"\r\n                :header-cell-style=\"{background:'#eef1f6',color:'#606266'}\"\r\n                tooltip-effect=\"dark\"\r\n                class=\"tableStyle\">\r\n        <el-table-column type=\"selection\"\r\n                         width=\"55\">\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"标题\"\r\n                         show-overflow-tooltip\r\n                         prop=\"title\"\r\n                         width=\"450\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\"\r\n                       @click=\"modify(scope.row)\"\r\n                       size=\"small\"> {{scope.row.title}}\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"审核状态\"\r\n                         width=\"110\"\r\n                         prop=\"auditStatus\">\r\n\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"overTime\"\r\n                         label=\"完成时间\"\r\n                         width=\"220\">\r\n          <template slot-scope=\"scope\">\r\n            <div> {{$format(scope.row.overTime).substr(0,16)}} </div>\r\n          </template>\r\n        </el-table-column>\r\n        <!-- <el-table-column prop=\"auditStatus\"\r\n                         label=\"审核状态\"\r\n                         show-overflow-tooltip>\r\n          <template slot-scope=\"scope\"> -->\r\n        <!-- <div v-if=\"scope.row.auditStatus == '1' \"> 待审核 </div>\r\n                <div v-if=\"scope.row.auditStatus == '0' \"> 审核通过</div> -->\r\n        <!-- <div>{{scope.row.auditStatus}}</div>\r\n          </template>\r\n        </el-table-column> -->\r\n\r\n        <el-table-column label=\"操作\"\r\n                         width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\"\r\n                       @click=\"handleClick(scope.row)\"\r\n                       size=\"small\"> 编辑</el-button>\r\n\r\n            <el-button type=\"text\"\r\n                       @click=\"handleDelete(scope.row.id)\"\r\n                       class=\"delBtn\"\r\n                       size=\"small\"> 删除</el-button>\r\n\r\n          </template>\r\n        </el-table-column>\r\n\r\n      </el-table>\r\n\r\n    </div>\r\n\r\n    <div class=\"paging_box\">\r\n      <el-pagination @size-change=\"handleSizeChange\"\r\n                     @current-change=\"handleCurrentChange\"\r\n                     :current-page.sync=\"currentPage\"\r\n                     :page-sizes=\"[10, 20, 30, 40]\"\r\n                     :page-size.sync=\"pageSize\"\r\n                     background\r\n                     layout=\"total, prev, pager, next, sizes, jumper\"\r\n                     :total=\"total\">\r\n      </el-pagination>\r\n    </div>\r\n\r\n    <zy-pop-up v-model=\"showFinish\"\r\n               class=\"titleStyle\"\r\n               title=\"完成情况\">\r\n      <newFinishDetail :id=\"id\"\r\n                       :uid=\"uid\"\r\n                       @newCallback=\"newCallback\">\r\n      </newFinishDetail>\r\n    </zy-pop-up>\r\n\r\n    <zy-pop-up v-model=\"showFinishDetail\"\r\n               class=\"titleStyle\"\r\n               title=\"完成情况详情\"\r\n               :beforeClose=\"updateList\">\r\n\r\n      <FinishDetailPop :id=\"id\"\r\n                       :uid=\"uid\"\r\n                       @newCallback=\"newCallback\">\r\n      </FinishDetailPop>\r\n\r\n    </zy-pop-up>\r\n\r\n  </div>\r\n</template>\r\n<script>\r\n// import newFinishDetail from '../newFinishDetail.vue'\r\nimport newFinishDetail from '../BusinessObjectives/newFinishDetail'\r\n\r\nimport FinishDetailPop from './FinishDetailPop.vue'\r\nimport tableData from '@mixins/tableData'\r\n\r\nexport default {\r\n  name: 'finishDetail',\r\n  mixins: [tableData],\r\n  components: {\r\n    newFinishDetail,\r\n    FinishDetailPop\r\n  },\r\n  data () {\r\n    return {\r\n      showFinish: false,\r\n      showFinishDetail: false,\r\n\r\n      tableData: [],\r\n      selectData: [],\r\n      selectObj: [],\r\n\r\n      evaluationId: '',\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      uid: '', // 完成情况列表的id\r\n      currentPage: 1,\r\n      total: 10\r\n\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  inject: ['newTab'],\r\n\r\n  mounted () {\r\n    if (this.id) {\r\n      this.getfinishDetailList()\r\n    }\r\n  },\r\n  methods: {\r\n    updateList () {\r\n      this.showFinishDetail = false\r\n      this.getfinishDetailList()\r\n    },\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckWork(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 审核\r\n    async getCheckWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckFinishDetail({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getfinishDetailList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n    newCallback () {\r\n      this.showFinish = false // 关闭弹窗\r\n      this.showFinishDetail = false\r\n      this.getfinishDetailList() // 重新调用(更新)一次列表\r\n    },\r\n    // 请求后台数据 获取列表信息\r\n    async getfinishDetailList () {\r\n      const res = await this.$api.AssessmentOrgan.reqfinishDetailList({\r\n        evaluationId: this.id, // TODO:工作目标或创新创优id(需检查是否传错)\r\n        pageNo: this.currentPage,\r\n        pageSize: this.pageSize,\r\n        // *******************\r\n        memberType: this.memberType,\r\n        auditStatus: this.auditStatus\r\n      })\r\n      var { data, total } = res\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n    },\r\n    // 新增 完成情况\r\n    finishStatus () {\r\n      this.showFinish = true\r\n      this.uid = 0 // 将newFinishDetail组件的属性:uid设置为0 (false) 达到新增页面效果\r\n    },\r\n    // 编辑\r\n    handleClick (row) {\r\n      this.uid = row.id\r\n      this.showFinish = true\r\n    },\r\n    // 详情\r\n    modify (row) {\r\n      this.uid = row.id\r\n      this.showFinishDetail = true\r\n    },\r\n    // 删除按钮方法  批量删除\r\n    handleDelete (ids) { // TODO: handleDelete 和 handleBatchDelete 1.组合使用为新增/删除栏的删除功能 2.单独使用handleDelete则为操作项的删除功能\r\n      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$api.AssessmentOrgan.reqDelFinishDetail({ ids }).then((res) => {\r\n          if (res.errcode === 200) {\r\n            this.getfinishDetailList()// 删除后更新页面\r\n            this.$message.success('删除成功')\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('取消删除')\r\n        // this.getfinishDetailList()\r\n        return false\r\n      })\r\n    },\r\n    handleBatchDelete () {\r\n      if (this.selectData.length === 0) {\r\n        return this.$message.warning('请选择想要删除的项')\r\n      }\r\n      this.handleDelete(this.selectData.join(','))\r\n    },\r\n    select (selection, row) {\r\n      var arr = this.selectData\r\n      if (this.selectObj[row.id]) {\r\n        arr.forEach((item, index) => {\r\n          if (item === row.id) {\r\n            arr.splice(index, 1)\r\n          }\r\n        })\r\n        delete this.selectObj[row.id]\r\n      } else {\r\n        this.selectObj[row.id] = row.id\r\n        arr.push(row.id)\r\n        this.selectData = arr\r\n      }\r\n    },\r\n    selectAll (selection) {\r\n      this.selectData = []\r\n      this.selectObj = []\r\n      if (selection.length) {\r\n        selection.forEach((item, index) => {\r\n          this.selectObj[item.id] = item.id\r\n          this.selectData.push(item.id)\r\n        })\r\n      }\r\n    },\r\n    // 底部页签\r\n    handleSizeChange () {\r\n      this.getfinishDetailList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getfinishDetailList()\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.finishDetail {\r\n  width: 1100px;\r\n  height: 100%;\r\n  padding: 1px 40px;\r\n\r\n  .qd-btn-box {\r\n    padding-bottom: 5px;\r\n  }\r\n\r\n  .listView {\r\n    height: 100%;\r\n\r\n    .tableStyle {\r\n      width: 100%;\r\n      height: 500px;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n    }\r\n  }\r\n\r\n  .tableZy {\r\n    height: 500px;\r\n  }\r\n}\r\n</style>\r\n"]}]}