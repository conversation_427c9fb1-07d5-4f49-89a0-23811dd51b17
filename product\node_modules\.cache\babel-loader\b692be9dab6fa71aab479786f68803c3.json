{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\candidates-user\\candidates-user.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\candidates-user\\candidates-user.vue", "mtime": 1752541693445}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA8FA;AACA;EACAA,sBADA;;EAEAC;IACA;MACAC,aADA;MAEAC,QAFA;MAGAH,QAHA;MAIAI,eAJA;MAKAC,iBALA;MAMAC,UANA;MAOAC,qBAPA;MAQAC,WARA;MASAC,eATA;MAUAC,aAVA;MAWAC,cAXA;MAYAC,kBAZA;MAaAC;IAbA;EAeA,CAlBA;;EAmBAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,YADA;MAEAC;IAFA,CALA;IASAhB;MACAe,WADA;MAEAC;IAFA,CATA;IAaAE;MACAH,YADA;MAEAC;IAFA,CAbA;IAiBAG;MACAJ,YADA;MAEAC;IAFA,CAjBA;IAqBAI;MACAL,WADA;MAEAC;IAFA,CArBA;IAyBAK;MACAN,WADA;MAEAC;IAFA,CAzBA;IA6BAM;MACAP,WADA;MAEAC;IAFA;EA7BA,CAnBA;;EAqDAO;IACA;MACA;IACA;;IACA;EACA,CA1DA;;EA2DAC;IACAH;MACA;QACA;QACAI;UACA;UACA;UACA;UACA;UACA;QACA,CANA;MAOA;IACA;;EAZA,CA3DA;EAyEAC;IACAC;MACA;;MACA;QACAC;MACA;;MACA;QACA;UACAA;QACA;MACA,CAJA;MAKA;QACA;UACAA;QACA;MACA,CAJA,EAVA,CAeA;MACA;MACA;MACA;MACA;;MACA;IACA,CAtBA;;IAuBAC;MACA;MACA;IACA,CA1BA;;IA2BAb;MACA;MACA;QACA;QACA;MACA,CAHA;IAIA,CAjCA;;IAkCA;MACA;MACA;QAAAhB;MAAA;MACA;MACA;MACA;;MACA;QACA8B;UACAC;YACA;cACAC;YACA,CAFA,MAEA;cACAC;YACA;UACA,CANA;QAOA,CARA;MASA;;MACA;QACA;QACAA;QACA;QACA;UAAAH;QAAA;QACA;MACA;IACA,CA1DA;;IA2DAI;MACA;MACA;QACAH;MACA,CAFA;MAGA;IACA,CAjEA;;IAkEAI;MACAC;QACA;;QACA;UACA;QACA;MACA,CALA;IAMA,CAzEA;;IA0EAC;MACA,2CADA,CACA;;MACA;;MACA;QACAC;QACA;;QACA;UACAC;UACAC;QACA;MACA;;MACA,2CAXA,CAWA;IACA,CAtFA;;IAuFAC;MACA;MACA;IACA,CA1FA;;IA2FA;MACA;QACAC,qBADA;QAEAC,WAFA;QAGAC;MAHA;MAKA;MACA;QAAA5C;MAAA;MACA;MACA;QAAA6C;QAAAC;QAAAC;MAAA;MACA/C;QAAA;QACAgD;UACA;YACA;YACA;UACA;QACA,CALA;MAMA,CAPA;MAQA;;MACA;QACA;MACA;IACA,CAjHA;;IAkHA;MACA;QACAN,qBADA;QAEAC,sBAFA;QAGAC;MAHA;MAKA;MACA;QAAA5C;MAAA;MACA;QACAA;MACA,CAFA;MAGA;MACA;;MACA;QACA;MACA;IACA,CAlIA;;IAmIAiD;MACA;MACA;QACAlB;;QACA;UACA;YACA;UACA;;UACAN,gDAJA,CAIA;QACA,CALA,MAKA;UACA;UACA;QACA;MACA,CAXA;MAYA;MACA;MACA;IACA,CApJA;;IAqJAyB;MACA;QACA;MACA;;MACA;MACA;MACA;MACA;MACAC;QACAC;MACA,CAFA;MAGA;QACA,gEACA,CADA,MACA;UACA;UACA;QACA;MACA,CANA;MAOAD;QACA,iEACA,CADA,MACA;UACA;;UACA;YACA;UACA;QACA;MACA,CARA;IASA,CAhLA;;IAiLAE;MACA;QACAC,uBADA;QAEAC,sBAFA;QAGAxC;MAHA,GAIAyC,IAJA,CAIA;QACA;QACA;UACA;YACAzB;UACA;QACA,CAJA;QAKA;QACA;QACAA;UACA;QACA,CAFA;;QAGA;UACA;YACAhB,YADA;YAEA0C;UAFA;QAIA;;QACA;MACA,CAvBA,EAuBAC,KAvBA,CAuBA;QACA;UACA3C,YADA;UAEA0C;QAFA;MAIA,CA5BA;IA6BA,CA/MA;;IAgNAE;MACA;QACA;UACA5C,YADA;UAEA0C;QAFA;QAIA;MACA;;MACA;MACA;MACA;IACA,CA3NA;;IA4NAG;MACA;QACA;MACA,CAFA;;MAGA;QACA;MACA;;MACA;MACA7B;QACA;UACAA;QACA;MACA,CAJA;MAKA;IACA,CA1OA;;IA2OA;MACA;QACA8B,gBADA;QAEA1C;MAFA;MAIA;MACA;QACAsC,mBADA;QAEA1C;MAFA;IAIA,CArPA;;IAsPA+C;MACA;QACA;UACA;QACA;MACA,CAJA;IAKA,CA5PA;;IA6PAC;MACA;MACA;QACA;UACAC;QACA;MACA,CAJA;MAKA;MACA;MACA;MACA;IACA,CAxQA;;IAyQAC;MACA;IACA,CA3QA;;IA4QAC;MACA;IACA;;EA9QA;AAzEA", "names": ["name", "data", "choiceval", "tree", "checkAll", "checkedCities", "cities", "isIndeterminate", "storage", "storageData", "selectObj", "loading", "defaultUnitIds", "defaultUnitShowIds", "props", "point", "type", "default", "max", "flag", "groupId", "disabled", "defualtUser", "defaultUnit", "created", "watch", "val", "methods", "maxUser", "show", "search", "that", "arr", "treeIdArr", "getArr", "getPidList", "getPid", "item", "filterTree", "node", "sub", "children", "choiceClick", "pointCode", "treeId", "keyword", "labelCode", "pageNo", "pageSize", "labeluserUsersData", "handleCheckAllChange", "handleCheckedCitiesChange", "value", "values", "deleteAll", "confirmButtonText", "cancelButtonText", "then", "message", "catch", "deleteclick", "deleteData", "userId", "pushData", "memoryChecked", "add", "submitForm", "resetForm"], "sourceRoot": "src/components/candidates-user", "sources": ["candidates-user.vue"], "sourcesContent": ["<template>\r\n  <div class=\"candidates-user\"\r\n       v-loading=\"loading\"\r\n       element-loading-text=\"拼命加载中\">\r\n    <div class=\"candidates-user-box\">\r\n      <div class=\"candidates-user-content\">\r\n        <div class=\"search-box\">\r\n          <el-input placeholder=\"搜索人员名字\"\r\n                    v-model=\"name\"\r\n                    clearable\r\n                    @keyup.enter.native=\"search\">\r\n            <div slot=\"prefix\"\r\n                 class=\"input-search\"></div>\r\n          </el-input>\r\n        </div>\r\n        <div class=\"user-box\">\r\n          <div class=\"user-tree-box\">\r\n            <div class=\"institutions-text\">选择机构</div>\r\n            <div class=\"user-tree\">\r\n              <!-- <zy-tree :tree=\"tree\"\r\n                       :choiceId.sync=\"choiceval\"\r\n                       @on-choice-click=\"choiceClick\"></zy-tree> -->\r\n              <zy-tree :tree=\"tree\"\r\n                       v-model=\"choiceval\"\r\n                       :props=\"{ children: 'children', label: 'name' }\"\r\n                       @on-tree-click=\"choiceClick\"\r\n                       :anykey=\"defaultUnitShowIds\"></zy-tree>\r\n            </div>\r\n          </div>\r\n          <div class=\"user-personnel-box\">\r\n            <div class=\"personnel-checkbox\">\r\n              <div class=\"personnel-checkbox-text\">人员列表</div>\r\n              <el-checkbox :indeterminate=\"isIndeterminate\"\r\n                           v-model=\"checkAll\"\r\n                           @change=\"handleCheckAllChange\"></el-checkbox>\r\n            </div>\r\n            <div class=\"user-content-box scrollBar\">\r\n              <el-checkbox-group v-model=\"checkedCities\"\r\n                                 @change=\"handleCheckedCitiesChange\">\r\n                <div class=\"user-content\"\r\n                     v-for=\"city in cities\"\r\n                     :key=\"city.userId\">\r\n                  <div class=\"user-content-icon-name\">\r\n                    <div class=\"user-content-icon\"></div>\r\n                    <div class=\"user-content-name el-checkbox__label ellipsis\">\r\n                      {{ city.userName }}\r\n                    </div>\r\n                  </div>\r\n                  <el-checkbox :value=\"city.userId\"\r\n                               :label=\"city.userId\"\r\n                               :disabled=\"maxUser(city.userId)\"></el-checkbox>\r\n                </div>\r\n              </el-checkbox-group>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"selected-user-box\">\r\n        <div class=\"selected-user-number\">\r\n          <div class=\"selected-user-number-text\">\r\n            {{ point }}已选择({{ storageData.length }}人)\r\n          </div>\r\n          <div class=\"selected-user-icon-delete\"\r\n               @click=\"deleteAll\"></div>\r\n        </div>\r\n        <div class=\"selected-user scrollBar\">\r\n          <div class=\"selected-user-content\"\r\n               v-for=\"(item, index) in storageData\"\r\n               :key=\"index\">\r\n            <div class=\"selected-user-icon\">\r\n              <div class=\"selected-user-icon-name\"></div>\r\n            </div>\r\n            <div class=\"selected-user-information\">\r\n              <div class=\"selected-user-name\">\r\n                {{ item.userName || item.name }}\r\n              </div>\r\n              <div class=\"selected-user-text ellipsis\">{{ item.position }}</div>\r\n            </div>\r\n            <div class=\"selected-user-delete\">\r\n              <div class=\"selected-user-icon-delete\"\r\n                   @click=\"deleteclick(item)\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"candidates-user-button\">\r\n      <el-button type=\"primary\"\r\n                 @click=\"submitForm\">确定</el-button>\r\n      <el-button @click=\"resetForm\">取消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport _ from 'lodash'\r\nexport default {\r\n  name: 'candidatesUser',\r\n  data () {\r\n    return {\r\n      choiceval: '',\r\n      tree: [],\r\n      name: '',\r\n      checkAll: false,\r\n      checkedCities: [],\r\n      cities: [],\r\n      isIndeterminate: true,\r\n      storage: {},\r\n      storageData: [],\r\n      selectObj: [],\r\n      loading: false,\r\n      defaultUnitIds: [],\r\n      defaultUnitShowIds: []\r\n    }\r\n  },\r\n  props: {\r\n    point: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    max: {\r\n      type: Number,\r\n      default: 10000\r\n    },\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    flag: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    groupId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    disabled: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    defualtUser: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    defaultUnit: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  created () {\r\n    if (this.data.length) {\r\n      this.default()\r\n    }\r\n    this.pointrees()\r\n  },\r\n  watch: {\r\n    defualtUser (val) {\r\n      if (val.length) {\r\n        this.cities = val\r\n        val.forEach(item => {\r\n          this.checkedCities = []\r\n          this.storageData = []\r\n          this.checkedCities.push(item.userId)\r\n          this.storage[this.choiceval] = item\r\n          this.storageData.push(item)\r\n        })\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    maxUser (id) {\r\n      var show = false\r\n      if (this.storageData.length >= this.max) {\r\n        show = true\r\n      }\r\n      this.storageData.forEach(item => {\r\n        if (item.userId === id) {\r\n          show = false\r\n        }\r\n      })\r\n      this.storageData.forEach(item => {\r\n        if (item.disabled && item.userId === id) {\r\n          show = true\r\n        }\r\n      })\r\n      // this.disabled.forEach(item => {\r\n      //   if (item.userId === id) {\r\n      //     show = true\r\n      //   }\r\n      // })\r\n      return show\r\n    },\r\n    search () {\r\n      this.loading = true\r\n      this.roleChooseusers()\r\n    },\r\n    default () {\r\n      // this.storageData = this.data\r\n      this.data.forEach(item => {\r\n        this.storageData.push(item)\r\n        this.selectObj[item.userId] = item.userId\r\n      })\r\n    },\r\n    async pointrees () {\r\n      const res = await this.$api.general.pointrees(this.point)\r\n      var { data } = res\r\n      this.tree = data\r\n      var that = this\r\n      const treeIdArr = []\r\n      function getArr (arr) {\r\n        that.defaultUnit.forEach(v => {\r\n          arr.forEach(v2 => {\r\n            if (v2.id.indexOf(v) !== -1) {\r\n              treeIdArr.push(v2.id)\r\n            } else {\r\n              getArr(v2.children)\r\n            }\r\n          })\r\n        })\r\n      }\r\n      if (this.defaultUnit.length) {\r\n        this.loading = true\r\n        getArr(this.tree)\r\n        this.defaultUnitIds = _.uniq(treeIdArr)\r\n        this.defaultUnitIds.forEach((v, index) => { that.forRoleChooseusers(v, index) })\r\n        this.getPidList()\r\n      }\r\n    },\r\n    getPidList () {\r\n      var arr = []\r\n      this.defaultUnitIds.forEach(v => {\r\n        arr = arr.concat(this.filterTree(this.tree, v))\r\n      })\r\n      this.getPid(arr)\r\n    },\r\n    getPid (item) {\r\n      item.forEach(v => {\r\n        this.defaultUnitShowIds.push(v.id)\r\n        if (v.children) {\r\n          this.getPid(v.children)\r\n        }\r\n      })\r\n    },\r\n    filterTree (nodes, id) {\r\n      if (!nodes || !nodes.length) return void 0 // eslint-disable-line\r\n      const children = []\r\n      for (let node of nodes) {\r\n        node = Object.assign({}, node)\r\n        const sub = this.filterTree(node.children, id)\r\n        if ((sub && sub.length) || node.id === id) {\r\n          sub && (node.children = sub)\r\n          children.push(node)\r\n        }\r\n      }\r\n      return children.length ? children : void 0 // eslint-disable-line\r\n    },\r\n    choiceClick (item) {\r\n      this.loading = true\r\n      this.roleChooseusers()\r\n    },\r\n    async forRoleChooseusers (val, index) {\r\n      var datas = {\r\n        pointCode: this.point,\r\n        treeId: val,\r\n        keyword: this.name\r\n      }\r\n      const res = await this.$api.general.pointreeUsers(datas)\r\n      var { data } = res\r\n      this.cities = this.cities.concat(data)\r\n      const labeluserUsersData = await this.$api.systemSettings.labeluserUsers({ labelCode: ' 202104', pageNo: 1, pageSize: 99999 })\r\n      data.forEach((item, index) => { // 匹配标签用户与 所展示的列表数据\r\n        labeluserUsersData.data.forEach((v) => {\r\n          if (v.userId === item.userId) {\r\n            this.checkedCities.push(item.userId)\r\n            this.storageData.push(item)\r\n          }\r\n        })\r\n      })\r\n      this.loading = false\r\n      if (index + 1 === this.defaultUnitIds.length) {\r\n        this.choiceval = this.defaultUnitIds[index]\r\n      }\r\n    },\r\n    async roleChooseusers () {\r\n      var datas = {\r\n        pointCode: this.point,\r\n        treeId: this.choiceval,\r\n        keyword: this.name\r\n      }\r\n      const res = await this.$api.general.pointreeUsers(datas)\r\n      var { data } = res\r\n      this.disabled.forEach(item => {\r\n        data = data.filter(tab => tab.userId !== item.userId)\r\n      })\r\n      this.cities = data\r\n      this.loading = false\r\n      if (!this.defaultUnit.length && !this.defualtUser.length) {\r\n        this.memoryChecked()\r\n      }\r\n    },\r\n    handleCheckAllChange (val) {\r\n      var arr = []\r\n      this.cities.forEach(item => {\r\n        arr.push(item.userId)\r\n        if (Object.prototype.hasOwnProperty.call(this.selectObj, item.userId)) {\r\n          if (!val) {\r\n            this.deleteData(item)\r\n          }\r\n          val ? null : delete this.selectObj[item.userId] // eslint-disable-line\r\n        } else {\r\n          this.selectObj[item.userId] = item.userId\r\n          this.pushData(item.userId)\r\n        }\r\n      })\r\n      this.checkedCities = val ? arr : []\r\n      this.storage[this.choiceval] = val ? arr : []\r\n      this.isIndeterminate = false\r\n    },\r\n    handleCheckedCitiesChange (value) {\r\n      if (!this.defaultUnit.length || !this.defualtUser.length) {\r\n        this.storage[this.choiceval] = value\r\n      }\r\n      const checkedCount = value.length\r\n      this.checkAll = checkedCount > this.cities.length || checkedCount === this.cities.length\r\n      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length\r\n      var values = []\r\n      value.forEach(item => {\r\n        values[item] = item\r\n      })\r\n      this.cities.forEach((item) => {\r\n        if (Object.prototype.hasOwnProperty.call(values, item.userId)) {\r\n        } else {\r\n          delete this.selectObj[item.userId]\r\n          this.deleteData(item)\r\n        }\r\n      })\r\n      value.forEach(item => {\r\n        if (Object.prototype.hasOwnProperty.call(this.selectObj, item)) {\r\n        } else {\r\n          this.selectObj[item] = item\r\n          if (!this.defaultUnit.length || !this.defualtUser.length) {\r\n            this.pushData(item)\r\n          }\r\n        }\r\n      })\r\n    },\r\n    deleteAll () {\r\n      this.$confirm('此操作将删除当前已选的所有用户, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        var arr = []\r\n        this.storageData.forEach(item => {\r\n          if (item.disabled) {\r\n            arr.push(item)\r\n          }\r\n        })\r\n        this.storageData = arr\r\n        this.selectObj = []\r\n        arr.forEach(item => {\r\n          this.selectObj[item.userId] = item.userId\r\n        })\r\n        if (arr.length) {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '当前选中用户有部分不能移除'\r\n          })\r\n        }\r\n        this.memoryChecked()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    deleteclick (data) {\r\n      if (data.disabled) {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '当前选中用户不能移除'\r\n        })\r\n        return\r\n      }\r\n      this.deleteData(data)\r\n      delete this.selectObj[data.userId]\r\n      this.memoryChecked()\r\n    },\r\n    deleteData (data) {\r\n      var found = this.data.some(function (item) {\r\n        return item.userId.includes(data.userId)\r\n      })\r\n      if (found) {\r\n        this.deleteApi(data)\r\n      }\r\n      const arr = this.storageData\r\n      arr.forEach((item, index) => {\r\n        if (item.userId === data.userId) {\r\n          arr.splice(index, 1)\r\n        }\r\n      })\r\n      this.storageData = arr\r\n    },\r\n    async deleteApi (a) {\r\n      var params = {\r\n        userId: a.userId,\r\n        groupId: this.groupId\r\n      }\r\n      const res = await this.$api.general.deleteApi(params)\r\n      this.$message({\r\n        message: res.errmsg,\r\n        type: 'success'\r\n      })\r\n    },\r\n    pushData (id) {\r\n      this.cities.forEach((item, index) => {\r\n        if (item.userId === id) {\r\n          this.storageData.push(item)\r\n        }\r\n      })\r\n    },\r\n    memoryChecked () {\r\n      var add = []\r\n      this.cities.forEach((row, index) => {\r\n        if (Object.prototype.hasOwnProperty.call(this.selectObj, row.userId)) {\r\n          add.push(row.userId)\r\n        }\r\n      })\r\n      this.checkedCities = add\r\n      const checkedCount = add.length\r\n      this.checkAll = checkedCount > this.cities.length || checkedCount === this.cities.length\r\n      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length\r\n    },\r\n    submitForm () {\r\n      this.$emit('userCallback', this.storageData, true)\r\n    },\r\n    resetForm () {\r\n      this.$emit('userCallback', this.data, false)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./candidates-user.scss\";\r\n</style>\r\n"]}]}