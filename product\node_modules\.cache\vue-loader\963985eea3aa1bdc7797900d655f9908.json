{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-upload\\zy-upload.vue?vue&type=style&index=0&id=b07495a2&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-upload\\zy-upload.vue", "mtime": 1752541693624}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3p5LXVwbG9hZC5zY3NzIjsNCg=="}, {"version": 3, "sources": ["zy-upload.vue"], "names": [], "mappings": ";AAmJA", "file": "zy-upload.vue", "sourceRoot": "src/components/zy-upload", "sourcesContent": ["<template>\r\n  <div class=\"zy-upload\">\r\n    <el-upload action=\"#\"\r\n               :class=\"{ uploadSty:showBtn,disUploadSty:!showBtn}\"\r\n               :multiple=\"multiple\"\r\n               :limit=\"limit\"\r\n               list-type=\"picture-card\"\r\n               accept=\".jpg,.jpeg,.png,.PNG,.JPG\"\r\n               :on-change=\"handleChange\"\r\n               :http-request=\"customUpload\"\r\n               :before-upload=\"beforeAvatarUpload\"\r\n               :file-list=\"filelist\">\r\n      <i slot=\"default\"\r\n         class=\"el-icon-plus\"></i>\r\n      <div slot=\"file\"\r\n           slot-scope=\"{file}\">\r\n        <img class=\"el-upload-list__item-thumbnail\"\r\n             :src=\"file.url\"\r\n             alt=\"\">\r\n        <span class=\"el-upload-list__item-actions\">\r\n          <span class=\"el-upload-list__item-preview\"\r\n                @click=\"handlePictureCardPreview(file)\">\r\n            <i class=\"el-icon-zoom-in\"></i>\r\n          </span>\r\n          <span class=\"el-upload-list__item-delete\"\r\n                @click=\"handleRemove(file)\">\r\n            <i class=\"el-icon-delete\"></i>\r\n          </span>\r\n        </span>\r\n      </div>\r\n    </el-upload>\r\n    <el-dialog :visible.sync=\"dialogVisible\"\r\n               append-to-body>\r\n      <img width=\"100%\"\r\n           :src=\"dialogImageUrl\"\r\n           alt=\"\">\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'zyUpload',\r\n  props: {\r\n    value: [Array],\r\n    limit: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    multiple: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    module: {\r\n      type: String,\r\n      default: 'minisuggestion'\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value', // 这个字段，是指父组件设置 v-model 时，将变量值传给子组件的 msg\r\n    event: 'file'// 这个字段，是指父组件监听 parent-event 事件\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.filelist = val\r\n        this.showBtn = this.filelist.length < this.limit\r\n      } else {\r\n        this.filelist = []\r\n      }\r\n    },\r\n    filelist (val) {\r\n      this.$emit('file', val)\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      dialogVisible: false,\r\n      dialogImageUrl: '',\r\n      filelist: this.value,\r\n      showBtn: true\r\n    }\r\n  },\r\n  methods: {\r\n    // 图片预览\r\n    handlePictureCardPreview (file) {\r\n      this.dialogImageUrl = file.url\r\n      this.dialogVisible = true\r\n    },\r\n    // 移除图片\r\n    handleRemove (file) {\r\n      // 存在浅拷贝 所以不需要再赋值\r\n      const arr = this.filelist\r\n      for (let i = 0; i < arr.length; i++) {\r\n        if (arr[i].uid === file.uid) {\r\n          arr.splice(i, 1)\r\n        }\r\n      }\r\n      this.showBtn = this.filelist.length < this.limit\r\n    },\r\n    // 校验文件类型和文件大小\r\n    beforeAvatarUpload (file) {\r\n      // const isJPG = file.type === 'image/jpeg'\r\n      const isLt2M = file.size / 1024 / 1024 < 10\r\n      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n      if (testmsg !== 'png' && testmsg !== 'jpg' && testmsg !== 'PNG' && testmsg !== 'jpeg' && testmsg !== 'JPG') {\r\n        this.$message.error('图片文件格式暂时不支持!')\r\n        return false\r\n      }\r\n      if (!isLt2M) {\r\n        this.$message.error('上传图片大小不能超过 10MB!')\r\n        return false\r\n      }\r\n      return isLt2M && testmsg\r\n    },\r\n    // 上传逻辑\r\n    async customUpload (file) {\r\n      const siteId = JSON.parse(sessionStorage.getItem('user' + this.$logo())).areaId\r\n      const formData = new FormData()\r\n      formData.append('attachment', file.file)\r\n      formData.append('module', this.module)\r\n      formData.append('siteId', siteId)\r\n      this.$api.microAdvice.uploadFile(formData).then(res => {\r\n        const { errcode, data } = res\r\n        if (errcode === 200) {\r\n          const fileData = {\r\n            name: data[0].fileName,\r\n            size: data[0].fileSize,\r\n            type: data[0].fileType,\r\n            url: data[0].filePath,\r\n            id: data[0].id,\r\n            uid: data[0].uid\r\n          }\r\n          this.filelist.push(fileData)\r\n        }\r\n      })\r\n    },\r\n    handleChange (file, filelist) {\r\n      if (file.status !== 'ready') {\r\n        this.showBtn = filelist.length < this.limit\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"./zy-upload.scss\";\r\n</style>\r\n"]}]}