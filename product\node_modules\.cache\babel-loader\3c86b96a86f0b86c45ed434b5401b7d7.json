{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\newFinishDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\newFinishDetail.vue", "mtime": 1752541693793}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAqDA;EACAA,uBADA;;EAEAC;IACA;MACA;MACAC,+DAFA;MAGAC;QACAC,SADA;QAEAC,YAFA;QAGAC,cAHA;QAIAC,WAJA;QAKA;QACA;QACA;QACAC,gBARA;QASAC;MATA,CAHA;MAcAC;QACAN,QACA;UAAAO;UAAAC;UAAAC;QAAA,CADA,CADA;QAKAR,WACA;UAAAM;UAAAC;UAAAC;QAAA,CADA;MALA,CAdA;MAuBAC;IAvBA;EA0BA,CA7BA;;EA8BAC,oBA9BA;;EA8BA;EACAC;IACA;IACA;IACA;MACA;IACA,CALA,CAMA;;;IACA,4CAPA,CAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA,CA/CA;;EAgDAC,sBAhDA;EAiDAC;IAEA;IACA;MACA,sEADA,CACA;;MACA,sBAFA,CAGA;;MACA;QACA;QACA;QACA;QAEAjB;UACAkB;UACAA,6BAFA,CAEA;;UACA;QACA,CAJA;MAKA;;MACA;QAAAC;QAAAC;QAAAjB;QAAAC;QAAAI;QAAAF;MAAA,aAfA,CAiBA;MACA;MACA;MACA;;MAEA;QAAAH;QAAAC;QAAAgB;QAAAD;QAAAb;QAAAE;MAAA,EAtBA,CAuBA;IACA,CA3BA;;IA6BAa;MACA;QACA;UACA;;UACA;YACAC;UACA;;UACA;YACAH,YADA;YACA;YACAC,qBAFA;YAGAjB,sBAHA;YAIAC,4BAJA;YAKAI,kCALA;YAMA;YACAF,0BAPA;YAQAiB,mDARA,CAQA;;UARA,GAUAC,IAVA,CAUAC;YACA;cAAAC;cAAAC;YAAA;;YACA;cACA;gBACAhB,eADA;gBAEAiB;cAFA;cAIA;YACA;UACA,CAnBA;QAoBA,CAzBA,MAyBA;UACA;YACAjB,iBADA;YAEAiB;UAFA;UAIA;QACA;MACA,CAjCA;IAkCA,CAhEA;;IAiEA;IACAC;MACA;QACAC,uBADA;QAEAC,sBAFA;QAGAH;MAHA,GAIAJ,IAJA,CAIA;QACA;QACA,0BAFA,CAEA;MACA,CAPA,EAOAQ,KAPA,CAOA,OAEA,CATA;IAUA,CA7EA;;IA8EA;IACAC;MACA;QACAH,uBADA;QAEAC,sBAFA;QAGAH;MAHA,GAIAJ,IAJA,CAIA;QACA;QACA;MACA,CAPA,EAOAQ,KAPA,CAOA,OACA,CARA;IASA;;EAzFA;AAjDA", "names": ["name", "data", "user", "form", "title", "overTime", "officeName", "content", "attachmentId", "auditStatus", "rules", "required", "message", "trigger", "file", "props", "mounted", "inject", "methods", "item", "id", "evaluationId", "submitForm", "url", "attchmentId", "then", "res", "<PERSON><PERSON><PERSON>", "errmsg", "type", "resetForm", "confirmButtonText", "cancelButtonText", "catch", "resetForm1"], "sourceRoot": "src/views/AssessmentOrgan/BusinessObjectives", "sources": ["newFinishDetail.vue"], "sourcesContent": ["<template>\r\n  <!-- 新建/编辑 完成情况 -->\r\n  <div class=\"newFinishDetail\">\r\n    <div class=\"add-form-title\">{{ uid? '编辑完成情况' : '新建完成情况'}}</div>\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"qd-form\">\r\n      <el-form-item label=\"标题\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"title\">\r\n        <el-input placeholder=\"请输入标题\"\r\n                  v-model=\"form.title\">\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"完成时间\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"overTime\">\r\n        <el-date-picker v-model=\"form.overTime\"\r\n                        type=\"datetime\"\r\n                        value-format=\"timestamp\"\r\n                        placeholder=\"选择完成时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <br>\r\n      <el-form-item label=\"上传附件\"\r\n                    class=\"form-item-wd100 form-upload\">\r\n        <zy-upload-file ref=\"upload\"\r\n                        module=\"notice\"\r\n                        :data=\"file\"\r\n                        :max=\"10\"\r\n                        placeholder=\"仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式\"></zy-upload-file>\r\n      </el-form-item>\r\n      <el-form-item label=\"内容\"\r\n                    class=\"form-item-wd100\">\r\n        <wang-editor v-model='form.content'></wang-editor>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"form-footer-btn\">\r\n      <el-button type=\"primary\"\r\n                 size=\"small\"\r\n                 @click=\"submitForm('form')\">提交</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm1('form')\">重置</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm('form')\">取消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'newFinishDetail',\r\n  data () {\r\n    return {\r\n      // id: this.$route.query.id,\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())),\r\n      form: {\r\n        title: '',\r\n        overTime: '',\r\n        officeName: '',\r\n        content: '',\r\n        // evaluationId: this.$route.query.mid,\r\n        // id: this.$route.query.id,\r\n        // ******\r\n        attachmentId: '',\r\n        auditStatus: ''\r\n      },\r\n      rules: {\r\n        title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n\r\n        overTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ]\r\n      },\r\n      file: []\r\n\r\n    }\r\n  },\r\n  props: ['id', 'uid'], // TODO: 为newFinishDetail组件设置两个属性 id:业务工作目标的id, uid:点击完成情况各列表的id\r\n  mounted () {\r\n    // console.log(this.id)\r\n    // this.form.overTime = this.$format()\r\n    if (this.uid) {\r\n      this.getFinishDetail()\r\n    }\r\n    // 需根据登录信息做判断\r\n    this.form.officeName = this.user.officeName\r\n    // TODO:测试环境暂时注释,提交时再解开*************************************************************************************\r\n    // 登录的user.otherInfo.IsEvaluationAdmin（是否考核管理员）如果等于true,则下面信息回显当前登录人的机构和用户信息，且为可编辑状态，可编辑状态下，调用选人点point_21选择人员并回显机构和人员信息，否则只回显当前登录人的机构和用户信息，不可编辑\r\n    // if (this.user.otherInfo.IsEvaluationAdmin) {\r\n    //   this.disabled = false\r\n    // } else {\r\n    //   this.disabled = true\r\n    // }\r\n    // *************************************************************************************************************************\r\n  },\r\n  inject: ['tabDelJump'],\r\n  methods: {\r\n\r\n    // 获取 完成情况详情\r\n    async getFinishDetail () {\r\n      const res = await this.$api.AssessmentOrgan.reqfinishDetail(this.uid) // uid\r\n      const data = res.data\r\n      // console.log('file1', this.file)\r\n      if (data.attachmentInfo) {\r\n        // data.attachmentInfo.uid = data.attachmentInfo.id\r\n        // data.attachmentInfo.fileName = data.attachmentInfo.oldName // 附件名称\r\n        // this.file.push(data.attachmentInfo)\r\n\r\n        data.attachmentInfo.forEach(item => {\r\n          item.uid = item.id\r\n          item.fileName = item.oldName // 附件名称\r\n          this.file.push(item)\r\n        })\r\n      }\r\n      const { id, evaluationId, title, overTime, auditStatus, content } = res.data\r\n\r\n      // this.file = attachment //报错原因:传入的附件attachment为对象 不能直接赋值给file数组\r\n      // for (const i in attachment) { // 方法:将对象转为数组(因为传入的附件数据为对象)\r\n      //   this.file.push(attachment[i])\r\n      // }\r\n\r\n      this.form = { title, overTime, evaluationId, id, content, auditStatus }\r\n      // console.log('@@@查看是否能收到审核状态auditStatus', this.form)\r\n    },\r\n\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var url = '/functionalperformance/add?'\r\n          if (this.uid) {\r\n            url = '/functionalperformance/edit?'\r\n          }\r\n          this.$api.AssessmentOrgan.reqAddfinishDetail(url, {\r\n            id: this.uid, // 完成情况列表的id\r\n            evaluationId: this.id,\r\n            title: this.form.title,\r\n            overTime: this.form.overTime,\r\n            auditStatus: this.form.auditStatus,\r\n            // ****************************\r\n            content: this.form.content,\r\n            attchmentId: this.$refs.upload.obtainId().join(',') // 获取附件id\r\n\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('newCallback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    resetForm () {\r\n      this.$confirm('是否离开当前页面', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tabDelJump()\r\n        this.$emit('newCallback') // 关闭弹窗并更新列表\r\n      }).catch(() => {\r\n\r\n      })\r\n    },\r\n    // 重置按钮\r\n    resetForm1 () {\r\n      this.$confirm('是否重置当前输入的内容', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.form.content = ''\r\n        this.$refs.form.resetFields()\r\n      }).catch(() => {\r\n      })\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.newFinishDetail {\r\n  width: 1000px;\r\n  height: 670px;\r\n  margin-bottom: 20px;\r\n  background-color: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);\r\n  padding: 20px 30px;\r\n\r\n  .wang-editor {\r\n    //解决内容输入过长时能自动换行\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n  }\r\n}\r\n</style>\r\n"]}]}