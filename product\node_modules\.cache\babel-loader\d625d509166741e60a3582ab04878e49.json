{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarChart.vue", "mtime": 1756371347097}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,uBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,aADA;MAEAC;IAFA,CALA;IASAE;MACAH,YADA;MAEAC;IAFA,CATA;IAaAG;MACAJ,WADA;MAEAC,cAFA;MAGAI;IAHA;EAbA,CAFA;;EAqBAC;IACA;MACAC;IADA;EAGA,CAzBA;;EA0BAC;IACA;EACA,CA5BA;;EA6BAC;IACA;MACA;IACA;;IACAC;EACA,CAlCA;;EAmCAC;IACAP;MACAQ;QACA;MACA,CAHA;;MAIAC;IAJA;EADA,CAnCA;EA2CAC;IACAC;MACA;MACA;MACA;MACA;MACAL;IACA,CAPA;;IAQAM;MACA;MACA;MACA;QACAC;UACAC,qBADA;UAEAZ,uBAFA;UAGAa,SAHA;UAIAC,cAJA;UAKAC;YACAC,aADA;YAEAC;UAFA,CALA;UASAC,aATA;UAUAC;QAVA,CADA;QAaAC;UACAN,oDADA;UAEAO,qDAFA;UAGAC,uDAHA;UAIAT,qDAJA;UAKAU;QALA,CAbA;QAoBAC;UACA9B,gBADA;UAEAM,eAFA;UAGAyB;YACAb,UADA;YAEAc;cACAV;YADA;UAFA,CAHA;UASAW;YACAf;UADA,CATA;UAYAgB;YACAhB,UADA;YAEAI,gBAFA;YAGAC,oDAHA;YAIAY,WAJA;YAKAC,SALA;YAMAC,SANA;YAOAC;UAPA;QAZA,CApBA;QA0CAC;UACAvC,aADA;UAEAwC,cAFA;UAGAT;YACAb;UADA,CAHA;UAMAe;YACAf;UADA,CANA;UASAgB;YACAhB,UADA;YAEAI,gBAFA;YAGAC;UAHA,CATA;UAcAkB;YACAvB,UADA;YAEAc;cACAV,iCADA;cAEAtB;YAFA;UAFA;QAdA,CA1CA;QAgEA0C,SACA;UACA1C,WADA;UAEAH,qBAFA;UAGAS,gBAHA;UAIAqC,YAJA;UAKAC,+BALA;UAKA;UACAC;YACAvB,iCADA;YAEAwB;UAFA,CANA;UAUAC;YACAzB;cACAtB,cADA;cAEAgD,IAFA;cAGAC,IAHA;cAIAC,KAJA;cAKAC,KALA;cAMAC,aACA;gBAAAC;gBAAA/B;cAAA,CADA,EAEA;gBAAA+B;gBAAA/B;cAAA,CAFA;YANA,CADA;YAYAwB;UAZA,CAVA;UAwBAQ;YACAP;cACAzB;gBACAtB,cADA;gBAEAgD,IAFA;gBAGAC,IAHA;gBAIAC,KAJA;gBAKAC,KALA;gBAMAC,aACA;kBAAAC;kBAAA/B;gBAAA,CADA,EAEA;kBAAA+B;kBAAA/B;gBAAA,CAFA,EAGA;kBAAA+B;kBAAA/B;gBAAA,CAHA;cANA;YADA;UADA,CAxBA;UAwCAiC;YACArC,UADA;YAEAsC,eAFA;YAGAlC,gBAHA;YAIAC;UAJA;QAxCA,CADA,CAhEA;QAiHAkC;UACAC,eADA;UAEAC,qCAFA;UAGAC,sBAHA;UAIAC,cAJA;UAKAxC;YACAC;UADA,CALA;UAQAwC;YACA9D;UADA,CARA;UAWAsC;YACA;YACA;UACA;QAdA;MAjHA;IAkIA,CA7IA;;IA8IAyB;MACA;MACA;IACA,CAjJA;;IAkJAC;MACA;QACA;MACA;IACA,CAtJA;;IAuJAC;MACA;MACA;QACA;QACA;UACA;UACA;YACA;UACA,CAJA,CAKA;;;UACA;UACA;QACA;MACA;;MACA;IACA;;EAtKA;AA3CA", "names": ["name", "props", "id", "type", "required", "legendShow", "<PERSON><PERSON><PERSON>", "chartData", "default", "data", "chart", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "window", "watch", "handler", "deep", "methods", "initChart", "getOption", "legend", "show", "top", "left", "textStyle", "color", "fontSize", "itemWidth", "itemHeight", "grid", "right", "bottom", "containLabel", "xAxis", "axisLine", "lineStyle", "axisTick", "axisLabel", "interval", "rotate", "margin", "formatter", "yAxis", "splitNumber", "splitLine", "series", "<PERSON><PERSON><PERSON><PERSON>", "showBackground", "backgroundStyle", "borderRadius", "itemStyle", "x", "y", "x2", "y2", "colorStops", "offset", "emphasis", "label", "position", "tooltip", "trigger", "backgroundColor", "borderColor", "borderWidth", "axisPointer", "<PERSON><PERSON><PERSON>", "resizeChart", "formatAxisLabel"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["BarChart.vue"], "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"discussion-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'DiscussionC<PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    legendShow: {\n      type: Boolean,\n      required: false\n    },\n    legendName: {\n      type: String,\n      required: ''\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  watch: {\n    chartData: {\n      handler () {\n        this.renderChart()\n      },\n      deep: true\n    }\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.renderChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n    getOption () {\n      const xAxisData = this.chartData.map(item => item.name)\n      const seriesData = this.chartData.map(item => item.value)\n      return {\n        legend: {\n          show: this.legendShow,\n          data: [this.legendName],\n          top: '2%',\n          left: 'center',\n          textStyle: {\n            color: '#fff',\n            fontSize: 12\n          },\n          itemWidth: 12,\n          itemHeight: 8\n        },\n        grid: {\n          left: this.id === 'committee_proposal' ? '0%' : '3%',\n          right: this.id === 'committee_proposal' ? '0%' : '3%',\n          bottom: this.id === 'committee_proposal' ? '20%' : '8%',\n          top: this.id === 'committee_proposal' ? '10%' : '10%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData,\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.3)'\n            }\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#A2B0B8',\n            fontSize: this.id === 'committee_proposal' ? 12 : 14,\n            interval: 0,\n            rotate: 0,\n            margin: 8,\n            formatter: this.id === 'committee_proposal' ? this.formatAxisLabel : null\n          }\n        },\n        yAxis: {\n          type: 'value',\n          splitNumber: 4,\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#A2B0B8',\n            fontSize: 14\n          },\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              type: 'dashed'\n            }\n          }\n        },\n        series: [\n          {\n            type: 'bar',\n            name: this.legendName,\n            data: seriesData,\n            barWidth: 25,\n            showBackground: this.legendShow, // 显示背景\n            backgroundStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              borderRadius: [2, 2, 0, 0]\n            },\n            itemStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: 'rgba(31, 198, 255, 1)' },\n                  { offset: 1, color: 'rgba(31, 198, 255, 0)' }\n                ]\n              },\n              borderRadius: [2, 2, 0, 0]\n            },\n            emphasis: {\n              itemStyle: {\n                color: {\n                  type: 'linear',\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: '#00E4FF' },\n                    { offset: 0.5, color: '#0090FF' },\n                    { offset: 1, color: '#005090' }\n                  ]\n                }\n              }\n            },\n            label: {\n              show: true,\n              position: 'top',\n              color: '#FFFFFF',\n              fontSize: 12\n            }\n          }\n        ],\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00D4FF',\n          borderWidth: 1,\n          textStyle: {\n            color: '#FFFFFF'\n          },\n          axisPointer: {\n            type: 'shadow'\n          },\n          formatter: function (params) {\n            const data = params[0]\n            return `${data.name}<br/>人数: ${data.value}人`\n          }\n        }\n      }\n    },\n    renderChart () {\n      if (!this.chart || !this.chartData.length) return\n      this.chart.setOption(this.getOption(), true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    },\n    formatAxisLabel (value) {\n      // 为committee_proposal处理文本换行\n      if (this.id === 'committee_proposal') {\n        // 根据文本长度进行换行处理\n        if (value.length > 4) {\n          // 如果包含\"委\"字，在\"委\"字后换行\n          if (value.includes('委') && value.indexOf('委') < value.length - 1) {\n            return value.replace('委', '委\\n')\n          }\n          // 否则在中间位置换行\n          const mid = Math.ceil(value.length / 2)\n          return value.substring(0, mid) + '\\n' + value.substring(mid)\n        }\n      }\n      return value\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.discussion-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}