{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarChart.vue", "mtime": 1756343512133}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICogYXMgZWNoYXJ0cyBmcm9tICdlY2hhcnRzJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdEaXNjdXNzaW9uQ2hhcnQnLAogIHByb3BzOiB7CiAgICBpZDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgY2hhcnREYXRhOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0KICB9LAoKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY2hhcnQ6IG51bGwKICAgIH07CiAgfSwKCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuaW5pdENoYXJ0KCk7CiAgfSwKCiAgYmVmb3JlRGVzdHJveSgpIHsKICAgIGlmICh0aGlzLmNoYXJ0KSB7CiAgICAgIHRoaXMuY2hhcnQuZGlzcG9zZSgpOwogICAgfQoKICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLnJlc2l6ZUNoYXJ0KTsKICB9LAoKICB3YXRjaDogewogICAgY2hhcnREYXRhOiB7CiAgICAgIGhhbmRsZXIoKSB7CiAgICAgICAgdGhpcy5yZW5kZXJDaGFydCgpOwogICAgICB9LAoKICAgICAgZGVlcDogdHJ1ZQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgaW5pdENoYXJ0KCkgewogICAgICBjb25zdCBjaGFydENvbnRhaW5lciA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKHRoaXMuaWQpOwogICAgICBpZiAoIWNoYXJ0Q29udGFpbmVyKSByZXR1cm47CiAgICAgIHRoaXMuY2hhcnQgPSBlY2hhcnRzLmluaXQoY2hhcnRDb250YWluZXIpOwogICAgICB0aGlzLnJlbmRlckNoYXJ0KCk7CiAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLnJlc2l6ZUNoYXJ0KTsKICAgIH0sCgogICAgZ2V0T3B0aW9uKCkgewogICAgICBjb25zdCB4QXhpc0RhdGEgPSB0aGlzLmNoYXJ0RGF0YS5tYXAoaXRlbSA9PiBpdGVtLm5hbWUpOwogICAgICBjb25zdCBzZXJpZXNEYXRhID0gdGhpcy5jaGFydERhdGEubWFwKGl0ZW0gPT4gaXRlbS52YWx1ZSk7CiAgICAgIHJldHVybiB7CiAgICAgICAgbGVnZW5kOiB7CiAgICAgICAgICBzaG93OiB0aGlzLmlkID09PSAnY29tbWl0dGVlX3Byb3Bvc2FsJyA/IHRydWUgOiBudWxsLAogICAgICAgICAgZGF0YTogWyfmj5DkuqTku7bmlbAnXSwKICAgICAgICAgIHRvcDogJzIlJywKICAgICAgICAgIGxlZnQ6ICdjZW50ZXInLAogICAgICAgICAgdGV4dFN0eWxlOiB7CiAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsCiAgICAgICAgICAgIGZvbnRTaXplOiAxMgogICAgICAgICAgfSwKICAgICAgICAgIGl0ZW1XaWR0aDogMTIsCiAgICAgICAgICBpdGVtSGVpZ2h0OiA4CiAgICAgICAgfSwKICAgICAgICBncmlkOiB7CiAgICAgICAgICBsZWZ0OiB0aGlzLmlkID09PSAnY29tbWl0dGVlX3Byb3Bvc2FsJyA/ICcwJScgOiAnMyUnLAogICAgICAgICAgcmlnaHQ6IHRoaXMuaWQgPT09ICdjb21taXR0ZWVfcHJvcG9zYWwnID8gJzAlJyA6ICczJScsCiAgICAgICAgICBib3R0b206IHRoaXMuaWQgPT09ICdjb21taXR0ZWVfcHJvcG9zYWwnID8gJzIwJScgOiAnOCUnLAogICAgICAgICAgdG9wOiB0aGlzLmlkID09PSAnY29tbWl0dGVlX3Byb3Bvc2FsJyA/ICcxMCUnIDogJzEwJScsCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUKICAgICAgICB9LAogICAgICAgIHhBeGlzOiB7CiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLAogICAgICAgICAgZGF0YTogeEF4aXNEYXRhLAogICAgICAgICAgYXhpc0xpbmU6IHsKICAgICAgICAgICAgc2hvdzogdHJ1ZSwKICAgICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyknCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICBheGlzVGljazogewogICAgICAgICAgICBzaG93OiBmYWxzZQogICAgICAgICAgfSwKICAgICAgICAgIGF4aXNMYWJlbDogewogICAgICAgICAgICBzaG93OiB0cnVlLAogICAgICAgICAgICBjb2xvcjogJyNBMkIwQjgnLAogICAgICAgICAgICBmb250U2l6ZTogdGhpcy5pZCA9PT0gJ2NvbW1pdHRlZV9wcm9wb3NhbCcgPyAxMiA6IDE0LAogICAgICAgICAgICBpbnRlcnZhbDogMCwKICAgICAgICAgICAgcm90YXRlOiAwLAogICAgICAgICAgICBtYXJnaW46IDgsCiAgICAgICAgICAgIGZvcm1hdHRlcjogdGhpcy5pZCA9PT0gJ2NvbW1pdHRlZV9wcm9wb3NhbCcgPyB0aGlzLmZvcm1hdEF4aXNMYWJlbCA6IG51bGwKICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIHlBeGlzOiB7CiAgICAgICAgICB0eXBlOiAndmFsdWUnLAogICAgICAgICAgc3BsaXROdW1iZXI6IDQsCiAgICAgICAgICBheGlzTGluZTogewogICAgICAgICAgICBzaG93OiBmYWxzZQogICAgICAgICAgfSwKICAgICAgICAgIGF4aXNUaWNrOiB7CiAgICAgICAgICAgIHNob3c6IGZhbHNlCiAgICAgICAgICB9LAogICAgICAgICAgYXhpc0xhYmVsOiB7CiAgICAgICAgICAgIHNob3c6IHRydWUsCiAgICAgICAgICAgIGNvbG9yOiAnI0EyQjBCOCcsCiAgICAgICAgICAgIGZvbnRTaXplOiAxNAogICAgICAgICAgfSwKICAgICAgICAgIHNwbGl0TGluZTogewogICAgICAgICAgICBzaG93OiB0cnVlLAogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKScsCiAgICAgICAgICAgICAgdHlwZTogJ2Rhc2hlZCcKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgc2VyaWVzOiB0aGlzLmlkID09PSAnY29tbWl0dGVlX3Byb3Bvc2FsJyA/IFt7CiAgICAgICAgICB0eXBlOiAnYmFyJywKICAgICAgICAgIG5hbWU6ICfmj5DkuqTku7bmlbAnLAogICAgICAgICAgZGF0YTogc2VyaWVzRGF0YSwKICAgICAgICAgIGJhcldpZHRoOiAyNSwKICAgICAgICAgIHNob3dCYWNrZ3JvdW5kOiB0cnVlLAogICAgICAgICAgLy8g5pi+56S66IOM5pmvCiAgICAgICAgICBiYWNrZ3JvdW5kU3R5bGU6IHsKICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSknLAogICAgICAgICAgICBib3JkZXJSYWRpdXM6IFsyLCAyLCAwLCAwXQogICAgICAgICAgfSwKICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICBjb2xvcjogewogICAgICAgICAgICAgIHR5cGU6ICdsaW5lYXInLAogICAgICAgICAgICAgIHg6IDAsCiAgICAgICAgICAgICAgeTogMCwKICAgICAgICAgICAgICB4MjogMCwKICAgICAgICAgICAgICB5MjogMSwKICAgICAgICAgICAgICBjb2xvclN0b3BzOiBbewogICAgICAgICAgICAgICAgb2Zmc2V0OiAwLAogICAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDMxLCAxOTgsIDI1NSwgMSknCiAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgb2Zmc2V0OiAxLAogICAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDMxLCAxOTgsIDI1NSwgMCknCiAgICAgICAgICAgICAgfV0KICAgICAgICAgICAgfSwKICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiBbMiwgMiwgMCwgMF0KICAgICAgICAgIH0KICAgICAgICB9XSA6IFt7CiAgICAgICAgICB0eXBlOiAnYmFyJywKICAgICAgICAgIGRhdGE6IHNlcmllc0RhdGEsCiAgICAgICAgICBiYXJXaWR0aDogMjAsCiAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgY29sb3I6IHsKICAgICAgICAgICAgICB0eXBlOiAnbGluZWFyJywKICAgICAgICAgICAgICB4OiAwLAogICAgICAgICAgICAgIHk6IDAsCiAgICAgICAgICAgICAgeDI6IDAsCiAgICAgICAgICAgICAgeTI6IDEsCiAgICAgICAgICAgICAgY29sb3JTdG9wczogW3sKICAgICAgICAgICAgICAgIG9mZnNldDogMCwKICAgICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgzMSwgMTk4LCAyNTUsIDEpJwogICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgIG9mZnNldDogMSwKICAgICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgzMSwgMTk4LCAyNTUsIDApJwogICAgICAgICAgICAgIH1dCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGJvcmRlclJhZGl1czogWzIsIDIsIDAsIDBdCiAgICAgICAgICB9LAogICAgICAgICAgZW1waGFzaXM6IHsKICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6IHsKICAgICAgICAgICAgICAgIHR5cGU6ICdsaW5lYXInLAogICAgICAgICAgICAgICAgeDogMCwKICAgICAgICAgICAgICAgIHk6IDAsCiAgICAgICAgICAgICAgICB4MjogMCwKICAgICAgICAgICAgICAgIHkyOiAxLAogICAgICAgICAgICAgICAgY29sb3JTdG9wczogW3sKICAgICAgICAgICAgICAgICAgb2Zmc2V0OiAwLAogICAgICAgICAgICAgICAgICBjb2xvcjogJyMwMEU0RkYnCiAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgIG9mZnNldDogMC41LAogICAgICAgICAgICAgICAgICBjb2xvcjogJyMwMDkwRkYnCiAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgIG9mZnNldDogMSwKICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMDA1MDkwJwogICAgICAgICAgICAgICAgfV0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICBzaG93OiBmYWxzZSwKICAgICAgICAgICAgcG9zaXRpb246ICd0b3AnLAogICAgICAgICAgICBjb2xvcjogJyMwMEQ0RkYnLAogICAgICAgICAgICBmb250U2l6ZTogMTIsCiAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJwogICAgICAgICAgfQogICAgICAgIH1dLAogICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywKICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMCwgMCwgMCwgMC44KScsCiAgICAgICAgICBib3JkZXJDb2xvcjogJyMwMEQ0RkYnLAogICAgICAgICAgYm9yZGVyV2lkdGg6IDEsCiAgICAgICAgICB0ZXh0U3R5bGU6IHsKICAgICAgICAgICAgY29sb3I6ICcjRkZGRkZGJwogICAgICAgICAgfSwKICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gKHBhcmFtcykgewogICAgICAgICAgICBjb25zdCBkYXRhID0gcGFyYW1zWzBdOwogICAgICAgICAgICByZXR1cm4gYCR7ZGF0YS5uYW1lfTxici8+5Lq65pWwOiAke2RhdGEudmFsdWV95Lq6YDsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH07CiAgICB9LAoKICAgIHJlbmRlckNoYXJ0KCkgewogICAgICBpZiAoIXRoaXMuY2hhcnQgfHwgIXRoaXMuY2hhcnREYXRhLmxlbmd0aCkgcmV0dXJuOwogICAgICB0aGlzLmNoYXJ0LnNldE9wdGlvbih0aGlzLmdldE9wdGlvbigpLCB0cnVlKTsKICAgIH0sCgogICAgcmVzaXplQ2hhcnQoKSB7CiAgICAgIGlmICh0aGlzLmNoYXJ0KSB7CiAgICAgICAgdGhpcy5jaGFydC5yZXNpemUoKTsKICAgICAgfQogICAgfSwKCiAgICBmb3JtYXRBeGlzTGFiZWwodmFsdWUpIHsKICAgICAgLy8g5Li6Y29tbWl0dGVlX3Byb3Bvc2Fs5aSE55CG5paH5pys5o2i6KGMCiAgICAgIGlmICh0aGlzLmlkID09PSAnY29tbWl0dGVlX3Byb3Bvc2FsJykgewogICAgICAgIC8vIOagueaNruaWh+acrOmVv+W6pui/m+ihjOaNouihjOWkhOeQhgogICAgICAgIGlmICh2YWx1ZS5sZW5ndGggPiA0KSB7CiAgICAgICAgICAvLyDlpoLmnpzljIXlkKsi5aeUIuWtl++8jOWcqCLlp5Qi5a2X5ZCO5o2i6KGMCiAgICAgICAgICBpZiAodmFsdWUuaW5jbHVkZXMoJ+WnlCcpICYmIHZhbHVlLmluZGV4T2YoJ+WnlCcpIDwgdmFsdWUubGVuZ3RoIC0gMSkgewogICAgICAgICAgICByZXR1cm4gdmFsdWUucmVwbGFjZSgn5aeUJywgJ+WnlFxuJyk7CiAgICAgICAgICB9IC8vIOWQpuWImeWcqOS4remXtOS9jee9ruaNouihjAoKCiAgICAgICAgICBjb25zdCBtaWQgPSBNYXRoLmNlaWwodmFsdWUubGVuZ3RoIC8gMik7CiAgICAgICAgICByZXR1cm4gdmFsdWUuc3Vic3RyaW5nKDAsIG1pZCkgKyAnXG4nICsgdmFsdWUuc3Vic3RyaW5nKG1pZCk7CiAgICAgICAgfQogICAgICB9CgogICAgICByZXR1cm4gdmFsdWU7CiAgICB9CgogIH0KfTs="}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,uBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,WADA;MAEAC,cAFA;MAGAE;IAHA;EALA,CAFA;;EAaAC;IACA;MACAC;IADA;EAGA,CAjBA;;EAkBAC;IACA;EACA,CApBA;;EAqBAC;IACA;MACA;IACA;;IACAC;EACA,CA1BA;;EA2BAC;IACAP;MACAQ;QACA;MACA,CAHA;;MAIAC;IAJA;EADA,CA3BA;EAmCAC;IACAC;MACA;MACA;MACA;MACA;MACAL;IACA,CAPA;;IAQAM;MACA;MACA;MACA;QACAC;UACAC,oDADA;UAEAZ,cAFA;UAGAa,SAHA;UAIAC,cAJA;UAKAC;YACAC,aADA;YAEAC;UAFA,CALA;UASAC,aATA;UAUAC;QAVA,CADA;QAaAC;UACAN,oDADA;UAEAO,qDAFA;UAGAC,uDAHA;UAIAT,qDAJA;UAKAU;QALA,CAbA;QAoBAC;UACA5B,gBADA;UAEAI,eAFA;UAGAyB;YACAb,UADA;YAEAc;cACAV;YADA;UAFA,CAHA;UASAW;YACAf;UADA,CATA;UAYAgB;YACAhB,UADA;YAEAI,gBAFA;YAGAC,oDAHA;YAIAY,WAJA;YAKAC,SALA;YAMAC,SANA;YAOAC;UAPA;QAZA,CApBA;QA0CAC;UACArC,aADA;UAEAsC,cAFA;UAGAT;YACAb;UADA,CAHA;UAMAe;YACAf;UADA,CANA;UASAgB;YACAhB,UADA;YAEAI,gBAFA;YAGAC;UAHA,CATA;UAcAkB;YACAvB,UADA;YAEAc;cACAV,iCADA;cAEApB;YAFA;UAFA;QAdA,CA1CA;QAgEAwC,4CACA;UACAxC,WADA;UAEAH,YAFA;UAGAO,gBAHA;UAIAqC,YAJA;UAKAC,oBALA;UAKA;UACAC;YACAvB,iCADA;YAEAwB;UAFA,CANA;UAUAC;YACAzB;cACApB,cADA;cAEA8C,IAFA;cAGAC,IAHA;cAIAC,KAJA;cAKAC,KALA;cAMAC,aACA;gBAAAC;gBAAA/B;cAAA,CADA,EAEA;gBAAA+B;gBAAA/B;cAAA,CAFA;YANA,CADA;YAYAwB;UAZA;QAVA,CADA,IA0BA,CACA;UACA5C,WADA;UAEAI,gBAFA;UAGAqC,YAHA;UAIAI;YACAzB;cACApB,cADA;cAEA8C,IAFA;cAGAC,IAHA;cAIAC,KAJA;cAKAC,KALA;cAMAC,aACA;gBAAAC;gBAAA/B;cAAA,CADA,EAEA;gBAAA+B;gBAAA/B;cAAA,CAFA;YANA,CADA;YAYAwB;UAZA,CAJA;UAkBAQ;YACAP;cACAzB;gBACApB,cADA;gBAEA8C,IAFA;gBAGAC,IAHA;gBAIAC,KAJA;gBAKAC,KALA;gBAMAC,aACA;kBAAAC;kBAAA/B;gBAAA,CADA,EAEA;kBAAA+B;kBAAA/B;gBAAA,CAFA,EAGA;kBAAA+B;kBAAA/B;gBAAA,CAHA;cANA;YADA;UADA,CAlBA;UAkCAiC;YACArC,WADA;YAEAsC,eAFA;YAGAlC,gBAHA;YAIAC,YAJA;YAKAkC;UALA;QAlCA,CADA,CA1FA;QAsIAC;UACAC,eADA;UAEAC,qCAFA;UAGAC,sBAHA;UAIAC,cAJA;UAKAzC;YACAC;UADA,CALA;UAQAgB;YACA;YACA;UACA;QAXA;MAtIA;IAoJA,CA/JA;;IAgKAyB;MACA;MACA;IACA,CAnKA;;IAoKAC;MACA;QACA;MACA;IACA,CAxKA;;IAyKAC;MACA;MACA;QACA;QACA;UACA;UACA;YACA;UACA,CAJA,CAKA;;;UACA;UACA;QACA;MACA;;MACA;IACA;;EAxLA;AAnCA", "names": ["name", "props", "id", "type", "required", "chartData", "default", "data", "chart", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "window", "watch", "handler", "deep", "methods", "initChart", "getOption", "legend", "show", "top", "left", "textStyle", "color", "fontSize", "itemWidth", "itemHeight", "grid", "right", "bottom", "containLabel", "xAxis", "axisLine", "lineStyle", "axisTick", "axisLabel", "interval", "rotate", "margin", "formatter", "yAxis", "splitNumber", "splitLine", "series", "<PERSON><PERSON><PERSON><PERSON>", "showBackground", "backgroundStyle", "borderRadius", "itemStyle", "x", "y", "x2", "y2", "colorStops", "offset", "emphasis", "label", "position", "fontWeight", "tooltip", "trigger", "backgroundColor", "borderColor", "borderWidth", "<PERSON><PERSON><PERSON>", "resizeChart", "formatAxisLabel"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["BarChart.vue"], "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"discussion-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'DiscussionChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  watch: {\n    chartData: {\n      handler () {\n        this.renderChart()\n      },\n      deep: true\n    }\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.renderChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n    getOption () {\n      const xAxisData = this.chartData.map(item => item.name)\n      const seriesData = this.chartData.map(item => item.value)\n      return {\n        legend: {\n          show: this.id === 'committee_proposal' ? true : null,\n          data: ['提交件数'],\n          top: '2%',\n          left: 'center',\n          textStyle: {\n            color: '#fff',\n            fontSize: 12\n          },\n          itemWidth: 12,\n          itemHeight: 8\n        },\n        grid: {\n          left: this.id === 'committee_proposal' ? '0%' : '3%',\n          right: this.id === 'committee_proposal' ? '0%' : '3%',\n          bottom: this.id === 'committee_proposal' ? '20%' : '8%',\n          top: this.id === 'committee_proposal' ? '10%' : '10%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData,\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.3)'\n            }\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#A2B0B8',\n            fontSize: this.id === 'committee_proposal' ? 12 : 14,\n            interval: 0,\n            rotate: 0,\n            margin: 8,\n            formatter: this.id === 'committee_proposal' ? this.formatAxisLabel : null\n          }\n        },\n        yAxis: {\n          type: 'value',\n          splitNumber: 4,\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#A2B0B8',\n            fontSize: 14\n          },\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              type: 'dashed'\n            }\n          }\n        },\n        series: this.id === 'committee_proposal' ? [\n          {\n            type: 'bar',\n            name: '提交件数',\n            data: seriesData,\n            barWidth: 25,\n            showBackground: true, // 显示背景\n            backgroundStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              borderRadius: [2, 2, 0, 0]\n            },\n            itemStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: 'rgba(31, 198, 255, 1)' },\n                  { offset: 1, color: 'rgba(31, 198, 255, 0)' }\n                ]\n              },\n              borderRadius: [2, 2, 0, 0]\n            }\n          }\n        ] : [\n          {\n            type: 'bar',\n            data: seriesData,\n            barWidth: 20,\n            itemStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: 'rgba(31, 198, 255, 1)' },\n                  { offset: 1, color: 'rgba(31, 198, 255, 0)' }\n                ]\n              },\n              borderRadius: [2, 2, 0, 0]\n            },\n            emphasis: {\n              itemStyle: {\n                color: {\n                  type: 'linear',\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: '#00E4FF' },\n                    { offset: 0.5, color: '#0090FF' },\n                    { offset: 1, color: '#005090' }\n                  ]\n                }\n              }\n            },\n            label: {\n              show: false,\n              position: 'top',\n              color: '#00D4FF',\n              fontSize: 12,\n              fontWeight: 'bold'\n            }\n          }\n        ],\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00D4FF',\n          borderWidth: 1,\n          textStyle: {\n            color: '#FFFFFF'\n          },\n          formatter: function (params) {\n            const data = params[0]\n            return `${data.name}<br/>人数: ${data.value}人`\n          }\n        }\n      }\n    },\n    renderChart () {\n      if (!this.chart || !this.chartData.length) return\n      this.chart.setOption(this.getOption(), true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    },\n    formatAxisLabel (value) {\n      // 为committee_proposal处理文本换行\n      if (this.id === 'committee_proposal') {\n        // 根据文本长度进行换行处理\n        if (value.length > 4) {\n          // 如果包含\"委\"字，在\"委\"字后换行\n          if (value.includes('委') && value.indexOf('委') < value.length - 1) {\n            return value.replace('委', '委\\n')\n          }\n          // 否则在中间位置换行\n          const mid = Math.ceil(value.length / 2)\n          return value.substring(0, mid) + '\\n' + value.substring(mid)\n        }\n      }\n      return value\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.discussion-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}