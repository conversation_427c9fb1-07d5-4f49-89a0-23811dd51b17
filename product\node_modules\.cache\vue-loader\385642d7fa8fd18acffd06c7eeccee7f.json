{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\CompilationColumn\\CompilationColumnNew\\CompilationColumnNew.vue?vue&type=style&index=0&id=15b010a2&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\CompilationColumn\\CompilationColumnNew\\CompilationColumnNew.vue", "mtime": 1752541697662}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL0NvbXBpbGF0aW9uQ29sdW1uTmV3LnNjc3MiOw0K"}, {"version": 3, "sources": ["CompilationColumnNew.vue"], "names": [], "mappings": ";AAmJA", "file": "CompilationColumnNew.vue", "sourceRoot": "src/views/wisdomWarehouse/CompilationColumn/CompilationColumnNew", "sourcesContent": ["<template>\r\n  <div class=\"CompilationColumnNew\">\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-position=\"top\"\r\n             class=\"newForm\">\r\n      <el-form-item label=\"栏目名称\"\r\n                    class=\"form-input\"\r\n                    prop=\"name\">\r\n        <el-input placeholder=\"请输入栏目名称\"\r\n                  v-model=\"form.name\"\r\n                  clearable>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"所属年份\" props=\"year\">\r\n            <el-date-picker\r\n      v-model=\"form.year\"\r\n      type=\"year\"\r\n      placeholder=\"选择年\">\r\n    </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"排序\"\r\n                    class=\"form-input\"\r\n                    prop=\"sort\">\r\n        <el-input-number placeholder=\"请输入排序\"\r\n                         :min=\"1\"\r\n                         style=\"width:296px;\"\r\n                         v-model=\"form.sort\"\r\n                         clearable></el-input-number>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否置顶\"\r\n                    class=\"form-input\">\r\n        <el-radio-group v-model=\"form.isTop\">\r\n          <el-radio label=\"1\">置顶</el-radio>\r\n          <el-radio label=\"0\">不置顶</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否APP显示\"\r\n                    class=\"form-input\">\r\n        <el-radio-group v-model=\"form.isPushApp\">\r\n          <el-radio label=\"1\">是</el-radio>\r\n          <el-radio label=\"0\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <div class=\"form-button\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'CompilationColumnNew',\r\n  data () {\r\n    return {\r\n      menu: [],\r\n      form: {\r\n        name: '',\r\n        superior: '',\r\n        sort: 0,\r\n        isTop: '1',\r\n        isPushApp: '1',\r\n        year: ''\r\n      },\r\n      rules: {\r\n        name: [\r\n          { required: true, message: '请输入栏目名称', trigger: 'blur' }\r\n        ],\r\n        sort: [\r\n          { required: true, message: '请输入排序', trigger: 'blur' }\r\n        ],\r\n        year: [\r\n          { required: true, message: '请输入排序', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  props: ['id', 'modules'],\r\n  mounted () {\r\n    this.informationColumnTree()\r\n    if (this.id) {\r\n      this.informationColumnInfo()\r\n    }\r\n  },\r\n  methods: {\r\n    async informationColumnTree () {\r\n      // const res = await this.$api.wisdomWarehouse.informationColumnTree({ module: this.modules })\r\n      // var { data } = res\r\n      // this.menu = data\r\n    },\r\n    async informationColumnInfo () {\r\n      const res = await this.$api.wisdomWarehouse.assemblycolumnInfo(this.id)\r\n      var { data: { parentId, sort, name, isTop, isApp, year } } = res\r\n      this.form.superior = parentId\r\n      this.form.name = name\r\n      this.form.sort = sort\r\n      this.form.isTop = isTop\r\n      this.form.isPushApp = isApp\r\n      this.form.year = year\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.superior === '') {\r\n            this.form.superior = 1\r\n          }\r\n          var url = '/assemblycolumn/add'\r\n          if (this.id) {\r\n            url = '/assemblycolumn/edit'\r\n          }\r\n          this.$api.wisdomWarehouse.assemblycolumn(url, {\r\n            id: this.id,\r\n            name: this.form.name,\r\n            sort: this.form.sort,\r\n            isTop: this.form.isTop,\r\n            year: new Date(this.form.year).getFullYear(),\r\n            isApp: this.form.isPushApp\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('newCallback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('newCallback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./CompilationColumnNew.scss\";\r\n</style>\r\n"]}]}