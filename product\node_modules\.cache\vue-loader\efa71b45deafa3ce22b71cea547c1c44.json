{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\ThreeActivities\\ThreeActivities.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\ThreeActivities\\ThreeActivities.vue", "mtime": 1752541693881}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ThreeActivities.vue"], "names": [], "mappings": ";AA8MA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "ThreeActivities.vue", "sourceRoot": "src/views/AssessmentOrgan/ThreeActivities", "sourcesContent": ["<template>\r\n  <!-- 三双活动 -->\r\n  <div class=\"ThreeActivities\">\r\n    <search-box @search-click=\"search\" @reset-click=\"reset\" title=\"筛选\">\r\n      <!--关键字查找框  -->\r\n      <zy-widget label=\"关键字\">\r\n        <el-input\r\n          placeholder=\"请输入关键词\"\r\n          v-model=\"form.keyword\"\r\n          clearable\r\n          @keyup.enter.native=\"search\"\r\n        >\r\n          <div slot=\"prefix\" class=\"input-search\"></div>\r\n        </el-input>\r\n      </zy-widget>\r\n\r\n      <!-- 时间查询 -->\r\n      <zy-widget label=\"时间查询\">\r\n        <!--  下拉列表中 选中年份功能  -->\r\n        <el-select\r\n          v-model=\"selectedYear\"\r\n          placeholder=\"请选择年份\"\r\n          @keyup.enter.native=\"search\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in timeArr\"\r\n            :key=\"item.id\"\r\n            :label=\"item.value\"\r\n            :value=\"item.id\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n\r\n      <!-- 部门查询(用到机构树)  -->\r\n      <zy-widget label=\"部门查询\" v-permissions=\"'auth:three:department'\">\r\n        <zy-select\r\n          v-model=\"searchParams.officeId\"\r\n          clearable\r\n          @keyup.enter.native=\"search\"\r\n          placeholder=\"请选择部门\"\r\n          node-key=\"id\"\r\n          :data=\"officeData\"\r\n          style=\"width: 100%\"\r\n        >\r\n          <div slot=\"prefix\" class=\"input-search\"></div>\r\n        </zy-select>\r\n      </zy-widget>\r\n\r\n      <!-- 审核状态查询 -->\r\n      <zy-widget label=\"审核状态查询\">\r\n        <el-select\r\n          v-model=\"searchParams.auditStatusParams\"\r\n          filterable\r\n          clearable\r\n          placeholder=\"请选择审核状态\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in auditStatusData\"\r\n            :key=\"item.id\"\r\n            :label=\"item.value\"\r\n            :value=\"item.id\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n    </search-box>\r\n    <div class=\"qd-list-wrap\">\r\n      <div class=\"qd-btn-box\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          v-permissions=\"'auth:three:new'\"\r\n          @click=\"handleAdd\"\r\n          >新增\r\n        </el-button>\r\n\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-circle-check\"\r\n          v-permissions=\"'auth:three:checkPass'\"\r\n          @click=\"passClick(2)\"\r\n          >审核通过\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-remove-outline\"\r\n          v-permissions=\"'auth:three:checkNoPass'\"\r\n          @click=\"passClick(3)\"\r\n          >审核不通过\r\n        </el-button>\r\n      </div>\r\n      <div class=\"tableData\">\r\n        <zy-table>\r\n          <el-table\r\n            :data=\"tableData\"\r\n            slot=\"zytable\"\r\n            row-key=\"menuId\"\r\n            ref=\"multipleTable\"\r\n            @select=\"selected\"\r\n            @select-all=\"selectedAll\"\r\n          >\r\n            <el-table-column type=\"selection\" width=\"50\"> </el-table-column>\r\n            <el-table-column\r\n              label=\"活动名称\"\r\n              show-overflow-tooltip\r\n              prop=\"title\"\r\n              width=\"270px\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\" @click=\"modify(scope.row)\" size=\"small\">\r\n                  {{ scope.row.title }}\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"举办部门\" width=\"120px\" prop=\"officeName\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"开始时间\" width=\"170\">\r\n              <template slot-scope=\"scope\">\r\n                <div>{{ $format(scope.row.meetStartTime).substr(0, 16) }}</div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"结束时间\" width=\"170\">\r\n              <template slot-scope=\"scope\">\r\n                <div>{{ $format(scope.row.meetEndTime).substr(0, 16) }}</div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"活动状态\" width=\"100\">\r\n              <template slot-scope=\"scope\">\r\n                <div>{{ scope.row.status }}</div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"审核状态\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <!-- <div v-if=\"scope.row.auditStatus == '1' \"> 待审核 </div>\r\n                <div v-if=\"scope.row.auditStatus == '0' \"> 审核通过</div> -->\r\n                <div>{{ scope.row.auditStatus }}</div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column\r\n              label=\"活动类型\"\r\n              show-overflow-tooltip\r\n              min-width=\"110\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <div>{{ scope.row.activityTypeName }}</div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <!-- <el-button type=\"text\"\r\n                           @click=\"editClick(scope.row)\"\r\n                           size=\"small\"> 编辑\r\n                </el-button> -->\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"editClick(scope.row)\"\r\n                  size=\"small\"\r\n                  :disabled=\"scope.row.auditStatus == '审核通过'\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"handleDelete(scope.row.id, scope.row.auditStatus)\"\r\n                  :class=\"scope.row.auditStatus == '审核通过' ? '' : 'delBtn'\"\r\n                  :disabled=\"scope.row.auditStatus == '审核通过'\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </zy-table>\r\n      </div>\r\n\r\n      <div class=\"paging_box\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page.sync=\"currentPage\"\r\n          :page-sizes=\"[10, 20, 30, 40]\"\r\n          :page-size.sync=\"pageSize\"\r\n          background\r\n          layout=\"total, prev, pager, next, sizes, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <zy-pop-up v-model=\"show\" title=\"编辑\">\r\n      <OrganReviewNew :id=\"id\" @newCallback=\"newCallback\"> </OrganReviewNew>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport OrganReviewNew from './OrganReviewNew.vue'\r\n\r\nimport tableData from '@mixins/tableData'\r\n\r\nexport default {\r\n  name: 'ThreeActivities',\r\n  mixins: [tableData],\r\n  components: {\r\n    OrganReviewNew\r\n  },\r\n  data () {\r\n    return {\r\n      officeData: [], // 机构树,\r\n      auditStatusData: [], // 审核状态\r\n      searchParams: {\r\n\r\n        officeId: '',\r\n        auditStatusParams: ''\r\n\r\n      },\r\n      tableData: [],\r\n      timeArr: [],\r\n      selectedYear: '',\r\n\r\n      form: {\r\n        keyword: ''\r\n      },\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      total: 10,\r\n      show: false,\r\n      id: '',\r\n\r\n      choose: [],\r\n      selectObj: [],\r\n      selectData: []\r\n    }\r\n  },\r\n  mounted () {\r\n    this.initTime()\r\n    this.treeList()\r\n    this.dictionaryPubkvs()\r\n    this.getThreeActivitiesList()\r\n    const mune = JSON.parse(sessionStorage.getItem('menuChild'))\r\n    const path = this.$route.path\r\n    this.getPa(mune, path)\r\n  },\r\n  activated () {\r\n    this.getThreeActivitiesList()\r\n  },\r\n  inject: ['newTab'],\r\n\r\n  methods: {\r\n    getPa (data, path) {\r\n      data.forEach(item => {\r\n        if (item.children) {\r\n          this.getPa(item.children, path)\r\n        }\r\n        if (item.to === path) {\r\n          this.permissionsArr = item.permissions\r\n        }\r\n      })\r\n    },\r\n    /**\r\n  *字典\r\n*/\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluate_audit_status'\r\n      })\r\n      var { data } = res\r\n      this.auditStatusData = data.evaluate_audit_status\r\n    },\r\n\r\n    /**\r\n      *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    // 请求后台数据 初始化时间 在时间查询下拉框内\r\n    async initTime () {\r\n      const res = await this.$api.AssessmentOrgan.getMonthlyWorkTime()\r\n      var { data } = res\r\n      this.timeArr = data\r\n    },\r\n    newCallback () {\r\n      this.getThreeActivitiesList()\r\n      this.show = false\r\n    },\r\n    // // 编辑\r\n    // editClick (row) {\r\n    //   this.id = row.id\r\n    //   this.show = true\r\n    // },\r\n    search () { // 搜索\r\n      this.currentPage = 1\r\n      this.getThreeActivitiesList()\r\n    },\r\n    reset () { // 重置\r\n      this.form.keyword = ''\r\n      this.selectedYear = ''\r\n      this.searchParams.officeId = ''\r\n      this.searchParams.auditStatusParams = ''\r\n      this.getThreeActivitiesList()\r\n    },\r\n    // 新增按钮方法\r\n    handleAdd () {\r\n      const mid = new Date().getTime().toString()\r\n      this.newTab({\r\n        name: '新建三双活动',\r\n        menuId: mid,\r\n        to: '/newOrEditThreeActivities',\r\n        params: { mid, titleId: 1 }\r\n      })\r\n    },\r\n    // 删除按钮方法  批量删除\r\n    handleDelete (ids) { // TODO: handleDelete 和 handleBatchDelete 1.组合使用为新增/删除栏的删除功能 2.单独使用handleDelete则为操作项的删除功能\r\n      if (ids.auditStatus !== '审核通过') {\r\n        this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.$api.AssessmentOrgan.reqDelThree({ ids }).then((res) => {\r\n            if (res.errcode === 200) {\r\n              this.getThreeActivitiesList()// 删除后更新页面\r\n              this.$message.success('删除成功')\r\n            }\r\n          })\r\n        }).catch(() => {\r\n          this.$message.info('取消删除')\r\n          // this.getThreeActivitiesList()\r\n          return false\r\n        })\r\n      } else {\r\n        this.$message.info('不能删除审核通过项')\r\n        return false\r\n      }\r\n    },\r\n\r\n    handleBatchDelete () {\r\n      if (this.choose.length === 0) {\r\n        return this.$message.warning('请选择想要删除的项')\r\n      }\r\n      this.handleDelete(this.choose.join(','))\r\n    },\r\n\r\n    async getThreeActivitiesList () {\r\n      const res = await this.$api.AssessmentOrgan.reqThreeActivitiesList({\r\n        keyword: this.form.keyword,\r\n        pageNo: this.currentPage,\r\n        sedateId: this.selectedYear,\r\n        pageSize: this.pageSize,\r\n        officeId: this.searchParams.officeId, // 部门查询\r\n        auditStatus: this.searchParams.auditStatusParams // 审核状态\r\n      })\r\n      var { data, total } = res\r\n      const arr = []\r\n      data.forEach(item => {\r\n        if (item.submiterType) {\r\n          if (item.submiterType.indexOf('-') != -1) { // eslint-disable-line\r\n            item.submiterType = item.submiterType.split('-')[1]\r\n            arr.push(item)\r\n          } else {\r\n            arr.push(item)\r\n          }\r\n        } else {\r\n          arr.push(item)\r\n        }\r\n      })\r\n      // 这个暂时没写\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n      this.selectObj = []\r\n      this.selectData = []\r\n      this.$nextTick(function () {\r\n        this.memoryChecked()\r\n      })\r\n    },\r\n    modify (row) {\r\n      this.newTab({\r\n        name: '详情',\r\n        menuId: '1',\r\n        to: '/ThreeDetails',\r\n        params: {\r\n          id: row.id,\r\n          approve: this.permissionsArr.includes('auth:three:checkPass'),\r\n          noApprove: this.permissionsArr.includes('auth:three:checkNoPass')\r\n        }\r\n      })\r\n    },\r\n    editClick (row) {\r\n      this.newTab({ name: '编辑三双活动', menuId: '1', to: '/newOrEditThreeActivities', params: { id: row.id } })\r\n    },\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckThree(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 审核 (三双活动)\r\n    async getCheckThree (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckThree({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getThreeActivitiesList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n    handleSizeChange () {\r\n      this.getThreeActivitiesList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getThreeActivitiesList()\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.ThreeActivities {\r\n  height: 100%;\r\n  width: 100%;\r\n  .tableData {\r\n    .el-scrollbar__wrap .el-scrollbar__view th {\r\n      background: #f5f7fb;\r\n    }\r\n    height: calc(100% - 150px);\r\n  }\r\n  .button-box {\r\n    background: #fff;\r\n    margin-top: 20px;\r\n  }\r\n  .button-box .el-button {\r\n    background: #fff;\r\n  }\r\n  .el-button--danger.is-plain:hover,\r\n  .el-button--danger.is-plain:focus {\r\n    // color: #f56c6c;\r\n    background: #f56c6c;\r\n    color: #fff;\r\n  }\r\n  .el-button--success.is-plain:hover,\r\n  .el-button--success.is-plain:focus {\r\n    background: #67c23a;\r\n    color: #fff;\r\n  }\r\n  .delBtn {\r\n    color: #f56c6c;\r\n  }\r\n  .paging_box {\r\n    background: #fff;\r\n  }\r\n  .scoreEdit {\r\n    width: 700px;\r\n    height: 100%;\r\n    padding: 24px;\r\n    .form-button {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n  .search-box {\r\n    //月工作实际title文字样式\r\n    .search-title {\r\n      width: 133px;\r\n      height: 16px;\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-left: 32px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}