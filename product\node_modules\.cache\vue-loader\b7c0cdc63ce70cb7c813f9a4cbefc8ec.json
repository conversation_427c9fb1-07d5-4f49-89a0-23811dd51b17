{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\ThreeActivities\\ThreeActivities.vue?vue&type=style&index=0&id=ea7a84c6&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\ThreeActivities\\ThreeActivities.vue", "mtime": 1752541693881}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouVGhyZWVBY3Rpdml0aWVzIHsNCiAgaGVpZ2h0OiAxMDAlOw0KICB3aWR0aDogMTAwJTsNCiAgLnRhYmxlRGF0YSB7DQogICAgLmVsLXNjcm9sbGJhcl9fd3JhcCAuZWwtc2Nyb2xsYmFyX192aWV3IHRoIHsNCiAgICAgIGJhY2tncm91bmQ6ICNmNWY3ZmI7DQogICAgfQ0KICAgIGhlaWdodDogY2FsYygxMDAlIC0gMTUwcHgpOw0KICB9DQogIC5idXR0b24tYm94IHsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgIG1hcmdpbi10b3A6IDIwcHg7DQogIH0NCiAgLmJ1dHRvbi1ib3ggLmVsLWJ1dHRvbiB7DQogICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgfQ0KICAuZWwtYnV0dG9uLS1kYW5nZXIuaXMtcGxhaW46aG92ZXIsDQogIC5lbC1idXR0b24tLWRhbmdlci5pcy1wbGFpbjpmb2N1cyB7DQogICAgLy8gY29sb3I6ICNmNTZjNmM7DQogICAgYmFja2dyb3VuZDogI2Y1NmM2YzsNCiAgICBjb2xvcjogI2ZmZjsNCiAgfQ0KICAuZWwtYnV0dG9uLS1zdWNjZXNzLmlzLXBsYWluOmhvdmVyLA0KICAuZWwtYnV0dG9uLS1zdWNjZXNzLmlzLXBsYWluOmZvY3VzIHsNCiAgICBiYWNrZ3JvdW5kOiAjNjdjMjNhOw0KICAgIGNvbG9yOiAjZmZmOw0KICB9DQogIC5kZWxCdG4gew0KICAgIGNvbG9yOiAjZjU2YzZjOw0KICB9DQogIC5wYWdpbmdfYm94IHsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICB9DQogIC5zY29yZUVkaXQgew0KICAgIHdpZHRoOiA3MDBweDsNCiAgICBoZWlnaHQ6IDEwMCU7DQogICAgcGFkZGluZzogMjRweDsNCiAgICAuZm9ybS1idXR0b24gew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgIH0NCiAgfQ0KICAuc2VhcmNoLWJveCB7DQogICAgLy/mnIjlt6XkvZzlrp7pmYV0aXRsZeaWh+Wtl+agt+W8jw0KICAgIC5zZWFyY2gtdGl0bGUgew0KICAgICAgd2lkdGg6IDEzM3B4Ow0KICAgICAgaGVpZ2h0OiAxNnB4Ow0KICAgICAgZm9udC1zaXplOiAkdGV4dFNpemUxNjsNCiAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZyBTQzsNCiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgY29sb3I6ICMzMzMzMzM7DQogICAgICBsaW5lLWhlaWdodDogMzZweDsNCiAgICAgIG1hcmdpbi1sZWZ0OiAzMnB4Ow0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["ThreeActivities.vue"], "names": [], "mappings": ";AAicA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ThreeActivities.vue", "sourceRoot": "src/views/AssessmentOrgan/ThreeActivities", "sourcesContent": ["<template>\r\n  <!-- 三双活动 -->\r\n  <div class=\"ThreeActivities\">\r\n    <search-box @search-click=\"search\" @reset-click=\"reset\" title=\"筛选\">\r\n      <!--关键字查找框  -->\r\n      <zy-widget label=\"关键字\">\r\n        <el-input\r\n          placeholder=\"请输入关键词\"\r\n          v-model=\"form.keyword\"\r\n          clearable\r\n          @keyup.enter.native=\"search\"\r\n        >\r\n          <div slot=\"prefix\" class=\"input-search\"></div>\r\n        </el-input>\r\n      </zy-widget>\r\n\r\n      <!-- 时间查询 -->\r\n      <zy-widget label=\"时间查询\">\r\n        <!--  下拉列表中 选中年份功能  -->\r\n        <el-select\r\n          v-model=\"selectedYear\"\r\n          placeholder=\"请选择年份\"\r\n          @keyup.enter.native=\"search\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in timeArr\"\r\n            :key=\"item.id\"\r\n            :label=\"item.value\"\r\n            :value=\"item.id\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n\r\n      <!-- 部门查询(用到机构树)  -->\r\n      <zy-widget label=\"部门查询\" v-permissions=\"'auth:three:department'\">\r\n        <zy-select\r\n          v-model=\"searchParams.officeId\"\r\n          clearable\r\n          @keyup.enter.native=\"search\"\r\n          placeholder=\"请选择部门\"\r\n          node-key=\"id\"\r\n          :data=\"officeData\"\r\n          style=\"width: 100%\"\r\n        >\r\n          <div slot=\"prefix\" class=\"input-search\"></div>\r\n        </zy-select>\r\n      </zy-widget>\r\n\r\n      <!-- 审核状态查询 -->\r\n      <zy-widget label=\"审核状态查询\">\r\n        <el-select\r\n          v-model=\"searchParams.auditStatusParams\"\r\n          filterable\r\n          clearable\r\n          placeholder=\"请选择审核状态\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in auditStatusData\"\r\n            :key=\"item.id\"\r\n            :label=\"item.value\"\r\n            :value=\"item.id\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n    </search-box>\r\n    <div class=\"qd-list-wrap\">\r\n      <div class=\"qd-btn-box\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          v-permissions=\"'auth:three:new'\"\r\n          @click=\"handleAdd\"\r\n          >新增\r\n        </el-button>\r\n\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-circle-check\"\r\n          v-permissions=\"'auth:three:checkPass'\"\r\n          @click=\"passClick(2)\"\r\n          >审核通过\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-remove-outline\"\r\n          v-permissions=\"'auth:three:checkNoPass'\"\r\n          @click=\"passClick(3)\"\r\n          >审核不通过\r\n        </el-button>\r\n      </div>\r\n      <div class=\"tableData\">\r\n        <zy-table>\r\n          <el-table\r\n            :data=\"tableData\"\r\n            slot=\"zytable\"\r\n            row-key=\"menuId\"\r\n            ref=\"multipleTable\"\r\n            @select=\"selected\"\r\n            @select-all=\"selectedAll\"\r\n          >\r\n            <el-table-column type=\"selection\" width=\"50\"> </el-table-column>\r\n            <el-table-column\r\n              label=\"活动名称\"\r\n              show-overflow-tooltip\r\n              prop=\"title\"\r\n              width=\"270px\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\" @click=\"modify(scope.row)\" size=\"small\">\r\n                  {{ scope.row.title }}\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"举办部门\" width=\"120px\" prop=\"officeName\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"开始时间\" width=\"170\">\r\n              <template slot-scope=\"scope\">\r\n                <div>{{ $format(scope.row.meetStartTime).substr(0, 16) }}</div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"结束时间\" width=\"170\">\r\n              <template slot-scope=\"scope\">\r\n                <div>{{ $format(scope.row.meetEndTime).substr(0, 16) }}</div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"活动状态\" width=\"100\">\r\n              <template slot-scope=\"scope\">\r\n                <div>{{ scope.row.status }}</div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"审核状态\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <!-- <div v-if=\"scope.row.auditStatus == '1' \"> 待审核 </div>\r\n                <div v-if=\"scope.row.auditStatus == '0' \"> 审核通过</div> -->\r\n                <div>{{ scope.row.auditStatus }}</div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column\r\n              label=\"活动类型\"\r\n              show-overflow-tooltip\r\n              min-width=\"110\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <div>{{ scope.row.activityTypeName }}</div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <!-- <el-button type=\"text\"\r\n                           @click=\"editClick(scope.row)\"\r\n                           size=\"small\"> 编辑\r\n                </el-button> -->\r\n                <el-button\r\n                  type=\"text\"\r\n                  @click=\"editClick(scope.row)\"\r\n                  size=\"small\"\r\n                  :disabled=\"scope.row.auditStatus == '审核通过'\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"handleDelete(scope.row.id, scope.row.auditStatus)\"\r\n                  :class=\"scope.row.auditStatus == '审核通过' ? '' : 'delBtn'\"\r\n                  :disabled=\"scope.row.auditStatus == '审核通过'\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </zy-table>\r\n      </div>\r\n\r\n      <div class=\"paging_box\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page.sync=\"currentPage\"\r\n          :page-sizes=\"[10, 20, 30, 40]\"\r\n          :page-size.sync=\"pageSize\"\r\n          background\r\n          layout=\"total, prev, pager, next, sizes, jumper\"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <zy-pop-up v-model=\"show\" title=\"编辑\">\r\n      <OrganReviewNew :id=\"id\" @newCallback=\"newCallback\"> </OrganReviewNew>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport OrganReviewNew from './OrganReviewNew.vue'\r\n\r\nimport tableData from '@mixins/tableData'\r\n\r\nexport default {\r\n  name: 'ThreeActivities',\r\n  mixins: [tableData],\r\n  components: {\r\n    OrganReviewNew\r\n  },\r\n  data () {\r\n    return {\r\n      officeData: [], // 机构树,\r\n      auditStatusData: [], // 审核状态\r\n      searchParams: {\r\n\r\n        officeId: '',\r\n        auditStatusParams: ''\r\n\r\n      },\r\n      tableData: [],\r\n      timeArr: [],\r\n      selectedYear: '',\r\n\r\n      form: {\r\n        keyword: ''\r\n      },\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      total: 10,\r\n      show: false,\r\n      id: '',\r\n\r\n      choose: [],\r\n      selectObj: [],\r\n      selectData: []\r\n    }\r\n  },\r\n  mounted () {\r\n    this.initTime()\r\n    this.treeList()\r\n    this.dictionaryPubkvs()\r\n    this.getThreeActivitiesList()\r\n    const mune = JSON.parse(sessionStorage.getItem('menuChild'))\r\n    const path = this.$route.path\r\n    this.getPa(mune, path)\r\n  },\r\n  activated () {\r\n    this.getThreeActivitiesList()\r\n  },\r\n  inject: ['newTab'],\r\n\r\n  methods: {\r\n    getPa (data, path) {\r\n      data.forEach(item => {\r\n        if (item.children) {\r\n          this.getPa(item.children, path)\r\n        }\r\n        if (item.to === path) {\r\n          this.permissionsArr = item.permissions\r\n        }\r\n      })\r\n    },\r\n    /**\r\n  *字典\r\n*/\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluate_audit_status'\r\n      })\r\n      var { data } = res\r\n      this.auditStatusData = data.evaluate_audit_status\r\n    },\r\n\r\n    /**\r\n      *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    // 请求后台数据 初始化时间 在时间查询下拉框内\r\n    async initTime () {\r\n      const res = await this.$api.AssessmentOrgan.getMonthlyWorkTime()\r\n      var { data } = res\r\n      this.timeArr = data\r\n    },\r\n    newCallback () {\r\n      this.getThreeActivitiesList()\r\n      this.show = false\r\n    },\r\n    // // 编辑\r\n    // editClick (row) {\r\n    //   this.id = row.id\r\n    //   this.show = true\r\n    // },\r\n    search () { // 搜索\r\n      this.currentPage = 1\r\n      this.getThreeActivitiesList()\r\n    },\r\n    reset () { // 重置\r\n      this.form.keyword = ''\r\n      this.selectedYear = ''\r\n      this.searchParams.officeId = ''\r\n      this.searchParams.auditStatusParams = ''\r\n      this.getThreeActivitiesList()\r\n    },\r\n    // 新增按钮方法\r\n    handleAdd () {\r\n      const mid = new Date().getTime().toString()\r\n      this.newTab({\r\n        name: '新建三双活动',\r\n        menuId: mid,\r\n        to: '/newOrEditThreeActivities',\r\n        params: { mid, titleId: 1 }\r\n      })\r\n    },\r\n    // 删除按钮方法  批量删除\r\n    handleDelete (ids) { // TODO: handleDelete 和 handleBatchDelete 1.组合使用为新增/删除栏的删除功能 2.单独使用handleDelete则为操作项的删除功能\r\n      if (ids.auditStatus !== '审核通过') {\r\n        this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.$api.AssessmentOrgan.reqDelThree({ ids }).then((res) => {\r\n            if (res.errcode === 200) {\r\n              this.getThreeActivitiesList()// 删除后更新页面\r\n              this.$message.success('删除成功')\r\n            }\r\n          })\r\n        }).catch(() => {\r\n          this.$message.info('取消删除')\r\n          // this.getThreeActivitiesList()\r\n          return false\r\n        })\r\n      } else {\r\n        this.$message.info('不能删除审核通过项')\r\n        return false\r\n      }\r\n    },\r\n\r\n    handleBatchDelete () {\r\n      if (this.choose.length === 0) {\r\n        return this.$message.warning('请选择想要删除的项')\r\n      }\r\n      this.handleDelete(this.choose.join(','))\r\n    },\r\n\r\n    async getThreeActivitiesList () {\r\n      const res = await this.$api.AssessmentOrgan.reqThreeActivitiesList({\r\n        keyword: this.form.keyword,\r\n        pageNo: this.currentPage,\r\n        sedateId: this.selectedYear,\r\n        pageSize: this.pageSize,\r\n        officeId: this.searchParams.officeId, // 部门查询\r\n        auditStatus: this.searchParams.auditStatusParams // 审核状态\r\n      })\r\n      var { data, total } = res\r\n      const arr = []\r\n      data.forEach(item => {\r\n        if (item.submiterType) {\r\n          if (item.submiterType.indexOf('-') != -1) { // eslint-disable-line\r\n            item.submiterType = item.submiterType.split('-')[1]\r\n            arr.push(item)\r\n          } else {\r\n            arr.push(item)\r\n          }\r\n        } else {\r\n          arr.push(item)\r\n        }\r\n      })\r\n      // 这个暂时没写\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n      this.selectObj = []\r\n      this.selectData = []\r\n      this.$nextTick(function () {\r\n        this.memoryChecked()\r\n      })\r\n    },\r\n    modify (row) {\r\n      this.newTab({\r\n        name: '详情',\r\n        menuId: '1',\r\n        to: '/ThreeDetails',\r\n        params: {\r\n          id: row.id,\r\n          approve: this.permissionsArr.includes('auth:three:checkPass'),\r\n          noApprove: this.permissionsArr.includes('auth:three:checkNoPass')\r\n        }\r\n      })\r\n    },\r\n    editClick (row) {\r\n      this.newTab({ name: '编辑三双活动', menuId: '1', to: '/newOrEditThreeActivities', params: { id: row.id } })\r\n    },\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckThree(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 审核 (三双活动)\r\n    async getCheckThree (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckThree({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getThreeActivitiesList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n    handleSizeChange () {\r\n      this.getThreeActivitiesList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getThreeActivitiesList()\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.ThreeActivities {\r\n  height: 100%;\r\n  width: 100%;\r\n  .tableData {\r\n    .el-scrollbar__wrap .el-scrollbar__view th {\r\n      background: #f5f7fb;\r\n    }\r\n    height: calc(100% - 150px);\r\n  }\r\n  .button-box {\r\n    background: #fff;\r\n    margin-top: 20px;\r\n  }\r\n  .button-box .el-button {\r\n    background: #fff;\r\n  }\r\n  .el-button--danger.is-plain:hover,\r\n  .el-button--danger.is-plain:focus {\r\n    // color: #f56c6c;\r\n    background: #f56c6c;\r\n    color: #fff;\r\n  }\r\n  .el-button--success.is-plain:hover,\r\n  .el-button--success.is-plain:focus {\r\n    background: #67c23a;\r\n    color: #fff;\r\n  }\r\n  .delBtn {\r\n    color: #f56c6c;\r\n  }\r\n  .paging_box {\r\n    background: #fff;\r\n  }\r\n  .scoreEdit {\r\n    width: 700px;\r\n    height: 100%;\r\n    padding: 24px;\r\n    .form-button {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n  .search-box {\r\n    //月工作实际title文字样式\r\n    .search-title {\r\n      width: 133px;\r\n      height: 16px;\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-left: 32px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}