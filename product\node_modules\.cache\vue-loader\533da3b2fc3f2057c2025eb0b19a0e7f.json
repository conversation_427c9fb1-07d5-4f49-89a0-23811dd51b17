{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\sinceManagement-zx\\SinceSituation\\MyColumn.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\sinceManagement-zx\\SinceSituation\\MyColumn.vue", "mtime": 1754616762320}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdNeUNvbHVtbicsDQogIHByb3BzOiB7DQogICAgY29sOiB7DQogICAgICB0eXBlOiBPYmplY3QNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["MyColumn.vue"], "names": [], "mappings": ";AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MyColumn.vue", "sourceRoot": "src/views/sinceManagement-zx/SinceSituation", "sourcesContent": ["<template>\r\n  <el-table-column :prop=\"col.fieldName\" :label=\"col.label\" align=\"center\" width=\"100\"\r\n    :class-name=\"col.fieldName === 'userName' ? 'username' : ''\">\r\n    <MyColumn v-for=\"(item, index) in col.children\" :key=\"index\" :col=\"item\">\r\n    </MyColumn>\r\n  </el-table-column>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MyColumn',\r\n  props: {\r\n    col: {\r\n      type: Object\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style>\r\n.username {\r\n  color: #3657c0;\r\n}\r\n\r\n.el-table__row .username .cell {\r\n  color: #3657c0;\r\n  cursor: pointer;\r\n}\r\n</style>\r\n"]}]}