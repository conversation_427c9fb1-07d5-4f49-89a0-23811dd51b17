{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu-tree\\zy-menu-tree.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-menu-tree\\zy-menu-tree.vue", "mtime": 1752541693532}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["zy-menu-tree.vue"], "names": [], "mappings": ";AA2BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-menu-tree.vue", "sourceRoot": "src/components/zy-menu-tree", "sourcesContent": ["<template>\r\n  <div class=\"zy-menu-tree scrollBar\">\r\n    <div v-for=\"(menu, index) in menuItem\"\r\n         :key=\"index\">\r\n      <div :class=\"['menu-item', menu.active?'menu-item-active':'']\"\r\n           :style=\"{ paddingLeft: padding(hierarchy) + 'px' }\"\r\n           v-if=\"judge(menu,false)\"\r\n           @click=\"selected(menu)\">{{menu[props.label]}}</div>\r\n      <div v-if=\"judge(menu,true)\">\r\n        <div class=\"menu-item menu-item-title\"\r\n             :style=\"{ paddingLeft: padding(hierarchy) + 'px' }\"\r\n             @click=\"submenu(menu)\">{{menu[props.label]}} <div :class=\"['menu-icon',menu.hidden? 'menu-icon-active':'']\"></div>\r\n        </div>\r\n        <el-collapse-transition>\r\n          <zy-menu-tree v-if=\"menu.hidden\"\r\n                        :show=\"show\"\r\n                        :menu=\"menu[props.children]\"\r\n                        :props=\"props\"\r\n                        v-model=\"menuId\"\r\n                        :nodeKey=\"nodeKey\"\r\n                        :hierarchy=\"hierarchy+1\"></zy-menu-tree>\r\n        </el-collapse-transition>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyMenuTree',\r\n  data () {\r\n    return {\r\n      menuId: this.value,\r\n      menuItem: []\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    menu: {\r\n      type: Array,\r\n      default: function () {\r\n        return []\r\n      }\r\n    },\r\n    hierarchy: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    show: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label',\r\n          show: 'show'\r\n        }\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  created () {\r\n    this.menudata(this.deepCopy(this.menu))\r\n  },\r\n  watch: {\r\n    menu () {\r\n      this.menudata(this.deepCopy(this.menu))\r\n    },\r\n    value (val) {\r\n      this.menuId = val\r\n      this.selectedId()\r\n    },\r\n    menuId (val) {\r\n      this.$emit('id', val)\r\n    }\r\n  },\r\n  methods: {\r\n    judge (data, type) {\r\n      var show = false\r\n      if (this.show) {\r\n        if (type) {\r\n          if (data[this.props.children].length !== 0 && data[this.props.show]) {\r\n            show = true\r\n          } else {\r\n            show = false\r\n          }\r\n        } else {\r\n          if (data[this.props.children].length === 0 && data[this.props.show]) {\r\n            show = true\r\n          } else {\r\n            show = false\r\n          }\r\n        }\r\n      } else {\r\n        if (type) {\r\n          if (data[this.props.children].length) {\r\n            show = true\r\n          } else {\r\n            show = false\r\n          }\r\n        } else {\r\n          if (data[this.props.children].length) {\r\n            show = false\r\n          } else {\r\n            show = true\r\n          }\r\n        }\r\n      }\r\n      return show\r\n    },\r\n    padding (index) {\r\n      var hierarchy = 24 + (16 * index)\r\n      return hierarchy\r\n    },\r\n    menudata (data) {\r\n      data.forEach(item => {\r\n        if (item[this.props.children].length === 0) {\r\n          item.active = false\r\n          if (this.menuId === item[this.nodeKey]) {\r\n            item.active = true\r\n          }\r\n        } else {\r\n          item.hidden = false\r\n        }\r\n      })\r\n      this.menuItem = data\r\n      this.selectedId()\r\n    },\r\n    submenu (data) {\r\n      const arr = this.menuItem\r\n      arr.forEach(item => {\r\n        if (item[this.nodeKey] === data[this.nodeKey]) {\r\n          item.hidden = !item.hidden\r\n        }\r\n      })\r\n    },\r\n    // rowClick (result) {\r\n    //   this.$emit('on-row-click', result)\r\n    // },\r\n    selected (data) {\r\n      this.menuId = data[this.nodeKey]\r\n      // let result = this.makeData(data)\r\n      // this.$emit('on-row-click', result)\r\n    },\r\n    selectedId (type) {\r\n      if (this.hierarchy === 0) {\r\n        this.menuhierarchy(this.menuItem)\r\n      }\r\n      const arr = this.menuItem\r\n      arr.forEach(item => {\r\n        if (item[this.props.children].length === 0) {\r\n          item.active = false\r\n          if (item[this.nodeKey] === this.menuId) {\r\n            item.active = true\r\n            if (this.$route.path !== item.to) {\r\n              this.$router.push({ path: item.to })\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    menuhierarchy (data) {\r\n      data.forEach(item => {\r\n        if (item[this.nodeKey] === this.menuId) {\r\n          sessionStorage.setItem('curMenuItem', JSON.stringify(item))\r\n          if (this.$route.path !== item.to) {\r\n            this.$router.push({ path: item.to })\r\n          }\r\n          const result = this.makeData(item)\r\n          this.$emit('on-row-click', result)\r\n        }\r\n        if (item[this.props.children].length) {\r\n          this.menuhierarchy(item[this.props.children])\r\n        }\r\n      })\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    },\r\n    makeData (data) {\r\n      const t = this.type(data)\r\n      let o\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (let i = 0; i < data.length; i++) {\r\n          o.push(this.makeData(data[i]))\r\n        }\r\n      } else if (t === 'object') {\r\n        for (const i in data) {\r\n          if (i != 'active' && i != 'hidden') {// eslint-disable-line\r\n            o[i] = this.makeData(data[i])\r\n          }\r\n        }\r\n      }\r\n      return o\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-menu-tree.scss\";\r\n</style>\r\n"]}]}