{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue", "mtime": 1756347044379}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RankingBarChart.vue"], "names": [], "mappings": ";AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RankingBarChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"ranking-bar-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'RankingBar<PERSON><PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    showValues: {\n      type: Boolean,\n      default: true\n    },\n    maxValue: {\n      type: Number,\n      default: null\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  watch: {\n    chartData: {\n      handler () {\n        this.renderChart()\n      },\n      deep: true\n    }\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.renderChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n    getOption () {\n      const xAxisData = this.chartData.map(item => item.name)\n      const seriesData = this.chartData.map(item => item.value)\n      return {\n        grid: {\n          left: '0%',\n          right: '0%',\n          bottom: '0%',\n          top: '10%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData,\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.2)'\n            }\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12,\n            interval: 0,\n            rotate: 35,\n            margin: 8,\n            formatter: (value) => {\n              return value\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          splitNumber: 5,\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12\n          },\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              type: 'dashed'\n            }\n          }\n        },\n        series: [\n          {\n            type: 'bar',\n            data: seriesData,\n            barWidth: 20,\n            showBackground: false,\n            itemStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: '#00E4FF' },\n                  { offset: 0.5, color: '#0090FF' },\n                  { offset: 1, color: '#004080' }\n                ]\n              },\n              borderRadius: [4, 4, 0, 0],\n              shadowColor: 'rgba(0, 212, 255, 0.3)',\n              shadowBlur: 10,\n              shadowOffsetY: 2\n            },\n            emphasis: {\n              itemStyle: {\n                color: {\n                  type: 'linear',\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: '#00FFFF' },\n                    { offset: 0.5, color: '#00AAFF' },\n                    { offset: 1, color: '#0066CC' }\n                  ]\n                },\n                shadowColor: 'rgba(0, 255, 255, 0.5)',\n                shadowBlur: 15\n              }\n            },\n            label: {\n              show: this.showValues,\n              position: 'top',\n              color: '#FFFFFF',\n              fontSize: 12,\n              formatter: '{c}'\n            }\n          }\n        ],\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 20, 40, 0.9)',\n          borderColor: '#00D4FF',\n          borderWidth: 1,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 12\n          },\n          formatter: function (params) {\n            const data = params[0]\n            return `${data.name}<br/>人数: ${data.value}人`\n          }\n          // formatter: (params) => {\n          //   const data = params[0]\n          //   return `<div style=\"padding: 5px;\">\n          //     <div style=\"color: #00D4FF; font-weight: bold;\">${data.name}</div>\n          //     <div style=\"margin-top: 5px;\">数量: <span style=\"color: #00E4FF; font-weight: bold;\">${data.value}</span></div>\n          //   </div>`\n          // }\n        }\n      }\n    },\n    renderChart () {\n      if (!this.chart || !this.chartData.length) return\n      this.chart.setOption(this.getOption(), true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.ranking-bar-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}