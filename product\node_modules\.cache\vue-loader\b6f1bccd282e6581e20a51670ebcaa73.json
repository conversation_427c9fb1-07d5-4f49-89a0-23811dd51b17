{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue", "mtime": 1756349949679}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RankingBarChart.vue"], "names": [], "mappings": ";AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RankingBarChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"ranking-bar-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'RankingBar<PERSON><PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    showValues: {\n      type: Boolean,\n      default: true\n    },\n    maxValue: {\n      type: Number,\n      default: null\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.renderChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n    getOption () {\n      const xAxisData = this.chartData.map(item => item.name)\n      const seriesData = this.chartData.map(item => item.value)\n      // 计算柱子宽度\n      // const dom = 300\n      const barWidth = 20\n      // 生成渐变色数组\n      const colors = []\n      for (let i = 0; i < this.chartData.length; i++) {\n        colors.push({\n          type: 'linear',\n          x: 0,\n          x2: 0,\n          y: 0,\n          y2: 1,\n          colorStops: [\n            {\n              offset: 0,\n              color: '#0093DD' // 顶部颜色\n            }, {\n              offset: 0.5,\n              color: '#006BBC' // 中间颜色，创建立体效果\n            }, {\n              offset: 0.5,\n              color: '#1FC6FF' // 中间分割线\n            }, {\n              offset: 1,\n              color: 'rgba(31,198,255,0.2)' // 底部颜色\n            }\n          ]\n        })\n      }\n\n      return {\n        // 提示框\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00D4FF',\n          borderWidth: 1,\n          axisPointer: {\n            type: 'shadow'\n          },\n          textStyle: {\n            color: '#FFFFFF'\n          },\n          formatter: function (params) {\n            const data = params[0]\n            return `${data.name}<br/>人数: ${data.value}人`\n          }\n        },\n        // 区域位置\n        grid: {\n          left: '0%',\n          right: '0%',\n          bottom: '0%',\n          top: '5%',\n          containLabel: true\n        },\n        // X轴\n        xAxis: {\n          data: xAxisData,\n          type: 'category',\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.2)'\n            }\n          },\n          splitLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12,\n            interval: 0,\n            rotate: 35,\n            margin: 8,\n            formatter: (value) => {\n              return value\n            }\n          }\n        },\n        // y轴\n        yAxis: {\n          type: 'value',\n          show: true,\n          splitNumber: 5,\n          axisLine: {\n            show: false\n          },\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              type: 'dashed'\n            }\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12\n          }\n        },\n        series: [\n          {\n            type: 'bar',\n            barWidth: barWidth,\n            itemStyle: {\n              normal: {\n                color: function (params) {\n                  return colors[params.dataIndex % colors.length]\n                }\n              }\n            },\n            label: {\n              show: true,\n              position: 'top',\n              color: '#FFFFFF',\n              fontSize: 12,\n              align: 'center'\n            },\n            data: seriesData\n          },\n          {\n            z: 2,\n            type: 'pictorialBar',\n            data: seriesData,\n            symbol: 'diamond',\n            symbolOffset: [0, '50%'],\n            symbolSize: [barWidth, barWidth * 0.5],\n            itemStyle: {\n              normal: {\n                color: function (params) {\n                  return colors[params.dataIndex % colors.length]\n                }\n              }\n            }\n          },\n          {\n            z: 3,\n            type: 'pictorialBar',\n            symbolPosition: 'end',\n            data: seriesData,\n            symbol: 'diamond',\n            symbolOffset: [0, '-50%'],\n            symbolSize: [barWidth, barWidth * 0.5],\n            itemStyle: {\n              normal: {\n                borderWidth: 0,\n                color: function (params) {\n                  return colors[params.dataIndex % colors.length].colorStops[0].color\n                }\n              }\n            }\n          }\n        ]\n      }\n    },\n    renderChart () {\n      if (!this.chart || !this.chartData.length) return\n      this.chart.setOption(this.getOption(), true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.ranking-bar-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}