{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\titleDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\titleDetail.vue", "mtime": 1752541693832}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["titleDetail.vue"], "names": [], "mappings": ";AA2EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "titleDetail.vue", "sourceRoot": "src/views/AssessmentOrgan/InnovationExcellence", "sourcesContent": ["<template>\r\n  <div class=\"titleDetail details\">\r\n    <div class=\"checkClass\">\r\n\r\n      <el-button type=\"primary\"\r\n                 icon=\"el-icon-circle-check\"\r\n                 v-permissions=\"'auth:innovation:checkPass'\"\r\n                 @click=\"passClick(2)\">审核通过\r\n      </el-button>\r\n      <el-button type=\"danger\"\r\n                 icon=\"el-icon-remove-outline\"\r\n                 v-permissions=\"'auth:innovation:checkNotPass'\"\r\n                 @click=\"passClick(3)\">审核不通过\r\n      </el-button>\r\n    </div>\r\n    <div class=\"details-title\">详情</div>\r\n    <div class=\"details-item-box\">\r\n      <div class=\"details-item-title\">\r\n        <div class=\"details-item-label\">标题</div>\r\n        <div class=\"details-item-value\">{{form.title}}</div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">发布时间</div>\r\n          <div class=\"details-item-value\">{{$format(form.publishTime).substr(0,16)}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">完成时间</div>\r\n          <div class=\"details-item-value\">{{$format(form.endTime).substr(0,16)}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">机构名</div>\r\n          <div class=\"details-item-value\">{{form.officeName}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">分值</div>\r\n          <div class=\"details-item-value\">{{form.score}}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">审核状态</div>\r\n          <div class=\"details-item-value\">{{auditStatusName}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">类型</div>\r\n          <div class=\"details-item-value\">{{classDetail}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">是否重点工作</div>\r\n          <div class=\"details-item-value\">{{form.isMainwork==1?'是':'否'}}</div>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"details-item\">\r\n        <div class=\"details-item-label\">附件</div>\r\n        <div class=\"details-item-value\">\r\n          <div class=\"details-item-files\"\r\n               v-for=\"(item, index) in details.attachmentList\"\r\n               :key=\"index\">\r\n            <p>{{item.fileName}}</p>\r\n            <div>\r\n              <el-button size=\"medium\"\r\n                         type=\"text\"\r\n                         @click=\"priew(item)\"> 预览 </el-button>\r\n              <el-button size=\"medium\"\r\n                         type=\"text\"\r\n                         @click=\"fileClick(item)\"> 下载 </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  props: ['id', 'uid'],\r\n  data () {\r\n    return {\r\n      form: {\r\n        id: '',\r\n        title: '',\r\n        officeName: '',\r\n        publishTime: '',\r\n        endTime: '',\r\n        score: '',\r\n        auditStatus: '',\r\n        isMainwork: '',\r\n        classify: ''\r\n      },\r\n      details: {},\r\n      classifyData: []\r\n    }\r\n  },\r\n  inject: ['openoffice', 'openPdf'],\r\n  mounted () {\r\n    // this.form.overTime = this.$format()\r\n    this.dictionaryPubkvs()\r\n    this.getInnovationDetails()\r\n  },\r\n  computed: {\r\n    auditStatusName () {\r\n      if (this.form.auditStatus === '1') {\r\n        return '待审核'\r\n      } else if (this.form.auditStatus === '2') {\r\n        return '审核通过'\r\n      } else {\r\n        return '审核不通过'\r\n      }\r\n    },\r\n    classDetail () {\r\n      if (typeof this.classifyData[this.form.classify - 1] === 'object') { return this.classifyData[this.form.classify - 1].value }\r\n      return ''\r\n    }\r\n  },\r\n  methods: {\r\n    passClick (auditStatus) {\r\n      this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.getCheckWork(this.id, auditStatus)\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消操作'\r\n        })\r\n      })\r\n    },\r\n    // 审核 (加分项)\r\n    async getCheckWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckWork({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n        this.getInnovationDetails()\r\n      }\r\n    },\r\n    /**\r\n    *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_functional_innovate'\r\n      })\r\n      var { data } = res\r\n      this.classifyData = data.evaluation_functional_innovate\r\n    },\r\n    // 获取 创新创优标题详情\r\n    async getInnovationDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqInnovationDetails(this.id)\r\n      const { id, title, officeName, publishTime, endTime, score, auditStatus, isMainwork, classify } = res.data\r\n\r\n      this.form = { id, title, officeName, publishTime, endTime, score, auditStatus, isMainwork, classify }\r\n    }\r\n    // 附件\r\n    // fileClick (data) {\r\n    //   this.$api.proposal.downloadFile({ id: data.id }, data.fileName)\r\n    // },\r\n    // priew (data) {\r\n    //   console.log(data)\r\n    //   if (data.fileType === 'pdf') {\r\n    //     this.openPdf(data.filePath)\r\n    //     return\r\n    //   }\r\n    //   const arr = ['doc', 'docx', 'xlsx']\r\n    //   if (arr.includes(data.fileType)) {\r\n    //     this.openoffice(data.filePath)\r\n    //   }\r\n    // }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.titleDetail {\r\n  width: 900px;\r\n  height: 100%;\r\n  padding: 0 24px;\r\n  padding-bottom: 24px;\r\n\r\n  .checkClass {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding-right: 10px;\r\n  }\r\n\r\n  .details-item-content {\r\n    width: 100%;\r\n    padding: 24px;\r\n\r\n    p {\r\n      line-height: 36px;\r\n    }\r\n  }\r\n\r\n  .details-item-img {\r\n    img {\r\n      width: calc(100% - 24px);\r\n    }\r\n  }\r\n\r\n  .details-item-files {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-right: 20px;\r\n  }\r\n}\r\n</style>\r\n"]}]}