{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\proposalStatistics\\proposalStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\proposalStatistics\\proposalStatisticsBox.vue", "mtime": 1756344694009}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyB1c2VJbmRleCB9IGZyb20gJy4uL3NjcmVlbi5qcycNCmltcG9ydCBQaWVDaGFydCBmcm9tICcuLi9jb21wb25lbnRzL1BpZUNoYXJ0LnZ1ZScNCmltcG9ydCBXb3JkQ2xvdWQgZnJvbSAnLi4vY29tcG9uZW50cy9Xb3JkQ2xvdWQudnVlJw0KaW1wb3J0IENpcmN1bGFyUHJvZ3Jlc3MgZnJvbSAnLi4vY29tcG9uZW50cy9DaXJjdWxhclByb2dyZXNzLnZ1ZScNCmltcG9ydCBQcm9ncmVzc0JhciBmcm9tICcuLi9jb21wb25lbnRzL1Byb2dyZXNzQmFyLnZ1ZScNCmltcG9ydCBCYXJDaGFydCBmcm9tICcuLi9jb21wb25lbnRzL0JhckNoYXJ0LnZ1ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnQmlnU2NyZWVuJywNCiAgY29tcG9uZW50czogew0KICAgIFBpZUNoYXJ0LA0KICAgIFdvcmRDbG91ZCwNCiAgICBDaXJjdWxhclByb2dyZXNzLA0KICAgIFByb2dyZXNzQmFyLA0KICAgIEJhckNoYXJ0DQogIH0sDQogIGRhdGEgKCkgew0KICAgIHJldHVybiB7DQogICAgICBjdXJyZW50VGltZTogJycsDQogICAgICBjYXRlZ29yeUNoYXJ0TmFtZTogJ+exu+WIq+WIhuaekCcsDQogICAgICBjYXRlZ29yeUNoYXJ0RGF0YTogWw0KICAgICAgICB7IG5hbWU6ICfmlL/lupzliLbnuqYnLCB2YWx1ZTogMjIuNTIgfSwNCiAgICAgICAgeyBuYW1lOiAn5Y6/5Yy65biC5pS/JywgdmFsdWU6IDE4LjMzIH0sDQogICAgICAgIHsgbmFtZTogJ+WPuOazleazleayuycsIHZhbHVlOiAxNS43MyB9LA0KICAgICAgICB7IG5hbWU6ICfljLrluILmlL/lupwnLCB2YWx1ZTogMTEuMzQgfSwNCiAgICAgICAgeyBuYW1lOiAn56eR5oqA5bel5ZWGJywgdmFsdWU6IDkuNTYgfSwNCiAgICAgICAgeyBuYW1lOiAn5pWZ6IKy5paH5YyWJywgdmFsdWU6IDguMDkgfSwNCiAgICAgICAgeyBuYW1lOiAn5rS+5Ye65py65p6EJywgdmFsdWU6IDQuMjEgfSwNCiAgICAgICAgeyBuYW1lOiAn56S+5Lya5LqL5LiaJywgdmFsdWU6IDMuNzEgfSwNCiAgICAgICAgeyBuYW1lOiAn5LyB5LqL5LiaJywgdmFsdWU6IDMuNjUgfSwNCiAgICAgICAgeyBuYW1lOiAn5Yac5p2R5Y2r55SfJywgdmFsdWU6IDMuMjEgfSwNCiAgICAgICAgeyBuYW1lOiAn5YW25LuW5py65p6EJywgdmFsdWU6IDEuODYgfSwNCiAgICAgICAgeyBuYW1lOiAn5ZCE576k5L2T5LuWJywgdmFsdWU6IDEuMDIgfQ0KICAgICAgXSwNCiAgICAgIGhvdFdvcmRzRGF0YTogWw0KICAgICAgICB7IG5hbWU6ICfnu4/mtY7lu7rorr4nLCB2YWx1ZTogMTAgfSwNCiAgICAgICAgeyBuYW1lOiAn5Lq65omN5Z+55YW7JywgdmFsdWU6IDkgfSwNCiAgICAgICAgeyBuYW1lOiAnQUnmioDmnK8nLCB2YWx1ZTogNiB9LA0KICAgICAgICB7IG5hbWU6ICfmlLnpnanliJvmlrAnLCB2YWx1ZTogNyB9LA0KICAgICAgICB7IG5hbWU6ICfmlZnogrInLCB2YWx1ZTogNSB9LA0KICAgICAgICB7IG5hbWU6ICfovabovobkuqTpgJonLCB2YWx1ZTogNiB9LA0KICAgICAgICB7IG5hbWU6ICfml4XmuLgnLCB2YWx1ZTogNSB9LA0KICAgICAgICB7IG5hbWU6ICflhazlhbHlronlhagnLCB2YWx1ZTogNyB9LA0KICAgICAgICB7IG5hbWU6ICfmmbrog73ljJYnLCB2YWx1ZTogNiB9LA0KICAgICAgICB7IG5hbWU6ICfnlLXmoq/mlYXpmpwnLCB2YWx1ZTogNCB9LA0KICAgICAgICB7IG5hbWU6ICfnpL7kvJrkv53pmpwnLCB2YWx1ZTogNiB9LA0KICAgICAgICB7IG5hbWU6ICfnjq/looPkv53miqQnLCB2YWx1ZTogNSB9LA0KICAgICAgICB7IG5hbWU6ICfljLvnlpfljavnlJ8nLCB2YWx1ZTogNyB9LA0KICAgICAgICB7IG5hbWU6ICfmlofljJblu7rorr4nLCB2YWx1ZTogNCB9LA0KICAgICAgICB7IG5hbWU6ICfnp5HmioDliJvmlrAnLCB2YWx1ZTogOCB9DQogICAgICBdLA0KICAgICAgLy8g5o+Q5qGI5pW05L2T5oOF5Ya15pWw5o2uDQogICAgICBwcm9wb3NhbE92ZXJhbGxEYXRhOiB7DQogICAgICAgIHRvdGFsUHJvcG9zYWxzOiAxNTAwLA0KICAgICAgICBhcHByb3ZlZFByb3Bvc2FsczogNjAwLA0KICAgICAgICByZXBsaWVkUHJvcG9zYWxzOiA2MDAsDQogICAgICAgIGFwcHJvdmFsUmF0ZTogNjksDQogICAgICAgIHJlcGx5UmF0ZTogNjkNCiAgICAgIH0sDQogICAgICAvLyDnrZTlpI3nsbvlnovnu5/kuIDmlbDmja7mupANCiAgICAgIHJlcGx5VHlwZURhdGE6IFsNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICfpnaLlpI0nLA0KICAgICAgICAgIHZhbHVlOiAzNjAsDQogICAgICAgICAgY29sb3I6ICcjMDBkNGZmJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+WHveWkjScsDQogICAgICAgICAgdmFsdWU6IDI0MCwNCiAgICAgICAgICBjb2xvcjogJyNmZmQ3MDAnDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICAvLyDlkITkuJPlp5TkvJrmj5DmoYjmlbDmlbDmja4NCiAgICAgIGNvbW1pdHRlZVByb3Bvc2FsRGF0YTogWw0KICAgICAgICB7IG5hbWU6ICfmj5DmoYjlp5QnLCB2YWx1ZTogNDMgfSwNCiAgICAgICAgeyBuYW1lOiAn57uP5rWO5aeUJywgdmFsdWU6IDY3IH0sDQogICAgICAgIHsgbmFtZTogJ+WGnOS4muWGnOadkeWnlCcsIHZhbHVlOiA4NCB9LA0KICAgICAgICB7IG5hbWU6ICfkurrlj6PotYTmupDnjq/looPlp5QnLCB2YWx1ZTogNTIgfSwNCiAgICAgICAgeyBuYW1lOiAn5pWZ56eR5Y2r5L2T5aeUJywgdmFsdWU6IDM2IH0sDQogICAgICAgIHsgbmFtZTogJ+ekvuS8muWSjOazleWItuWnlCcsIHZhbHVlOiA2NiB9LA0KICAgICAgICB7IG5hbWU6ICfmsJHml4/lrpfmlZnlp5QnLCB2YWx1ZTogMjYgfSwNCiAgICAgICAgeyBuYW1lOiAn5riv5r6z5Y+w5L6o5aSW5LqL5aeUJywgdmFsdWU6IDYwIH0sDQogICAgICAgIHsgbmFtZTogJ+aWh+WMluaWh+WPsuWSjOWtpuS5oOWnlCcsIHZhbHVlOiA0NiB9DQogICAgICBdLA0KICAgICAgLy8g6YeN54K55o+Q5qGI5pWw5o2uDQogICAgICBrZXlQcm9wb3NhbHNEYXRhOiBbDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMSwNCiAgICAgICAgICB0aXRsZTogJ+W4guaUv+WNj+ekvuS8muWSjOazleWItuW3peS9nOWKnuWFrOWupOWbtOe7lSLlsYXlrrbpgILogIHljJbmlLnpgKAi5byA5bGV5LiT6aKY6LCD56CUJywNCiAgICAgICAgICBuYW1lOiAn6LW15Zu96IOcJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDIsDQogICAgICAgICAgdGl0bGU6ICci5LiO5rCR5ZCM6KGMIOWFseWIm+WFsei1oiLmlrDmoLzlsYDkuIvmsJHokKXkvIHkuJrovazlnovlj5HlsZXluqfosIjkvJrlj6zlvIAnLA0KICAgICAgICAgIG5hbWU6ICfmnY7popbkuYsnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMywNCiAgICAgICAgICB0aXRsZTogJyLmg6DmsJHnlJ/Ct+WfuuWxguihjCLkuYnor4rmtLvliqjmuKnmmpbkurrlv4MnLA0KICAgICAgICAgIG5hbWU6ICfnjovnuqLlpq4nDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogNCwNCiAgICAgICAgICB0aXRsZTogJ+W4guenkeaKgOWxgOmdouWkjeW4guaUv+WNj+enkeaKgOeVjOWIq+aPkOahiCcsDQogICAgICAgICAgbmFtZTogJ+W8oOS4h+W8uicNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA1LA0KICAgICAgICAgIHRpdGxlOiAn5biC5pS/5Y2P5Y+s5byAIuaOqOi/m+aVsOWtl+WMlui9rOWeiyLkuJPpopjljY/llYbkvJonLA0KICAgICAgICAgIG5hbWU6ICflvKDkuIflvLonDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogNiwNCiAgICAgICAgICB0aXRsZTogJ+aUv+WNj+WnlOWRmOa3seWFpeWfuuWxguW8gOWxlSLkuInmnI3liqEi5rS75YqoJywNCiAgICAgICAgICBuYW1lOiAn5byg5LiH5by6Jw0KICAgICAgICB9DQogICAgICBdDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8vIOS4uumlvOWbvue7hOS7tuaPkOS+m+aVsOaNruagvOW8jw0KICAgIHJlcGx5VHlwZUNoYXJ0RGF0YSAoKSB7DQogICAgICByZXR1cm4gdGhpcy5yZXBseVR5cGVEYXRhLm1hcChpdGVtID0+ICh7DQogICAgICAgIG5hbWU6IGl0ZW0ubmFtZSwNCiAgICAgICAgdmFsdWU6IGl0ZW0udmFsdWUsDQogICAgICAgIGNvbG9yOiBpdGVtLmNvbG9yDQogICAgICB9KSkNCiAgICB9LA0KDQogICAgLy8g5Li66L+b5bqm5p2h57uE5Lu25o+Q5L6b5pWw5o2u5qC85byPDQogICAgcmVwbHlUeXBlUHJvZ3Jlc3NEYXRhICgpIHsNCiAgICAgIGNvbnN0IHRvdGFsID0gdGhpcy5yZXBseVR5cGVEYXRhLnJlZHVjZSgoc3VtLCBpdGVtKSA9PiBzdW0gKyBpdGVtLnZhbHVlLCAwKQ0KICAgICAgcmV0dXJuIHRoaXMucmVwbHlUeXBlRGF0YS5tYXAoaXRlbSA9PiAoew0KICAgICAgICBsYWJlbDogaXRlbS5uYW1lLA0KICAgICAgICB2YWx1ZTogaXRlbS52YWx1ZSwNCiAgICAgICAgcGVyY2VudDogTWF0aC5yb3VuZCgoaXRlbS52YWx1ZSAvIHRvdGFsKSAqIDEwMCksDQogICAgICAgIGNvbG9yOiBpdGVtLmNvbG9yDQogICAgICB9KSkNCiAgICB9DQogIH0sDQogIG1vdW50ZWQgKCkgew0KICAgIHRoaXMuaW5pdFNjcmVlbigpDQogICAgdGhpcy51cGRhdGVUaW1lKCkNCiAgICB0aGlzLnRpbWVJbnRlcnZhbCA9IHNldEludGVydmFsKHRoaXMudXBkYXRlVGltZSwgMTAwMCkNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSAoKSB7DQogICAgaWYgKHRoaXMudGltZUludGVydmFsKSB7DQogICAgICBjbGVhckludGVydmFsKHRoaXMudGltZUludGVydmFsKQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGluaXRTY3JlZW4gKCkgew0KICAgICAgY29uc3QgeyBjYWxjUmF0ZSwgd2luZG93RHJhdyB9ID0gdXNlSW5kZXgodGhpcy4kcmVmcy5iaWdTY3JlZW4pDQogICAgICBjYWxjUmF0ZSgpDQogICAgICB3aW5kb3dEcmF3KCkNCiAgICB9LA0KICAgIHVwZGF0ZVRpbWUgKCkgew0KICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKQ0KICAgICAgdGhpcy5jdXJyZW50VGltZSA9IG5vdy50b0xvY2FsZVN0cmluZygnemgtQ04nLCB7DQogICAgICAgIHllYXI6ICdudW1lcmljJywNCiAgICAgICAgbW9udGg6ICcyLWRpZ2l0JywNCiAgICAgICAgZGF5OiAnMi1kaWdpdCcsDQogICAgICAgIGhvdXI6ICcyLWRpZ2l0JywNCiAgICAgICAgbWludXRlOiAnMi1kaWdpdCcsDQogICAgICAgIHNlY29uZDogJzItZGlnaXQnDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g6L+U5Zue6aaW6aG1DQogICAgZ29Ib21lICgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogJy9ob21lQm94JyB9KQ0KICAgIH0sDQogICAgLy8g6K+N5LqR54K55Ye75LqL5Lu2DQogICAgb25Xb3JkQ2xpY2sgKHdvcmQpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfor43msYfngrnlh7s6Jywgd29yZCkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["proposalStatisticsBox.vue"], "names": [], "mappings": ";AAwIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "proposalStatisticsBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/proposalStatistics", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>提案统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 类别分布 -->\r\n        <div class=\"category_distribution\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">类别分布</span>\r\n          </div>\r\n          <div class=\"category_distribution_content\">\r\n            <PieChart id=\"category_distribution\" :chart-data=\"categoryChartData\" :name=\"categoryChartName\" />\r\n          </div>\r\n        </div>\r\n        <!-- 热词分析 -->\r\n        <div class=\"hot_word_analysis\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">热词分析</span>\r\n          </div>\r\n          <div class=\"hot_word_analysis_content\">\r\n            <WordCloud chart-id=\"hotWordChart\" :words=\"hotWordsData\" @word-click=\"onWordClick\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"center-panel\">\r\n        <!-- 提案整体情况 -->\r\n        <div class=\"proposal_overall_situation\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">提案整体情况</span>\r\n          </div>\r\n          <div class=\"proposal_overall_situation_content\">\r\n            <!-- 左侧数据卡片 -->\r\n            <div class=\"left-section\">\r\n              <div class=\"data-card total-proposals\">\r\n                <div class=\"card-number\">{{ proposalOverallData.totalProposals }}</div>\r\n                <div class=\"card-label\">提案总件数</div>\r\n              </div>\r\n              <div class=\"data-card approved-proposals\">\r\n                <div class=\"card-number\">{{ proposalOverallData.approvedProposals }}</div>\r\n                <div class=\"card-label\">立案总件数</div>\r\n              </div>\r\n              <div class=\"data-card replied-proposals\">\r\n                <div class=\"card-number\">{{ proposalOverallData.repliedProposals }}</div>\r\n                <div class=\"card-label\">答复总件数</div>\r\n              </div>\r\n            </div>\r\n            <!-- 右侧图表区域 -->\r\n            <div class=\"right-section\">\r\n              <div class=\"top-charts\">\r\n                <div class=\"chart-item approval-rate\">\r\n                  <CircularProgress id=\"approval-rate-chart\" :percentage=\"proposalOverallData.approvalRate\" label=\"立案率\"\r\n                    color=\"#00d4ff\" />\r\n                </div>\r\n                <div class=\"chart-item reply-rate\">\r\n                  <CircularProgress id=\"reply-rate-chart\" :percentage=\"proposalOverallData.replyRate\" label=\"答复率\"\r\n                    color=\"#ffd700\" />\r\n                </div>\r\n              </div>\r\n              <div class=\"bottom-section\">\r\n                <div class=\"reply-pie-chart\">\r\n                  <PieChart id=\"reply-type-pie\" :chart-data=\"replyTypeChartData\" name=\"答复类型\" />\r\n                </div>\r\n                <div class=\"reply-progress\">\r\n                  <ProgressBar :progress-data=\"replyTypeProgressData\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 各专委会提案数 -->\r\n        <div class=\"committee_proposal_number\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">各专委会提案数</span>\r\n          </div>\r\n          <div class=\"committee_proposal_content\">\r\n            <BarChart id=\"committee_proposal\" :chart-data=\"committeeProposalData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"right-panel\">\r\n        <!-- 提交情况 -->\r\n        <div class=\"submission_status\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">提交情况</span>\r\n          </div>\r\n          <div class=\"submission_status_content\">\r\n          </div>\r\n        </div>\r\n        <!-- 办理单位统计前十 -->\r\n        <div class=\"hand_unit\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">办理单位统计（前十）</span>\r\n          </div>\r\n          <div class=\"hand_unit_content\">\r\n          </div>\r\n        </div>\r\n        <!-- 重点提案 -->\r\n        <div class=\"key_proposals\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">重点提案</span>\r\n            <span class=\"header_text_right\"></span>\r\n          </div>\r\n          <div class=\"key_proposals_list\">\r\n            <div v-for=\"(item, index) in keyProposalsData\" :key=\"item.id\" class=\"key_proposals_item\"\r\n              :class=\"{ 'with-bg-image': index % 2 === 0, 'with-bg-color': index % 2 === 1 }\">\r\n              <div class=\"key_proposals_content\">\r\n                <div class=\"key_proposals_title\">{{ item.title }}</div>\r\n                <div class=\"key_proposals_name\">{{ item.name }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport PieChart from '../components/PieChart.vue'\r\nimport WordCloud from '../components/WordCloud.vue'\r\nimport CircularProgress from '../components/CircularProgress.vue'\r\nimport ProgressBar from '../components/ProgressBar.vue'\r\nimport BarChart from '../components/BarChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    PieChart,\r\n    WordCloud,\r\n    CircularProgress,\r\n    ProgressBar,\r\n    BarChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      categoryChartName: '类别分析',\r\n      categoryChartData: [\r\n        { name: '政府制约', value: 22.52 },\r\n        { name: '县区市政', value: 18.33 },\r\n        { name: '司法法治', value: 15.73 },\r\n        { name: '区市政府', value: 11.34 },\r\n        { name: '科技工商', value: 9.56 },\r\n        { name: '教育文化', value: 8.09 },\r\n        { name: '派出机构', value: 4.21 },\r\n        { name: '社会事业', value: 3.71 },\r\n        { name: '企事业', value: 3.65 },\r\n        { name: '农村卫生', value: 3.21 },\r\n        { name: '其他机构', value: 1.86 },\r\n        { name: '各群体他', value: 1.02 }\r\n      ],\r\n      hotWordsData: [\r\n        { name: '经济建设', value: 10 },\r\n        { name: '人才培养', value: 9 },\r\n        { name: 'AI技术', value: 6 },\r\n        { name: '改革创新', value: 7 },\r\n        { name: '教育', value: 5 },\r\n        { name: '车辆交通', value: 6 },\r\n        { name: '旅游', value: 5 },\r\n        { name: '公共安全', value: 7 },\r\n        { name: '智能化', value: 6 },\r\n        { name: '电梯故障', value: 4 },\r\n        { name: '社会保障', value: 6 },\r\n        { name: '环境保护', value: 5 },\r\n        { name: '医疗卫生', value: 7 },\r\n        { name: '文化建设', value: 4 },\r\n        { name: '科技创新', value: 8 }\r\n      ],\r\n      // 提案整体情况数据\r\n      proposalOverallData: {\r\n        totalProposals: 1500,\r\n        approvedProposals: 600,\r\n        repliedProposals: 600,\r\n        approvalRate: 69,\r\n        replyRate: 69\r\n      },\r\n      // 答复类型统一数据源\r\n      replyTypeData: [\r\n        {\r\n          name: '面复',\r\n          value: 360,\r\n          color: '#00d4ff'\r\n        },\r\n        {\r\n          name: '函复',\r\n          value: 240,\r\n          color: '#ffd700'\r\n        }\r\n      ],\r\n      // 各专委会提案数数据\r\n      committeeProposalData: [\r\n        { name: '提案委', value: 43 },\r\n        { name: '经济委', value: 67 },\r\n        { name: '农业农村委', value: 84 },\r\n        { name: '人口资源环境委', value: 52 },\r\n        { name: '教科卫体委', value: 36 },\r\n        { name: '社会和法制委', value: 66 },\r\n        { name: '民族宗教委', value: 26 },\r\n        { name: '港澳台侨外事委', value: 60 },\r\n        { name: '文化文史和学习委', value: 46 }\r\n      ],\r\n      // 重点提案数据\r\n      keyProposalsData: [\r\n        {\r\n          id: 1,\r\n          title: '市政协社会和法制工作办公室围绕\"居家适老化改造\"开展专题调研',\r\n          name: '赵国胜'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '\"与民同行 共创共赢\"新格局下民营企业转型发展座谈会召开',\r\n          name: '李颖之'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '\"惠民生·基层行\"义诊活动温暖人心',\r\n          name: '王红妮'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '市科技局面复市政协科技界别提案',\r\n          name: '张万强'\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '市政协召开\"推进数字化转型\"专题协商会',\r\n          name: '张万强'\r\n        },\r\n        {\r\n          id: 6,\r\n          title: '政协委员深入基层开展\"三服务\"活动',\r\n          name: '张万强'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    // 为饼图组件提供数据格式\r\n    replyTypeChartData () {\r\n      return this.replyTypeData.map(item => ({\r\n        name: item.name,\r\n        value: item.value,\r\n        color: item.color\r\n      }))\r\n    },\r\n\r\n    // 为进度条组件提供数据格式\r\n    replyTypeProgressData () {\r\n      const total = this.replyTypeData.reduce((sum, item) => sum + item.value, 0)\r\n      return this.replyTypeData.map(item => ({\r\n        label: item.name,\r\n        value: item.value,\r\n        percent: Math.round((item.value / total) * 100),\r\n        color: item.color\r\n      }))\r\n    }\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 词云点击事件\r\n    onWordClick (word) {\r\n      console.log('词汇点击:', word)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 35px 20px 0 20px;\r\n    gap: 30px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel,\r\n    .right-panel {\r\n      width: 470px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 30px 30px;\r\n    }\r\n\r\n    .left-panel {\r\n      .category_distribution {\r\n        background: url('../../../assets/largeScreen/category_distribution_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 585px;\r\n        width: 100%;\r\n\r\n        .category_distribution_content {\r\n          height: calc(100% - 70px);\r\n          margin-top: 70px;\r\n        }\r\n      }\r\n\r\n      .hot_word_analysis {\r\n        background: url('../../../assets/largeScreen/hot_word_analysis_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 320px;\r\n        width: 100%;\r\n\r\n        .hot_word_analysis_content {\r\n          height: calc(100% - 92px);\r\n          margin-top: 72px;\r\n          margin-bottom: 20px;\r\n          position: relative;\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n      .submission_status {\r\n        background: url('../../../assets/largeScreen/submission_status_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 245px;\r\n        width: 100%;\r\n\r\n        .submission_status_content {\r\n          margin-top: 75px;\r\n          margin-left: 12px;\r\n          margin-right: 12px;\r\n        }\r\n      }\r\n\r\n      .hand_unit {\r\n        background: url('../../../assets/largeScreen/hand_unit_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 315px;\r\n        width: 100%;\r\n\r\n        .hand_unit_content {\r\n          margin-top: 70px;\r\n          margin-left: 20px;\r\n          margin-right: 20px;\r\n        }\r\n      }\r\n\r\n      .key_proposals {\r\n        background: url('../../../assets/largeScreen/key_proposals_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 315px;\r\n        width: 100%;\r\n\r\n        .key_proposals_list {\r\n          margin-top: 60px;\r\n          margin-left: 14px;\r\n          margin-right: 14px;\r\n          height: calc(100% - 70px);\r\n          overflow-y: auto;\r\n\r\n          &::-webkit-scrollbar {\r\n            width: 4px;\r\n          }\r\n\r\n          &::-webkit-scrollbar-track {\r\n            background: rgba(0, 30, 60, 0.3);\r\n            border-radius: 2px;\r\n          }\r\n\r\n          &::-webkit-scrollbar-thumb {\r\n            background: rgba(0, 212, 255, 0.4);\r\n            border-radius: 2px;\r\n\r\n            &:hover {\r\n              background: rgba(0, 212, 255, 0.6);\r\n            }\r\n          }\r\n\r\n          .key_proposals_item {\r\n            margin-bottom: 12px;\r\n            overflow: hidden;\r\n            position: relative;\r\n\r\n            &:last-child {\r\n              margin-bottom: 0;\r\n            }\r\n\r\n            // 奇数项 - 背景图片样式\r\n            &.with-bg-image {\r\n              background: url('../../../assets/largeScreen/table_bg.png') no-repeat;\r\n              background-size: 100% 100%;\r\n              background-position: center;\r\n            }\r\n\r\n            // 偶数项 - 背景颜色样式\r\n            &.with-bg-color {\r\n              background: rgba(6, 79, 219, 0.05);\r\n            }\r\n\r\n            .key_proposals_content {\r\n              padding: 12px 15px;\r\n              position: relative;\r\n              z-index: 2;\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n\r\n              .key_proposals_title {\r\n                flex: 1;\r\n                color: #fff;\r\n                font-size: 16px;\r\n                margin-right: 16px;\r\n                // 文本溢出处理\r\n                display: -webkit-box;\r\n                -webkit-line-clamp: 1;\r\n                -webkit-box-orient: vertical;\r\n                overflow: hidden;\r\n                text-overflow: ellipsis;\r\n              }\r\n\r\n              .key_proposals_name {\r\n                flex-shrink: 0;\r\n                font-size: 16px;\r\n                color: #FFFFFF;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .center-panel {\r\n      flex: 1;\r\n      gap: 30px;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      .proposal_overall_situation {\r\n        background: url('../../../assets/largeScreen/overall_situation_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        height: 505px;\r\n        width: 100%;\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .proposal_overall_situation_content {\r\n          width: 100%;\r\n          height: calc(100% - 70px);\r\n          margin-top: 70px;\r\n          margin-left: 40px;\r\n          margin-right: 40px;\r\n          display: flex;\r\n          gap: 50px;\r\n\r\n          .left-section {\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: space-around;\r\n            margin: 10px 0;\r\n\r\n            .data-card {\r\n              width: 133px;\r\n              height: 98px;\r\n              display: flex;\r\n              flex-direction: column;\r\n              justify-content: center;\r\n              align-items: center;\r\n\r\n              &.total-proposals {\r\n                background: url('../../../assets/largeScreen/icon_proposal_total.png') no-repeat;\r\n                background-size: 100% 100%;\r\n                background-position: center;\r\n              }\r\n\r\n              &.approved-proposals {\r\n                background: url('../../../assets/largeScreen/icon_proposal_total.png') no-repeat;\r\n                background-size: 100% 100%;\r\n                background-position: center;\r\n              }\r\n\r\n              &.replied-proposals {\r\n                background: url('../../../assets/largeScreen/icon_reply_total.png') no-repeat;\r\n                background-size: 100% 100%;\r\n                background-position: center;\r\n              }\r\n\r\n              .card-number {\r\n                font-weight: 500;\r\n                font-size: 32px;\r\n                margin-bottom: 8px;\r\n              }\r\n\r\n              &.total-proposals .card-number {\r\n                color: #FFFFFF;\r\n              }\r\n\r\n              &.approved-proposals .card-number {\r\n                color: #1FC6FF;\r\n              }\r\n\r\n              &.replied-proposals .card-number {\r\n                color: #F5E74F;\r\n              }\r\n\r\n              .card-label {\r\n                font-size: 16px;\r\n                color: #FFFFFF;\r\n                opacity: 0.9;\r\n              }\r\n            }\r\n          }\r\n\r\n          .right-section {\r\n            flex: 1;\r\n            display: flex;\r\n            flex-direction: column;\r\n            // gap: 100px;\r\n\r\n            .top-charts {\r\n              display: flex;\r\n              justify-content: space-evenly;\r\n\r\n              .chart-item {\r\n                width: 199px;\r\n                height: 199px;\r\n                position: relative;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n\r\n                &.approval-rate {\r\n                  background: url('../../../assets/largeScreen/icon_case_filing.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                  z-index: 1; // 背景图最低优先级\r\n                }\r\n\r\n                &.reply-rate {\r\n                  background: url('../../../assets/largeScreen/icon_reply_rate.png') no-repeat;\r\n                  background-size: 100% 100%;\r\n                  background-position: center;\r\n                  z-index: 1; // 背景图最低优先级\r\n                }\r\n\r\n                // 确保圆形进度图在背景图中央，但在文字下方\r\n                .circular-progress {\r\n                  position: absolute;\r\n                  top: 50%;\r\n                  left: 50%;\r\n                  transform: translate(-50%, -50%);\r\n                  width: 140px;\r\n                  height: 140px;\r\n                  z-index: 50; // 图表中等优先级\r\n                }\r\n              }\r\n            }\r\n\r\n            .bottom-section {\r\n              display: flex;\r\n              justify-content: space-evenly;\r\n              width: 100%;\r\n              height: 230px;\r\n\r\n              .reply-pie-chart {\r\n                width: 55%;\r\n                height: 100%;\r\n              }\r\n\r\n              .reply-progress {\r\n                width: 45%;\r\n                height: 100%;\r\n                display: flex;\r\n                align-items: center;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .committee_proposal_number {\r\n        background: url('../../../assets/largeScreen/committee_proposal_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        height: 400px;\r\n        width: 100%;\r\n\r\n        .committee_proposal_content {\r\n          height: 100%;\r\n          margin-top: 65px;\r\n          margin-left: 16px;\r\n          margin-right: 16px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}