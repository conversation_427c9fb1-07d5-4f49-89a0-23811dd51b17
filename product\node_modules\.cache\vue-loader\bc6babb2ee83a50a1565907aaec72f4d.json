{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecord\\detailsContents.vue?vue&type=style&index=0&id=4a23d992&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecord\\detailsContents.vue", "mtime": 1752541693846}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["detailsContents.vue"], "names": [], "mappings": ";AAmLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detailsContents.vue", "sourceRoot": "src/views/AssessmentOrgan/MonthlyWorkRecord", "sourcesContent": ["<template>\r\n  <div class=\"detailComment scrollBar \">\r\n    <div class=\"officeDetial-title\"> {{form.title}} </div>\r\n    <div class=\"officeDetial-org\">\r\n      <div class=\"org-item\"> 所属个人: <span> {{form.publishUserName}}</span> </div>\r\n      <div class=\"org-item\"> 时间： <span>{{ $format(form.publishTime).substr(0,16)}}</span> </div>\r\n      <div class=\"org-item\"> 部门： <span> {{form.officeName}}</span> </div>\r\n      <div>\r\n\r\n        <el-button type=\"primary\"\r\n                   size=\"small\"\r\n                   v-if=\"this.approve==='true'\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n\r\n        <el-button type=\"danger\"\r\n                   size=\"small\"\r\n                   v-if=\"this.noApprove==='true'\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n      </div>\r\n\r\n    </div>\r\n    <!-- 内容 -->\r\n    <div class=\"contBox\">\r\n      <div class=\"content\"\r\n           v-if=\"form.content\"\r\n           v-html=\"form.content\">\r\n      </div>\r\n    </div>\r\n\r\n    <!-- *****附件***** -->\r\n    <div class=\"fileBox\"\r\n         v-if=\"form.attachment\">\r\n      <div class=\"file_title\"\r\n           v-if=\"form.attachment.length!==0\"> 附件 </div>\r\n\r\n      <div class=\"fileListt\">\r\n        <div class=\"file_item\"\r\n             v-for=\"(item,index) in form.attachment \"\r\n             :key=\"index\">\r\n\r\n          <div class=\"file_name\"> {{item.oldName}} </div>\r\n\r\n          <div class=\"file_load\">\r\n            <div class=\"load_text\"><a :href=\"item.fullPath\"\r\n                 target=\"_blank\">预览</a>\r\n            </div>\r\n            <div class=\"shu\"></div>\r\n            <div class=\"load_text\"\r\n                 @click=\"download(item)\">下载</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { mapActions, mapState } from 'vuex'\r\n\r\nexport default {\r\n  name: 'detailsContents',\r\n  components: {},\r\n\r\n  data () {\r\n    return {\r\n      ids: [],\r\n      TyposShow: false,\r\n      // SimilarityShow: false,\r\n      form: {},\r\n      fileList: [],\r\n      manuscriptData: {},\r\n      all: this.$route.query.all || false,\r\n      rowId: this.$route.query.rowId,\r\n\r\n      approve: this.$route.query.approve,\r\n      noApprove: this.$route.query.noApprove,\r\n\r\n      manuscriptFlag: false,\r\n      isexcellent: this.$route.query.isexcellent || false,\r\n      helper: this.$route.query.helper || false,\r\n      similar: '0%',\r\n      suspend: 0,\r\n      wrong: 0,\r\n      show: true,\r\n      clickTime: ''\r\n\r\n    }\r\n  },\r\n  inject: ['openoffice', 'openPdf'],\r\n  created () {\r\n    this.getWorkDetails()\r\n  },\r\n  mounted () {\r\n  },\r\n  methods: {\r\n\r\n    passClick (auditStatus) {\r\n      this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.getCheckMonthlyWork(this.$route.query.rowId, auditStatus)\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消操作'\r\n        })\r\n      })\r\n    },\r\n    // 审核 (加分项)\r\n    async getCheckMonthlyWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckMonthlyWork({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n\r\n    ...mapActions(['getWorkDetails']),\r\n    showHelper () {\r\n      if (new Date().getTime() - this.clickTime > 1000) {\r\n        this.clickTime = new Date().getTime()\r\n        this.show = !this.show\r\n      }\r\n    },\r\n\r\n    async getcorrector () {\r\n      const res = await this.$api.publicOpinionNew.corrector({\r\n        title: '',\r\n        content: this.form.content,\r\n        sessions: '',\r\n        times: ''\r\n      })\r\n      var data = JSON.parse(res.data)\r\n\r\n      this.suspend = data.detail.length\r\n      this.wrong = data.suspend_detail.length\r\n    },\r\n    // 预览(另一种方法替代)\r\n    // priew (data) {\r\n    //   const arr = ['doc', 'docx', 'xlsx']\r\n    //   if (arr.includes(data.extension)) {\r\n    //     this.openoffice(data.openUrl)\r\n    //   }\r\n    // },\r\n\r\n    async getWorkDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqWorkDetails(this.$route.query.rowId)\r\n      var { data } = res\r\n      this.form = data\r\n      if (this.form.content) {\r\n        this.form.content = this.form.content.replace(/&amp;nbsp;/g, ' ') // 消除空格字符\r\n      }\r\n    },\r\n    // 通用的附件下载方法download 只需要改对应的附件id和名字\r\n    download (data) {\r\n      this.$api.proposal.downloadFile({ id: this.form.attachment.id }, this.form.attachment.oldName)\r\n    },\r\n    callback (data) {\r\n      this.TyposShow = false\r\n      console.log(data)\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    // 测试: 通过映射函数 获取title信息\r\n    ...mapState(['title'])\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" >\r\n.detailComment {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding-top: 33px;\r\n  background: #fff;\r\n  .officeDetial-title {\r\n    font-size: 26px;\r\n    font-size: 24px;\r\n    font-family: PingFang SC;\r\n    font-weight: 800;\r\n    color: #333333;\r\n    line-height: 36px;\r\n    text-align: center;\r\n    margin: 29px;\r\n  }\r\n  .officeDetial-org {\r\n    font-size: $textSize14;\r\n    font-family: PingFang SC;\r\n    font-weight: 400;\r\n    line-height: 36px;\r\n    display: flex;\r\n    justify-content: space-around;\r\n    // padding-left: 40px;\r\n    .org-item {\r\n      color: #999999;\r\n      // margin-right: 140px;\r\n      // min-width: 300px;\r\n      span {\r\n        margin-left: 38px;\r\n      }\r\n    }\r\n    // .org-item + .org-item {\r\n    //     margin-left: 140px;\r\n    // }\r\n  }\r\n\r\n  .contBox {\r\n    margin-top: 20px;\r\n    border-top: 2px solid #ebebeb;\r\n    display: flex;\r\n\r\n    .content {\r\n      flex: 1;\r\n      padding: 30px 40px;\r\n      line-height: 30px;\r\n      min-height: 500px;\r\n    }\r\n    .content + .content {\r\n      border-left: 1px solid #ebebeb;\r\n    }\r\n  }\r\n\r\n  .similarityImg {\r\n    padding-left: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    img {\r\n      width: 80px;\r\n      cursor: pointer;\r\n    }\r\n    .analysisReslut {\r\n      height: 60px;\r\n      line-height: 60px;\r\n      padding: 0 44px;\r\n      // background: #f5f5fb;\r\n      background-image: url(\"../../../assets/qdimg/round.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      border-radius: 20px;\r\n      span {\r\n        font-size: 26px;\r\n        font-family: PangMenZhengDao;\r\n        font-weight: 700;\r\n        color: #007bff;\r\n        line-height: 36px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n  .manuscript {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-right: 40px;\r\n    padding-bottom: 20px;\r\n    .yuangoa {\r\n      flex: 1;\r\n      span {\r\n        font-size: 26px;\r\n        font-family: PingFang SC;\r\n        font-weight: 500;\r\n        color: #999999;\r\n        line-height: 36px;\r\n        margin-left: 40px;\r\n      }\r\n      span + span {\r\n        margin-left: 50%;\r\n      }\r\n    }\r\n  }\r\n  .fileBox {\r\n    width: 100%;\r\n    background: #ffffff;\r\n    border-radius: 10px;\r\n    padding: 30px;\r\n    .file_title {\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-bottom: 23px;\r\n    }\r\n    .fileListt {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      justify-content: space-between;\r\n      .file_item {\r\n        width: 48%;\r\n        background: #f5f5fb;\r\n        flex-shrink: 0;\r\n        height: 60px;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0 18px;\r\n        margin-bottom: 10px;\r\n        .file_type {\r\n          width: 32px;\r\n          height: 32px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .file_name {\r\n          margin-left: 12px;\r\n          flex: 1;\r\n          cursor: pointer;\r\n        }\r\n        .file_load {\r\n          display: flex;\r\n          align-items: center;\r\n          .load_text {\r\n            font-size: $textSize16;\r\n            font-family: PingFang SC;\r\n            font-weight: 500;\r\n            color: #007bff;\r\n            line-height: 36px;\r\n            cursor: pointer;\r\n          }\r\n          .shu {\r\n            width: 2px;\r\n            height: 22px;\r\n            background: #4f96fe;\r\n            margin: 0 12px;\r\n          }\r\n          .del {\r\n            width: 24px;\r\n            height: 24px;\r\n            margin-left: 23px;\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .hanldtype {\r\n    display: flex;\r\n    padding: 20px 30px;\r\n    justify-content: space-between;\r\n    > div {\r\n      font-weight: 700;\r\n    }\r\n  }\r\n  .handinfo {\r\n    width: 100%;\r\n    .hanldClounm .hanldCont .el-checkbox {\r\n      width: 25%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}