{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarChart.vue", "mtime": 1756371347097}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCAqIGFzIGVjaGFydHMgZnJvbSAnZWNoYXJ0cycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRGlzY3Vzc2lvbkNoYXJ0JywKICBwcm9wczogewogICAgaWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKICAgIGxlZ2VuZFNob3c6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgcmVxdWlyZWQ6IGZhbHNlCiAgICB9LAogICAgbGVnZW5kTmFtZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiAnJwogICAgfSwKICAgIGNoYXJ0RGF0YTogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdCiAgICB9CiAgfSwKICBkYXRhICgpIHsKICAgIHJldHVybiB7CiAgICAgIGNoYXJ0OiBudWxsCiAgICB9CiAgfSwKICBtb3VudGVkICgpIHsKICAgIHRoaXMuaW5pdENoYXJ0KCkKICB9LAogIGJlZm9yZURlc3Ryb3kgKCkgewogICAgaWYgKHRoaXMuY2hhcnQpIHsKICAgICAgdGhpcy5jaGFydC5kaXNwb3NlKCkKICAgIH0KICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLnJlc2l6ZUNoYXJ0KQogIH0sCiAgd2F0Y2g6IHsKICAgIGNoYXJ0RGF0YTogewogICAgICBoYW5kbGVyICgpIHsKICAgICAgICB0aGlzLnJlbmRlckNoYXJ0KCkKICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgaW5pdENoYXJ0ICgpIHsKICAgICAgY29uc3QgY2hhcnRDb250YWluZXIgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCh0aGlzLmlkKQogICAgICBpZiAoIWNoYXJ0Q29udGFpbmVyKSByZXR1cm4KICAgICAgdGhpcy5jaGFydCA9IGVjaGFydHMuaW5pdChjaGFydENvbnRhaW5lcikKICAgICAgdGhpcy5yZW5kZXJDaGFydCgpCiAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLnJlc2l6ZUNoYXJ0KQogICAgfSwKICAgIGdldE9wdGlvbiAoKSB7CiAgICAgIGNvbnN0IHhBeGlzRGF0YSA9IHRoaXMuY2hhcnREYXRhLm1hcChpdGVtID0+IGl0ZW0ubmFtZSkKICAgICAgY29uc3Qgc2VyaWVzRGF0YSA9IHRoaXMuY2hhcnREYXRhLm1hcChpdGVtID0+IGl0ZW0udmFsdWUpCiAgICAgIHJldHVybiB7CiAgICAgICAgbGVnZW5kOiB7CiAgICAgICAgICBzaG93OiB0aGlzLmxlZ2VuZFNob3csCiAgICAgICAgICBkYXRhOiBbdGhpcy5sZWdlbmROYW1lXSwKICAgICAgICAgIHRvcDogJzIlJywKICAgICAgICAgIGxlZnQ6ICdjZW50ZXInLAogICAgICAgICAgdGV4dFN0eWxlOiB7CiAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsCiAgICAgICAgICAgIGZvbnRTaXplOiAxMgogICAgICAgICAgfSwKICAgICAgICAgIGl0ZW1XaWR0aDogMTIsCiAgICAgICAgICBpdGVtSGVpZ2h0OiA4CiAgICAgICAgfSwKICAgICAgICBncmlkOiB7CiAgICAgICAgICBsZWZ0OiB0aGlzLmlkID09PSAnY29tbWl0dGVlX3Byb3Bvc2FsJyA/ICcwJScgOiAnMyUnLAogICAgICAgICAgcmlnaHQ6IHRoaXMuaWQgPT09ICdjb21taXR0ZWVfcHJvcG9zYWwnID8gJzAlJyA6ICczJScsCiAgICAgICAgICBib3R0b206IHRoaXMuaWQgPT09ICdjb21taXR0ZWVfcHJvcG9zYWwnID8gJzIwJScgOiAnOCUnLAogICAgICAgICAgdG9wOiB0aGlzLmlkID09PSAnY29tbWl0dGVlX3Byb3Bvc2FsJyA/ICcxMCUnIDogJzEwJScsCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUKICAgICAgICB9LAogICAgICAgIHhBeGlzOiB7CiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLAogICAgICAgICAgZGF0YTogeEF4aXNEYXRhLAogICAgICAgICAgYXhpc0xpbmU6IHsKICAgICAgICAgICAgc2hvdzogdHJ1ZSwKICAgICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyknCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICBheGlzVGljazogewogICAgICAgICAgICBzaG93OiBmYWxzZQogICAgICAgICAgfSwKICAgICAgICAgIGF4aXNMYWJlbDogewogICAgICAgICAgICBzaG93OiB0cnVlLAogICAgICAgICAgICBjb2xvcjogJyNBMkIwQjgnLAogICAgICAgICAgICBmb250U2l6ZTogdGhpcy5pZCA9PT0gJ2NvbW1pdHRlZV9wcm9wb3NhbCcgPyAxMiA6IDE0LAogICAgICAgICAgICBpbnRlcnZhbDogMCwKICAgICAgICAgICAgcm90YXRlOiAwLAogICAgICAgICAgICBtYXJnaW46IDgsCiAgICAgICAgICAgIGZvcm1hdHRlcjogdGhpcy5pZCA9PT0gJ2NvbW1pdHRlZV9wcm9wb3NhbCcgPyB0aGlzLmZvcm1hdEF4aXNMYWJlbCA6IG51bGwKICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIHlBeGlzOiB7CiAgICAgICAgICB0eXBlOiAndmFsdWUnLAogICAgICAgICAgc3BsaXROdW1iZXI6IDQsCiAgICAgICAgICBheGlzTGluZTogewogICAgICAgICAgICBzaG93OiBmYWxzZQogICAgICAgICAgfSwKICAgICAgICAgIGF4aXNUaWNrOiB7CiAgICAgICAgICAgIHNob3c6IGZhbHNlCiAgICAgICAgICB9LAogICAgICAgICAgYXhpc0xhYmVsOiB7CiAgICAgICAgICAgIHNob3c6IHRydWUsCiAgICAgICAgICAgIGNvbG9yOiAnI0EyQjBCOCcsCiAgICAgICAgICAgIGZvbnRTaXplOiAxNAogICAgICAgICAgfSwKICAgICAgICAgIHNwbGl0TGluZTogewogICAgICAgICAgICBzaG93OiB0cnVlLAogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKScsCiAgICAgICAgICAgICAgdHlwZTogJ2Rhc2hlZCcKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgc2VyaWVzOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHR5cGU6ICdiYXInLAogICAgICAgICAgICBuYW1lOiB0aGlzLmxlZ2VuZE5hbWUsCiAgICAgICAgICAgIGRhdGE6IHNlcmllc0RhdGEsCiAgICAgICAgICAgIGJhcldpZHRoOiAyNSwKICAgICAgICAgICAgc2hvd0JhY2tncm91bmQ6IHRoaXMubGVnZW5kU2hvdywgLy8g5pi+56S66IOM5pmvCiAgICAgICAgICAgIGJhY2tncm91bmRTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpJywKICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IFsyLCAyLCAwLCAwXQogICAgICAgICAgICB9LAogICAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogewogICAgICAgICAgICAgICAgdHlwZTogJ2xpbmVhcicsCiAgICAgICAgICAgICAgICB4OiAwLAogICAgICAgICAgICAgICAgeTogMCwKICAgICAgICAgICAgICAgIHgyOiAwLAogICAgICAgICAgICAgICAgeTI6IDEsCiAgICAgICAgICAgICAgICBjb2xvclN0b3BzOiBbCiAgICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAwLCBjb2xvcjogJ3JnYmEoMzEsIDE5OCwgMjU1LCAxKScgfSwKICAgICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDEsIGNvbG9yOiAncmdiYSgzMSwgMTk4LCAyNTUsIDApJyB9CiAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IFsyLCAyLCAwLCAwXQogICAgICAgICAgICB9LAogICAgICAgICAgICBlbXBoYXNpczogewogICAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgICAgY29sb3I6IHsKICAgICAgICAgICAgICAgICAgdHlwZTogJ2xpbmVhcicsCiAgICAgICAgICAgICAgICAgIHg6IDAsCiAgICAgICAgICAgICAgICAgIHk6IDAsCiAgICAgICAgICAgICAgICAgIHgyOiAwLAogICAgICAgICAgICAgICAgICB5MjogMSwKICAgICAgICAgICAgICAgICAgY29sb3JTdG9wczogWwogICAgICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAwLCBjb2xvcjogJyMwMEU0RkYnIH0sCiAgICAgICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDAuNSwgY29sb3I6ICcjMDA5MEZGJyB9LAogICAgICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAxLCBjb2xvcjogJyMwMDUwOTAnIH0KICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwKICAgICAgICAgICAgbGFiZWw6IHsKICAgICAgICAgICAgICBzaG93OiB0cnVlLAogICAgICAgICAgICAgIHBvc2l0aW9uOiAndG9wJywKICAgICAgICAgICAgICBjb2xvcjogJyNGRkZGRkYnLAogICAgICAgICAgICAgIGZvbnRTaXplOiAxMgogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgXSwKICAgICAgICB0b29sdGlwOiB7CiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsCiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDAsIDAsIDAsIDAuOCknLAogICAgICAgICAgYm9yZGVyQ29sb3I6ICcjMDBENEZGJywKICAgICAgICAgIGJvcmRlcldpZHRoOiAxLAogICAgICAgICAgdGV4dFN0eWxlOiB7CiAgICAgICAgICAgIGNvbG9yOiAnI0ZGRkZGRicKICAgICAgICAgIH0sCiAgICAgICAgICBheGlzUG9pbnRlcjogewogICAgICAgICAgICB0eXBlOiAnc2hhZG93JwogICAgICAgICAgfSwKICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gKHBhcmFtcykgewogICAgICAgICAgICBjb25zdCBkYXRhID0gcGFyYW1zWzBdCiAgICAgICAgICAgIHJldHVybiBgJHtkYXRhLm5hbWV9PGJyLz7kurrmlbA6ICR7ZGF0YS52YWx1ZX3kurpgCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgcmVuZGVyQ2hhcnQgKCkgewogICAgICBpZiAoIXRoaXMuY2hhcnQgfHwgIXRoaXMuY2hhcnREYXRhLmxlbmd0aCkgcmV0dXJuCiAgICAgIHRoaXMuY2hhcnQuc2V0T3B0aW9uKHRoaXMuZ2V0T3B0aW9uKCksIHRydWUpCiAgICB9LAogICAgcmVzaXplQ2hhcnQgKCkgewogICAgICBpZiAodGhpcy5jaGFydCkgewogICAgICAgIHRoaXMuY2hhcnQucmVzaXplKCkKICAgICAgfQogICAgfSwKICAgIGZvcm1hdEF4aXNMYWJlbCAodmFsdWUpIHsKICAgICAgLy8g5Li6Y29tbWl0dGVlX3Byb3Bvc2Fs5aSE55CG5paH5pys5o2i6KGMCiAgICAgIGlmICh0aGlzLmlkID09PSAnY29tbWl0dGVlX3Byb3Bvc2FsJykgewogICAgICAgIC8vIOagueaNruaWh+acrOmVv+W6pui/m+ihjOaNouihjOWkhOeQhgogICAgICAgIGlmICh2YWx1ZS5sZW5ndGggPiA0KSB7CiAgICAgICAgICAvLyDlpoLmnpzljIXlkKsi5aeUIuWtl++8jOWcqCLlp5Qi5a2X5ZCO5o2i6KGMCiAgICAgICAgICBpZiAodmFsdWUuaW5jbHVkZXMoJ+WnlCcpICYmIHZhbHVlLmluZGV4T2YoJ+WnlCcpIDwgdmFsdWUubGVuZ3RoIC0gMSkgewogICAgICAgICAgICByZXR1cm4gdmFsdWUucmVwbGFjZSgn5aeUJywgJ+WnlFxuJykKICAgICAgICAgIH0KICAgICAgICAgIC8vIOWQpuWImeWcqOS4remXtOS9jee9ruaNouihjAogICAgICAgICAgY29uc3QgbWlkID0gTWF0aC5jZWlsKHZhbHVlLmxlbmd0aCAvIDIpCiAgICAgICAgICByZXR1cm4gdmFsdWUuc3Vic3RyaW5nKDAsIG1pZCkgKyAnXG4nICsgdmFsdWUuc3Vic3RyaW5nKG1pZCkKICAgICAgICB9CiAgICAgIH0KICAgICAgcmV0dXJuIHZhbHVlCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["BarChart.vue"], "names": [], "mappings": ";AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BarChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"discussion-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'DiscussionC<PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    legendShow: {\n      type: Boolean,\n      required: false\n    },\n    legendName: {\n      type: String,\n      required: ''\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  watch: {\n    chartData: {\n      handler () {\n        this.renderChart()\n      },\n      deep: true\n    }\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.renderChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n    getOption () {\n      const xAxisData = this.chartData.map(item => item.name)\n      const seriesData = this.chartData.map(item => item.value)\n      return {\n        legend: {\n          show: this.legendShow,\n          data: [this.legendName],\n          top: '2%',\n          left: 'center',\n          textStyle: {\n            color: '#fff',\n            fontSize: 12\n          },\n          itemWidth: 12,\n          itemHeight: 8\n        },\n        grid: {\n          left: this.id === 'committee_proposal' ? '0%' : '3%',\n          right: this.id === 'committee_proposal' ? '0%' : '3%',\n          bottom: this.id === 'committee_proposal' ? '20%' : '8%',\n          top: this.id === 'committee_proposal' ? '10%' : '10%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData,\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.3)'\n            }\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#A2B0B8',\n            fontSize: this.id === 'committee_proposal' ? 12 : 14,\n            interval: 0,\n            rotate: 0,\n            margin: 8,\n            formatter: this.id === 'committee_proposal' ? this.formatAxisLabel : null\n          }\n        },\n        yAxis: {\n          type: 'value',\n          splitNumber: 4,\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#A2B0B8',\n            fontSize: 14\n          },\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              type: 'dashed'\n            }\n          }\n        },\n        series: [\n          {\n            type: 'bar',\n            name: this.legendName,\n            data: seriesData,\n            barWidth: 25,\n            showBackground: this.legendShow, // 显示背景\n            backgroundStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              borderRadius: [2, 2, 0, 0]\n            },\n            itemStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: 'rgba(31, 198, 255, 1)' },\n                  { offset: 1, color: 'rgba(31, 198, 255, 0)' }\n                ]\n              },\n              borderRadius: [2, 2, 0, 0]\n            },\n            emphasis: {\n              itemStyle: {\n                color: {\n                  type: 'linear',\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: '#00E4FF' },\n                    { offset: 0.5, color: '#0090FF' },\n                    { offset: 1, color: '#005090' }\n                  ]\n                }\n              }\n            },\n            label: {\n              show: true,\n              position: 'top',\n              color: '#FFFFFF',\n              fontSize: 12\n            }\n          }\n        ],\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00D4FF',\n          borderWidth: 1,\n          textStyle: {\n            color: '#FFFFFF'\n          },\n          axisPointer: {\n            type: 'shadow'\n          },\n          formatter: function (params) {\n            const data = params[0]\n            return `${data.name}<br/>人数: ${data.value}人`\n          }\n        }\n      }\n    },\n    renderChart () {\n      if (!this.chart || !this.chartData.length) return\n      this.chart.setOption(this.getOption(), true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    },\n    formatAxisLabel (value) {\n      // 为committee_proposal处理文本换行\n      if (this.id === 'committee_proposal') {\n        // 根据文本长度进行换行处理\n        if (value.length > 4) {\n          // 如果包含\"委\"字，在\"委\"字后换行\n          if (value.includes('委') && value.indexOf('委') < value.length - 1) {\n            return value.replace('委', '委\\n')\n          }\n          // 否则在中间位置换行\n          const mid = Math.ceil(value.length / 2)\n          return value.substring(0, mid) + '\\n' + value.substring(mid)\n        }\n      }\n      return value\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.discussion-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}