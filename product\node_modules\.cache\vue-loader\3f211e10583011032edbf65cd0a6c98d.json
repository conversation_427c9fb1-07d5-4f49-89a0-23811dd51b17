{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tree\\zy-tree.vue?vue&type=style&index=0&id=6e01f169&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tree\\zy-tree.vue", "mtime": 1752541693607}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICcuL3p5LXRyZWUuc2Nzcyc7DQo="}, {"version": 3, "sources": ["zy-tree.vue"], "names": [], "mappings": ";AAkFA", "file": "zy-tree.vue", "sourceRoot": "src/components/zy-tree", "sourcesContent": ["<template>\r\n  <div class=\"zy-tree\">\r\n    <el-scrollbar class=\"zy-tree-box\">\r\n      <zy-tree-components\r\n        :tree=\"tree\"\r\n        v-model=\"id\"\r\n        :anykey=\"anykey\"\r\n        :props=\"props\"\r\n        :node-key=\"nodeKey\"\r\n        @on-tree-click=\"selectedClick\"\r\n      ></zy-tree-components>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyTree',\r\n  data () {\r\n    return {\r\n      id: this.value\r\n    }\r\n  },\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    // 数据\r\n    tree: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 宽度\r\n    width: {\r\n      type: String,\r\n      default: '296'\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    anykey: {\r\n      type: Array,\r\n      default: function () {\r\n        return []\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.id = val\r\n      }\r\n    },\r\n    id (val) {\r\n      this.$emit('id', val)\r\n    }\r\n  },\r\n  methods: {\r\n    selectedClick (item) {\r\n      this.$emit('on-tree-click', item)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './zy-tree.scss';\r\n</style>\r\n"]}]}