{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-cascader-checkbox\\zy-cascader-checkbox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-cascader-checkbox\\zy-cascader-checkbox.vue", "mtime": 1660102037660}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICd6eUNhc2NhZGVyQ2hlY2tib3gnLAoKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAga2V5d29yZDogJycsCiAgICAgIHNlbGVjdGVkczogW10sCiAgICAgIG9wdGlvbnNfc2hvdzogZmFsc2UsCiAgICAgIGNoZWNrZWRfa2V5czogW10KICAgIH07CiAgfSwKCiAgcHJvcHM6IHsKICAgIC8vIOaVsOaNrgogICAgZGF0YTogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0sCiAgICAvLyDmoJHnu5PmnoTphY3nva4KICAgIHByb3BzOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogKCkgPT4gewogICAgICAgIHJldHVybiB7fTsKICAgICAgfQogICAgfSwKICAgIC8vIG5vZGUta2V5CiAgICBub2RlS2V5OiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJ2lkJwogICAgfSwKICAgIC8vIOmAieS4reaVsOaNrgogICAgdmFsdWU6IFtTdHJpbmcsIE51bWJlciwgQXJyYXksIE9iamVjdF0sCiAgICAvLyDmmK/lkKblj6rlj6/pgInlj7blrZDoioLngrkKICAgIGxlYWY6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogdHJ1ZQogICAgfSwKICAgIC8vIOWuveW6pgogICAgd2lkdGg6IFN0cmluZywKICAgIC8vIOinpuWPkeaWueW8jyBjbGljay9mb2N1cy9ob3Zlci9tYW51YWwKICAgIHRyaWdnZXI6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnY2xpY2snCiAgICB9LAogICAgLy8g5piv5ZCm56aB55SoCiAgICBkaXNhYmxlZDogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfSwKICAgIHBsYWNlaG9sZGVyOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJ+ivt+mAieaLqScKICAgIH0sCiAgICAvLyDmmK/lkKblsZXlvIDlhajpg6gKICAgIGRlZmF1bHRFeHBhbmRBbGw6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0sCiAgICAvLyDpu5jorqTlsZXlvIDnmoToioLngrnnmoQga2V5IOeahOaVsOe7hAogICAgZGVmYXVsdEV4cGFuZGVkS2V5czogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogKCkgPT4gewogICAgICAgIHJldHVybiBbXTsKICAgICAgfQogICAgfSwKICAgIG1heDogewogICAgICB0eXBlOiBOdW1iZXIsCiAgICAgIGRlZmF1bHQ6IDAKICAgIH0KICB9LAogIG1vZGVsOiB7CiAgICBwcm9wOiAndmFsdWUnLAogICAgZXZlbnQ6ICdjaGFuZ2UnCiAgfSwKCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuY2hhZWNrRGVmYXVsdFZhbHVlKCk7CiAgfSwKCiAgd2F0Y2g6IHsKICAgIGtleXdvcmQodmFsKSB7CiAgICAgIHRoaXMuJHJlZnNbJ3RyZWUtc2VsZWN0J10uZmlsdGVyKHZhbCk7CiAgICB9LAoKICAgIHZhbHVlKHZhbCkgewogICAgICB0aGlzLmNoYWVja0RlZmF1bHRWYWx1ZSgpOwogICAgfSwKCiAgICBkYXRhKHZhbCkgewogICAgICB0aGlzLmNoYWVja0RlZmF1bHRWYWx1ZSgpOwogICAgfQoKICB9LAogIGNvbXB1dGVkOiB7CiAgICBzZWxmRGF0YSgpIHsKICAgICAgcmV0dXJuIHRoaXMuZGF0YTsKICAgIH0sCgogICAgc2VsZlByb3BzKCkgewogICAgICByZXR1cm4gewogICAgICAgIGxhYmVsOiAnbGFiZWwnLAogICAgICAgIGNoaWxkcmVuOiAnY2hpbGRyZW4nLAogICAgICAgIGRpc2FibGVkOiBkYXRhID0+IHsKICAgICAgICAgIHJldHVybiBkYXRhLmRpc2FibGVkOwogICAgICAgIH0sCiAgICAgICAgLi4udGhpcy5wcm9wcwogICAgICB9OwogICAgfQoKICB9LAogIG1ldGhvZHM6IHsKICAgIGZpbHRlck5vZGUodmFsdWUsIGRhdGEpIHsKICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWU7CiAgICAgIHJldHVybiBkYXRhW3RoaXMuc2VsZlByb3BzLmxhYmVsXS5pbmRleE9mKHZhbHVlKSAhPT0gLTE7CiAgICB9LAoKICAgIGhhbmRsZUNoZWNrQ2hhbmdlKCkgewogICAgICBjb25zdCBub2RlcyA9IHRoaXMuJHJlZnNbJ3RyZWUtc2VsZWN0J10uZ2V0Q2hlY2tlZE5vZGVzKHRoaXMubGVhZik7CiAgICAgIHRoaXMuc2VsZWN0ZWRzID0gbm9kZXM7CiAgICAgIHRoaXMuJGVtaXQoJ2NoYW5nZScsIG5vZGVzKTsKICAgIH0sCgogICAgdGFiQ2xvc2UoSWQpIHsKICAgICAgaWYgKHRoaXMuZGlzYWJsZWQpIHJldHVybjsKICAgICAgdGhpcy4kcmVmc1sndHJlZS1zZWxlY3QnXS5zZXRDaGVja2VkKElkLCBmYWxzZSwgdHJ1ZSk7CiAgICAgIHRoaXMuc2VsZWN0ZWRzID0gdGhpcy4kcmVmc1sndHJlZS1zZWxlY3QnXS5nZXRDaGVja2VkTm9kZXMoKTsKICAgICAgdGhpcy4kZW1pdCgnY2hhbmdlJywgdGhpcy5zZWxlY3RlZHMpOwogICAgfSwKCiAgICBjaGFlY2tEZWZhdWx0VmFsdWUoKSB7CiAgICAgIGNvbnN0IHZhbCA9IHRoaXMudmFsdWU7CgogICAgICBpZiAoIXZhbCB8fCBBcnJheS5pc0FycmF5KHZhbCkgJiYgdmFsLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuc2VsZWN0ZWRzID0gW107CiAgICAgICAgdGhpcy5jaGVja2VkX2tleXMgPSBbXTsKICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICB0aGlzLiRyZWZzWyd0cmVlLXNlbGVjdCddLnNldENoZWNrZWRLZXlzKFtdKTsKICAgICAgICB9KTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRoaXMuY2hlY2tlZF9rZXlzID0gdHlwZW9mIHZhbFswXSA9PT0gJ29iamVjdCcgPyB2YWwubWFwKGkgPT4gaVt0aGlzLm5vZGVLZXldKSA6IHZhbDsKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuc2VsZWN0ZWRzID0gdGhpcy4kcmVmc1sndHJlZS1zZWxlY3QnXS5nZXRDaGVja2VkTm9kZXModGhpcy5sZWFmKTsKICAgICAgfSk7CiAgICB9CgogIH0KfTs="}, {"version": 3, "mappings": "AAkDA;EACAA,0BADA;;EAEAC;IACA;MACAC,WADA;MAEAC,aAFA;MAGAC,mBAHA;MAIAC;IAJA;EAMA,CATA;;EAUAC;IACA;IACAL;MACAM,WADA;MAEAC;IAFA,CAFA;IAMA;IACAF;MACAC,YADA;MAEAC;QACA;MACA;IAJA,CAPA;IAaA;IACAC;MACAF,YADA;MAEAC;IAFA,CAdA;IAkBA;IACAE,sCAnBA;IAoBA;IACAC;MACAJ,aADA;MAEAC;IAFA,CArBA;IAyBA;IACAI,aA1BA;IA2BA;IACAC;MACAN,YADA;MAEAC;IAFA,CA5BA;IAgCA;IACAM;MACAP,aADA;MAEAC;IAFA,CAjCA;IAqCAO;MACAR,YADA;MAEAC;IAFA,CArCA;IAyCA;IACAQ;MACAT,aADA;MAEAC;IAFA,CA1CA;IA8CA;IACAS;MACAV,WADA;MAEAC;QACA;MACA;IAJA,CA/CA;IAqDAU;MACAX,YADA;MAEAC;IAFA;EArDA,CAVA;EAoEAW;IACAC,aADA;IAEAC;EAFA,CApEA;;EAwEAC;IACA;EACA,CA1EA;;EA2EAC;IACArB;MACA;IACA,CAHA;;IAIAQ;MACA;IACA,CANA;;IAOAT;MACA;IACA;;EATA,CA3EA;EAsFAuB;IACAC;MACA;IACA,CAHA;;IAIAC;MACA;QACAC,cADA;QAEAC,oBAFA;QAGAd;UACA;QACA,CALA;QAMA;MANA;IAQA;;EAbA,CAtFA;EAqGAe;IACAC;MACA;MACA;IACA,CAJA;;IAKAC;MACA;MACA;MACA;IACA,CATA;;IAUAC;MACA;MACA;MACA;MACA;IACA,CAfA;;IAgBAC;MACA;;MACA;QACA;QACA;QACA;UACA;QACA,CAFA;QAGA;MACA;;MACA;MACA;QACA;MACA,CAFA;IAGA;;EA9BA;AArGA", "names": ["name", "data", "keyword", "selecteds", "options_show", "checked_keys", "props", "type", "default", "nodeKey", "value", "leaf", "width", "trigger", "disabled", "placeholder", "defaultExpandAll", "defaultExpandedKeys", "max", "model", "prop", "event", "created", "watch", "computed", "selfData", "selfProps", "label", "children", "methods", "filterNode", "handleCheckChange", "tabClose", "chaeckDefaultValue"], "sourceRoot": "src/components/zy-cascader-checkbox", "sources": ["zy-cascader-checkbox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-cascader-checkbox\"\r\n       :style=\"{ width: width + 'px' }\">\r\n    <el-popover popper-class=\"zy-cascader-checkbox-popover\"\r\n                :trigger=\"trigger\"\r\n                :disabled=\"disabled\"\r\n                :width=\"width\"\r\n                v-model=\"options_show\">\r\n      <div slot=\"reference\"\r\n           :class=\"['zy-cascader-checkbox-input',disabled?'zy-cascader-checkbox-input-disabled':'']\">\r\n        <div :class=\"['zy-cascader-checkbox-input-icon',options_show?'zy-cascader-checkbox-input-icon-a':'']\">\r\n          <i class=\"el-icon-arrow-down\"></i>\r\n        </div>\r\n        <div v-if=\"!selecteds.length\"\r\n             class=\"zy-cascader-checkbox-input-text\">{{placeholder}}</div>\r\n        <el-tag closable\r\n                size=\"medium\"\r\n                v-for=\"item in selecteds\"\r\n                :disable-transitions=\"false\"\r\n                :title=\"item[selfProps.label]\"\r\n                :key=\"item[nodeKey]\"\r\n                @close=\"tabClose(item[nodeKey])\">{{ item[selfProps.label] }}</el-tag>\r\n      </div>\r\n\r\n      <el-scrollbar class=\"zy-cascader-checkbox-box-box\">\r\n        <div>\r\n          <el-input placeholder=\"请输入关键字查询\"\r\n                    v-model=\"keyword\"\r\n                    clearable>\r\n          </el-input>\r\n        </div>\r\n        <el-scrollbar class=\"zy-cascader-checkbox-box\">\r\n          <el-tree show-checkbox\r\n                   :data=\"selfData\"\r\n                   ref=\"tree-select\"\r\n                   highlight-current\r\n                   :props=\"selfProps\"\r\n                   :node-key=\"nodeKey\"\r\n                   @check=\"handleCheckChange\"\r\n                   :filter-node-method=\"filterNode\"\r\n                   class=\"zy-cascader-checkbox-tree\"\r\n                   :default-checked-keys=\"checked_keys\"\r\n                   :default-expand-all=\"defaultExpandAll\"\r\n                   :default-expanded-keys=\"defaultExpandedKeys\"></el-tree>\r\n        </el-scrollbar>\r\n      </el-scrollbar>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyCascaderCheckbox',\r\n  data () {\r\n    return {\r\n      keyword: '',\r\n      selecteds: [],\r\n      options_show: false,\r\n      checked_keys: []\r\n    }\r\n  },\r\n  props: {\r\n    // 数据\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {}\r\n      }\r\n    },\r\n    // node-key\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    // 选中数据\r\n    value: [String, Number, Array, Object],\r\n    // 是否只可选叶子节点\r\n    leaf: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 宽度\r\n    width: String,\r\n    // 触发方式 click/focus/hover/manual\r\n    trigger: {\r\n      type: String,\r\n      default: 'click'\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '请选择'\r\n    },\r\n    // 是否展开全部\r\n    defaultExpandAll: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 默认展开的节点的 key 的数组\r\n    defaultExpandedKeys: {\r\n      type: Array,\r\n      default: () => {\r\n        return []\r\n      }\r\n    },\r\n    max: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'change'\r\n  },\r\n  created () {\r\n    this.chaeckDefaultValue()\r\n  },\r\n  watch: {\r\n    keyword (val) {\r\n      this.$refs['tree-select'].filter(val)\r\n    },\r\n    value (val) {\r\n      this.chaeckDefaultValue()\r\n    },\r\n    data (val) {\r\n      this.chaeckDefaultValue()\r\n    }\r\n  },\r\n  computed: {\r\n    selfData () {\r\n      return this.data\r\n    },\r\n    selfProps () {\r\n      return {\r\n        label: 'label',\r\n        children: 'children',\r\n        disabled: data => {\r\n          return data.disabled\r\n        },\r\n        ...this.props\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    filterNode (value, data) {\r\n      if (!value) return true\r\n      return data[this.selfProps.label].indexOf(value) !== -1\r\n    },\r\n    handleCheckChange () {\r\n      const nodes = this.$refs['tree-select'].getCheckedNodes(this.leaf)\r\n      this.selecteds = nodes\r\n      this.$emit('change', nodes)\r\n    },\r\n    tabClose (Id) {\r\n      if (this.disabled) return\r\n      this.$refs['tree-select'].setChecked(Id, false, true)\r\n      this.selecteds = this.$refs['tree-select'].getCheckedNodes()\r\n      this.$emit('change', this.selecteds)\r\n    },\r\n    chaeckDefaultValue () {\r\n      const val = this.value\r\n      if (!val || (Array.isArray(val) && val.length === 0)) {\r\n        this.selecteds = []\r\n        this.checked_keys = []\r\n        this.$nextTick(() => {\r\n          this.$refs['tree-select'].setCheckedKeys([])\r\n        })\r\n        return\r\n      }\r\n      this.checked_keys = typeof val[0] === 'object' ? val.map(i => i[this.nodeKey]) : val\r\n      this.$nextTick(() => {\r\n        this.selecteds = this.$refs['tree-select'].getCheckedNodes(this.leaf)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-cascader-checkbox.scss\";\r\n</style>\r\n"]}]}