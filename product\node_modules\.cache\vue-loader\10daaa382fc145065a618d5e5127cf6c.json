{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\candidates-box\\candidates-box.vue?vue&type=template&id=bf671e62&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\candidates-box\\candidates-box.vue", "mtime": 1752541693438}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uIHJlbmRlcigpIHsKICB2YXIgX3ZtID0gdGhpcywKICAgICAgX2MgPSBfdm0uX3NlbGYuX2M7CgogIHJldHVybiBfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJjYW5kaWRhdGVzLWJveCIsCiAgICBvbjogewogICAgICBjbGljazogX3ZtLnVzZXJDbGljawogICAgfQogIH0sIFtfYygiZWwtc2Nyb2xsYmFyIiwgewogICAgc3RhdGljQ2xhc3M6ICJjYW5kaWRhdGVzLS11c2VyLWJveCIKICB9LCBbIV92bS51c2VyRGF0YS5sZW5ndGggPyBfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJmb3JtLXVzZXItYm94LXRleHQiCiAgfSwgW192bS5fdihfdm0uX3MoX3ZtLnBsYWNlaG9sZGVyKSldKSA6IF92bS5fZSgpLCBfdm0uX2woX3ZtLnVzZXJEYXRhLCBmdW5jdGlvbiAodGFnKSB7CiAgICByZXR1cm4gX2MoImVsLXRhZyIsIHsKICAgICAga2V5OiB0YWcudXNlcklkLAogICAgICBhdHRyczogewogICAgICAgIHNpemU6ICJtZWRpdW0iLAogICAgICAgIGNsb3NhYmxlOiAiIiwKICAgICAgICAiZGlzYWJsZS10cmFuc2l0aW9ucyI6IGZhbHNlCiAgICAgIH0sCiAgICAgIG9uOiB7CiAgICAgICAgY2xvc2U6IGZ1bmN0aW9uICgkZXZlbnQpIHsKICAgICAgICAgICRldmVudC5zdG9wUHJvcGFnYXRpb24oKTsKICAgICAgICAgIHJldHVybiBfdm0ucmVtb3ZlKHRhZyk7CiAgICAgICAgfQogICAgICB9CiAgICB9LCBbX3ZtLl92KCIgIiArIF92bS5fcyh0YWcubmFtZSkgKyAiICIpXSk7CiAgfSldLCAyKSwgX2MoInp5LXBvcC11cCIsIHsKICAgIGF0dHJzOiB7CiAgICAgIHRpdGxlOiBfdm0ucGxhY2Vob2xkZXIKICAgIH0sCiAgICBtb2RlbDogewogICAgICB2YWx1ZTogX3ZtLnVzZXJTaG93LAogICAgICBjYWxsYmFjazogZnVuY3Rpb24gKCQkdikgewogICAgICAgIF92bS51c2VyU2hvdyA9ICQkdjsKICAgICAgfSwKICAgICAgZXhwcmVzc2lvbjogInVzZXJTaG93IgogICAgfQogIH0sIFtfYygiY2FuZGlkYXRlcy11c2VyIiwgewogICAgYXR0cnM6IHsKICAgICAgcG9pbnQ6IF92bS5wb2ludCwKICAgICAgZGlzYWJsZWQ6IF92bS5kaXNhYmxlZCwKICAgICAgZGF0YTogX3ZtLnVzZXJEYXRhCiAgICB9LAogICAgb246IHsKICAgICAgdXNlckNhbGxiYWNrOiBfdm0udXNlckNhbGxiYWNrCiAgICB9CiAgfSldLCAxKV0sIDEpOwp9OwoKdmFyIHN0YXRpY1JlbmRlckZucyA9IFtdOwpyZW5kZXIuX3dpdGhTdHJpcHBlZCA9IHRydWU7CmV4cG9ydCB7IHJlbmRlciwgc3RhdGljUmVuZGVyRm5zIH07"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "on", "click", "userClick", "userData", "length", "_v", "_s", "placeholder", "_e", "_l", "tag", "key", "userId", "attrs", "size", "closable", "close", "$event", "stopPropagation", "remove", "name", "title", "model", "value", "userShow", "callback", "$$v", "expression", "point", "disabled", "data", "userCallback", "staticRenderFns", "_withStripped"], "sources": ["D:/zy/xm/pc/qdzx/product/src/components/candidates-box/candidates-box.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"candidates-box\", on: { click: _vm.userClick } },\n    [\n      _c(\n        \"el-scrollbar\",\n        { staticClass: \"candidates--user-box\" },\n        [\n          !_vm.userData.length\n            ? _c(\"div\", { staticClass: \"form-user-box-text\" }, [\n                _vm._v(_vm._s(_vm.placeholder)),\n              ])\n            : _vm._e(),\n          _vm._l(_vm.userData, function (tag) {\n            return _c(\n              \"el-tag\",\n              {\n                key: tag.userId,\n                attrs: {\n                  size: \"medium\",\n                  closable: \"\",\n                  \"disable-transitions\": false,\n                },\n                on: {\n                  close: function ($event) {\n                    $event.stopPropagation()\n                    return _vm.remove(tag)\n                  },\n                },\n              },\n              [_vm._v(\" \" + _vm._s(tag.name) + \" \")]\n            )\n          }),\n        ],\n        2\n      ),\n      _c(\n        \"zy-pop-up\",\n        {\n          attrs: { title: _vm.placeholder },\n          model: {\n            value: _vm.userShow,\n            callback: function ($$v) {\n              _vm.userShow = $$v\n            },\n            expression: \"userShow\",\n          },\n        },\n        [\n          _c(\"candidates-user\", {\n            attrs: {\n              point: _vm.point,\n              disabled: _vm.disabled,\n              data: _vm.userData,\n            },\n            on: { userCallback: _vm.userCallback },\n          }),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAT,GAAkB;EAC7B,IAAIC,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CACP,KADO,EAEP;IAAEE,WAAW,EAAE,gBAAf;IAAiCC,EAAE,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAACM;IAAb;EAArC,CAFO,EAGP,CACEL,EAAE,CACA,cADA,EAEA;IAAEE,WAAW,EAAE;EAAf,CAFA,EAGA,CACE,CAACH,GAAG,CAACO,QAAJ,CAAaC,MAAd,GACIP,EAAE,CAAC,KAAD,EAAQ;IAAEE,WAAW,EAAE;EAAf,CAAR,EAA+C,CAC/CH,GAAG,CAACS,EAAJ,CAAOT,GAAG,CAACU,EAAJ,CAAOV,GAAG,CAACW,WAAX,CAAP,CAD+C,CAA/C,CADN,GAIIX,GAAG,CAACY,EAAJ,EALN,EAMEZ,GAAG,CAACa,EAAJ,CAAOb,GAAG,CAACO,QAAX,EAAqB,UAAUO,GAAV,EAAe;IAClC,OAAOb,EAAE,CACP,QADO,EAEP;MACEc,GAAG,EAAED,GAAG,CAACE,MADX;MAEEC,KAAK,EAAE;QACLC,IAAI,EAAE,QADD;QAELC,QAAQ,EAAE,EAFL;QAGL,uBAAuB;MAHlB,CAFT;MAOEf,EAAE,EAAE;QACFgB,KAAK,EAAE,UAAUC,MAAV,EAAkB;UACvBA,MAAM,CAACC,eAAP;UACA,OAAOtB,GAAG,CAACuB,MAAJ,CAAWT,GAAX,CAAP;QACD;MAJC;IAPN,CAFO,EAgBP,CAACd,GAAG,CAACS,EAAJ,CAAO,MAAMT,GAAG,CAACU,EAAJ,CAAOI,GAAG,CAACU,IAAX,CAAN,GAAyB,GAAhC,CAAD,CAhBO,CAAT;EAkBD,CAnBD,CANF,CAHA,EA8BA,CA9BA,CADJ,EAiCEvB,EAAE,CACA,WADA,EAEA;IACEgB,KAAK,EAAE;MAAEQ,KAAK,EAAEzB,GAAG,CAACW;IAAb,CADT;IAEEe,KAAK,EAAE;MACLC,KAAK,EAAE3B,GAAG,CAAC4B,QADN;MAELC,QAAQ,EAAE,UAAUC,GAAV,EAAe;QACvB9B,GAAG,CAAC4B,QAAJ,GAAeE,GAAf;MACD,CAJI;MAKLC,UAAU,EAAE;IALP;EAFT,CAFA,EAYA,CACE9B,EAAE,CAAC,iBAAD,EAAoB;IACpBgB,KAAK,EAAE;MACLe,KAAK,EAAEhC,GAAG,CAACgC,KADN;MAELC,QAAQ,EAAEjC,GAAG,CAACiC,QAFT;MAGLC,IAAI,EAAElC,GAAG,CAACO;IAHL,CADa;IAMpBH,EAAE,EAAE;MAAE+B,YAAY,EAAEnC,GAAG,CAACmC;IAApB;EANgB,CAApB,CADJ,CAZA,EAsBA,CAtBA,CAjCJ,CAHO,EA6DP,CA7DO,CAAT;AA+DD,CAlED;;AAmEA,IAAIC,eAAe,GAAG,EAAtB;AACArC,MAAM,CAACsC,aAAP,GAAuB,IAAvB;AAEA,SAAStC,MAAT,EAAiBqC,eAAjB"}]}