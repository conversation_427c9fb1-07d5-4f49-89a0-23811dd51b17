{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\qd-upload-img\\qd-upload-img.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\qd-upload-img\\qd-upload-img.vue", "mtime": 1752541693469}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAiBA;EACAA,qBADA;EAEAC;IACAC,YADA;IAEAC;MACAC,YADA;MAEAC;IAFA,CAFA;IAMAC;MACAF,YADA;MAEAC;IAFA,CANA;IAUAE;MACAH,WADA;MAEAC;QAAA;MAAA;IAFA,CAVA;IAcAG;MACAJ,WADA;MAEAC;QACA;MACA;IAJA;EAdA,CAFA;EAuBAI;IACAC,aADA;IACA;IACAC,aAFA,CAEA;;EAFA,CAvBA;;EA2BAC;IACA;MACAC;IADA;EAGA,CA/BA;;EAgCAC;IACAZ;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA;IACA,CAPA;;IAQAW;MACA;IACA;;EAVA,CAhCA;EA4CAE;IACA;IACAC;MACA;MACA;MACA;;MACA;QACA;QACA;MACA;;MACA;QACA;QACA;MACA;;MACA;IACA,CAfA;;IAgBA;IACA;MACA;MACA;MACAC;MACAA;MACAA;MACA;QACA;UAAAC;UAAAN;QAAA;;QACA;UACA;QACA;MACA,CALA;MAMA;IACA;;EA9BA;AA5CA", "names": ["name", "props", "value", "module", "type", "default", "tip", "size", "photoList", "model", "prop", "event", "data", "files", "watch", "methods", "beforeAvatarUpload", "formData", "<PERSON><PERSON><PERSON>"], "sourceRoot": "src/components/qd-upload-img", "sources": ["qd-upload-img.vue"], "sourcesContent": ["<template>\r\n  <el-upload\r\n    class=\"avatar-uploader\"\r\n    action=\"/#\"\r\n    accept=\".jpg,.jpeg,.png,.PNG,.JPG\"\r\n    :show-file-list=\"false\"\r\n    :http-request=\"customUpload\"\r\n    :before-upload=\"beforeAvatarUpload\"\r\n  >\r\n    <img v-if=\"photoList.length\" :src=\"photoList[0].filePath\" class=\"avatar\" />\r\n    <img v-else-if=\"files.length > 0\" :src=\"files[0].filePath\" class=\"avatar\" />\r\n    <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\r\n    <div slot=\"tip\" class=\"el-upload__tip\">{{ tip }}</div>\r\n  </el-upload>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'qd-upload-img',\r\n  props: {\r\n    value: Array,\r\n    module: {\r\n      type: String,\r\n      default: 'splashImg'\r\n    },\r\n    tip: {\r\n      type: String,\r\n      default: '只能上传jpg/png等图片格式的文件，且不超过10mb'\r\n    },\r\n    size: {\r\n      type: Array,\r\n      default: () => { return [] }\r\n    },\r\n    photoList: {\r\n      type: Array,\r\n      default: () => {\r\n        return []\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value', // 这个字段，是指父组件设置 v-model 时，将变量值传给子组件的 msg\r\n    event: 'file'// 这个字段，是指父组件监听 parent-event 事件\r\n  },\r\n  data () {\r\n    return {\r\n      files: []\r\n    }\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.files = val\r\n      } else {\r\n        this.files = []\r\n      }\r\n    },\r\n    files (val) {\r\n      this.$emit('file', val)\r\n    }\r\n  },\r\n  methods: {\r\n    // 校验文件类型和文件大小\r\n    beforeAvatarUpload (file) {\r\n      // const isJPG = file.type === 'image/jpeg'\r\n      const isLt2M = file.size / 1024 / 1024 < 10\r\n      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n      if (testmsg !== 'png' && testmsg !== 'jpg' && testmsg !== 'PNG' && testmsg !== 'jpeg' && testmsg !== 'JPG' && testmsg !== 'gif') {\r\n        this.$message.error('图片文件格式暂时不支持!')\r\n        return false\r\n      }\r\n      if (!isLt2M) {\r\n        this.$message.error('上传图片大小不能超过 10MB!')\r\n        return false\r\n      }\r\n      return isLt2M && testmsg\r\n    },\r\n    // 上传逻辑\r\n    async customUpload (file) {\r\n      const siteId = JSON.parse(sessionStorage.getItem('user' + this.$logo())).areaId\r\n      const formData = new FormData()\r\n      formData.append('attachment', file.file)\r\n      formData.append('module', this.module)\r\n      formData.append('siteId', siteId)\r\n      this.$api.microAdvice.uploadFile(formData).then(res => {\r\n        const { errcode, data } = res\r\n        if (errcode === 200) {\r\n          this.files = data\r\n        }\r\n      })\r\n      this.$emit('initPhoto')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.avatar-uploader {\r\n  .el-upload {\r\n    border: 1px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n  }\r\n  .el-upload:hover {\r\n    border-color: #409eff;\r\n  }\r\n  .avatar-uploader-icon {\r\n    font-size: 28px;\r\n    color: #8c939d;\r\n    width: 148px;\r\n    height: 148px;\r\n    line-height: 148px;\r\n    text-align: center;\r\n  }\r\n  .avatar {\r\n    width: 148px;\r\n    height: 148px;\r\n    display: block;\r\n  }\r\n}\r\n</style>\r\n"]}]}