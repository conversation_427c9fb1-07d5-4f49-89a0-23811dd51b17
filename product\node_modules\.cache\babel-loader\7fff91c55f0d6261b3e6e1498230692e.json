{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\titleDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\titleDetail.vue", "mtime": 1752541693795}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA8EA;EACAA,oBADA;;EAEAC;IACA;MACAC;QACAC,MADA;QAEAC,SAFA;QAGAC,cAHA;QAIAC,eAJA;QAKAC,WALA;QAMAC,SANA;QAOAC,eAPA;QAQAC,cARA;QASAC;MATA,CADA;MAYAC,WAZA;MAaAC;IAbA;EAeA,CAlBA;;EAmBAC,iCAnBA;;EAoBAC;IACA;IACA;IACA;EACA,CAxBA;;EAyBAC;IACAC;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA,CAFA,MAEA;QACA;MACA;IACA,CATA;;IAUAC;MACA;QAAA;MAAA;;MACA;IACA;;EAbA,CAzBA;EAwCAC;IACAC;MACA;QACAC,uBADA;QAEAC,sBAFA;QAGAC;MAHA,GAIAC,IAJA,CAIA;QACA;MACA,CANA,EAMAC,KANA,CAMA;QACA;UACAF,YADA;UAEAG;QAFA;MAIA,CAXA;IAYA,CAdA;;IAeA;IACA;MACA;QAAAC;QAAAlB;MAAA;MACA;QAAAmB;QAAAC;MAAA;;MACA;QACA;UACAH,eADA;UAEAH;QAFA;QAIA;MACA;IACA,CA1BA;;IA2BA;AACA;AACA;IACA;MACA;QACAO;MADA;MAGA;QAAA7B;MAAA;MACA;IACA,CApCA;;IAqCA;IACA;MACA;MACA;QAAAE;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;MAEA;QAAAR;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,EAJA,CAKA;IACA,CA5CA,CA6CA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;EA3DA;AAxCA", "names": ["props", "data", "form", "id", "title", "officeName", "publishTime", "endTime", "score", "auditStatus", "isMainwork", "classify", "details", "classifyData", "inject", "mounted", "computed", "auditStatusName", "classDetail", "methods", "passClick", "confirmButtonText", "cancelButtonText", "type", "then", "catch", "message", "ids", "<PERSON><PERSON><PERSON>", "errmsg", "types"], "sourceRoot": "src/views/AssessmentOrgan/BusinessObjectives", "sources": ["titleDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"titleDetail details\">\r\n\r\n    <div class=\"checkClass\">\r\n\r\n      <el-button type=\"primary\"\r\n                 icon=\"el-icon-circle-check\"\r\n                 v-permissions=\"'auth:business:checkPass'\"\r\n                 @click=\"passClick(2)\">审核通过\r\n      </el-button>\r\n\r\n      <el-button type=\"danger\"\r\n                 v-permissions=\"'auth:business:checkNotPass'\"\r\n                 icon=\"el-icon-remove-outline\"\r\n                 @click=\"passClick(3)\">审核不通过\r\n      </el-button>\r\n    </div>\r\n\r\n    <div class=\"details-title\">详情</div>\r\n    <div class=\"details-item-box\">\r\n      <div class=\"details-item-title\">\r\n        <div class=\"details-item-label\">标题</div>\r\n        <div class=\"details-item-value\">{{form.title}}</div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">发布时间</div>\r\n          <div class=\"details-item-value\">{{$format(form.publishTime).substr(0,16)}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">完成时间</div>\r\n          <div class=\"details-item-value\">{{$format(form.endTime).substr(0,16)}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">机构名</div>\r\n          <div class=\"details-item-value\">{{form.officeName}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">分值</div>\r\n          <div class=\"details-item-value\">{{form.score}}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">审核状态</div>\r\n          <div class=\"details-item-value\">{{auditStatusName}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">类型</div>\r\n          <div class=\"details-item-value\">{{classDetail}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">是否重点工作</div>\r\n          <div class=\"details-item-value\">{{form.isMainwork==1?'是':'否'}}</div>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"details-item\">\r\n        <div class=\"details-item-label\">附件</div>\r\n        <div class=\"details-item-value\">\r\n          <div class=\"details-item-files\"\r\n               v-for=\"(item, index) in details.attachmentList\"\r\n               :key=\"index\">\r\n            <p>{{item.fileName}}</p>\r\n            <div>\r\n              <el-button size=\"medium\"\r\n                         type=\"text\"\r\n                         @click=\"priew(item)\"> 预览 </el-button>\r\n              <el-button size=\"medium\"\r\n                         type=\"text\"\r\n                         @click=\"fileClick(item)\"> 下载 </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  props: ['id', 'uid'],\r\n  data () {\r\n    return {\r\n      form: {\r\n        id: '',\r\n        title: '',\r\n        officeName: '',\r\n        publishTime: '',\r\n        endTime: '',\r\n        score: '',\r\n        auditStatus: '',\r\n        isMainwork: '',\r\n        classify: ''\r\n      },\r\n      details: {},\r\n      classifyData: []\r\n    }\r\n  },\r\n  inject: ['openoffice', 'openPdf'],\r\n  mounted () {\r\n    // this.form.overTime = this.$format()\r\n    this.dictionaryPubkvs()\r\n    this.getBusinessObjectiveDetails()\r\n  },\r\n  computed: {\r\n    auditStatusName () {\r\n      if (this.form.auditStatus === '1') {\r\n        return '待审核'\r\n      } else if (this.form.auditStatus === '2') {\r\n        return '审核通过'\r\n      } else {\r\n        return '审核不通过'\r\n      }\r\n    },\r\n    classDetail () {\r\n      if (typeof this.classifyData[this.form.classify - 1] === 'object') { return this.classifyData[this.form.classify - 1].value }\r\n      return ''\r\n    }\r\n  },\r\n  methods: {\r\n    passClick (auditStatus) {\r\n      this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.getCheckWork(this.id, auditStatus)\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消操作'\r\n        })\r\n      })\r\n    },\r\n    // 审核 (加分项)\r\n    async getCheckWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckWork({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n        this.getBusinessObjectiveDetails()\r\n      }\r\n    },\r\n    /**\r\n      *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_functional_work'\r\n      })\r\n      var { data } = res\r\n      this.classifyData = data.evaluation_functional_work\r\n    },\r\n    // 获取 标题详情\r\n    async getBusinessObjectiveDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqBusinessObjectiveDetails(this.id)\r\n      const { id, title, officeName, publishTime, endTime, score, auditStatus, isMainwork, classify } = res.data\r\n\r\n      this.form = { id, title, officeName, publishTime, endTime, score, auditStatus, isMainwork, classify }\r\n      // console.log('@@@查看是否能收到审核状态auditStatus', this.form)\r\n    }\r\n    // 附件\r\n    // fileClick (data) {\r\n    //   this.$api.proposal.downloadFile({ id: data.id }, data.fileName)\r\n    // },\r\n    // priew (data) {\r\n    //   console.log(data)\r\n    //   if (data.fileType === 'pdf') {\r\n    //     this.openPdf(data.filePath)\r\n    //     return\r\n    //   }\r\n    //   const arr = ['doc', 'docx', 'xlsx']\r\n    //   if (arr.includes(data.fileType)) {\r\n    //     this.openoffice(data.filePath)\r\n    //   }\r\n    // }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.titleDetail {\r\n  width: 1000px;\r\n  height: 100%;\r\n  padding: 0 24px;\r\n  padding-bottom: 24px;\r\n\r\n  .checkClass {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding-right: 10px;\r\n  }\r\n\r\n  .details-item-content {\r\n    width: 100%;\r\n    padding: 24px;\r\n\r\n    p {\r\n      line-height: 36px;\r\n    }\r\n  }\r\n\r\n  .details-item-img {\r\n    img {\r\n      width: calc(100% - 24px);\r\n    }\r\n  }\r\n\r\n  .details-item-files {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-right: 20px;\r\n  }\r\n}\r\n</style>\r\n"]}]}