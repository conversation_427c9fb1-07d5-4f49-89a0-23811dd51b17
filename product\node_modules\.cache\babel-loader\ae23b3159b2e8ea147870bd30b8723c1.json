{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue", "mtime": 1756349949679}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,uBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,WADA;MAEAC,cAFA;MAGAE;IAHA,CALA;IAUAC;MACAJ,YADA;MAEAG;IAFA,CAVA;IAcAE;MACAL,aADA;MAEAG;IAFA,CAdA;IAkBAG;MACAN,YADA;MAEAG;IAFA;EAlBA,CAFA;;EAyBAI;IACA;MACAC;IADA;EAGA,CA7BA;;EA8BAC;IACA;EACA,CAhCA;;EAiCAC;IACA;MACA;IACA;;IACAC;EACA,CAtCA;;EAuCAC;IACAC;MACA;MACA;MACA;MACA;MACAF;IACA,CAPA;;IAQAG;MACA;MACA,0DAFA,CAGA;MACA;;MACA,oBALA,CAMA;;MACA;;MACA;QACAC;UACAf,cADA;UAEAgB,IAFA;UAGAC,KAHA;UAIAC,IAJA;UAKAC,KALA;UAMAC,aACA;YACAC,SADA;YAEAC,gBAFA,CAEA;;UAFA,CADA,EAIA;YACAD,WADA;YAEAC,gBAFA,CAEA;;UAFA,CAJA,EAOA;YACAD,WADA;YAEAC,gBAFA,CAEA;;UAFA,CAPA,EAUA;YACAD,SADA;YAEAC,6BAFA,CAEA;;UAFA,CAVA;QANA;MAsBA;;MAEA;QACA;QACAC;UACAC,eADA;UAEAC,qCAFA;UAGAC,sBAHA;UAIAC,cAJA;UAKAC;YACA5B;UADA,CALA;UAQA6B;YACAP;UADA,CARA;UAWAQ;YACA;YACA;UACA;QAdA,CAFA;QAkBA;QACAC;UACAC,UADA;UAEAC,WAFA;UAGAC,YAHA;UAIAC,SAJA;UAKAC;QALA,CAnBA;QA0BA;QACAC;UACA9B,eADA;UAEAP,gBAFA;UAGAsC;YACAC,UADA;YAEAC;cACAlB;YADA;UAFA,CAHA;UASAmB;YACAF;UADA,CATA;UAYAG;YACAH;UADA,CAZA;UAeAI;YACAJ,UADA;YAEAjB,gBAFA;YAGAsB,YAHA;YAIAC,WAJA;YAKAC,UALA;YAMAC,SANA;YAOAjB;cACA;YACA;UATA;QAfA,CA3BA;QAsDA;QACAkB;UACAhD,aADA;UAEAuC,UAFA;UAGAU,cAHA;UAIAX;YACAC;UADA,CAJA;UAOAE;YACAF,UADA;YAEAC;cACAlB,iCADA;cAEAtB;YAFA;UAFA,CAPA;UAcA2C;YACAJ,UADA;YAEAjB,gBAFA;YAGAsB;UAHA;QAdA,CAvDA;QA2EAM,SACA;UACAlD,WADA;UAEAmD,kBAFA;UAGAC;YACAC;cACA/B;gBACA;cACA;YAHA;UADA,CAHA;UAUAgC;YACAf,UADA;YAEAgB,eAFA;YAGAjC,gBAHA;YAIAsB,YAJA;YAKAY;UALA,CAVA;UAiBAjD;QAjBA,CADA,EAoBA;UACAkD,IADA;UAEAzD,oBAFA;UAGAO,gBAHA;UAIAmD,iBAJA;UAKAC,wBALA;UAMAC,sCANA;UAOAR;YACAC;cACA/B;gBACA;cACA;YAHA;UADA;QAPA,CApBA,EAmCA;UACAmC,IADA;UAEAzD,oBAFA;UAGA6D,qBAHA;UAIAtD,gBAJA;UAKAmD,iBALA;UAMAC,yBANA;UAOAC,sCAPA;UAQAR;YACAC;cACA1B,cADA;cAEAL;gBACA;cACA;YAJA;UADA;QARA,CAnCA;MA3EA;IAiIA,CA1KA;;IA2KAwC;MACA;MACA;IACA,CA9KA;;IA+KAC;MACA;QACA;MACA;IACA;;EAnLA;AAvCA", "names": ["name", "props", "id", "type", "required", "chartData", "default", "title", "showValues", "maxValue", "data", "chart", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "window", "methods", "initChart", "getOption", "colors", "x", "x2", "y", "y2", "colorStops", "offset", "color", "tooltip", "trigger", "backgroundColor", "borderColor", "borderWidth", "axisPointer", "textStyle", "formatter", "grid", "left", "right", "bottom", "top", "containLabel", "xAxis", "axisLine", "show", "lineStyle", "splitLine", "axisTick", "axisLabel", "fontSize", "interval", "rotate", "margin", "yAxis", "splitNumber", "series", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "normal", "label", "position", "align", "z", "symbol", "symbolOffset", "symbolSize", "symbolPosition", "<PERSON><PERSON><PERSON>", "resizeChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["RankingBarChart.vue"], "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"ranking-bar-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'RankingBar<PERSON><PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    showValues: {\n      type: Boolean,\n      default: true\n    },\n    maxValue: {\n      type: Number,\n      default: null\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.renderChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n    getOption () {\n      const xAxisData = this.chartData.map(item => item.name)\n      const seriesData = this.chartData.map(item => item.value)\n      // 计算柱子宽度\n      // const dom = 300\n      const barWidth = 20\n      // 生成渐变色数组\n      const colors = []\n      for (let i = 0; i < this.chartData.length; i++) {\n        colors.push({\n          type: 'linear',\n          x: 0,\n          x2: 0,\n          y: 0,\n          y2: 1,\n          colorStops: [\n            {\n              offset: 0,\n              color: '#0093DD' // 顶部颜色\n            }, {\n              offset: 0.5,\n              color: '#006BBC' // 中间颜色，创建立体效果\n            }, {\n              offset: 0.5,\n              color: '#1FC6FF' // 中间分割线\n            }, {\n              offset: 1,\n              color: 'rgba(31,198,255,0.2)' // 底部颜色\n            }\n          ]\n        })\n      }\n\n      return {\n        // 提示框\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00D4FF',\n          borderWidth: 1,\n          axisPointer: {\n            type: 'shadow'\n          },\n          textStyle: {\n            color: '#FFFFFF'\n          },\n          formatter: function (params) {\n            const data = params[0]\n            return `${data.name}<br/>人数: ${data.value}人`\n          }\n        },\n        // 区域位置\n        grid: {\n          left: '0%',\n          right: '0%',\n          bottom: '0%',\n          top: '5%',\n          containLabel: true\n        },\n        // X轴\n        xAxis: {\n          data: xAxisData,\n          type: 'category',\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.2)'\n            }\n          },\n          splitLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12,\n            interval: 0,\n            rotate: 35,\n            margin: 8,\n            formatter: (value) => {\n              return value\n            }\n          }\n        },\n        // y轴\n        yAxis: {\n          type: 'value',\n          show: true,\n          splitNumber: 5,\n          axisLine: {\n            show: false\n          },\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              type: 'dashed'\n            }\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12\n          }\n        },\n        series: [\n          {\n            type: 'bar',\n            barWidth: barWidth,\n            itemStyle: {\n              normal: {\n                color: function (params) {\n                  return colors[params.dataIndex % colors.length]\n                }\n              }\n            },\n            label: {\n              show: true,\n              position: 'top',\n              color: '#FFFFFF',\n              fontSize: 12,\n              align: 'center'\n            },\n            data: seriesData\n          },\n          {\n            z: 2,\n            type: 'pictorialBar',\n            data: seriesData,\n            symbol: 'diamond',\n            symbolOffset: [0, '50%'],\n            symbolSize: [barWidth, barWidth * 0.5],\n            itemStyle: {\n              normal: {\n                color: function (params) {\n                  return colors[params.dataIndex % colors.length]\n                }\n              }\n            }\n          },\n          {\n            z: 3,\n            type: 'pictorialBar',\n            symbolPosition: 'end',\n            data: seriesData,\n            symbol: 'diamond',\n            symbolOffset: [0, '-50%'],\n            symbolSize: [barWidth, barWidth * 0.5],\n            itemStyle: {\n              normal: {\n                borderWidth: 0,\n                color: function (params) {\n                  return colors[params.dataIndex % colors.length].colorStops[0].color\n                }\n              }\n            }\n          }\n        ]\n      }\n    },\n    renderChart () {\n      if (!this.chart || !this.chartData.length) return\n      this.chart.setOption(this.getOption(), true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.ranking-bar-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}