{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue", "mtime": 1756347189730}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,uBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,WADA;MAEAC,cAFA;MAGAE;IAHA,CALA;IAUAC;MACAJ,YADA;MAEAG;IAFA,CAVA;IAcAE;MACAL,aADA;MAEAG;IAFA,CAdA;IAkBAG;MACAN,YADA;MAEAG;IAFA;EAlBA,CAFA;;EAyBAI;IACA;MACAC;IADA;EAGA,CA7BA;;EA8BAC;IACA;EACA,CAhCA;;EAiCAC;IACA;MACA;IACA;;IACAC;EACA,CAtCA;;EAuCAC;IACAV;MACAW;QACA;MACA,CAHA;;MAIAC;IAJA;EADA,CAvCA;EA+CAC;IACAC;MACA;MACA;MACA;MACA;MACAL;IACA,CAPA;;IAQAM;MACA;MACA;MACA;QACAC;UACAC,UADA;UAEAC,WAFA;UAGAC,YAHA;UAIAC,UAJA;UAKAC;QALA,CADA;QAQAC;UACAxB,gBADA;UAEAO,eAFA;UAGAkB;YACAC,UADA;YAEAC;cACAC;YADA;UAFA,CAHA;UASAC;YACAH;UADA,CATA;UAYAI;YACAJ,UADA;YAEAE,gBAFA;YAGAG,YAHA;YAIAC,WAJA;YAKAC,UALA;YAMAC,SANA;YAOAC;cACA;YACA;UATA;QAZA,CARA;QAgCAC;UACApC,aADA;UAEAqC,cAFA;UAGAZ;YACAC;UADA,CAHA;UAMAG;YACAH;UADA,CANA;UASAI;YACAJ,UADA;YAEAE,gBAFA;YAGAG;UAHA,CATA;UAcAO;YACAZ,UADA;YAEAC;cACAC,iCADA;cAEA5B;YAFA;UAFA;QAdA,CAhCA;QAsDAuC,SACA;UACAvC,WADA;UAEAO,gBAFA;UAGAiC,YAHA;UAIAC,qBAJA;UAKAC;YACAd,gBADA;YAEAe;UAFA,CALA;UASAC;YACAF;cACAd;YADA;UADA,CATA;UAcAiB;YACAnB,qBADA;YAEAoB,eAFA;YAGAlB,gBAHA;YAIAG,YAJA;YAKAI;UALA;QAdA,CADA,CAtDA;QA8EAY;UACAC,eADA;UAEAC,uCAFA;UAGAC,sBAHA;UAIAC,cAJA;UAKAC;YACAxB,gBADA;YAEAG;UAFA,CALA;UASAI;YACA;YACA;UACA,CAZA,CAaA;UACA;UACA;UACA;UACA;UACA;UACA;;QAnBA;MA9EA;IAoGA,CA/GA;;IAgHAkB;MACA;MACA;IACA,CAnHA;;IAoHAC;MACA;QACA;MACA;IACA;;EAxHA;AA/CA", "names": ["name", "props", "id", "type", "required", "chartData", "default", "title", "showValues", "maxValue", "data", "chart", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "window", "watch", "handler", "deep", "methods", "initChart", "getOption", "grid", "left", "right", "bottom", "top", "containLabel", "xAxis", "axisLine", "show", "lineStyle", "color", "axisTick", "axisLabel", "fontSize", "interval", "rotate", "margin", "formatter", "yAxis", "splitNumber", "splitLine", "series", "<PERSON><PERSON><PERSON><PERSON>", "showBackground", "itemStyle", "borderRadius", "emphasis", "label", "position", "tooltip", "trigger", "backgroundColor", "borderColor", "borderWidth", "textStyle", "<PERSON><PERSON><PERSON>", "resizeChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["RankingBarChart.vue"], "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"ranking-bar-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'RankingBar<PERSON><PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    showValues: {\n      type: Boolean,\n      default: true\n    },\n    maxValue: {\n      type: Number,\n      default: null\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  watch: {\n    chartData: {\n      handler () {\n        this.renderChart()\n      },\n      deep: true\n    }\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.renderChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n    getOption () {\n      const xAxisData = this.chartData.map(item => item.name)\n      const seriesData = this.chartData.map(item => item.value)\n      return {\n        grid: {\n          left: '0%',\n          right: '0%',\n          bottom: '0%',\n          top: '10%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData,\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.2)'\n            }\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12,\n            interval: 0,\n            rotate: 35,\n            margin: 8,\n            formatter: (value) => {\n              return value\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          splitNumber: 5,\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12\n          },\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              type: 'dashed'\n            }\n          }\n        },\n        series: [\n          {\n            type: 'bar',\n            data: seriesData,\n            barWidth: 20,\n            showBackground: false,\n            itemStyle: {\n              color: '#00D4FF',\n              borderRadius: [2, 2, 0, 0]\n            },\n            emphasis: {\n              itemStyle: {\n                color: '#00FFFF'\n              }\n            },\n            label: {\n              show: this.showValues,\n              position: 'top',\n              color: '#FFFFFF',\n              fontSize: 12,\n              formatter: '{c}'\n            }\n          }\n        ],\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 20, 40, 0.9)',\n          borderColor: '#00D4FF',\n          borderWidth: 1,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 12\n          },\n          formatter: function (params) {\n            const data = params[0]\n            return `${data.name}<br/>人数: ${data.value}人`\n          }\n          // formatter: (params) => {\n          //   const data = params[0]\n          //   return `<div style=\"padding: 5px;\">\n          //     <div style=\"color: #00D4FF; font-weight: bold;\">${data.name}</div>\n          //     <div style=\"margin-top: 5px;\">数量: <span style=\"color: #00E4FF; font-weight: bold;\">${data.value}</span></div>\n          //   </div>`\n          // }\n        }\n      }\n    },\n    renderChart () {\n      if (!this.chart || !this.chartData.length) return\n      this.chart.setOption(this.getOption(), true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.ranking-bar-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}