{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue", "mtime": 1756347044379}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,uBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,WADA;MAEAC,cAFA;MAGAE;IAHA,CALA;IAUAC;MACAJ,YADA;MAEAG;IAFA,CAVA;IAcAE;MACAL,aADA;MAEAG;IAFA,CAdA;IAkBAG;MACAN,YADA;MAEAG;IAFA;EAlBA,CAFA;;EAyBAI;IACA;MACAC;IADA;EAGA,CA7BA;;EA8BAC;IACA;EACA,CAhCA;;EAiCAC;IACA;MACA;IACA;;IACAC;EACA,CAtCA;;EAuCAC;IACAV;MACAW;QACA;MACA,CAHA;;MAIAC;IAJA;EADA,CAvCA;EA+CAC;IACAC;MACA;MACA;MACA;MACA;MACAL;IACA,CAPA;;IAQAM;MACA;MACA;MACA;QACAC;UACAC,UADA;UAEAC,WAFA;UAGAC,YAHA;UAIAC,UAJA;UAKAC;QALA,CADA;QAQAC;UACAxB,gBADA;UAEAO,eAFA;UAGAkB;YACAC,UADA;YAEAC;cACAC;YADA;UAFA,CAHA;UASAC;YACAH;UADA,CATA;UAYAI;YACAJ,UADA;YAEAE,gBAFA;YAGAG,YAHA;YAIAC,WAJA;YAKAC,UALA;YAMAC,SANA;YAOAC;cACA;YACA;UATA;QAZA,CARA;QAgCAC;UACApC,aADA;UAEAqC,cAFA;UAGAZ;YACAC;UADA,CAHA;UAMAG;YACAH;UADA,CANA;UASAI;YACAJ,UADA;YAEAE,gBAFA;YAGAG;UAHA,CATA;UAcAO;YACAZ,UADA;YAEAC;cACAC,iCADA;cAEA5B;YAFA;UAFA;QAdA,CAhCA;QAsDAuC,SACA;UACAvC,WADA;UAEAO,gBAFA;UAGAiC,YAHA;UAIAC,qBAJA;UAKAC;YACAd;cACA5B,cADA;cAEA2C,IAFA;cAGAC,IAHA;cAIAC,KAJA;cAKAC,KALA;cAMAC,aACA;gBAAAC;gBAAApB;cAAA,CADA,EAEA;gBAAAoB;gBAAApB;cAAA,CAFA,EAGA;gBAAAoB;gBAAApB;cAAA,CAHA;YANA,CADA;YAaAqB,0BAbA;YAcAC,qCAdA;YAeAC,cAfA;YAgBAC;UAhBA,CALA;UAuBAC;YACAX;cACAd;gBACA5B,cADA;gBAEA2C,IAFA;gBAGAC,IAHA;gBAIAC,KAJA;gBAKAC,KALA;gBAMAC,aACA;kBAAAC;kBAAApB;gBAAA,CADA,EAEA;kBAAAoB;kBAAApB;gBAAA,CAFA,EAGA;kBAAAoB;kBAAApB;gBAAA,CAHA;cANA,CADA;cAaAsB,qCAbA;cAcAC;YAdA;UADA,CAvBA;UAyCAG;YACA5B,qBADA;YAEA6B,eAFA;YAGA3B,gBAHA;YAIAG,YAJA;YAKAI;UALA;QAzCA,CADA,CAtDA;QAyGAqB;UACAC,eADA;UAEAC,uCAFA;UAGAC,sBAHA;UAIAC,cAJA;UAKAC;YACAjC,gBADA;YAEAG;UAFA,CALA;UASAI;YACA;YACA;UACA,CAZA,CAaA;UACA;UACA;UACA;UACA;UACA;UACA;;QAnBA;MAzGA;IA+HA,CA1IA;;IA2IA2B;MACA;MACA;IACA,CA9IA;;IA+IAC;MACA;QACA;MACA;IACA;;EAnJA;AA/CA", "names": ["name", "props", "id", "type", "required", "chartData", "default", "title", "showValues", "maxValue", "data", "chart", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "window", "watch", "handler", "deep", "methods", "initChart", "getOption", "grid", "left", "right", "bottom", "top", "containLabel", "xAxis", "axisLine", "show", "lineStyle", "color", "axisTick", "axisLabel", "fontSize", "interval", "rotate", "margin", "formatter", "yAxis", "splitNumber", "splitLine", "series", "<PERSON><PERSON><PERSON><PERSON>", "showBackground", "itemStyle", "x", "y", "x2", "y2", "colorStops", "offset", "borderRadius", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetY", "emphasis", "label", "position", "tooltip", "trigger", "backgroundColor", "borderColor", "borderWidth", "textStyle", "<PERSON><PERSON><PERSON>", "resizeChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["RankingBarChart.vue"], "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"ranking-bar-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'RankingBar<PERSON><PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    showValues: {\n      type: Boolean,\n      default: true\n    },\n    maxValue: {\n      type: Number,\n      default: null\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  watch: {\n    chartData: {\n      handler () {\n        this.renderChart()\n      },\n      deep: true\n    }\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.renderChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n    getOption () {\n      const xAxisData = this.chartData.map(item => item.name)\n      const seriesData = this.chartData.map(item => item.value)\n      return {\n        grid: {\n          left: '0%',\n          right: '0%',\n          bottom: '0%',\n          top: '10%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData,\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.2)'\n            }\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12,\n            interval: 0,\n            rotate: 35,\n            margin: 8,\n            formatter: (value) => {\n              return value\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          splitNumber: 5,\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12\n          },\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              type: 'dashed'\n            }\n          }\n        },\n        series: [\n          {\n            type: 'bar',\n            data: seriesData,\n            barWidth: 20,\n            showBackground: false,\n            itemStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: '#00E4FF' },\n                  { offset: 0.5, color: '#0090FF' },\n                  { offset: 1, color: '#004080' }\n                ]\n              },\n              borderRadius: [4, 4, 0, 0],\n              shadowColor: 'rgba(0, 212, 255, 0.3)',\n              shadowBlur: 10,\n              shadowOffsetY: 2\n            },\n            emphasis: {\n              itemStyle: {\n                color: {\n                  type: 'linear',\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: '#00FFFF' },\n                    { offset: 0.5, color: '#00AAFF' },\n                    { offset: 1, color: '#0066CC' }\n                  ]\n                },\n                shadowColor: 'rgba(0, 255, 255, 0.5)',\n                shadowBlur: 15\n              }\n            },\n            label: {\n              show: this.showValues,\n              position: 'top',\n              color: '#FFFFFF',\n              fontSize: 12,\n              formatter: '{c}'\n            }\n          }\n        ],\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 20, 40, 0.9)',\n          borderColor: '#00D4FF',\n          borderWidth: 1,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 12\n          },\n          formatter: function (params) {\n            const data = params[0]\n            return `${data.name}<br/>人数: ${data.value}人`\n          }\n          // formatter: (params) => {\n          //   const data = params[0]\n          //   return `<div style=\"padding: 5px;\">\n          //     <div style=\"color: #00D4FF; font-weight: bold;\">${data.name}</div>\n          //     <div style=\"margin-top: 5px;\">数量: <span style=\"color: #00E4FF; font-weight: bold;\">${data.value}</span></div>\n          //   </div>`\n          // }\n        }\n      }\n    },\n    renderChart () {\n      if (!this.chart || !this.chartData.length) return\n      this.chart.setOption(this.getOption(), true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.ranking-bar-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}