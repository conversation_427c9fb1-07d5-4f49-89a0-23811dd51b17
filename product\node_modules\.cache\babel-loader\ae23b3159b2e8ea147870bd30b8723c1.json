{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue", "mtime": 1756350777151}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,uBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,WADA;MAEAC,cAFA;MAGAE;IAHA,CALA;IAUAC;MACAJ,YADA;MAEAG;IAFA,CAVA;IAcAE;MACAL,aADA;MAEAG;IAFA,CAdA;IAkBAG;MACAN,YADA;MAEAG;IAFA;EAlBA,CAFA;;EAyBAI;IACA;MACAC;IADA;EAGA,CA7BA;;EA8BAC;IACA;EACA,CAhCA;;EAiCAC;IACA;MACA;IACA;;IACAC;EACA,CAtCA;;EAuCAC;IACAC;MACA;MACA;MACA;MACA;MACAF;IACA,CAPA;;IAQAG;MACA;MACA,0DAFA,CAGA;MACA;;MACA,oBALA,CAMA;;MACA;;MACA;QACAC;UACAf,cADA;UAEAgB,IAFA;UAGAC,KAHA;UAIAC,IAJA;UAKAC,KALA;UAMAC,aACA;YACAC,SADA;YAEAC,gBAFA,CAEA;;UAFA,CADA,EAIA;YACAD,WADA;YAEAC,gBAFA,CAEA;;UAFA,CAJA,EAOA;YACAD,WADA;YAEAC,gBAFA,CAEA;;UAFA,CAPA,EAUA;YACAD,SADA;YAEAC;UAFA,CAVA;QANA;MAqBA;;MAEA;QACA;QACAC;UACAC,eADA;UAEAC,qCAFA;UAGAC,sBAHA;UAIAC,cAJA;UAKAC;YACA5B;UADA,CALA;UAQA6B;YACAP;UADA,CARA;UAWAQ;YACA;YACA;UACA;QAdA,CAFA;QAkBA;QACAC;UACAC,UADA;UAEAC,WAFA;UAGAC,YAHA;UAIAC,SAJA;UAKAC;QALA,CAnBA;QA0BA;QACAC;UACA9B,eADA;UAEAP,gBAFA;UAGAsC;YACAC,UADA;YAEAC;cACAlB;YADA;UAFA,CAHA;UASAmB;YACAF;UADA,CATA;UAYAG;YACAH;UADA,CAZA;UAeAI;YACAJ,UADA;YAEAjB,gBAFA;YAGAsB,YAHA;YAIAC,WAJA;YAKAC,UALA;YAMAC,SANA;YAOAjB;cACA;YACA;UATA;QAfA,CA3BA;QAsDA;QACAkB;UACAhD,aADA;UAEAuC,UAFA;UAGAU,cAHA;UAIAX;YACAC;UADA,CAJA;UAOAE;YACAF,UADA;YAEAC;cACAlB,iCADA;cAEAtB;YAFA;UAFA,CAPA;UAcA2C;YACAJ,UADA;YAEAjB,gBAFA;YAGAsB;UAHA;QAdA,CAvDA;QA2EAM,SACA;UACAlD,WADA;UAEAmD,kBAFA;UAGAC;YACAC;cACA/B;gBACA;cACA;YAHA;UADA,CAHA;UAUAgC;YACAf,UADA;YAEAgB,eAFA;YAGAjC,gBAHA;YAIAsB,YAJA;YAKAY;UALA,CAVA;UAiBAjD;QAjBA,CADA,EAoBA;UACAkD,IADA;UAEAzD,oBAFA;UAGAO,gBAHA;UAIAmD,iBAJA;UAKAC,wBALA;UAMAC,sCANA;UAOAR;YACAC;cACA/B;gBACAtB,cADA;gBAEAgB,IAFA;gBAGAE,IAHA;gBAIAD,KAJA;gBAKAE,KALA;gBAMAC,aACA;kBAAAC;kBAAAC;gBAAA,CADA,EAEA;kBAAAD;kBAAAC;gBAAA,CAFA;cANA;YADA;UADA;QAPA,CApBA,EA2CA;UACAmC,IADA;UAEAzD,oBAFA;UAGA6D,qBAHA;UAIAtD,gBAJA;UAKAmD,iBALA;UAMAC,yBANA;UAOAC,sCAPA;UAQAR;YACAC;cACA1B,cADA;cAEAL;gBACAtB,cADA;gBAEAgB,IAFA;gBAGAE,IAHA;gBAIAD,KAJA;gBAKAE,KALA;gBAMAC,aACA;kBAAAC;kBAAAC;gBAAA,CADA,EAEA;kBAAAD;kBAAAC;gBAAA,CAFA;cANA;YAFA;UADA;QARA,CA3CA;MA3EA;IAiJA,CAzLA;;IA0LAwC;MACA;MACA;IACA,CA7LA;;IA8LAC;MACA;QACA;MACA;IACA;;EAlMA;AAvCA", "names": ["name", "props", "id", "type", "required", "chartData", "default", "title", "showValues", "maxValue", "data", "chart", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "window", "methods", "initChart", "getOption", "colors", "x", "x2", "y", "y2", "colorStops", "offset", "color", "tooltip", "trigger", "backgroundColor", "borderColor", "borderWidth", "axisPointer", "textStyle", "formatter", "grid", "left", "right", "bottom", "top", "containLabel", "xAxis", "axisLine", "show", "lineStyle", "splitLine", "axisTick", "axisLabel", "fontSize", "interval", "rotate", "margin", "yAxis", "splitNumber", "series", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "normal", "label", "position", "align", "z", "symbol", "symbolOffset", "symbolSize", "symbolPosition", "<PERSON><PERSON><PERSON>", "resizeChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["RankingBarChart.vue"], "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"ranking-bar-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'RankingBar<PERSON><PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    showValues: {\n      type: Boolean,\n      default: true\n    },\n    maxValue: {\n      type: Number,\n      default: null\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.renderChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n    getOption () {\n      const xAxisData = this.chartData.map(item => item.name)\n      const seriesData = this.chartData.map(item => item.value)\n      // 计算柱子宽度\n      // const dom = 300\n      const barWidth = 20\n      // 生成渐变色数组\n      const colors = []\n      for (let i = 0; i < this.chartData.length; i++) {\n        colors.push({\n          type: 'linear',\n          x: 0,\n          x2: 1,\n          y: 0,\n          y2: 0,\n          colorStops: [\n            {\n              offset: 0,\n              color: '#0093DD' // 最左边\n            }, {\n              offset: 0.5,\n              color: '#006BBC' // 左边的右边 颜色\n            }, {\n              offset: 0.5,\n              color: '#0093DD' // 右边的左边 颜色\n            }, {\n              offset: 1,\n              color: '#006BBC'\n            }]\n        })\n      }\n\n      return {\n        // 提示框\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00D4FF',\n          borderWidth: 1,\n          axisPointer: {\n            type: 'shadow'\n          },\n          textStyle: {\n            color: '#FFFFFF'\n          },\n          formatter: function (params) {\n            const data = params[0]\n            return `${data.name}<br/>人数: ${data.value}人`\n          }\n        },\n        // 区域位置\n        grid: {\n          left: '0%',\n          right: '0%',\n          bottom: '0%',\n          top: '5%',\n          containLabel: true\n        },\n        // X轴\n        xAxis: {\n          data: xAxisData,\n          type: 'category',\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.2)'\n            }\n          },\n          splitLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12,\n            interval: 0,\n            rotate: 35,\n            margin: 8,\n            formatter: (value) => {\n              return value\n            }\n          }\n        },\n        // y轴\n        yAxis: {\n          type: 'value',\n          show: true,\n          splitNumber: 5,\n          axisLine: {\n            show: false\n          },\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              type: 'dashed'\n            }\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12\n          }\n        },\n        series: [\n          {\n            type: 'bar',\n            barWidth: barWidth,\n            itemStyle: {\n              normal: {\n                color: function (params) {\n                  return colors[params.dataIndex % colors.length]\n                }\n              }\n            },\n            label: {\n              show: true,\n              position: 'top',\n              color: '#FFFFFF',\n              fontSize: 12,\n              align: 'center'\n            },\n            data: seriesData\n          },\n          {\n            z: 2,\n            type: 'pictorialBar',\n            data: seriesData,\n            symbol: 'diamond',\n            symbolOffset: [0, '50%'],\n            symbolSize: [barWidth, barWidth * 0.5],\n            itemStyle: {\n              normal: {\n                color: {\n                  type: 'linear',\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: '#1FC6FF' },\n                    { offset: 1, color: '#0072DD' }\n                  ]\n                }\n              }\n            }\n          },\n          {\n            z: 3,\n            type: 'pictorialBar',\n            symbolPosition: 'end',\n            data: seriesData,\n            symbol: 'diamond',\n            symbolOffset: [0, '-50%'],\n            symbolSize: [barWidth, barWidth * 0.5],\n            itemStyle: {\n              normal: {\n                borderWidth: 0,\n                color: {\n                  type: 'linear',\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: '#1FC6FF' },\n                    { offset: 1, color: '#0072DD' }\n                  ]\n                }\n              }\n            }\n          }\n        ]\n      }\n    },\n    renderChart () {\n      if (!this.chart || !this.chartData.length) return\n      this.chart.setOption(this.getOption(), true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.ranking-bar-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}