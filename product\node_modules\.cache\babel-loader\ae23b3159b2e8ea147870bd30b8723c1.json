{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\RankingBarChart.vue", "mtime": 1756349639780}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,uBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,WADA;MAEAC,cAFA;MAGAE;IAHA,CALA;IAUAC;MACAJ,YADA;MAEAG;IAFA,CAVA;IAcAE;MACAL,aADA;MAEAG;IAFA,CAdA;IAkBAG;MACAN,YADA;MAEAG;IAFA;EAlBA,CAFA;;EAyBAI;IACA;MACAC;IADA;EAGA,CA7BA;;EA8BAC;IACA;EACA,CAhCA;;EAiCAC;IACA;MACA;IACA;;IACAC;EACA,CAtCA;;EAuCAC;IACAC;MACA;MACA;MACA;MACA;MACAF;IACA,CAPA;;IAQAG;MACA;MACA,0DAFA,CAGA;MACA;;MACA,oBALA,CAMA;;MACA;;MACA;QACAC;UACAf,cADA;UAEAgB,IAFA;UAGAC,KAHA;UAIAC,IAJA;UAKAC,KALA;UAMAC,aACA;YACAC,SADA;YAEAC,gBAFA,CAEA;;UAFA,CADA,EAIA;YACAD,WADA;YAEAC,6BAFA,CAEA;;UAFA,CAJA,EAOA;YACAD,WADA;YAEAC,iBAFA,CAEA;;UAFA,CAPA,EAUA;YACAD,SADA;YAEAC;UAFA,CAVA;QANA;MAsBA;;MAEA;QACA;QACAC;UACAC,eADA;UAEAC,qCAFA;UAGAC,sBAHA;UAIAC,cAJA;UAKAC;YACA5B;UADA,CALA;UAQA6B;YACAP;UADA,CARA;UAWAQ;YACA;YACA;UACA;QAdA,CAFA;QAkBA;QACAC;UACAC,UADA;UAEAC,WAFA;UAGAC,YAHA;UAIAC,SAJA;UAKAC;QALA,CAnBA;QA0BA;QACAC;UACA9B,eADA;UAEAP,gBAFA;UAGAsC;YACAC,UADA;YAEAC;cACAlB;YADA;UAFA,CAHA;UASAmB;YACAF;UADA,CATA;UAYAG;YACAH;UADA,CAZA;UAeAI;YACAJ,UADA;YAEAjB,gBAFA;YAGAsB,YAHA;YAIAC,WAJA;YAKAC,UALA;YAMAC,SANA;YAOAjB;cACA;YACA;UATA;QAfA,CA3BA;QAsDA;QACAkB;UACAhD,aADA;UAEAuC,UAFA;UAGAU,cAHA;UAIAX;YACAC;UADA,CAJA;UAOAE;YACAF,UADA;YAEAC;cACAlB,iCADA;cAEAtB;YAFA;UAFA,CAPA;UAcA2C;YACAJ,UADA;YAEAjB,gBAFA;YAGAsB;UAHA;QAdA,CAvDA;QA2EAM,SACA;UACAlD,WADA;UAEAmD,kBAFA;UAGAC;YACAC;cACA/B;gBACA;cACA;YAHA;UADA,CAHA;UAUAgC;YACAf,UADA;YAEAgB,eAFA;YAGAjC,gBAHA;YAIAsB,YAJA;YAKAY;UALA,CAVA;UAiBAjD;QAjBA,CADA,EAoBA;UACAkD,IADA;UAEAzD,oBAFA;UAGAO,gBAHA;UAIAmD,iBAJA;UAKAC,wBALA;UAMAC,sCANA;UAOAR;YACAC;cACA/B;gBACA;cACA;YAHA;UADA;QAPA,CApBA,EAmCA;UACAmC,IADA;UAEAzD,oBAFA;UAGA6D,qBAHA;UAIAtD,gBAJA;UAKAmD,iBALA;UAMAC,yBANA;UAOAC,sCAPA;UAQAR;YACAC;cACA1B,cADA;cAEAL;gBACA;cACA;YAJA;UADA;QARA,CAnCA;MA3EA;IAiIA,CA1KA;;IA2KAwC;MACA;MACA;IACA,CA9KA;;IA+KAC;MACA;QACA;MACA;IACA;;EAnLA;AAvCA", "names": ["name", "props", "id", "type", "required", "chartData", "default", "title", "showValues", "maxValue", "data", "chart", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "window", "methods", "initChart", "getOption", "colors", "x", "x2", "y", "y2", "colorStops", "offset", "color", "tooltip", "trigger", "backgroundColor", "borderColor", "borderWidth", "axisPointer", "textStyle", "formatter", "grid", "left", "right", "bottom", "top", "containLabel", "xAxis", "axisLine", "show", "lineStyle", "splitLine", "axisTick", "axisLabel", "fontSize", "interval", "rotate", "margin", "yAxis", "splitNumber", "series", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "normal", "label", "position", "align", "z", "symbol", "symbolOffset", "symbolSize", "symbolPosition", "<PERSON><PERSON><PERSON>", "resizeChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["RankingBarChart.vue"], "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"ranking-bar-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'RankingBar<PERSON><PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    showValues: {\n      type: Boolean,\n      default: true\n    },\n    maxValue: {\n      type: Number,\n      default: null\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.renderChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n    getOption () {\n      const xAxisData = this.chartData.map(item => item.name)\n      const seriesData = this.chartData.map(item => item.value)\n      // 计算柱子宽度\n      // const dom = 300\n      const barWidth = 20\n      // 生成渐变色数组\n      const colors = []\n      for (let i = 0; i < this.chartData.length; i++) {\n        colors.push({\n          type: 'linear',\n          x: 0,\n          x2: 1,\n          y: 0,\n          y2: 0,\n          colorStops: [\n            {\n              offset: 0,\n              color: '#006BBC' // 最左边\n            }, {\n              offset: 0.5,\n              color: 'rgba(31,198,255,0.2)' // 左边的右边 颜色\n            }, {\n              offset: 0.5,\n              color: '#006BBC ' // 右边的左边 颜色\n            }, {\n              offset: 1,\n              color: '#3dc8ca'\n            }\n          ]\n        })\n      }\n\n      return {\n        // 提示框\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00D4FF',\n          borderWidth: 1,\n          axisPointer: {\n            type: 'shadow'\n          },\n          textStyle: {\n            color: '#FFFFFF'\n          },\n          formatter: function (params) {\n            const data = params[0]\n            return `${data.name}<br/>人数: ${data.value}人`\n          }\n        },\n        // 区域位置\n        grid: {\n          left: '0%',\n          right: '0%',\n          bottom: '0%',\n          top: '5%',\n          containLabel: true\n        },\n        // X轴\n        xAxis: {\n          data: xAxisData,\n          type: 'category',\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.2)'\n            }\n          },\n          splitLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12,\n            interval: 0,\n            rotate: 35,\n            margin: 8,\n            formatter: (value) => {\n              return value\n            }\n          }\n        },\n        // y轴\n        yAxis: {\n          type: 'value',\n          show: true,\n          splitNumber: 5,\n          axisLine: {\n            show: false\n          },\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              type: 'dashed'\n            }\n          },\n          axisLabel: {\n            show: true,\n            color: '#D9E6FF',\n            fontSize: 12\n          }\n        },\n        series: [\n          {\n            type: 'bar',\n            barWidth: barWidth,\n            itemStyle: {\n              normal: {\n                color: function (params) {\n                  return colors[params.dataIndex % colors.length]\n                }\n              }\n            },\n            label: {\n              show: true,\n              position: 'top',\n              color: '#FFFFFF',\n              fontSize: 12,\n              align: 'center'\n            },\n            data: seriesData\n          },\n          {\n            z: 2,\n            type: 'pictorialBar',\n            data: seriesData,\n            symbol: 'diamond',\n            symbolOffset: [0, '50%'],\n            symbolSize: [barWidth, barWidth * 0.5],\n            itemStyle: {\n              normal: {\n                color: function (params) {\n                  return colors[params.dataIndex % colors.length]\n                }\n              }\n            }\n          },\n          {\n            z: 3,\n            type: 'pictorialBar',\n            symbolPosition: 'end',\n            data: seriesData,\n            symbol: 'diamond',\n            symbolOffset: [0, '-50%'],\n            symbolSize: [barWidth, barWidth * 0.5],\n            itemStyle: {\n              normal: {\n                borderWidth: 0,\n                color: function (params) {\n                  return colors[params.dataIndex % colors.length].colorStops[0].color\n                }\n              }\n            }\n          }\n        ]\n      }\n    },\n    renderChart () {\n      if (!this.chart || !this.chartData.length) return\n      this.chart.setOption(this.getOption(), true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.ranking-bar-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}