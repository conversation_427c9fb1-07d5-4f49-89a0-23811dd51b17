{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-calendar\\zy-calendar.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-calendar\\zy-calendar.vue", "mtime": 1660102037658}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA6CA;AACA;EACAA,kBADA;;EAEAC;IACA;MACAC,SADA;MAEAC,SAFA;MAGAC,WAHA;MAIAC,eAJA;MAKAC,UALA;MAMAC,UANA;MAOAC,QAPA;MAQAC,gBARA;MASAC;IATA;EAWA,CAdA;;EAeAC;IACAC;MACAC,WADA;MAEAC;IAFA,CADA;IAKAC;MACAF,WADA;MAEAC;IAFA,CALA;IASAE;MACAH,WADA;MAEAC;IAFA,CATA;IAaAG;MACAJ,aADA;MAEAC;IAFA,CAbA;IAiBAI;MACAL,YADA;MAEAC;IAFA,CAjBA;IAqBAK;MACAN,YADA;MAEAC;IAFA,CArBA;IAyBAM;MACAP,aADA;MAEAC;IAFA;EAzBA,CAfA;;EA6CAO;IACA;IACA;IACA;EACA,CAjDA;;EAkDAC;IACAC;MACA;MACA;MACA,uEAHA,CAGA;;MACA;MACA;IACA,CAPA;;IAQAC;MACA;MACA;MACA;MACA;MACA;;MACA;QACArB;MACA;;MACA;QACAsB;MACA;;MACA;MACA;IACA,CAtBA;;IAuBAC;MACA;;MACA;QACA;QACA;QACAC;UACA;QACA,CAFA,EAEA,GAFA;MAGA;;MACA;QACA;QACA;QACAA;UACA;QACA,CAFA,EAEA,GAFA;MAGA;IACA,CAvCA;;IAwCAC;MACAC;IACA,CA1CA;;IA2CAC;MACA;MACAC;MACA;IACA,CA/CA;;IAgDAC;MACA;QACA;MACA;;MACA;QACAC,iCACA,wBADA,GAEA,yBAFA;MAGA;IACA,CAzDA;IA0DAC;MACAC;MACA;MACA;;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA;IACA,CAnEA;IAoEAC;MACAD;MACA;MACA;;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA;IACA,CA7EA;IA8EAE;MACAF;MACA;MACA;;MACA;QACA;MACA,CAFA,MAEA;QACA;MACA;IACA,CAvFA;IAwFAG;MACA;MACA;MACA1B;QACA;MACA,CAFA;MAGAG;QACAwB;QACA;MACA,CAHA;MAIA;IACA,CAnGA;IAoGAC;MACA;MACA;MACA;;MACA;QACA;QACA;QACAD;QACA;QACA;;QACA;UACA;YACAE;UACA;QACA;;QACAF;QACAA;QACAA;;QACA;UACA;QACA;;QACA;;QACA;UACA;UACA;UACA;UACAA;QACA,CALA,MAKA,IACA,+DACA,UADA,IAEAjC,IAHA,EAIA;UACAiC;QACA;MACA;;MACA;IACA;EAxIA,CAlDA;;EA4LAG;IACA;EACA,CA9LA;;EA+LAC;IACA/B;MACAgC;QACA;MACA,CAHA;;MAIAC;IAJA,CADA;IAOA9B;MACA6B;QACA;MACA,CAHA;;MAIAC;IAJA,CAPA;IAaA3B;MACA0B;QACA;MACA,CAHA;;MAIAC;IAJA,CAbA;IAmBA1B;MACAyB;QACA;MACA,CAHA;;MAIAC;IAJA,CAnBA;IAyBA5B;MACA2B;QACA;QACA;MACA,CAJA;;MAKAC;IALA;EAzBA;AA/LA", "names": ["name", "data", "years", "month", "Whatday", "yearsshow", "flag", "myDate", "list", "historyChose", "dateTop", "props", "markDate", "type", "default", "markDateMore", "textTop", "sundayStart", "agoDayHide", "futureDayHide", "showHeader", "created", "methods", "getWeekDate", "getNowFormatDate", "strDate", "selected", "setTimeout", "intStart", "timeUtil", "setClass", "obj", "clickDay", "item", "ChoseMonth", "date", "PreMonth", "NextMonth", "forMatArgs", "k", "getList", "mark<PERSON><PERSON><PERSON>ame", "mounted", "watch", "handler", "deep"], "sourceRoot": "src/components/zy-calendar", "sources": ["zy-calendar.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-calendar\">\r\n    <div class=\"zy-calendar-selected\"\r\n         v-if=\"showHeader\">\r\n      <transition name=\"fadeY\">\r\n        <div class=\"years\"\r\n             v-if=\"yearsshow\">{{years}}</div>\r\n      </transition>\r\n      <transition name=\"fadeY\">\r\n        <div class=\"time\"\r\n             v-if=\"flag\">{{month+'&nbsp;&nbsp;'+Whatday}}</div>\r\n      </transition>\r\n    </div>\r\n    <section class=\"zy_container\">\r\n      <div class=\"zy_content_all\">\r\n        <div class=\"zy_top_changge\">\r\n          <li @click=\"PreMonth(myDate,false)\">\r\n            <div class=\"zy_jiantou1\"></div>\r\n          </li>\r\n          <li class=\"zy_content_li\">{{dateTop}}</li>\r\n          <li @click=\"NextMonth(myDate,false)\">\r\n            <div class=\"zy_jiantou2\"></div>\r\n          </li>\r\n        </div>\r\n        <div class=\"zy_content\">\r\n          <div class=\"zy_content_item\"\r\n               v-for=\"(tag,index) in textTop\"\r\n               :key=\"index\">\r\n            <div class=\"zy_top_tag\">{{tag}}</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"zy_content\">\r\n          <div class=\"zy_content_item\"\r\n               v-for=\"(item,index) in list\"\r\n               :key=\"index\"\r\n               @click=\"clickDay(item,index)\">\r\n            <div class=\"zy_item_date\"\r\n                 :class=\"[{ zy_isMark: item.isMark},{zy_other_dayhide:item.otherMonth!=='nowMonth'},{zy_want_dayhide:item.dayHide},{zy_isToday:item.isToday},{zy_chose_day:item.chooseDay},setClass(item)]\">{{item.id}}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</template>\r\n<script>\r\nimport timeUtil from './timeUtil'\r\nexport default {\r\n  name: 'zyCalendar',\r\n  data () {\r\n    return {\r\n      years: '',\r\n      month: '',\r\n      Whatday: '',\r\n      yearsshow: true,\r\n      flag: true,\r\n      myDate: [],\r\n      list: [],\r\n      historyChose: [],\r\n      dateTop: ''\r\n    }\r\n  },\r\n  props: {\r\n    markDate: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    markDateMore: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    textTop: {\r\n      type: Array,\r\n      default: () => ['一', '二', '三', '四', '五', '六', '日']\r\n    },\r\n    sundayStart: {\r\n      type: Boolean,\r\n      default: () => false\r\n    },\r\n    agoDayHide: {\r\n      type: String,\r\n      default: '0'\r\n    },\r\n    futureDayHide: {\r\n      type: String,\r\n      default: '2554387200'\r\n    },\r\n    showHeader: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  created () {\r\n    this.intStart()\r\n    this.myDate = new Date()\r\n    this.selected(this.getNowFormatDate(this.myDate))\r\n  },\r\n  methods: {\r\n    getWeekDate (data) {\r\n      var now = new Date(data)\r\n      var day = now.getDay()\r\n      var weeks = new Array('星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六')// eslint-disable-line\r\n      var week = weeks[day]\r\n      return week\r\n    },\r\n    getNowFormatDate (data) {\r\n      var date = data\r\n      var seperator1 = '-'\r\n      var year = date.getFullYear()\r\n      var month = date.getMonth() + 1\r\n      var strDate = date.getDate()\r\n      if (month >= 1 && month <= 9) {\r\n        month = '0' + month\r\n      }\r\n      if (strDate >= 0 && strDate <= 9) {\r\n        strDate = '0' + strDate\r\n      }\r\n      var currentdate = year + seperator1 + month + seperator1 + strDate\r\n      return currentdate\r\n    },\r\n    selected (data) {\r\n      this.Whatday = this.getWeekDate(data)\r\n      if (this.years !== data.slice(0, 4)) {\r\n        this.years = data.slice(0, 4)\r\n        this.yearsshow = false\r\n        setTimeout(() => {\r\n          this.yearsshow = true\r\n        }, 200)\r\n      }\r\n      if (this.month !== data.slice(5).replace('/', '-')) {\r\n        this.month = data.slice(5).replace('/', '-')\r\n        this.flag = false\r\n        setTimeout(() => {\r\n          this.flag = true\r\n        }, 200)\r\n      }\r\n    },\r\n    intStart () {\r\n      timeUtil.sundayStart = this.sundayStart\r\n    },\r\n    setClass (data) {\r\n      const obj = {}\r\n      obj[data.markClassName] = data.markClassName\r\n      return obj\r\n    },\r\n    clickDay: function (item, index) {\r\n      if (item.otherMonth === 'nowMonth' && !item.dayHide) {\r\n        this.getList(this.myDate, item.date)\r\n      }\r\n      if (item.otherMonth !== 'nowMonth') {\r\n        item.otherMonth === 'preMonth'\r\n          ? this.PreMonth(item.date)\r\n          : this.NextMonth(item.date)\r\n      }\r\n    },\r\n    ChoseMonth: function (date, isChosedDay = true) {\r\n      date = timeUtil.dateFormat(date)\r\n      this.myDate = new Date(date)\r\n      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate))\r\n      if (isChosedDay) {\r\n        this.getList(this.myDate, date, isChosedDay)\r\n      } else {\r\n        this.getList(this.myDate)\r\n      }\r\n    },\r\n    PreMonth: function (date, isChosedDay = true) {\r\n      date = timeUtil.dateFormat(date)\r\n      this.myDate = timeUtil.getOtherMonth(this.myDate, 'preMonth')\r\n      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate))\r\n      if (isChosedDay) {\r\n        this.getList(this.myDate, date, isChosedDay)\r\n      } else {\r\n        this.getList(this.myDate)\r\n      }\r\n    },\r\n    NextMonth: function (date, isChosedDay = true) {\r\n      date = timeUtil.dateFormat(date)\r\n      this.myDate = timeUtil.getOtherMonth(this.myDate, 'nextMonth')\r\n      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate))\r\n      if (isChosedDay) {\r\n        this.getList(this.myDate, date, isChosedDay)\r\n      } else {\r\n        this.getList(this.myDate)\r\n      }\r\n    },\r\n    forMatArgs: function () {\r\n      let markDate = this.markDate\r\n      let markDateMore = this.markDateMore\r\n      markDate = markDate.map(k => {\r\n        return timeUtil.dateFormat(k)\r\n      })\r\n      markDateMore = markDateMore.map(k => {\r\n        k.date = timeUtil.dateFormat(k.date)\r\n        return k\r\n      })\r\n      return [markDate, markDateMore]\r\n    },\r\n    getList: function (date, chooseDay, isChosedDay = true) {\r\n      const [markDate, markDateMore] = this.forMatArgs()\r\n      this.dateTop = `${date.getFullYear()}年${date.getMonth() + 1}月`\r\n      const arr = timeUtil.getMonthList(this.myDate)\r\n      for (let i = 0; i < arr.length; i++) {\r\n        let markClassName = ''\r\n        const k = arr[i]\r\n        k.chooseDay = false\r\n        const nowTime = k.date\r\n        const t = new Date(nowTime).getTime() / 1000\r\n        for (const c of markDateMore) {\r\n          if (c.date === nowTime) {\r\n            markClassName = c.className || ''\r\n          }\r\n        }\r\n        k.markClassName = markClassName\r\n        k.isMark = markDate.indexOf(nowTime) > -1\r\n        k.dayHide = t < this.agoDayHide || t > this.futureDayHide\r\n        if (k.isToday) {\r\n          this.$emit('isToday', nowTime)\r\n        }\r\n        const flag = !k.dayHide && k.otherMonth === 'nowMonth'\r\n        if (chooseDay && chooseDay === nowTime && flag) {\r\n          this.$emit('choseDay', nowTime)\r\n          this.selected(nowTime)\r\n          this.historyChose.push(nowTime)\r\n          k.chooseDay = true\r\n        } else if (\r\n          this.historyChose[this.historyChose.length - 1] === nowTime &&\r\n          !chooseDay &&\r\n          flag\r\n        ) {\r\n          k.chooseDay = true\r\n        }\r\n      }\r\n      this.list = arr\r\n    }\r\n  },\r\n  mounted () {\r\n    this.getList(this.myDate)\r\n  },\r\n  watch: {\r\n    markDate: {\r\n      handler (val, oldVal) {\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    },\r\n    markDateMore: {\r\n      handler (val, oldVal) {\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    },\r\n    agoDayHide: {\r\n      handler (val, oldVal) {\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    },\r\n    futureDayHide: {\r\n      handler (val, oldVal) {\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    },\r\n    sundayStart: {\r\n      handler (val, oldVal) {\r\n        this.intStart()\r\n        this.getList(this.myDate)\r\n      },\r\n      deep: true\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-calendar.scss\";\r\n</style>\r\n"]}]}