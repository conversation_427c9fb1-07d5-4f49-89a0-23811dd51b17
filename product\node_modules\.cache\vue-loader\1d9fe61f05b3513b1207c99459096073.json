{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\ThreeActivities\\OrganReviewNew.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\ThreeActivities\\OrganReviewNew.vue", "mtime": 1752541693878}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["OrganReviewNew.vue"], "names": [], "mappings": ";AA0CA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "OrganReviewNew.vue", "sourceRoot": "src/views/AssessmentOrgan/ThreeActivities", "sourcesContent": ["<template>\r\n\r\n  <div class=\"OrganReviewNew\">\r\n    <el-form :model=\"form\"\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"demo-form\">\r\n\r\n      <!-- <el-form-item label=\"三双活动类型\"\r\n                    class=\"form-title\">\r\n        <el-input-number v-model=\"form.meetTypeName\"\r\n                         controls-position=\"right\"\r\n                         @change=\"handleChange\"\r\n                         :min=\"0\"\r\n                         :max=\"100\"\r\n                         class=\"form-content\">\r\n        </el-input-number>\r\n      </el-form-item> -->\r\n\r\n      <el-form-item label=\"三双活动类型\"\r\n                    prop=\"activityTypeName\">\r\n        <el-select v-model=\"form.activityTypeName\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择类型\">\r\n          <el-option v-for=\"item in classifyData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'OrganReviewNew',\r\n  data () {\r\n    return {\r\n      classifyData: [],\r\n\r\n      form: {\r\n        id: '',\r\n        type: '',\r\n        officeId: '',\r\n        sedateId: '',\r\n        officeName: '',\r\n        activityTypeName: ''\r\n      },\r\n\r\n      circlesStatus: []\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  mounted () {\r\n    this.dictionaryPubkvs()\r\n\r\n    this.templatePageInfo()\r\n  },\r\n  methods: {\r\n    /**\r\n*字典\r\n*/\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluate_activiti_three'\r\n      })\r\n      var { data } = res\r\n      this.classifyData = data.evaluate_activiti_three\r\n    },\r\n    select (row) {\r\n      this.form.officeName = row ? row.label : ''\r\n    },\r\n\r\n    // 获取详情  (没有详情接口,接口从列表获取)\r\n    async templatePageInfo () {\r\n      const res = await this.$api.AssessmentOrgan.reqThreeActivitiesList(this.id)\r\n      var { data } = res\r\n      this.form.id = data.id\r\n      data.forEach(item => {\r\n        if (this.id === item.id) {\r\n          this.form.activityTypeName = item.activityTypeName\r\n        }\r\n      })\r\n    },\r\n\r\n    async historycirclesInfo () {\r\n      const res = await this.$api.memberInformation.historycirclesInfo(this.id)\r\n      var { data } = res\r\n      this.form.boutYear = data.boutYear\r\n      this.form.circlesStatus = data.circlesStatus\r\n    },\r\n    handleChange (value) {\r\n      console.log(value)\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          this.$api.AssessmentOrgan.reqEditThreeClass({\r\n            ids: this.id,\r\n            type: this.form.activityTypeName\r\n\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('newCallback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('newCallback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.OrganReviewNew {\r\n  width: 430px;\r\n  height: 100%;\r\n  padding: 24px 40px;\r\n  // overflow-y: scroll;\r\n  overflow-x: hidden;\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n"]}]}