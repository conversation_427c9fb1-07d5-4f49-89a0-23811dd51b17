{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\newFinishDetail.vue?vue&type=style&index=0&id=65ec26ae&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\newFinishDetail.vue", "mtime": 1752541693793}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoubmV3RmluaXNoRGV0YWlsIHsNCiAgd2lkdGg6IDEwMDBweDsNCiAgaGVpZ2h0OiA2NzBweDsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogMTBweDsNCiAgYm94LXNoYWRvdzogMHB4IDRweCA2cHggMHB4IHJnYmEoMjMzLCAyMzMsIDIzMywgMC40KTsNCiAgcGFkZGluZzogMjBweCAzMHB4Ow0KDQogIC53YW5nLWVkaXRvciB7DQogICAgLy/op6PlhrPlhoXlrrnovpPlhaXov4fplb/ml7bog73oh6rliqjmjaLooYwNCiAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["newFinishDetail.vue"], "names": [], "mappings": ";AAqMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "newFinishDetail.vue", "sourceRoot": "src/views/AssessmentOrgan/BusinessObjectives", "sourcesContent": ["<template>\r\n  <!-- 新建/编辑 完成情况 -->\r\n  <div class=\"newFinishDetail\">\r\n    <div class=\"add-form-title\">{{ uid? '编辑完成情况' : '新建完成情况'}}</div>\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"qd-form\">\r\n      <el-form-item label=\"标题\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"title\">\r\n        <el-input placeholder=\"请输入标题\"\r\n                  v-model=\"form.title\">\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"完成时间\"\r\n                    class=\"form-item-wd100\"\r\n                    prop=\"overTime\">\r\n        <el-date-picker v-model=\"form.overTime\"\r\n                        type=\"datetime\"\r\n                        value-format=\"timestamp\"\r\n                        placeholder=\"选择完成时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <br>\r\n      <el-form-item label=\"上传附件\"\r\n                    class=\"form-item-wd100 form-upload\">\r\n        <zy-upload-file ref=\"upload\"\r\n                        module=\"notice\"\r\n                        :data=\"file\"\r\n                        :max=\"10\"\r\n                        placeholder=\"仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式\"></zy-upload-file>\r\n      </el-form-item>\r\n      <el-form-item label=\"内容\"\r\n                    class=\"form-item-wd100\">\r\n        <wang-editor v-model='form.content'></wang-editor>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"form-footer-btn\">\r\n      <el-button type=\"primary\"\r\n                 size=\"small\"\r\n                 @click=\"submitForm('form')\">提交</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm1('form')\">重置</el-button>\r\n      <el-button size=\"small\"\r\n                 @click=\"resetForm('form')\">取消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'newFinishDetail',\r\n  data () {\r\n    return {\r\n      // id: this.$route.query.id,\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())),\r\n      form: {\r\n        title: '',\r\n        overTime: '',\r\n        officeName: '',\r\n        content: '',\r\n        // evaluationId: this.$route.query.mid,\r\n        // id: this.$route.query.id,\r\n        // ******\r\n        attachmentId: '',\r\n        auditStatus: ''\r\n      },\r\n      rules: {\r\n        title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n\r\n        overTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ]\r\n      },\r\n      file: []\r\n\r\n    }\r\n  },\r\n  props: ['id', 'uid'], // TODO: 为newFinishDetail组件设置两个属性 id:业务工作目标的id, uid:点击完成情况各列表的id\r\n  mounted () {\r\n    // console.log(this.id)\r\n    // this.form.overTime = this.$format()\r\n    if (this.uid) {\r\n      this.getFinishDetail()\r\n    }\r\n    // 需根据登录信息做判断\r\n    this.form.officeName = this.user.officeName\r\n    // TODO:测试环境暂时注释,提交时再解开*************************************************************************************\r\n    // 登录的user.otherInfo.IsEvaluationAdmin（是否考核管理员）如果等于true,则下面信息回显当前登录人的机构和用户信息，且为可编辑状态，可编辑状态下，调用选人点point_21选择人员并回显机构和人员信息，否则只回显当前登录人的机构和用户信息，不可编辑\r\n    // if (this.user.otherInfo.IsEvaluationAdmin) {\r\n    //   this.disabled = false\r\n    // } else {\r\n    //   this.disabled = true\r\n    // }\r\n    // *************************************************************************************************************************\r\n  },\r\n  inject: ['tabDelJump'],\r\n  methods: {\r\n\r\n    // 获取 完成情况详情\r\n    async getFinishDetail () {\r\n      const res = await this.$api.AssessmentOrgan.reqfinishDetail(this.uid) // uid\r\n      const data = res.data\r\n      // console.log('file1', this.file)\r\n      if (data.attachmentInfo) {\r\n        // data.attachmentInfo.uid = data.attachmentInfo.id\r\n        // data.attachmentInfo.fileName = data.attachmentInfo.oldName // 附件名称\r\n        // this.file.push(data.attachmentInfo)\r\n\r\n        data.attachmentInfo.forEach(item => {\r\n          item.uid = item.id\r\n          item.fileName = item.oldName // 附件名称\r\n          this.file.push(item)\r\n        })\r\n      }\r\n      const { id, evaluationId, title, overTime, auditStatus, content } = res.data\r\n\r\n      // this.file = attachment //报错原因:传入的附件attachment为对象 不能直接赋值给file数组\r\n      // for (const i in attachment) { // 方法:将对象转为数组(因为传入的附件数据为对象)\r\n      //   this.file.push(attachment[i])\r\n      // }\r\n\r\n      this.form = { title, overTime, evaluationId, id, content, auditStatus }\r\n      // console.log('@@@查看是否能收到审核状态auditStatus', this.form)\r\n    },\r\n\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var url = '/functionalperformance/add?'\r\n          if (this.uid) {\r\n            url = '/functionalperformance/edit?'\r\n          }\r\n          this.$api.AssessmentOrgan.reqAddfinishDetail(url, {\r\n            id: this.uid, // 完成情况列表的id\r\n            evaluationId: this.id,\r\n            title: this.form.title,\r\n            overTime: this.form.overTime,\r\n            auditStatus: this.form.auditStatus,\r\n            // ****************************\r\n            content: this.form.content,\r\n            attchmentId: this.$refs.upload.obtainId().join(',') // 获取附件id\r\n\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('newCallback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    resetForm () {\r\n      this.$confirm('是否离开当前页面', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tabDelJump()\r\n        this.$emit('newCallback') // 关闭弹窗并更新列表\r\n      }).catch(() => {\r\n\r\n      })\r\n    },\r\n    // 重置按钮\r\n    resetForm1 () {\r\n      this.$confirm('是否重置当前输入的内容', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.form.content = ''\r\n        this.$refs.form.resetFields()\r\n      }).catch(() => {\r\n      })\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.newFinishDetail {\r\n  width: 1000px;\r\n  height: 670px;\r\n  margin-bottom: 20px;\r\n  background-color: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);\r\n  padding: 20px 30px;\r\n\r\n  .wang-editor {\r\n    //解决内容输入过长时能自动换行\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n  }\r\n}\r\n</style>\r\n"]}]}