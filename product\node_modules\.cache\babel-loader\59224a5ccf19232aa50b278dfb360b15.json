{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\WordCloud.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\WordCloud.vue", "mtime": 1756282591803}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AACA;AACA;EACAA,iBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,WADA;MAEAG,cAFA;MAGAF,iBAHA,CAIA;;IAJA;EALA,CAFA;;EAcAG;IACA;MACAC,WADA;MAEA;MACAC,SACA,SADA,EACA;MACA,SAFA,EAEA;MACA,SAHA,EAGA;MACA,SAJA,EAIA;MACA,SALA,EAKA;MACA,SANA,EAMA;MACA,SAPA,EAOA;MACA,SARA,EAQA;MACA,SATA,EASA;MACA,SAVA,EAUA;MACA,SAXA,EAWA;MACA,SAZA,EAYA;MACA,SAbA,EAaA;MACA,SAdA,EAcA;MACA,SAfA,CAeA;MAfA;IAHA;EAqBA,CApCA;;EAqCAC;IACA;EACA,CAvCA;;EAwCAC;IACA;MACA;IACA;;IACAC;EACA,CA7CA;;EA8CAC;IACAC;MACA;MACA;MACA;MACA;QACA;MACA,CAFA,EAJA,CAOA;;MACAF,oDARA,CASA;;MACA;QACA;UACAG,oBADA;UACA;UACAC;QAFA;MAIA,CALA;IAMA,CAjBA;;IAkBAC;MACA,wBADA,CAEA;;MACA;QACAC,8BADA;QAEAC;UACAC,UADA;UAEAC,eAFA;UAGAC;YACAC;UADA;QAHA,CAFA;QASAC;UACArB,iBADA;UAEA;UACAsB,YAHA;UAIAC,mBAJA;UAKAC,SALA;UAMAC,qBANA;UAOAC,qBAPA;UAQA;UACAC,cATA;UAUAC,aAVA;UAWAC,WAXA;UAYAC,YAZA;UAaAC,aAbA;UAcAC,cAdA;UAeAb;YACAc;cACAC;gBACA,iBACAC,oCADA,EAEAA,oCAFA,EAGAA,oCAHA,EAIAC,IAJA,CAIA,GAJA,IAIA,GAJA;cAKA;YAPA,CADA;YAUAC;cACAC,cADA;cAEAC;YAFA;UAVA,CAfA;UA8BAnC;QA9BA;MATA;MA0CA;IACA,CAhEA;;IAiEAoC;MACA;QACA;MACA;IACA;;EArEA;AA9CA", "names": ["name", "props", "chartId", "type", "default", "words", "required", "data", "chart", "colors", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "window", "methods", "initChart", "text", "weight", "updateChart", "backgroundColor", "tooltip", "show", "position", "textStyle", "fontSize", "series", "gridSize", "sizeRange", "size", "rotation<PERSON>ange", "drawOutOfBound", "left", "top", "right", "bottom", "width", "height", "normal", "color", "Math", "join", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "resizeChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["WordCloud.vue"], "sourcesContent": ["<template>\n  <div :id=\"chartId\" class=\"word-cloud-container\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport 'echarts-wordcloud'\nexport default {\n  name: 'WordCloud',\n  props: {\n    chartId: {\n      type: String,\n      default: 'wordCloud'\n    },\n    words: {\n      type: Array,\n      required: true,\n      default: () => []\n      // 期望格式: [{ text: '经济建设', weight: 10 }, { text: '人才培养', weight: 8 }, ...]\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      // 预定义颜色数组，模拟图片中的颜色效果\n      colors: [\n        '#4FC3F7', // 浅蓝色\n        '#26C6DA', // 青色\n        '#66BB6A', // 绿色\n        '#FFA726', // 橙色\n        '#FF7043', // 橙红色\n        '#AB47BC', // 紫色\n        '#5C6BC0', // 蓝紫色\n        '#42A5F5', // 蓝色\n        '#FFCA28', // 黄色\n        '#4CAF50', // 绿色\n        '#EF5350', // 红色\n        '#A1E2FF', // 浅蓝色\n        '#00BCD4', // 青蓝色\n        '#FF9800', // 深橙色\n        '#9C27B0' // 深紫色\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.chartId)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.$nextTick(() => {\n        this.updateChart()\n      })\n      // 监听窗口大小变化\n      window.addEventListener('resize', this.resizeChart)\n      // 添加点击事件\n      this.chart.on('click', (params) => {\n        this.$emit('word-click', {\n          text: params.data[3], // 散点图数据格式 [x, y, value, name]\n          weight: params.data[2]\n        })\n      })\n    },\n    updateChart () {\n      if (!this.chart) return\n      // 按照你提供的样式配置词云\n      const option = {\n        backgroundColor: 'transparent',\n        tooltip: {\n          show: true,\n          position: 'top',\n          textStyle: {\n            fontSize: 16\n          }\n        },\n        series: [{\n          type: 'wordCloud',\n          // 网格大小，各项之间间距\n          gridSize: 20,\n          sizeRange: [20, 40],\n          size: 0.6,\n          rotationRange: [0, 0],\n          drawOutOfBound: false,\n          // 位置相关设置\n          left: 'center',\n          top: 'center',\n          right: null,\n          bottom: null,\n          width: '100%',\n          height: '100%',\n          textStyle: {\n            normal: {\n              color: function () {\n                return 'rgb(' + [\n                  Math.round(Math.random() * 200 + 55),\n                  Math.round(Math.random() * 200 + 55),\n                  Math.round(Math.random() * 200 + 55)\n                ].join(',') + ')'\n              }\n            },\n            emphasis: {\n              shadowBlur: 10,\n              shadowColor: '#2ac'\n            }\n          },\n          data: this.words\n        }]\n      }\n      this.chart.setOption(option, true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.word-cloud-container {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}