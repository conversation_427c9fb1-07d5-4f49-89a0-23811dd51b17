{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\SinceSituation\\MyColumn.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\SinceSituation\\MyColumn.vue", "mtime": 1752541697666}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdNeUNvbHVtbicsDQogIHByb3BzOiB7DQogICAgY29sOiB7DQogICAgICB0eXBlOiBPYmplY3QNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["MyColumn.vue"], "names": [], "mappings": ";AAyBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MyColumn.vue", "sourceRoot": "src/views/wisdomWarehouse/SinceSituation", "sourcesContent": ["<!--\r\n * @Description:\r\n * @Autor: <PERSON>\r\n * @LastEditors: <PERSON>\r\n * @LastEditTime: 2021-09-28 15:26:50\r\n-->\r\n<template>\r\n\r\n  <el-table-column\r\n    :prop=\"col.fieldName\"\r\n    :label=\"col.label\"\r\n    align=\"center\"\r\n    :width=\"col.fieldName === 'userName'? 180 :''\"\r\n    :class-name=\"col.fieldName === 'userName'? 'username' :''\"\r\n  >\r\n    <MyColumn\r\n      v-for=\"(item, index) in col.children\"\r\n      :key=\"index\"\r\n      :col=\"item\"\r\n    >\r\n    </MyColumn>\r\n  </el-table-column>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MyColumn',\r\n  props: {\r\n    col: {\r\n      type: Object\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style >\r\n.username {\r\n    color: #3657c0;\r\n}\r\n.el-table__row .username .cell {\r\n    color: #3657c0;\r\n    cursor: pointer;\r\n}\r\n</style>\r\n"]}]}