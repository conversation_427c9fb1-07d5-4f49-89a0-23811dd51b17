{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\signature\\signature.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\signature\\signature.vue", "mtime": 1752541693490}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdzaWduYXR1cmUnLAoKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcGFzc3dvcmQ6ICcnCiAgICB9OwogIH0sCgogIG1ldGhvZHM6IHsKICAgIC8vIOWOu+iwg+eUqOetvuWQjQogICAgaGFuZGxlU3VyZSgpIHsKICAgICAgaWYgKHRoaXMucGFzc3dvcmQgPT09ICcnKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36L6T5YWl562+5ZCN5a+G56CBJyk7CiAgICAgIH0KCiAgICAgIHRoaXMuJGFwaS5vZmZpY2VBdXRvbWF0aW9uLnVzZXJTaWduYXR1cmVUb2tlbih7CiAgICAgICAgcGFzc1dvcmQ6IHRoaXMucGFzc3dvcmQKICAgICAgfSkudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuZXJyY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICB0aGlzLiRlbWl0KCdjbG9zZScsIHJlcy5kYXRhKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKCiAgICAvLyDlj5bmtogKICAgIGhhbmRsZUNhbmNlbCgpIHsKICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKTsKICAgIH0KCiAgfQp9Ow=="}, {"version": 3, "mappings": "AAwBA;EACAA,iBADA;;EAEAC;IACA;MACAC;IADA;EAGA,CANA;;EAOAC;IACA;IACAC;MACA;QACA;MACA;;MACA;QAAAC;MAAA;QACA;UACA;QACA;MACA,CAJA;IAKA,CAXA;;IAYA;IACAC;MACA;IACA;;EAfA;AAPA", "names": ["name", "data", "password", "methods", "handleSure", "passWord", "handleCancel"], "sourceRoot": "src/components/signature", "sources": ["signature.vue"], "sourcesContent": ["<template>\r\n  <div class=\"signature\">\r\n    <p class=\"title\">\r\n      <i class=\"el-icon-lock\"></i>\r\n      签名密码\r\n    </p>\r\n    <div class=\"input-box\">\r\n      <el-input v-model=\"password\"\r\n                placeholder=\"请输入签名密码\"\r\n                clearable\r\n                type=\"password\"></el-input>\r\n    </div>\r\n    <div class=\"btn_box\">\r\n      <el-button type=\"primary\"\r\n                 size=\"mini\"\r\n                 @click=\"handleSure\">确定</el-button>\r\n      <el-button type=\"primary\"\r\n                 size=\"mini\"\r\n                 @click=\"handleCancel\">取消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'signature',\r\n  data () {\r\n    return {\r\n      password: ''\r\n    }\r\n  },\r\n  methods: {\r\n    // 去调用签名\r\n    handleSure () {\r\n      if (this.password === '') {\r\n        return this.$message.warning('请输入签名密码')\r\n      }\r\n      this.$api.officeAutomation.userSignatureToken({ passWord: this.password }).then(res => {\r\n        if (res.errcode === 200) {\r\n          this.$emit('close', res.data)\r\n        }\r\n      })\r\n    },\r\n    // 取消\r\n    handleCancel () {\r\n      this.$emit('close')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.signature {\r\n  width: 600px;\r\n  padding: 15px 20px;\r\n  .title {\r\n    color: #3364a8;\r\n    font-size: $textSize16;\r\n    line-height: 30px;\r\n  }\r\n  .btn_box {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-top: 20px;\r\n    align-items: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}