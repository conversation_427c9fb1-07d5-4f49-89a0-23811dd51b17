{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\ThreeActivities\\OrganReviewNew.vue?vue&type=style&index=0&id=c58780d6&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\ThreeActivities\\OrganReviewNew.vue", "mtime": 1752541693878}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouT3JnYW5SZXZpZXdOZXcgew0KICB3aWR0aDogNDMwcHg7DQogIGhlaWdodDogMTAwJTsNCiAgcGFkZGluZzogMjRweCA0MHB4Ow0KICAvLyBvdmVyZmxvdy15OiBzY3JvbGw7DQogIG92ZXJmbG93LXg6IGhpZGRlbjsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCn0NCg=="}, {"version": 3, "sources": ["OrganReviewNew.vue"], "names": [], "mappings": ";AAuIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "OrganReviewNew.vue", "sourceRoot": "src/views/AssessmentOrgan/ThreeActivities", "sourcesContent": ["<template>\r\n\r\n  <div class=\"OrganReviewNew\">\r\n    <el-form :model=\"form\"\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"demo-form\">\r\n\r\n      <!-- <el-form-item label=\"三双活动类型\"\r\n                    class=\"form-title\">\r\n        <el-input-number v-model=\"form.meetTypeName\"\r\n                         controls-position=\"right\"\r\n                         @change=\"handleChange\"\r\n                         :min=\"0\"\r\n                         :max=\"100\"\r\n                         class=\"form-content\">\r\n        </el-input-number>\r\n      </el-form-item> -->\r\n\r\n      <el-form-item label=\"三双活动类型\"\r\n                    prop=\"activityTypeName\">\r\n        <el-select v-model=\"form.activityTypeName\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择类型\">\r\n          <el-option v-for=\"item in classifyData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'OrganReviewNew',\r\n  data () {\r\n    return {\r\n      classifyData: [],\r\n\r\n      form: {\r\n        id: '',\r\n        type: '',\r\n        officeId: '',\r\n        sedateId: '',\r\n        officeName: '',\r\n        activityTypeName: ''\r\n      },\r\n\r\n      circlesStatus: []\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  mounted () {\r\n    this.dictionaryPubkvs()\r\n\r\n    this.templatePageInfo()\r\n  },\r\n  methods: {\r\n    /**\r\n*字典\r\n*/\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluate_activiti_three'\r\n      })\r\n      var { data } = res\r\n      this.classifyData = data.evaluate_activiti_three\r\n    },\r\n    select (row) {\r\n      this.form.officeName = row ? row.label : ''\r\n    },\r\n\r\n    // 获取详情  (没有详情接口,接口从列表获取)\r\n    async templatePageInfo () {\r\n      const res = await this.$api.AssessmentOrgan.reqThreeActivitiesList(this.id)\r\n      var { data } = res\r\n      this.form.id = data.id\r\n      data.forEach(item => {\r\n        if (this.id === item.id) {\r\n          this.form.activityTypeName = item.activityTypeName\r\n        }\r\n      })\r\n    },\r\n\r\n    async historycirclesInfo () {\r\n      const res = await this.$api.memberInformation.historycirclesInfo(this.id)\r\n      var { data } = res\r\n      this.form.boutYear = data.boutYear\r\n      this.form.circlesStatus = data.circlesStatus\r\n    },\r\n    handleChange (value) {\r\n      console.log(value)\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          this.$api.AssessmentOrgan.reqEditThreeClass({\r\n            ids: this.id,\r\n            type: this.form.activityTypeName\r\n\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('newCallback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('newCallback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.OrganReviewNew {\r\n  width: 430px;\r\n  height: 100%;\r\n  padding: 24px 40px;\r\n  // overflow-y: scroll;\r\n  overflow-x: hidden;\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n"]}]}