{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\CircularProgress.vue?vue&type=style&index=0&id=0be54746&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\CircularProgress.vue", "mtime": 1756285769449}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5jaXJjdWxhci1wcm9ncmVzcyB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogMTAwJTsKICB6LWluZGV4OiA1MDsgLy8g56Gu5L+d5Zu+6KGo5a655Zmo5Zyo6IOM5pmv5Zu+5LiK5pa5CgogIC8vIOa3u+WKoOaVtOS9k+eahOWPkeWFieaViOaenAogICY6OmJlZm9yZSB7CiAgICBjb250ZW50OiAnJzsKICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgIHRvcDogNTAlOwogICAgbGVmdDogNTAlOwogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7CiAgICB3aWR0aDogOTAlOwogICAgaGVpZ2h0OiA5MCU7CiAgICBib3JkZXItcmFkaXVzOiA1MCU7CiAgICBiYWNrZ3JvdW5kOiByYWRpYWwtZ3JhZGllbnQoY2lyY2xlLCByZ2JhKDAsIDIxMiwgMjU1LCAwLjEpIDAlLCB0cmFuc3BhcmVudCA3MCUpOwogICAgei1pbmRleDogMjsgLy8g5Y+R5YWJ5pWI5p6c5Zyo6IOM5pmv5Zu+5LiK5pa577yM5L2G5Zyo5Zu+6KGo5LiL5pa5CiAgfQp9Cg=="}, {"version": 3, "sources": ["CircularProgress.vue"], "names": [], "mappings": ";AAsKA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CircularProgress.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"circular-progress\" :id=\"id\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'CircularProgress',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    percentage: {\n      type: Number,\n      required: true,\n      default: 0\n    },\n    label: {\n      type: String,\n      required: true,\n      default: ''\n    },\n    color: {\n      type: String,\n      default: '#00d4ff'\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  watch: {\n    percentage: {\n      handler () {\n        this.updateChart()\n      }\n    }\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.updateChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n\n    updateChart () {\n      if (!this.chart) return\n      const option = {\n        graphic: [\n          // 百分比数字 - 最高优先级\n          {\n            type: 'text',\n            left: 'center',\n            top: '30%',\n            z: 1000,\n            style: {\n              text: this.percentage + '%',\n              fontSize: 32,\n              fontWeight: 'bold',\n              fill: '#fff',\n              textShadowColor: 'rgba(0, 0, 0, 1)',\n              textShadowBlur: 15,\n              textShadowOffsetX: 3,\n              textShadowOffsetY: 3\n            }\n          },\n          // 标签文字 - 最高优先级\n          {\n            type: 'text',\n            left: 'center',\n            top: '58%',\n            z: 1000,\n            style: {\n              text: this.label,\n              fontSize: 16,\n              fontWeight: '500',\n              fill: '#fff',\n              textShadowColor: 'rgba(0, 0, 0, 1)',\n              textShadowBlur: 12,\n              textShadowOffsetX: 2,\n              textShadowOffsetY: 2\n            }\n          }\n        ],\n        series: [\n          {\n            type: 'pie',\n            radius: ['0%', '75%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            z: 100,\n            data: [\n              {\n                value: this.percentage,\n                itemStyle: {\n                  color: {\n                    type: 'linear',\n                    x: 0,\n                    y: 0,\n                    x2: 1,\n                    y2: 1,\n                    colorStops: [\n                      { offset: 0, color: this.color },\n                      { offset: 0.3, color: this.color + 'E6' },\n                      { offset: 0.7, color: this.color + 'B3' },\n                      { offset: 1, color: this.color + '80' }\n                    ]\n                  },\n                  shadowBlur: 25,\n                  shadowColor: this.color + '80',\n                  shadowOffsetX: 0,\n                  shadowOffsetY: 0\n                }\n              },\n              {\n                value: 100 - this.percentage,\n                itemStyle: {\n                  color: {\n                    type: 'radial',\n                    x: 0.5,\n                    y: 0.5,\n                    r: 0.8,\n                    colorStops: [\n                      { offset: 0, color: 'rgba(255, 255, 255, 0.0)' },\n                      { offset: 0.7, color: 'rgba(255, 255, 255, 0.0)' },\n                      { offset: 1, color: 'rgba(255, 255, 255, 0.0)' }\n                    ]\n                  }\n                }\n              }\n            ],\n            label: { show: false },\n            labelLine: { show: false },\n            animation: true,\n            animationDuration: 2000,\n            animationEasing: 'cubicOut'\n          }\n        ]\n      }\n      this.chart.setOption(option)\n    },\n\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.circular-progress {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 50; // 确保图表容器在背景图上方\n\n  // 添加整体的发光效果\n  &::before {\n    content: '';\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    width: 90%;\n    height: 90%;\n    border-radius: 50%;\n    background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, transparent 70%);\n    z-index: 2; // 发光效果在背景图上方，但在图表下方\n  }\n}\n</style>\n"]}]}