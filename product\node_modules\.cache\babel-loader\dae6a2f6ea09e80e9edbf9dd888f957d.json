{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tree-components\\zy-tree-components.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tree-components\\zy-tree-components.vue", "mtime": 1752541693604}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAsDA;EACAA,wBADA;;EAEAC;IACA;MACAC,kBADA;MAEAC;IAFA;EAIA,CAPA;;EAQAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,YADA;MAEAC;IAFA,CALA;IASAE;MACAH,WADA;MAEAC;QACA;MACA;IAJA,CATA;IAeAG;MACAJ,YADA;MAEAC;IAFA,CAfA;IAmBAI;MACAL,YADA;MAEAC;IAFA,CAnBA;IAuBA;IACAH;MACAE,YADA;MAEAC;QACA;UACAK,oBADA;UAEAC;QAFA;MAIA;IAPA,CAxBA;IAiCAC;MACAR,aADA;MAEAC;IAFA,CAjCA;IAqCAQ;MACAT,aADA;MAEAC;IAFA,CArCA;IAyCAS;MACAV,WADA;MAEAC;QACA;MACA;IAJA;EAzCA,CARA;EAwDAU;IACAC,aADA;IAEAC;EAFA,CAxDA;EA4DAC;IACAf;MACA;MACA;IACA,CAJA;;IAKAH;MACA;IACA,CAPA;;IAQAO;MACA;IACA,CAVA;;IAWAO;MACA;IACA;;EAbA,CA5DA;;EA2EAK;IACA;EACA,CA7EA;;EA8EAC;IACAC;MACA;MACA;IACA,CAJA;;IAKAC;MACAvB;QACAwB;;QACA;UACAA;QACA;;QACA;UACA;YAAA;YACAA;UACA;;UACA;YACA;cACAA;YACA;UACA,CAJA;QAKA;MACA,CAfA;MAgBA;MACA;IACA,CAxBA;;IAyBAC;MACAzB;QACA;UACA;;UACA;YACA;UACA;QACA;;QACA;UACA;QACA;MACA,CAVA;IAWA,CArCA;;IAsCA0B;MACA;IACA,CAxCA;;IAyCAC;MACA;QACA;MACA;;MACA;MACAzB;QACAsB;;QACA;UACAA;QACA;MACA,CALA;MAMA;IACA,CArDA;;IAsDAI;MACA;QACA;MACA;;MACA;MACA1B;QACA;UACAsB;QACA;MACA,CAJA;MAKA;IACA,CAjEA;;IAkEAK;MACA;MACA3B;QACA;UACAsB;QACA;MACA,CAJA;MAKA;IACA,CA1EA;;IA2EAM;MACA;MACA;MACA;MACA;;MACA;QACAC;MACA,CAFA,MAEA;QACAA;MACA,CAFA,MAEA;QACA;MACA;;MACA;QACA;UACAA;QACA;;QACA;MACA,CALA,MAKA;QACA;UACAA;QACA;;QACA;MACA;IACA,CAlGA;;IAmGA1B;MACA;MACA;QACA,6BADA;QAEA,2BAFA;QAGA,2BAHA;QAIA,+BAJA;QAKA,yBALA;QAMA,uBANA;QAOA,2BAPA;QAQA,iCARA;QASA,uBATA;QAUA;MAVA;MAYA;IACA,CAlHA;;IAmHA2B;MACA;MACA;;MACA;QACAD;MACA,CAFA,MAEA;QACAA;MACA,CAFA,MAEA;QACA;MACA;;MACA;QACA;UACAA;QACA;MACA,CAJA,MAIA;QACA;UACA;YAAA;YACAA;UACA;QACA;MACA;;MACA;IACA,CAzIA;;IA0IAE;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;IACA,CAlJA;;IAmJAC;MACA;QACA;MACA;;MACA,2CAJA,CAIA;;MACA;;MACA;QACAC;QACA;;QACA;UACAC;;UACA;YACAD;UACA;;UACAxB;QACA;MACA;;MACA,2CAjBA,CAiBA;IACA,CArKA;;IAsKA0B;MACA;IACA;;EAxKA;AA9EA", "names": ["name", "data", "treeId", "treeData", "props", "value", "type", "default", "keyword", "tree", "hierarchy", "nodeKey", "children", "label", "determine", "child", "anykey", "model", "prop", "event", "watch", "created", "methods", "padding", "treeDataCope", "item", "treehierarchy", "selected", "selectedMethods", "subTree", "subTreeicon", "deepCopy", "o", "makeData", "filterTreeData", "filterTree", "node", "sub", "shopfilterNode"], "sourceRoot": "src/components/zy-tree-components", "sources": ["zy-tree-components.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-tree-components\">\r\n    <div v-for=\"treeItem in filterTreeData(treeData)\" :key=\"treeItem.id\">\r\n      <div\r\n        @click=\"selected(treeItem)\"\r\n        v-if=\"!treeItem[props.children].length\"\r\n        :style=\"{ paddingLeft: padding(hierarchy) + 'px' }\"\r\n        :class=\"[\r\n          'zy-tree-components-item',\r\n          treeItem.selected ? 'zy-tree-components-item-selected' : ''\r\n        ]\"\r\n      >\r\n        <div class=\"zy-tree-components-item-icon\"></div>\r\n        <div class=\"zy-tree-components-item-text\">\r\n          {{ treeItem[props.label] }}\r\n        </div>\r\n      </div>\r\n      <div v-else>\r\n        <div\r\n          @click=\"subTree(treeItem)\"\r\n          :style=\"{ paddingLeft: padding(hierarchy) + 'px' }\"\r\n          :class=\"[\r\n            'zy-tree-components-item',\r\n            treeItem.active ? 'zy-tree-components-item-active' : '',\r\n            treeItem.selected ? 'zy-tree-components-item-selected' : ''\r\n          ]\"\r\n        >\r\n          <div\r\n            class=\"zy-tree-components-item-icon\"\r\n            @click.stop=\"subTreeicon(treeItem)\"\r\n          >\r\n            <i class=\"el-icon-caret-right\"></i>\r\n          </div>\r\n          <div class=\"zy-tree-components-item-text\">\r\n            {{ treeItem[props.label] }}\r\n          </div>\r\n        </div>\r\n        <el-collapse-transition>\r\n          <zy-tree-components\r\n            v-if=\"treeItem.active\"\r\n            :anykey=\"anykey\"\r\n            :child=\"child\"\r\n            :props=\"props\"\r\n            v-model=\"treeId\"\r\n            :nodeKey=\"nodeKey\"\r\n            :hierarchy=\"hierarchy + 1\"\r\n            :tree=\"treeItem[props.children]\"\r\n          ></zy-tree-components>\r\n        </el-collapse-transition>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyTreeComponents',\r\n  data () {\r\n    return {\r\n      treeId: this.value,\r\n      treeData: []\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    keyword: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    tree: {\r\n      type: Array,\r\n      default: function () {\r\n        return []\r\n      }\r\n    },\r\n    hierarchy: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    determine: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    child: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    anykey: {\r\n      type: Array,\r\n      default: function () {\r\n        return []\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      this.treeId = val\r\n      this.selectedMethods()\r\n    },\r\n    treeId (val) {\r\n      this.$emit('id', val)\r\n    },\r\n    tree (val) {\r\n      this.treeDataCope(this.deepCopy(this.tree))\r\n    },\r\n    anykey (val) {\r\n      this.treeDataCope(this.deepCopy(this.tree))\r\n    }\r\n  },\r\n  created () {\r\n    this.treeDataCope(this.deepCopy(this.tree), true)\r\n  },\r\n  methods: {\r\n    padding (index) {\r\n      var hierarchy = 18 * index\r\n      return hierarchy\r\n    },\r\n    treeDataCope (data) {\r\n      data.forEach(item => {\r\n        item.selected = false\r\n        if (this.treeId === item[this.nodeKey]) {\r\n          item.selected = true\r\n        }\r\n        if (item[this.props.children].length) {\r\n          if ((typeof item.active) === 'undefined') { // eslint-disable-line\r\n            item.active = false\r\n          }\r\n          this.anykey.forEach(items => {\r\n            if (item[this.nodeKey] === items) {\r\n              item.active = true\r\n            }\r\n          })\r\n        }\r\n      })\r\n      this.treeData = data\r\n      this.treehierarchy(this.treeData, true)\r\n    },\r\n    treehierarchy (data, type) {\r\n      data.forEach(item => {\r\n        if (item[this.nodeKey] === this.treeId) {\r\n          const result = this.makeData(item)\r\n          if (!type) {\r\n            this.$emit('on-tree-click', result)\r\n          }\r\n        }\r\n        if (item[this.props.children].length) {\r\n          this.treehierarchy(item[this.props.children], type)\r\n        }\r\n      })\r\n    },\r\n    selected (data) {\r\n      this.treeId = data[this.nodeKey]\r\n    },\r\n    selectedMethods () {\r\n      if (this.hierarchy === 0) {\r\n        this.treehierarchy(this.treeData)\r\n      }\r\n      const treeData = this.treeData\r\n      treeData.forEach(item => {\r\n        item.selected = false\r\n        if (this.treeId === item[this.nodeKey]) {\r\n          item.selected = true\r\n        }\r\n      })\r\n      this.treeData = treeData\r\n    },\r\n    subTree (data) {\r\n      if (!this.child) {\r\n        this.treeId = data[this.nodeKey]\r\n      }\r\n      const treeData = this.treeData\r\n      treeData.forEach(item => {\r\n        if (item[this.nodeKey] === data[this.nodeKey]) {\r\n          item.active = !item.active\r\n        }\r\n      })\r\n      this.treeData = treeData\r\n    },\r\n    subTreeicon (data) {\r\n      const treeData = this.treeData\r\n      treeData.forEach(item => {\r\n        if (item[this.nodeKey] === data[this.nodeKey]) {\r\n          item.active = !item.active\r\n        }\r\n      })\r\n      this.treeData = treeData\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    },\r\n    makeData (data) {\r\n      const t = this.type(data)\r\n      let o\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (let i = 0; i < data.length; i++) {\r\n          o.push(this.makeData(data[i]))\r\n        }\r\n      } else if (t === 'object') {\r\n        for (const i in data) {\r\n          if (i != 'active' && i != 'selected') {// eslint-disable-line\r\n            o[i] = this.makeData(data[i])\r\n          }\r\n        }\r\n      }\r\n      return o\r\n    },\r\n    filterTreeData (data) {\r\n      if (this.determine) {\r\n        return data\r\n      }\r\n      if (this.keyword === '') {\r\n        return data\r\n      }\r\n      return this.filterTree(data, this.shopfilterNode) || []\r\n    },\r\n    filterTree (nodes, predicate) {\r\n      if (this.hierarchy !== 0) {\r\n        return nodes\r\n      }\r\n      if (!nodes || !nodes.length) return void 0 // eslint-disable-line\r\n      const children = []\r\n      for (let node of nodes) {\r\n        node = Object.assign({}, node)\r\n        const sub = this.filterTree(node[this.props.children], predicate)\r\n        if ((sub && sub.length) || predicate(node)) {\r\n          sub && (node[this.props.children] = sub)\r\n          if (this.keyword) {\r\n            node.active = true\r\n          }\r\n          children.push(node)\r\n        }\r\n      }\r\n      return children.length ? children : void 0 // eslint-disable-line\r\n    },\r\n    shopfilterNode (data) {\r\n      return data[this.props.label].includes(this.keyword)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './zy-tree-components.scss';\r\n</style>\r\n"]}]}