{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\QuarterlyReview\\QuarterlyReview.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\QuarterlyReview\\QuarterlyReview.vue", "mtime": 1752541693862}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgdGFibGVEYXRhIGZyb20gJ0BtaXhpbnMvdGFibGVEYXRhJw0KDQovLyBpbXBvcnQgZnVuYyBmcm9tICd2dWUtZWRpdG9yLWJyaWRnZScNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1F1YXJ0ZXJseVJldmlldycsDQogIGRhdGEgKCkgew0KICAgIHJldHVybiB7DQogICAgICBpZDogJycsDQogICAgICByYWRpbzogMSwNCiAgICAgIHRpbWVBcnI6IFt7DQogICAgICAgIHZhbHVlOiAnMScsDQogICAgICAgIGxhYmVsOiAn56ys5LiA5a2j5bqmJw0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogJzInLA0KICAgICAgICBsYWJlbDogJ+esrOS6jOWto+W6picNCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6ICczJywNCiAgICAgICAgbGFiZWw6ICfnrKzkuInlraPluqYnDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiAnNCcsDQogICAgICAgIGxhYmVsOiAn56ys5Zub5a2j5bqmJw0KICAgICAgfV0sDQogICAgICBvZmZpY2VEYXRhOiBbXSwgLy8g5py65p6E5qCRLA0KICAgICAgc2VhcmNoUGFyYW1zOiB7DQogICAgICAgIGtleXdvcmQ6ICcnLA0KICAgICAgICBxdWFydGVyOiAnJywNCiAgICAgICAgb2ZmaWNlSWQ6ICcnLA0KICAgICAgICBhdWRpdFN0YXR1c1BhcmFtczogJycNCg0KICAgICAgfSwNCiAgICAgIHBhZ2VObzogMSwNCiAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgIHRvdGFsOiAxMCwNCg0KICAgICAgLy8gKioqKioqKioqDQogICAgICB0YWJsZURhdGE6IFtdLA0KICAgICAgY3VycmVudFJvdzogbnVsbCwNCiAgICAgIGV2YWx1YXRlTGV2ZWw6ICcnLA0KICAgICAgc3RhdHVzOiBbXQ0KICAgICAgLy8gY2hvb3NlOiBbXSwNCiAgICAgIC8vIHNlbGVjdE9iajogW10sDQogICAgICAvLyBzZWxlY3REYXRhOiBbXQ0KICAgICAgLy8gYXVkaXRKc29uOiBbXQ0KDQogICAgfQ0KICB9LA0KICBtb3VudGVkICgpIHsNCiAgICB0aGlzLmRpY3Rpb25hcnlQdWJrdnMoKQ0KICAgIHRoaXMuZ2V0UXVhcnRlckxpc3QoKQ0KICAgIHRoaXMudHJlZUxpc3QoKQ0KICAgIC8vIGNvbnNvbGUubG9nKGRvY3VtZW50LmdldEVsZW1lbnRzQnlOYW1lKCdwZXJmb3JtYW5jZVN0YXR1cycpKQ0KICB9LA0KICBpbmplY3Q6IFsndGFiRGVsSnVtcCddLA0KICBtaXhpbnM6IFt0YWJsZURhdGFdLA0KDQogIG1ldGhvZHM6IHsNCg0KICAgIC8qKg0KICAgICAq5a2X5YW4DQogICAgKi8NCiAgICBhc3luYyBkaWN0aW9uYXJ5UHVia3ZzICgpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHRoaXMuJGFwaS5zeXN0ZW1TZXR0aW5ncy5kaWN0aW9uYXJ5UHVia3ZzKHsNCiAgICAgICAgdHlwZXM6ICdldmFsdWF0aW9uX2F1ZGl0X3N0YXR1cyxldmFsdWF0ZV9hdWRpdF9zdGF0dXMnDQogICAgICB9KQ0KICAgICAgdmFyIHsgZGF0YSB9ID0gcmVzDQogICAgICB0aGlzLnN0YXR1cyA9IGRhdGEuZXZhbHVhdGlvbl9hdWRpdF9zdGF0dXMNCiAgICAgIHRoaXMuYXVkaXRTdGF0dXNEYXRhID0gZGF0YS5ldmFsdWF0ZV9hdWRpdF9zdGF0dXMNCiAgICB9LA0KDQogICAgLyoqDQogICAgICAq5py65p6E5qCRDQogICAgKi8NCiAgICBhc3luYyB0cmVlTGlzdCAoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCB0aGlzLiRhcGkuc3lzdGVtU2V0dGluZ3MudHJlZUxpc3Qoe30pDQogICAgICB2YXIgeyBkYXRhIH0gPSByZXMNCiAgICAgIHRoaXMub2ZmaWNlRGF0YSA9IGRhdGENCiAgICB9LA0KDQogICAgcGFzc0NsaWNrICgpIHsNCiAgICAgIC8vIGlmICh0aGlzLmNob29zZS5sZW5ndGgpIHsNCiAgICAgIC8vICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG6YCJ5oup55qE6LWE6K6v55qE54q25oCB5pS55Li6Pz8sIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6Jywgew0KICAgICAgLy8gICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgIC8vICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgIC8vICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIC8vICAgfSkudGhlbigoKSA9PiB7DQogICAgICAvLyAgICAgLy8gY29uc29sZS5sb2codGhpcy50YWJsZURhdGEpDQogICAgICAvLyAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgIC8vICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgIC8vICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgIC8vICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtojmk43kvZwnDQogICAgICAvLyAgICAgfSkNCiAgICAgIC8vICAgfSkNCiAgICAgIC8vIH0gZWxzZSB7DQogICAgICAvLyAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgLy8gICAgIG1lc3NhZ2U6ICfor7foh7PlsJHpgInmi6nkuIDmnaHmlbDmja4nLA0KICAgICAgLy8gICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgLy8gICB9KQ0KICAgICAgLy8gfQ0KICAgICAgdGhpcy5zYXZlSW5mbygpDQogICAgfSwNCiAgICAvLyDkv53lrZgNCiAgICBhc3luYyBzYXZlSW5mbyAoKSB7DQogICAgICB2YXIgYXJyID0gW10NCiAgICAgIHRoaXMudGFibGVEYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIGFyci5wdXNoKHsgaWQ6IGl0ZW0uaWQsIGV2YWx1YXRlTGV2ZWw6IGl0ZW0uZXZhbHVhdGVMZXZlbCB9KQ0KICAgICAgfSkNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHRoaXMuJGFwaS5Bc3Nlc3NtZW50T3JnYW4ucmVxU2F2ZVF1YXJ0ZXIoew0KICAgICAgICBhdWRpdEpzb246IEpTT04uc3RyaW5naWZ5KGFyciksDQogICAgICAgIHF1YXJ0ZXI6IHRoaXMuc2VhcmNoUGFyYW1zLnF1YXJ0ZXINCiAgICAgIH0pDQogICAgICB2YXIgeyBlcnJjb2RlLCBlcnJtc2cgfSA9IHJlcw0KICAgICAgaWYgKGVycmNvZGUgPT09IDIwMCkgew0KICAgICAgICB0aGlzLmdldFF1YXJ0ZXJMaXN0KCkNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogZXJybXNnLA0KICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDor7fmsYLlkI7lj7DmlbDmja4g6I635Y+W5YiX6KGo5L+h5oGvDQogICAgYXN5bmMgZ2V0UXVhcnRlckxpc3QgKCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgdGhpcy4kYXBpLkFzc2Vzc21lbnRPcmdhbi5yZXFRdWFydGVyTGlzdCh7DQogICAgICAgIHBhZ2VObzogdGhpcy5wYWdlTm8sDQogICAgICAgIHBhZ2VTaXplOiB0aGlzLnBhZ2VTaXplLA0KICAgICAgICBrZXl3b3JkOiB0aGlzLnNlYXJjaFBhcmFtcy5rZXl3b3JkLA0KICAgICAgICBxdWFydGVyOiB0aGlzLnNlYXJjaFBhcmFtcy5xdWFydGVyLA0KICAgICAgICAvLyAvLyBzZWRhdGVJZDogdGhpcy5zZWRhdGVJZA0KICAgICAgICAvLyBzZWRhdGVJZDogdGhpcy5zZWxlY3RlZFllYXINCiAgICAgICAgb2ZmaWNlSWQ6IHRoaXMuc2VhcmNoUGFyYW1zLm9mZmljZUlkLCAvLyDpg6jpl6jmn6Xor6INCiAgICAgICAgYXVkaXRTdGF0dXM6IHRoaXMuc2VhcmNoUGFyYW1zLmF1ZGl0U3RhdHVzUGFyYW1zIC8vIOWuoeaguOeKtuaAgQ0KICAgICAgfSkNCiAgICAgIHZhciB7IGRhdGEsIHRvdGFsIH0gPSByZXMNCiAgICAgIHRoaXMudGFibGVEYXRhID0gZGF0YQ0KICAgICAgdGhpcy50b3RhbCA9IHRvdGFsDQogICAgfSwNCiAgICAvLyBwYWdpbmdfYm94IOmHjOmdoueUqOWIsOeahOaWueazlQ0KICAgIGhhbmRsZVNpemVDaGFuZ2UgKCkgew0KICAgICAgdGhpcy5nZXRRdWFydGVyTGlzdCgpDQogICAgfSwNCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlICgpIHsNCiAgICAgIHRoaXMuZ2V0UXVhcnRlckxpc3QoKQ0KICAgIH0sDQogICAgLy8g5LiA5pW05Liq5pCc57Si5qGG5YaFIOafpeivouaMiemSriDnmoTpgLvovpFzZWFyY2gNCiAgICBzZWFyY2ggKCkgew0KICAgICAgdGhpcy5wYWdlTm8gPSAxDQogICAgICB0aGlzLmdldFF1YXJ0ZXJMaXN0KCkNCiAgICB9LA0KICAgIC8vIOS4gOaVtOS4quaQnOe0ouahhuWGhSDph43nva7mjInpkq4g6YC76L6RDQogICAgcmVzZXQgKCkgew0KICAgICAgdGhpcy5zZWFyY2hQYXJhbXMua2V5d29yZCA9ICcnDQogICAgICB0aGlzLnNlYXJjaFBhcmFtcy5xdWFydGVyID0gJycNCiAgICAgIHRoaXMuc2VhcmNoUGFyYW1zLm9mZmljZUlkID0gJycNCiAgICAgIHRoaXMuc2VhcmNoUGFyYW1zLmF1ZGl0U3RhdHVzUGFyYW1zID0gJycNCiAgICAgIHRoaXMuZ2V0UXVhcnRlckxpc3QoKQ0KICAgIH0NCg0KICB9DQp9DQo="}, {"version": 3, "sources": ["QuarterlyReview.vue"], "names": [], "mappings": ";AAuIA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "QuarterlyReview.vue", "sourceRoot": "src/views/AssessmentOrgan/QuarterlyReview", "sourcesContent": ["<template>\r\n  <!--  季度评议  -->\r\n  <div class=\"QuarterlyReview\">\r\n    <!-- 搜索栏search-box -->\r\n    <search-box @search-click=\"search\"\r\n                @reset-click=\"reset\"\r\n                title=\"季度评议筛选\">\r\n      <!--关键字查找框  -->\r\n      <zy-widget label=\"关键字\">\r\n        <el-input placeholder=\"请输入关键词\"\r\n                  v-model=\"searchParams.keyword\"\r\n                  clearable\r\n                  @keyup.enter.native=\"search\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\"></div>\r\n        </el-input>\r\n      </zy-widget>\r\n      <!--季度查询  -->\r\n      <zy-widget label=\"季度查询\">\r\n        <el-select v-model=\"searchParams.quarter\"\r\n                   clearable\r\n                   @keyup.enter.native=\"search\"\r\n                   placeholder=\"请选择季度\">\r\n          <el-option v-for=\"item in timeArr\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.label\"\r\n                     :value=\"item.value\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n\r\n      <!-- 部门查询(用到机构树)  -->\r\n      <zy-widget label=\"部门查询\"\r\n                 v-permissions=\"'auth:QuarterlyReview:department'\">\r\n        <zy-select v-model=\"searchParams.officeId\"\r\n                   clearable\r\n                   @keyup.enter.native=\"search\"\r\n                   placeholder=\"请选择部门\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\">\r\n          </div>\r\n        </zy-select>\r\n      </zy-widget>\r\n\r\n      <!-- 审核状态查询 -->\r\n      <!-- <zy-widget label=\"审核状态查询\">\r\n        <el-select v-model=\"searchParams.auditStatusParams\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择审核状态\">\r\n          <el-option v-for=\"item in auditStatusData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget> -->\r\n\r\n    </search-box>\r\n\r\n    <div class=\"qd-list-wrap \">\r\n      <div class=\"qd-btn-box rightMove\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"passClick()\">保存</el-button>\r\n      </div>\r\n      <!-- *** -->\r\n      <div class=\"tableData\">\r\n        <zy-table>\r\n          <el-table ref=\"singleTable\"\r\n                    slot=\"zytable\"\r\n                    @select=\"selected\"\r\n                    @select-all=\"selectedAll\"\r\n                    row-key=\"id\"\r\n                    :data=\"tableData\"\r\n                    highlight-current-row\r\n                    style=\"width: 100%\">\r\n            <el-table-column label=\"序号\"\r\n                             type=\"index\"\r\n                             fixed=\"left\"\r\n                             width=\"120\">\r\n            </el-table-column>\r\n            <el-table-column property=\"userName\"\r\n                             label=\"考核人\"\r\n                             width=\"250\">\r\n            </el-table-column>\r\n            <el-table-column property=\"officeName\"\r\n                             label=\"所属部门\"\r\n                             width=\"250\">\r\n            </el-table-column>\r\n            <el-table-column property=\"quarter\"\r\n                             label=\"季度\"\r\n                             min-width=\"250\">\r\n            </el-table-column>\r\n\r\n            <!-- TODO:evaluateLevel 评级值，匹配字典evaluation_audit_status -->\r\n            <el-table-column label=\"岗位履职状况\"\r\n                             width=\"350\"\r\n                             fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-radio-group v-model=\"scope.row.evaluateLevel\"\r\n                                v-for=\"(item, index) in status\"\r\n                                :key=\"index\">\r\n                  <el-radio :label=\"item.id\"\r\n                            class=\"mright\">{{item.value}}\r\n                  </el-radio>\r\n                </el-radio-group>\r\n              </template>\r\n\r\n            </el-table-column>\r\n\r\n          </el-table>\r\n        </zy-table>\r\n      </div>\r\n      <!-- 底部页签功能 -->\r\n      <div class=\"paging_box\">\r\n        <el-pagination @size-change=\"handleSizeChange\"\r\n                       @current-change=\"handleCurrentChange\"\r\n                       :current-page.sync=\"pageNo\"\r\n                       :page-sizes=\"[10, 20, 30, 40]\"\r\n                       :page-size.sync=\"pageSize\"\r\n                       background\r\n                       layout=\"total, prev, pager, next, sizes, jumper\"\r\n                       :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport tableData from '@mixins/tableData'\r\n\r\n// import func from 'vue-editor-bridge'\r\nexport default {\r\n  name: 'QuarterlyReview',\r\n  data () {\r\n    return {\r\n      id: '',\r\n      radio: 1,\r\n      timeArr: [{\r\n        value: '1',\r\n        label: '第一季度'\r\n      }, {\r\n        value: '2',\r\n        label: '第二季度'\r\n      }, {\r\n        value: '3',\r\n        label: '第三季度'\r\n      }, {\r\n        value: '4',\r\n        label: '第四季度'\r\n      }],\r\n      officeData: [], // 机构树,\r\n      searchParams: {\r\n        keyword: '',\r\n        quarter: '',\r\n        officeId: '',\r\n        auditStatusParams: ''\r\n\r\n      },\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      total: 10,\r\n\r\n      // *********\r\n      tableData: [],\r\n      currentRow: null,\r\n      evaluateLevel: '',\r\n      status: []\r\n      // choose: [],\r\n      // selectObj: [],\r\n      // selectData: []\r\n      // auditJson: []\r\n\r\n    }\r\n  },\r\n  mounted () {\r\n    this.dictionaryPubkvs()\r\n    this.getQuarterList()\r\n    this.treeList()\r\n    // console.log(document.getElementsByName('performanceStatus'))\r\n  },\r\n  inject: ['tabDelJump'],\r\n  mixins: [tableData],\r\n\r\n  methods: {\r\n\r\n    /**\r\n     *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_audit_status,evaluate_audit_status'\r\n      })\r\n      var { data } = res\r\n      this.status = data.evaluation_audit_status\r\n      this.auditStatusData = data.evaluate_audit_status\r\n    },\r\n\r\n    /**\r\n      *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n\r\n    passClick () {\r\n      // if (this.choose.length) {\r\n      //   this.$confirm('此操作将选择的资讯的状态改为??, 是否继续?', '提示', {\r\n      //     confirmButtonText: '确定',\r\n      //     cancelButtonText: '取消',\r\n      //     type: 'warning'\r\n      //   }).then(() => {\r\n      //     // console.log(this.tableData)\r\n      //   }).catch(() => {\r\n      //     this.$message({\r\n      //       type: 'info',\r\n      //       message: '已取消操作'\r\n      //     })\r\n      //   })\r\n      // } else {\r\n      //   this.$message({\r\n      //     message: '请至少选择一条数据',\r\n      //     type: 'warning'\r\n      //   })\r\n      // }\r\n      this.saveInfo()\r\n    },\r\n    // 保存\r\n    async saveInfo () {\r\n      var arr = []\r\n      this.tableData.forEach(item => {\r\n        arr.push({ id: item.id, evaluateLevel: item.evaluateLevel })\r\n      })\r\n      const res = await this.$api.AssessmentOrgan.reqSaveQuarter({\r\n        auditJson: JSON.stringify(arr),\r\n        quarter: this.searchParams.quarter\r\n      })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getQuarterList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 请求后台数据 获取列表信息\r\n    async getQuarterList () {\r\n      const res = await this.$api.AssessmentOrgan.reqQuarterList({\r\n        pageNo: this.pageNo,\r\n        pageSize: this.pageSize,\r\n        keyword: this.searchParams.keyword,\r\n        quarter: this.searchParams.quarter,\r\n        // // sedateId: this.sedateId\r\n        // sedateId: this.selectedYear\r\n        officeId: this.searchParams.officeId, // 部门查询\r\n        auditStatus: this.searchParams.auditStatusParams // 审核状态\r\n      })\r\n      var { data, total } = res\r\n      this.tableData = data\r\n      this.total = total\r\n    },\r\n    // paging_box 里面用到的方法\r\n    handleSizeChange () {\r\n      this.getQuarterList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getQuarterList()\r\n    },\r\n    // 一整个搜索框内 查询按钮 的逻辑search\r\n    search () {\r\n      this.pageNo = 1\r\n      this.getQuarterList()\r\n    },\r\n    // 一整个搜索框内 重置按钮 逻辑\r\n    reset () {\r\n      this.searchParams.keyword = ''\r\n      this.searchParams.quarter = ''\r\n      this.searchParams.officeId = ''\r\n      this.searchParams.auditStatusParams = ''\r\n      this.getQuarterList()\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.QuarterlyReview {\r\n  height: 100%;\r\n  width: 100%;\r\n\r\n  .mright {\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .tableData {\r\n    .el-scrollbar__wrap .el-scrollbar__view th {\r\n      background: #f5f7fb;\r\n    }\r\n    height: calc(100% - 106px);\r\n  }\r\n  .button-box {\r\n    background: #fff;\r\n    margin-top: 20px;\r\n  }\r\n  .button-box .el-button {\r\n    background: #fff;\r\n  }\r\n  .el-button--danger.is-plain:hover,\r\n  .el-button--danger.is-plain:focus {\r\n    // color: #f56c6c;\r\n    background: #f56c6c;\r\n    color: #fff;\r\n  }\r\n  .el-button--success.is-plain:hover,\r\n  .el-button--success.is-plain:focus {\r\n    background: #67c23a;\r\n    color: #fff;\r\n  }\r\n  .delBtn {\r\n    color: #f56c6c;\r\n  }\r\n  .paging_box {\r\n    background: #fff;\r\n  }\r\n  .scoreEdit {\r\n    width: 700px;\r\n    height: 100%;\r\n    padding: 24px;\r\n    .form-button {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n  .search-box {\r\n    //title文字样式\r\n    .search-title {\r\n      width: 115px;\r\n      height: 16px;\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-left: 32px;\r\n    }\r\n  }\r\n  // 利用新特性弹性布局flex,设置justify-content:flex-end; 项目位于容器的结尾\r\n  // 优点:按钮会固定位置随着页面缩放,不跑偏\r\n  .rightMove {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    height: 50px;\r\n    margin-top: -8px;\r\n    margin-bottom: 2px;\r\n  }\r\n}\r\n</style>\r\n"]}]}