{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\qd-upload-img\\qd-upload-img.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\qd-upload-img\\qd-upload-img.vue", "mtime": 1752541693469}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["qd-upload-img.vue"], "names": [], "mappings": ";AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "qd-upload-img.vue", "sourceRoot": "src/components/qd-upload-img", "sourcesContent": ["<template>\r\n  <el-upload\r\n    class=\"avatar-uploader\"\r\n    action=\"/#\"\r\n    accept=\".jpg,.jpeg,.png,.PNG,.JPG\"\r\n    :show-file-list=\"false\"\r\n    :http-request=\"customUpload\"\r\n    :before-upload=\"beforeAvatarUpload\"\r\n  >\r\n    <img v-if=\"photoList.length\" :src=\"photoList[0].filePath\" class=\"avatar\" />\r\n    <img v-else-if=\"files.length > 0\" :src=\"files[0].filePath\" class=\"avatar\" />\r\n    <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\r\n    <div slot=\"tip\" class=\"el-upload__tip\">{{ tip }}</div>\r\n  </el-upload>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'qd-upload-img',\r\n  props: {\r\n    value: Array,\r\n    module: {\r\n      type: String,\r\n      default: 'splashImg'\r\n    },\r\n    tip: {\r\n      type: String,\r\n      default: '只能上传jpg/png等图片格式的文件，且不超过10mb'\r\n    },\r\n    size: {\r\n      type: Array,\r\n      default: () => { return [] }\r\n    },\r\n    photoList: {\r\n      type: Array,\r\n      default: () => {\r\n        return []\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value', // 这个字段，是指父组件设置 v-model 时，将变量值传给子组件的 msg\r\n    event: 'file'// 这个字段，是指父组件监听 parent-event 事件\r\n  },\r\n  data () {\r\n    return {\r\n      files: []\r\n    }\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.files = val\r\n      } else {\r\n        this.files = []\r\n      }\r\n    },\r\n    files (val) {\r\n      this.$emit('file', val)\r\n    }\r\n  },\r\n  methods: {\r\n    // 校验文件类型和文件大小\r\n    beforeAvatarUpload (file) {\r\n      // const isJPG = file.type === 'image/jpeg'\r\n      const isLt2M = file.size / 1024 / 1024 < 10\r\n      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n      if (testmsg !== 'png' && testmsg !== 'jpg' && testmsg !== 'PNG' && testmsg !== 'jpeg' && testmsg !== 'JPG' && testmsg !== 'gif') {\r\n        this.$message.error('图片文件格式暂时不支持!')\r\n        return false\r\n      }\r\n      if (!isLt2M) {\r\n        this.$message.error('上传图片大小不能超过 10MB!')\r\n        return false\r\n      }\r\n      return isLt2M && testmsg\r\n    },\r\n    // 上传逻辑\r\n    async customUpload (file) {\r\n      const siteId = JSON.parse(sessionStorage.getItem('user' + this.$logo())).areaId\r\n      const formData = new FormData()\r\n      formData.append('attachment', file.file)\r\n      formData.append('module', this.module)\r\n      formData.append('siteId', siteId)\r\n      this.$api.microAdvice.uploadFile(formData).then(res => {\r\n        const { errcode, data } = res\r\n        if (errcode === 200) {\r\n          this.files = data\r\n        }\r\n      })\r\n      this.$emit('initPhoto')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.avatar-uploader {\r\n  .el-upload {\r\n    border: 1px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n  }\r\n  .el-upload:hover {\r\n    border-color: #409eff;\r\n  }\r\n  .avatar-uploader-icon {\r\n    font-size: 28px;\r\n    color: #8c939d;\r\n    width: 148px;\r\n    height: 148px;\r\n    line-height: 148px;\r\n    text-align: center;\r\n  }\r\n  .avatar {\r\n    width: 148px;\r\n    height: 148px;\r\n    display: block;\r\n  }\r\n}\r\n</style>\r\n"]}]}