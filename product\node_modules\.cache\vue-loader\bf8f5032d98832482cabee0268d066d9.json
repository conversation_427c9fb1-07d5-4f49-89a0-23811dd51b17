{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\DoubleQuote\\DoubleDetails.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\DoubleQuote\\DoubleDetails.vue", "mtime": 1752541693814}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DoubleDetails.vue"], "names": [], "mappings": ";AAoEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "DoubleDetails.vue", "sourceRoot": "src/views/AssessmentOrgan/DoubleQuote", "sourcesContent": ["<template>\r\n  <!-- 双招双引详情 -->\r\n  <div class=\"detailComment scrollBar \">\r\n    <div class=\"officeDetial-title\"> {{form.title}} </div>\r\n\r\n    <div class=\"relevantInformation\">\r\n\r\n      <div class=\"officeDetial-org\">\r\n        <div class=\"org-item\"> 类型 : <span> {{doubleClass}}</span> </div>\r\n        <div class=\"org-item\"> 所属个人: <span> {{form.publishUserName}}</span> </div>\r\n      </div>\r\n      <div class=\"officeDetial-org\">\r\n        <div class=\"org-item\"> 部门： <span> {{form.officeName}}</span> </div>\r\n        <div class=\"org-item\"> 时间： <span>{{ $format(form.publishTime)  }}</span></div>\r\n\r\n      </div>\r\n      <div>\r\n        <el-button type=\"primary\"\r\n                   v-if=\"this.approve==='true'\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n\r\n        <el-button type=\"danger\"\r\n                   v-if=\"this.noApprove==='true'\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n    <!-- 内容 -->\r\n    <div class=\"contBox\">\r\n      <!-- <div v-if=\"manuscriptFlag\"\r\n           class=\"content\"\r\n           v-html=\"manuscriptData.content\"> </div> -->\r\n      <div class=\"content\"\r\n           v-if=\"form.content\"\r\n           v-html=\"form.content\"></div>\r\n    </div>\r\n\r\n    <!-- *****附件***** -->\r\n    <div class=\"fileBox\"\r\n         v-if=\"form.attachmentInfo\">\r\n      <div class=\"file_title\"\r\n           v-if=\"form.attachmentInfo.length!==0\"> 资讯附件 </div>\r\n      <div class=\"fileListt\">\r\n\r\n        <div class=\"file_item\"\r\n             v-for=\"(item,index) in form.attachmentInfo\"\r\n             :key=\"index\">\r\n\r\n          <div class=\"file_name\"> {{item.oldName}} </div>\r\n\r\n          <div class=\"file_load\">\r\n            <div class=\"load_text\"><a :href=\"item.fullPath\"\r\n                 target=\"_blank\">预览</a>\r\n            </div>\r\n            <div class=\"shu\"></div>\r\n            <div class=\"load_text\"\r\n                 @click=\"download(item)\">下载</div>\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n// 套用模板:src\\views\\socialpublic-qd\\detailComment\\detailComment.vue\r\n// 修改了import引入的三个文件的相对路径\r\n// import handinfo from '../../socialpublic-qd/socialpulicManage/Allsocialpublic/handinfo/handinfo'\r\n// import similarity from '../../socialpublic-qd/info/submitSocialpublic/similarity/similarity'\r\n// import Typos from '../../socialpublic-qd/info/submitSocialpublic/Typos/Typos'\r\nimport { mapActions, mapState } from 'vuex'\r\n\r\nexport default {\r\n  name: 'DoubleDetails',\r\n  // components: { handinfo, similarity, Typos },\r\n  components: {},\r\n\r\n  data () {\r\n    return {\r\n      approve: this.$route.query.approve,\r\n      noApprove: this.$route.query.noApprove,\r\n      doubleTypeData: [],\r\n      ids: [],\r\n      TyposShow: false,\r\n      // SimilarityShow: false,\r\n      form: {},\r\n      fileList: [],\r\n      manuscriptData: {},\r\n      all: this.$route.query.all || false,\r\n      rowId: this.$route.query.rowId,\r\n      manuscriptFlag: false,\r\n      isexcellent: this.$route.query.isexcellent || false,\r\n      helper: this.$route.query.helper || false,\r\n      similar: '0%',\r\n      suspend: 0,\r\n      wrong: 0,\r\n      show: true,\r\n      clickTime: ''\r\n\r\n    }\r\n  },\r\n  inject: ['openoffice', 'openPdf'],\r\n  created () {\r\n    this.getDoubleQuoteDetails()\r\n  },\r\n  methods: {\r\n    passClick (auditStatus) {\r\n      this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.getCheckDouble(this.$route.query.rowId, auditStatus)\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消操作'\r\n        })\r\n      })\r\n    },\r\n    // 审核 (加分项)\r\n    async getCheckDouble (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckDouble({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n\r\n    /**\r\n        *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_same_double'\r\n      })\r\n      var { data } = res\r\n      this.doubleTypeData = data.evaluation_same_double\r\n    },\r\n\r\n    // **************\r\n    ...mapActions(['getDoubleQuoteDetails']),\r\n    // ******************\r\n    showHelper () {\r\n      if (new Date().getTime() - this.clickTime > 1000) {\r\n        this.clickTime = new Date().getTime()\r\n        this.show = !this.show\r\n      }\r\n    },\r\n\r\n    async getcorrector () {\r\n      const res = await this.$api.publicOpinionNew.corrector({\r\n        title: '',\r\n        content: this.form.content,\r\n        sessions: '',\r\n        times: ''\r\n      })\r\n      var data = JSON.parse(res.data)\r\n\r\n      this.suspend = data.detail.length\r\n      this.wrong = data.suspend_detail.length\r\n    },\r\n    // 预览(另一种方法替代)\r\n    // priew (data) {\r\n    //   const arr = ['doc', 'docx', 'xlsx']\r\n    //   if (arr.includes(data.extension)) {\r\n    //     this.openoffice(data.openUrl)\r\n    //   }\r\n    // },\r\n\r\n    // 后台获取详情数据(部分还需修改)\r\n    async getDoubleQuoteDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqDoubleQuoteDetails(this.$route.query.rowId)\r\n      var { data } = res\r\n\r\n      this.form = data\r\n    },\r\n    // 通用的附件下载方法download 只需要改对应的附件id和名字\r\n    download (item) {\r\n      this.$api.proposal.downloadFile({ id: item.id }, item.oldName)\r\n    },\r\n    callback (data) {\r\n      this.TyposShow = false\r\n      console.log(data)\r\n    }\r\n  },\r\n  mounted () {\r\n    this.dictionaryPubkvs()\r\n  },\r\n  computed: {\r\n    // 测试: 通过映射函数 获取title信息\r\n    ...mapState(['title']),\r\n    doubleClass () {\r\n      var double1 = ''\r\n      this.doubleTypeData.forEach(item => {\r\n        if (item.id === this.form.doubleType) {\r\n          double1 = item.value\r\n        }\r\n      })\r\n      return double1\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" >\r\n.detailComment {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding-top: 33px;\r\n  background: #fff;\r\n  .officeDetial-title {\r\n    font-size: 26px;\r\n    font-size: 24px;\r\n    font-family: PingFang SC;\r\n    font-weight: 800;\r\n    color: #333333;\r\n    line-height: 36px;\r\n    text-align: center;\r\n    margin: 29px;\r\n  }\r\n  .relevantInformation {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    .officeDetial-org {\r\n      font-size: $textSize14;\r\n      font-family: PingFang SC;\r\n      font-weight: 400;\r\n      line-height: 24px;\r\n      // display: flex;\r\n      // justify-content: space-around;\r\n      // padding-left: 40px;\r\n      .org-item {\r\n        color: #999999;\r\n        // margin-right: 140px;\r\n        // min-width: 300px;\r\n        span {\r\n          margin-left: 38px;\r\n        }\r\n      }\r\n      // .org-item + .org-item {\r\n      //     margin-left: 140px;\r\n      // }\r\n    }\r\n  }\r\n\r\n  .contBox {\r\n    margin-top: 20px;\r\n    border-top: 2px solid #ebebeb;\r\n    display: flex;\r\n\r\n    .content {\r\n      flex: 1;\r\n      padding: 30px 40px;\r\n      line-height: 30px;\r\n      min-height: 500px;\r\n    }\r\n    .content + .content {\r\n      border-left: 1px solid #ebebeb;\r\n    }\r\n  }\r\n\r\n  .similarityImg {\r\n    padding-left: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    img {\r\n      width: 80px;\r\n      cursor: pointer;\r\n    }\r\n    .analysisReslut {\r\n      height: 60px;\r\n      line-height: 60px;\r\n      padding: 0 44px;\r\n      // background: #f5f5fb;\r\n      background-image: url(\"../../../assets/qdimg/round.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      border-radius: 20px;\r\n      span {\r\n        font-size: 26px;\r\n        font-family: PangMenZhengDao;\r\n        font-weight: 700;\r\n        color: #007bff;\r\n        line-height: 36px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n  .manuscript {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-right: 40px;\r\n    padding-bottom: 20px;\r\n    .yuangoa {\r\n      flex: 1;\r\n      span {\r\n        font-size: 26px;\r\n        font-family: PingFang SC;\r\n        font-weight: 500;\r\n        color: #999999;\r\n        line-height: 36px;\r\n        margin-left: 40px;\r\n      }\r\n      span + span {\r\n        margin-left: 50%;\r\n      }\r\n    }\r\n  }\r\n  .fileBox {\r\n    width: 100%;\r\n    background: #ffffff;\r\n    border-radius: 10px;\r\n    padding: 30px;\r\n    .file_title {\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-bottom: 23px;\r\n    }\r\n    .fileListt {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      justify-content: space-between;\r\n      .file_item {\r\n        width: 48%;\r\n        background: #f5f5fb;\r\n        flex-shrink: 0;\r\n        height: 60px;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0 18px;\r\n        margin-bottom: 10px;\r\n        .file_type {\r\n          width: 32px;\r\n          height: 32px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .file_name {\r\n          margin-left: 12px;\r\n          flex: 1;\r\n          cursor: pointer;\r\n        }\r\n        .file_load {\r\n          display: flex;\r\n          align-items: center;\r\n          .load_text {\r\n            font-size: $textSize16;\r\n            font-family: PingFang SC;\r\n            font-weight: 500;\r\n            color: #007bff;\r\n            line-height: 36px;\r\n            cursor: pointer;\r\n          }\r\n          .shu {\r\n            width: 2px;\r\n            height: 22px;\r\n            background: #4f96fe;\r\n            margin: 0 12px;\r\n          }\r\n          .del {\r\n            width: 24px;\r\n            height: 24px;\r\n            margin-left: 23px;\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .hanldtype {\r\n    display: flex;\r\n    padding: 20px 30px;\r\n    justify-content: space-between;\r\n    > div {\r\n      font-weight: 700;\r\n    }\r\n  }\r\n  .handinfo {\r\n    width: 100%;\r\n    .hanldClounm .hanldCont .el-checkbox {\r\n      width: 25%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}