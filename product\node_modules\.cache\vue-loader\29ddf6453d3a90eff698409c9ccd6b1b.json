{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-upload\\zy-upload.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-upload\\zy-upload.vue", "mtime": 1752541693624}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["zy-upload.vue"], "names": [], "mappings": ";AAyCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-upload.vue", "sourceRoot": "src/components/zy-upload", "sourcesContent": ["<template>\r\n  <div class=\"zy-upload\">\r\n    <el-upload action=\"#\"\r\n               :class=\"{ uploadSty:showBtn,disUploadSty:!showBtn}\"\r\n               :multiple=\"multiple\"\r\n               :limit=\"limit\"\r\n               list-type=\"picture-card\"\r\n               accept=\".jpg,.jpeg,.png,.PNG,.JPG\"\r\n               :on-change=\"handleChange\"\r\n               :http-request=\"customUpload\"\r\n               :before-upload=\"beforeAvatarUpload\"\r\n               :file-list=\"filelist\">\r\n      <i slot=\"default\"\r\n         class=\"el-icon-plus\"></i>\r\n      <div slot=\"file\"\r\n           slot-scope=\"{file}\">\r\n        <img class=\"el-upload-list__item-thumbnail\"\r\n             :src=\"file.url\"\r\n             alt=\"\">\r\n        <span class=\"el-upload-list__item-actions\">\r\n          <span class=\"el-upload-list__item-preview\"\r\n                @click=\"handlePictureCardPreview(file)\">\r\n            <i class=\"el-icon-zoom-in\"></i>\r\n          </span>\r\n          <span class=\"el-upload-list__item-delete\"\r\n                @click=\"handleRemove(file)\">\r\n            <i class=\"el-icon-delete\"></i>\r\n          </span>\r\n        </span>\r\n      </div>\r\n    </el-upload>\r\n    <el-dialog :visible.sync=\"dialogVisible\"\r\n               append-to-body>\r\n      <img width=\"100%\"\r\n           :src=\"dialogImageUrl\"\r\n           alt=\"\">\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'zyUpload',\r\n  props: {\r\n    value: [Array],\r\n    limit: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    multiple: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    module: {\r\n      type: String,\r\n      default: 'minisuggestion'\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value', // 这个字段，是指父组件设置 v-model 时，将变量值传给子组件的 msg\r\n    event: 'file'// 这个字段，是指父组件监听 parent-event 事件\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.filelist = val\r\n        this.showBtn = this.filelist.length < this.limit\r\n      } else {\r\n        this.filelist = []\r\n      }\r\n    },\r\n    filelist (val) {\r\n      this.$emit('file', val)\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      dialogVisible: false,\r\n      dialogImageUrl: '',\r\n      filelist: this.value,\r\n      showBtn: true\r\n    }\r\n  },\r\n  methods: {\r\n    // 图片预览\r\n    handlePictureCardPreview (file) {\r\n      this.dialogImageUrl = file.url\r\n      this.dialogVisible = true\r\n    },\r\n    // 移除图片\r\n    handleRemove (file) {\r\n      // 存在浅拷贝 所以不需要再赋值\r\n      const arr = this.filelist\r\n      for (let i = 0; i < arr.length; i++) {\r\n        if (arr[i].uid === file.uid) {\r\n          arr.splice(i, 1)\r\n        }\r\n      }\r\n      this.showBtn = this.filelist.length < this.limit\r\n    },\r\n    // 校验文件类型和文件大小\r\n    beforeAvatarUpload (file) {\r\n      // const isJPG = file.type === 'image/jpeg'\r\n      const isLt2M = file.size / 1024 / 1024 < 10\r\n      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n      if (testmsg !== 'png' && testmsg !== 'jpg' && testmsg !== 'PNG' && testmsg !== 'jpeg' && testmsg !== 'JPG') {\r\n        this.$message.error('图片文件格式暂时不支持!')\r\n        return false\r\n      }\r\n      if (!isLt2M) {\r\n        this.$message.error('上传图片大小不能超过 10MB!')\r\n        return false\r\n      }\r\n      return isLt2M && testmsg\r\n    },\r\n    // 上传逻辑\r\n    async customUpload (file) {\r\n      const siteId = JSON.parse(sessionStorage.getItem('user' + this.$logo())).areaId\r\n      const formData = new FormData()\r\n      formData.append('attachment', file.file)\r\n      formData.append('module', this.module)\r\n      formData.append('siteId', siteId)\r\n      this.$api.microAdvice.uploadFile(formData).then(res => {\r\n        const { errcode, data } = res\r\n        if (errcode === 200) {\r\n          const fileData = {\r\n            name: data[0].fileName,\r\n            size: data[0].fileSize,\r\n            type: data[0].fileType,\r\n            url: data[0].filePath,\r\n            id: data[0].id,\r\n            uid: data[0].uid\r\n          }\r\n          this.filelist.push(fileData)\r\n        }\r\n      })\r\n    },\r\n    handleChange (file, filelist) {\r\n      if (file.status !== 'ready') {\r\n        this.showBtn = filelist.length < this.limit\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"./zy-upload.scss\";\r\n</style>\r\n"]}]}