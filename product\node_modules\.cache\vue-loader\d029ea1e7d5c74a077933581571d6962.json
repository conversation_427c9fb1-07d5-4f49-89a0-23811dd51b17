{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-select\\zy-select.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-select\\zy-select.vue", "mtime": 1752541693557}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["zy-select.vue"], "names": [], "mappings": ";AAgDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-select.vue", "sourceRoot": "src/components/zy-select", "sourcesContent": ["<template>\r\n  <div class=\"zy-tree-select\"\r\n       ref=\"zyTreeSelect\">\r\n    <el-popover :width=\"w\"\r\n                placement=\"bottom\"\r\n                trigger=\"manual\"\r\n                v-model=\"visible\"\r\n                popper-class=\"zy-tree-select-popover\">\r\n      <template slot=\"reference\">\r\n        <el-input :placeholder=\"inputvalue\"\r\n                  :readonly=\"!filterable\"\r\n                  @mouseover.native=\"mouseover\"\r\n                  @mouseleave.native=\"mouseleave\"\r\n                  :disabled=\"disabled\"\r\n                  v-model=\"input\"\r\n                  @focus=\"focus\"\r\n                  @blur=\"blur\"\r\n                  ref=\"input\">\r\n          <template slot=\"suffix\">\r\n            <i v-if=\"show\"\r\n               :class=\"['zy-tree-select-icon','el-icon-arrow-down',visible?'el-icon-arrow-down-a':'']\"></i>\r\n            <i v-if=\"!show\"\r\n               @click=\"empty\"\r\n               class=\"el-icon-circle-close\"></i>\r\n          </template>\r\n        </el-input>\r\n      </template>\r\n      <el-scrollbar class=\"zy-tree-select-body\"\r\n                    :style=\"{height: h}\">\r\n        <div class=\"select-body\"\r\n             ref=\"selectBody\">\r\n          <el-tree ref=\"tree\"\r\n                   :data=\"data\"\r\n                   :props=\"props\"\r\n                   highlight-current\r\n                   :node-key=\"nodeKey\"\r\n                   @node-expand=\"switchClick\"\r\n                   @node-collapse=\"switchClick\"\r\n                   @node-click=\"handleNodeClick\"\r\n                   :filter-node-method=\"filterNode\"\r\n                   :expand-on-click-node=\"false\"></el-tree>\r\n        </div>\r\n      </el-scrollbar>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nconst delay = (function () {\r\n  let timer = 0\r\n  return function (callback, ms) {\r\n    clearTimeout(timer)\r\n    timer = setTimeout(callback, ms)\r\n  }\r\n})()\r\nexport default {\r\n  name: 'zySelect',\r\n  data () {\r\n    return {\r\n      h: 'auto',\r\n      w: 0,\r\n      visible: false,\r\n      i: 0,\r\n      show: true,\r\n      input: '',\r\n      inputvalue: ''\r\n    }\r\n  },\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 是否开启关键字搜索\r\n    filterable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '请选择内容'\r\n    },\r\n    // 是否可以清空\r\n    clearable: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  watch: {\r\n    visible (val) {\r\n      if (val) {\r\n        this.w = this.$refs.zyTreeSelect.offsetWidth\r\n        this.$nextTick(() => {\r\n          const that = this\r\n          erd.listenTo(this.$refs.selectBody, (element) => {\r\n            that.$nextTick(() => {\r\n              if (element.offsetHeight > 260) {\r\n                that.h = '260px'\r\n              } else {\r\n                that.h = element.offsetHeight + 'px'\r\n                // that.h = 'auto'\r\n              }\r\n            })\r\n          })\r\n        })\r\n      } else {\r\n        erd.uninstall(this.$refs.selectBody)\r\n      }\r\n    },\r\n    value (val) {\r\n      if (val) {\r\n        this.selectedMethods(this.data)\r\n      } else {\r\n        this.input = ''\r\n        this.inputvalue = this.placeholder\r\n        this.$nextTick(function () {\r\n          this.$refs.tree.setCurrentKey()\r\n        })\r\n      }\r\n    },\r\n    data () {\r\n      this.selectedMethods(this.data)\r\n    },\r\n    input (val) {\r\n      this.$refs.tree.filter(val)\r\n    }\r\n  },\r\n  mounted () {\r\n    this.selectedMethods(this.data)\r\n    this.inputvalue = this.placeholder\r\n  },\r\n  methods: {\r\n    focus () {\r\n      delay(() => {\r\n        this.visible = true\r\n        if (this.filterable && this.value && this.i === 0) {\r\n          this.inputvalue = this.input\r\n          this.input = ''\r\n        }\r\n      }, 200)\r\n    },\r\n    blur () {\r\n      delay(() => {\r\n        this.visible = false\r\n        if (this.filterable && this.i !== 1) {\r\n          if (this.value && this.inputvalue !== this.placeholder) {\r\n            this.input = this.inputvalue\r\n          }\r\n          this.inputvalue = this.placeholder\r\n        }\r\n        this.i = 0\r\n      }, 200)\r\n    },\r\n    switchClick () {\r\n      delay(() => {\r\n        this.visible = true\r\n      }, 200)\r\n      if (this.filterable) {\r\n        this.i = 2\r\n      }\r\n      this.$refs.input.focus()\r\n    },\r\n    filterNode (value, data) {\r\n      if (!this.filterable) return true\r\n      if (!value) return true\r\n      return data[this.props.label].indexOf(value) !== -1\r\n    },\r\n    handleNodeClick (data) {\r\n      if (this.filterable) {\r\n        this.i = 1\r\n      }\r\n      this.$emit('input', data[this.nodeKey])\r\n    },\r\n    // 首次进来默认选中\r\n    selectedMethods (data) {\r\n      data.forEach(item => {\r\n        if (item[this.nodeKey] === this.value) {\r\n          this.input = item[this.props.label]\r\n          this.$emit('select', item)\r\n          this.$nextTick(function () {\r\n            this.$refs.tree.setCurrentKey(item[this.nodeKey])\r\n          })\r\n        }\r\n        if (item.children && item.children.length > 0) {\r\n          this.selectedMethods(item.children)\r\n        }\r\n      })\r\n    },\r\n    empty () {\r\n      this.i = 1\r\n      this.$emit('input', '')\r\n    },\r\n    mouseover () {\r\n      if (this.value && this.clearable) {\r\n        this.show = false\r\n      }\r\n    },\r\n    mouseleave () {\r\n      console.log(1)\r\n      this.show = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"./zy-tree-select.scss\";\r\n</style>\r\n"]}]}