<template>
  <div :id="chartId" class="word-cloud-container"></div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-wordcloud'
export default {
  name: 'WordCloud',
  props: {
    chartId: {
      type: String,
      default: 'wordCloud'
    },
    words: {
      type: Array,
      required: true,
      default: () => []
      // 期望格式: [{ text: '经济建设', weight: 10 }, { text: '人才培养', weight: 8 }, ...]
    }
  },
  data () {
    return {
      chart: null,
      // 预定义颜色数组，模拟图片中的颜色效果
      colors: [
        '#4FC3F7', // 浅蓝色
        '#26C6DA', // 青色
        '#66BB6A', // 绿色
        '#FFA726', // 橙色
        '#FF7043', // 橙红色
        '#AB47BC', // 紫色
        '#5C6BC0', // 蓝紫色
        '#42A5F5', // 蓝色
        '#FFCA28', // 黄色
        '#4CAF50', // 绿色
        '#EF5350', // 红色
        '#A1E2FF', // 浅蓝色
        '#00BCD4', // 青蓝色
        '#FF9800', // 深橙色
        '#9C27B0' // 深紫色
      ]
    }
  },
  mounted () {
    this.initChart()
  },
  beforeDestroy () {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart () {
      const chartContainer = document.getElementById(this.chartId)
      if (!chartContainer) return
      this.chart = echarts.init(chartContainer)
      this.$nextTick(() => {
        this.updateChart()
      })
      // 监听窗口大小变化
      window.addEventListener('resize', this.resizeChart)
      // 添加点击事件
      this.chart.on('click', (params) => {
        this.$emit('word-click', {
          text: params.data[3], // 散点图数据格式 [x, y, value, name]
          weight: params.data[2]
        })
      })
    },
    updateChart () {
      if (!this.chart) return
      // 按照你提供的样式配置词云
      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          show: true,
          position: 'top',
          textStyle: {
            fontSize: 16
          }
        },
        series: [{
          type: 'wordCloud',
          // 网格大小，各项之间间距
          gridSize: 20,
          sizeRange: [20, 40],
          size: 0.6,
          rotationRange: [0, 0],
          drawOutOfBound: false,
          // 位置相关设置
          left: 'center',
          top: 'center',
          right: null,
          bottom: null,
          width: '100%',
          height: '100%',
          textStyle: {
            normal: {
              color: function () {
                return 'rgb(' + [
                  Math.round(Math.random() * 200 + 55),
                  Math.round(Math.random() * 200 + 55),
                  Math.round(Math.random() * 200 + 55)
                ].join(',') + ')'
              }
            },
            emphasis: {
              shadowBlur: 10,
              shadowColor: '#2ac'
            }
          },
          data: this.words
        }]
      }
      this.chart.setOption(option, true)
    },
    resizeChart () {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.word-cloud-container {
  width: 100%;
  height: 100%;
}
</style>
