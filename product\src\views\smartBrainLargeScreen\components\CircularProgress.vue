<template>
  <div class="circular-progress" :id="id"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'CircularProgress',
  props: {
    id: {
      type: String,
      required: true
    },
    percentage: {
      type: Number,
      required: true,
      default: 0
    },
    label: {
      type: String,
      required: true,
      default: ''
    },
    color: {
      type: String,
      default: '#00d4ff'
    }
  },
  data () {
    return {
      chart: null
    }
  },
  mounted () {
    this.initChart()
  },
  beforeDestroy () {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.resizeChart)
  },
  watch: {
    percentage: {
      handler () {
        this.updateChart()
      }
    }
  },
  methods: {
    initChart () {
      const chartContainer = document.getElementById(this.id)
      if (!chartContainer) return
      this.chart = echarts.init(chartContainer)
      this.updateChart()
      window.addEventListener('resize', this.resizeChart)
    },

    updateChart () {
      if (!this.chart) return
      const option = {
        graphic: [
          // 百分比数字 - 最高优先级
          {
            type: 'text',
            left: 'center',
            top: '30%',
            z: 1000,
            style: {
              text: this.percentage + '%',
              fontSize: 32,
              fontWeight: 'bold',
              fill: '#fff',
              textShadowColor: 'rgba(0, 0, 0, 1)',
              textShadowBlur: 15,
              textShadowOffsetX: 3,
              textShadowOffsetY: 3
            }
          },
          // 标签文字 - 最高优先级
          {
            type: 'text',
            left: 'center',
            top: '58%',
            z: 1000,
            style: {
              text: this.label,
              fontSize: 16,
              fontWeight: '500',
              fill: '#fff',
              textShadowColor: 'rgba(0, 0, 0, 1)',
              textShadowBlur: 12,
              textShadowOffsetX: 2,
              textShadowOffsetY: 2
            }
          }
        ],
        series: [
          {
            type: 'pie',
            radius: ['0%', '75%'],
            center: ['50%', '50%'],
            startAngle: 90,
            z: 100,
            data: [
              {
                value: this.percentage,
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [
                      { offset: 0, color: this.color },
                      { offset: 0.3, color: this.color + 'E6' },
                      { offset: 0.7, color: this.color + 'B3' },
                      { offset: 1, color: this.color + '80' }
                    ]
                  },
                  shadowBlur: 25,
                  shadowColor: this.color + '80',
                  shadowOffsetX: 0,
                  shadowOffsetY: 0
                }
              },
              {
                value: 100 - this.percentage,
                itemStyle: {
                  color: {
                    type: 'radial',
                    x: 0.5,
                    y: 0.5,
                    r: 0.8,
                    colorStops: [
                      { offset: 0, color: 'rgba(255, 255, 255, 0.0)' },
                      { offset: 0.7, color: 'rgba(255, 255, 255, 0.0)' },
                      { offset: 1, color: 'rgba(255, 255, 255, 0.0)' }
                    ]
                  }
                }
              }
            ],
            label: { show: false },
            labelLine: { show: false },
            animation: true,
            animationDuration: 2000,
            animationEasing: 'cubicOut'
          }
        ]
      }
      this.chart.setOption(option)
    },

    resizeChart () {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.circular-progress {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 50; // 确保图表容器在背景图上方

  // 添加整体的发光效果
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    height: 90%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
    z-index: 2; // 发光效果在背景图上方，但在图表下方
  }
}
</style>
