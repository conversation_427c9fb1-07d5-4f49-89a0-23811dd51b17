{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\zy\\xm\\pc\\qdzx\\product\\src\\mixins\\general.js", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\mixins\\general.js", "mtime": 1752541693648}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzeXN0ZW1TaG93OiBbXSwKICAgICAgaGVscFNob3c6IFtdLAogICAgICBtZW51SWQ6ICcnLAogICAgICBtZW51RGF0YTogSlNPTi5wYXJzZShzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdtZW51Q2hpbGQnKSksCiAgICAgIGNydW1ic0lkOiAnJywKICAgICAgY3J1bWJzRGF0YTogW10sCiAgICAgIHBhdGhQYXJhbXM6IHt9LAogICAgICBpbmNsdWRlczogW10KICAgIH07CiAgfSwKCiAgd2F0Y2g6IHsKICAgIGNydW1ic0RhdGEodmFsKSB7CiAgICAgIHZhciBhcnIgPSBbXTsKICAgICAgdmFsLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgYXJyLnB1c2goaXRlbS50by5zdWJzdHJpbmcoMSkpOwogICAgICB9KTsKICAgICAgdGhpcy5pbmNsdWRlcyA9IGFycjsKICAgIH0KCiAgfSwKCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuc3lzdGVtRGF0YSgpOwogICAgdGhpcy5oZWxwRGF0YSgpOwogICAgdGhpcy5tZW51U2VsZWN0ZWQoKTsKICB9LAoKICBtb3VudGVkKCkgewogICAgaWYgKHdpbmRvdy5oaXN0b3J5ICYmIHdpbmRvdy5oaXN0b3J5LnB1c2hTdGF0ZSkgewogICAgICAvLyDlkJHljoblj7LorrDlvZXkuK3mj5LlhaXkuoblvZPliY3pobUKICAgICAgaGlzdG9yeS5wdXNoU3RhdGUobnVsbCwgbnVsbCwgZG9jdW1lbnQuVVJMKTsKICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3BvcHN0YXRlJywgdGhpcy5nb0JhY2ssIGZhbHNlKTsKICAgIH0KICB9LAoKICBkZXN0cm95ZWQoKSB7CiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncG9wc3RhdGUnLCB0aGlzLmdvQmFjaywgZmFsc2UpOwogIH0sCgogIG1ldGhvZHM6IHsKICAgIGdvQmFjaygpIHsKICAgICAgaWYgKHRoaXMuY3J1bWJzRGF0YS5sZW5ndGggIT09IDEpIHsKICAgICAgICB0aGlzLnRhYkRlbEp1bXAoKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnJldHVybkNsaWNrKCk7CiAgICAgIH0KCiAgICAgIGhpc3RvcnkucHVzaFN0YXRlKG51bGwsIG51bGwsIGRvY3VtZW50LlVSTCk7CiAgICB9LAoKICAgIG1lbnVTZWxlY3RlZCgpIHsKICAgICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5Lm9iaikgewogICAgICAgIHRoaXMuY3J1bWJzRGF0YSA9IEpTT04ucGFyc2UodGhpcy4kcm91dGUucXVlcnkub2JqKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmNydW1ic0RhdGEgPSB0aGlzLm1lbnVEYXRhTGlzdCh0aGlzLm1lbnVEYXRhKTsKICAgICAgfQoKICAgICAgdGhpcy5tZW51SWQgPSB0aGlzLmNydW1ic0RhdGFbMF0uaWQ7IC8vIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogdGhpcy5jcnVtYnNEYXRhWzBdLnRvIH0pCiAgICB9LAoKICAgIG1lbnVEYXRhTGlzdChkYXRhKSB7CiAgICAgIGxldCBhcnIgPSBbXTsKICAgICAgZGF0YS5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gewogICAgICAgIGlmIChpbmRleCA9PT0gMCkgewogICAgICAgICAgaWYgKGl0ZW0uY2hpbGRyZW4ubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICAgIGFyci5wdXNoKHsKICAgICAgICAgICAgICBpZDogaXRlbS5tZW51SWQsCiAgICAgICAgICAgICAgbmFtZTogaXRlbS5uYW1lLAogICAgICAgICAgICAgIHRvOiBpdGVtLnRvLAogICAgICAgICAgICAgIHBhcmFtczoge30KICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhcnIgPSBhcnIuY29uY2F0KHRoaXMubWVudURhdGFMaXN0KGl0ZW0uY2hpbGRyZW4pKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgICByZXR1cm4gYXJyOwogICAgfSwKCiAgICBzeXN0ZW1EYXRhKCkgewogICAgICB2YXIgbmFtZSA9IEpTT04ucGFyc2Uoc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgnbmFtZScpKTsKICAgICAgaWYgKG5hbWUgPT09ICfns7vnu5/nrqHnkIYnKSByZXR1cm47CiAgICAgIHZhciBtZW51cyA9IEpTT04ucGFyc2Uoc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgnbWVudXMnICsgdGhpcy4kbG9nbygpKSk7CiAgICAgIG1lbnVzLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgaWYgKGl0ZW0ubmFtZSA9PT0gJ+ezu+e7n+euoeeQhicpIHsKICAgICAgICAgIHRoaXMuc3lzdGVtU2hvdyA9IGl0ZW0uY2hpbGRyZW47CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgaGVscERhdGEoKSB7CiAgICAgIHZhciBuYW1lID0gSlNPTi5wYXJzZShzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCduYW1lJykpOwogICAgICBpZiAobmFtZSA9PT0gJ+W4ruWKqeS4reW/gycpIHJldHVybjsKICAgICAgdmFyIG1lbnVzID0gSlNPTi5wYXJzZShzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdtZW51cycgKyB0aGlzLiRsb2dvKCkpKTsKICAgICAgbWVudXMuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpZiAoaXRlbS5uYW1lID09PSAn5biu5Yqp5Lit5b+DJykgewogICAgICAgICAgdGhpcy5oZWxwU2hvdyA9IGl0ZW0uY2hpbGRyZW47CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgaGVscCgpIHsKICAgICAgc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbSgnbmFtZScsIEpTT04uc3RyaW5naWZ5KCfluK7liqnkuK3lv4MnKSk7CiAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oJ21lbnVDaGlsZCcsIEpTT04uc3RyaW5naWZ5KHRoaXMuaGVscFNob3cpKTsKICAgICAgdGhpcy5uYW1lID0gJ+W4ruWKqeS4reW/gyc7CiAgICAgIHRoaXMubWVudUlkID0gJyc7CiAgICAgIHRoaXMubWVudURhdGEgPSB0aGlzLmhlbHBTaG93OwogICAgICB0aGlzLmhlbHBTaG93ID0gW107CiAgICAgIHRoaXMubWVudVNlbGVjdGVkKCk7CiAgICAgIHRoaXMuc3lzdGVtRGF0YSgpOwogICAgfSwKCiAgICAvLyDngrnlh7vnmoToj5zljZUKICAgIG1lbnVTZWxlY3QoZGF0YSkgewogICAgICB0aGlzLmNydW1ic0RhdGEgPSBbewogICAgICAgIGlkOiBkYXRhLm1lbnVJZCwKICAgICAgICBuYW1lOiBkYXRhLm5hbWUsCiAgICAgICAgdG86IGRhdGEudG8KICAgICAgfV07CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiBkYXRhLnRvLAogICAgICAgIHF1ZXJ5OiB7IC4uLmRhdGEucGFyYW1zLAogICAgICAgICAgLi4udGhpcy5wYXRoUGFyYW1zCiAgICAgICAgfSB8fCB7fQogICAgICB9KTsKICAgICAgdGhpcy5wYXRoUGFyYW1zID0ge307CiAgICB9LAoKICAgIG5ld1RhYihkYXRhKSB7CiAgICAgIHZhciBpbmRleCA9IGZhbHNlOwogICAgICB2YXIgY3J1bWJzRGF0YSA9IHRoaXMuY3J1bWJzRGF0YTsKICAgICAgY3J1bWJzRGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGlmIChpdGVtLmlkID09PSBkYXRhLm1lbnVJZCkgewogICAgICAgICAgaW5kZXggPSB0cnVlOwogICAgICAgICAgaXRlbS50byA9IGRhdGEudG87CiAgICAgICAgICBpdGVtLm5hbWUgPSBkYXRhLm5hbWU7CiAgICAgICAgICBpdGVtLnBhcmFtcyA9IGRhdGEucGFyYW1zIHx8IHt9OwogICAgICAgIH0KICAgICAgfSk7CgogICAgICBpZiAoaW5kZXgpIHsKICAgICAgICB0aGlzLmNydW1ic0RhdGEgPSBjcnVtYnNEYXRhOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuY3J1bWJzRGF0YS5wdXNoKHsKICAgICAgICAgIGlkOiBkYXRhLm1lbnVJZCwKICAgICAgICAgIG5hbWU6IGRhdGEubmFtZSwKICAgICAgICAgIHRvOiBkYXRhLnRvLAogICAgICAgICAgcGFyYW1zOiBkYXRhLnBhcmFtcyB8fCB7fQogICAgICAgIH0pOwogICAgICB9CgogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogZGF0YS50bywKICAgICAgICBxdWVyeTogZGF0YS5wYXJhbXMgfHwge30KICAgICAgfSk7CiAgICB9LAoKICAgIG1hdGNoaW5nTWVudShtdW5lLCBkYXRhLCBwYXJhbXMgPSB7fSkgewogICAgICBtdW5lLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgaWYgKGl0ZW0udG8gPT0gZGF0YSkgewogICAgICAgICAgLy/CoGVzbGludC1kaXNhYmxlLWxpbmXCoAogICAgICAgICAgdGhpcy5wYXRoUGFyYW1zID0gcGFyYW1zOwogICAgICAgICAgdGhpcy5tZW51SWQgPSBpdGVtLm1lbnVJZCArICcnOyAvLyB0aGlzLmNydW1ic0RhdGEgPSBbeyBpZDogaXRlbS5tZW51SWQsIG5hbWU6IGl0ZW0ubmFtZSwgdG86IGl0ZW0udG8sIHBhcmFtczogcGFyYW1zIH1dCiAgICAgICAgICAvLyB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6IGl0ZW0udG8sIHF1ZXJ5OiBwYXJhbXMgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5tYXRjaGluZ01lbnUoaXRlbS5jaGlsZHJlbiwgZGF0YSwgcGFyYW1zKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKCiAgICBjcnVtYnNDbGNpayhkYXRhLCBpKSB7CiAgICAgIHZhciBhcnIgPSBbXTsKICAgICAgdGhpcy5jcnVtYnNEYXRhLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7CiAgICAgICAgaWYgKGluZGV4IDw9IGkpIHsKICAgICAgICAgIGFyci5wdXNoKGl0ZW0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHRoaXMuY3J1bWJzRGF0YSA9IGFycjsKICAgIH0sCgogICAgdGFiRGVsSnVtcCgpIHsKICAgICAgdmFyIGFyciA9IFtdOwogICAgICB2YXIgZGF0YSA9IHt9OwogICAgICB0aGlzLmNydW1ic0RhdGEuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsKICAgICAgICBpZiAoaW5kZXggPD0gdGhpcy5jcnVtYnNEYXRhLmxlbmd0aCAtIDIpIHsKICAgICAgICAgIGFyci5wdXNoKGl0ZW0pOwogICAgICAgIH0KCiAgICAgICAgaWYgKGluZGV4ID09PSB0aGlzLmNydW1ic0RhdGEubGVuZ3RoIC0gMikgewogICAgICAgICAgZGF0YSA9IGl0ZW07CiAgICAgICAgfQogICAgICB9KTsKICAgICAgdGhpcy5jcnVtYnNEYXRhID0gYXJyOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogZGF0YS50bywKICAgICAgICBxdWVyeTogZGF0YS5wYXJhbXMgfHwge30KICAgICAgfSk7CiAgICB9LAoKICAgIHRhYk5hbWVEZWxldGUoKSB7CiAgICAgIHZhciBhcnIgPSBbXTsKICAgICAgdmFyIGRhdGEgPSB7fTsKICAgICAgdGhpcy5jcnVtYnNEYXRhLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7CiAgICAgICAgaWYgKGluZGV4IDw9IHRoaXMuY3J1bWJzRGF0YS5sZW5ndGggLSAyKSB7CiAgICAgICAgICBhcnIucHVzaChpdGVtKTsKICAgICAgICB9CgogICAgICAgIGlmIChpbmRleCA9PT0gdGhpcy5jcnVtYnNEYXRhLmxlbmd0aCAtIDIpIHsKICAgICAgICAgIGRhdGEgPSBpdGVtOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHRoaXMuY3J1bWJzRGF0YSA9IGFycjsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIHBhdGg6IGRhdGEudG8sCiAgICAgICAgcXVlcnk6IGRhdGEucGFyYW1zIHx8IHt9CiAgICAgIH0pOwogICAgfSwKCiAgICBqdW1wTWVudSgpIHt9LAoKICAgIHN5c3RlbSgpIHsKICAgICAgc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbSgnbmFtZScsIEpTT04uc3RyaW5naWZ5KCfns7vnu5/nrqHnkIYnKSk7CiAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oJ21lbnVDaGlsZCcsIEpTT04uc3RyaW5naWZ5KHRoaXMuc3lzdGVtU2hvdykpOwogICAgICB0aGlzLm5hbWUgPSAn57O757uf566h55CGJzsKICAgICAgdGhpcy5tZW51SWQgPSAnJzsKICAgICAgdGhpcy5tZW51RGF0YSA9IHRoaXMuc3lzdGVtU2hvdzsKICAgICAgdGhpcy5zeXN0ZW1TaG93ID0gW107CiAgICAgIHRoaXMubWVudVNlbGVjdGVkKCk7CiAgICAgIHRoaXMuaGVscERhdGEoKTsKICAgIH0sCgogICAgcmV0dXJuQ2xpY2soKSB7CiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCduYW1lJyk7CiAgICAgIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oJ21lbnVDaGlsZCcpOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgbmFtZTogJ2hvbWUnCiAgICAgIH0pOwogICAgfQoKICB9Cn07"}, {"version": 3, "names": ["data", "systemShow", "helpShow", "menuId", "menuData", "JSON", "parse", "sessionStorage", "getItem", "crumbsId", "crumbsData", "pathParams", "includes", "watch", "val", "arr", "for<PERSON>ach", "item", "push", "to", "substring", "created", "systemData", "helpData", "menuSelected", "mounted", "window", "history", "pushState", "document", "URL", "addEventListener", "goBack", "destroyed", "removeEventListener", "methods", "length", "tabDelJump", "returnClick", "$route", "query", "obj", "menuDataList", "id", "index", "children", "name", "params", "concat", "menus", "$logo", "help", "setItem", "stringify", "menuSelect", "$router", "path", "newTab", "matchingMenu", "mune", "crumbsClcik", "i", "tabNameDelete", "jumpMenu", "system", "localStorage", "removeItem"], "sources": ["D:/zy/xm/pc/qdzx/product/src/mixins/general.js"], "sourcesContent": ["export default {\r\n  data () {\r\n    return {\r\n      systemShow: [],\r\n      helpShow: [],\r\n      menuId: '',\r\n      menuData: JSON.parse(sessionStorage.getItem('menuChild')),\r\n      crumbsId: '',\r\n      crumbsData: [],\r\n      pathParams: {},\r\n      includes: []\r\n    }\r\n  },\r\n  watch: {\r\n    crumbsData (val) {\r\n      var arr = []\r\n      val.forEach(item => {\r\n        arr.push(item.to.substring(1))\r\n      })\r\n      this.includes = arr\r\n    }\r\n  },\r\n  created () {\r\n    this.systemData()\r\n    this.helpData()\r\n    this.menuSelected()\r\n  },\r\n  mounted () {\r\n    if (window.history && window.history.pushState) {\r\n      // 向历史记录中插入了当前页\r\n      history.pushState(null, null, document.URL)\r\n      window.addEventListener('popstate', this.goBack, false)\r\n    }\r\n  },\r\n  destroyed () {\r\n    window.removeEventListener('popstate', this.goBack, false)\r\n  },\r\n  methods: {\r\n    goBack () {\r\n      if (this.crumbsData.length !== 1) {\r\n        this.tabDelJump()\r\n      } else {\r\n        this.returnClick()\r\n      }\r\n      history.pushState(null, null, document.URL)\r\n    },\r\n    menuSelected () {\r\n      if (this.$route.query.obj) {\r\n        this.crumbsData = JSON.parse(this.$route.query.obj)\r\n      } else {\r\n        this.crumbsData = this.menuDataList(this.menuData)\r\n      }\r\n      this.menuId = this.crumbsData[0].id\r\n      // this.$router.push({ path: this.crumbsData[0].to })\r\n    },\r\n    menuDataList (data) {\r\n      let arr = []\r\n      data.forEach((item, index) => {\r\n        if (index === 0) {\r\n          if (item.children.length === 0) {\r\n            arr.push({ id: item.menuId, name: item.name, to: item.to, params: {} })\r\n          } else {\r\n            arr = arr.concat(this.menuDataList(item.children))\r\n          }\r\n        }\r\n      })\r\n      return arr\r\n    },\r\n    systemData () {\r\n      var name = JSON.parse(sessionStorage.getItem('name'))\r\n      if (name === '系统管理') return\r\n      var menus = JSON.parse(sessionStorage.getItem('menus' + this.$logo()))\r\n      menus.forEach((item) => {\r\n        if (item.name === '系统管理') {\r\n          this.systemShow = item.children\r\n        }\r\n      })\r\n    },\r\n    helpData () {\r\n      var name = JSON.parse(sessionStorage.getItem('name'))\r\n      if (name === '帮助中心') return\r\n      var menus = JSON.parse(sessionStorage.getItem('menus' + this.$logo()))\r\n      menus.forEach((item) => {\r\n        if (item.name === '帮助中心') {\r\n          this.helpShow = item.children\r\n        }\r\n      })\r\n    },\r\n    help () {\r\n      sessionStorage.setItem('name', JSON.stringify('帮助中心'))\r\n      sessionStorage.setItem('menuChild', JSON.stringify(this.helpShow))\r\n      this.name = '帮助中心'\r\n      this.menuId = ''\r\n      this.menuData = this.helpShow\r\n      this.helpShow = []\r\n      this.menuSelected()\r\n      this.systemData()\r\n    },\r\n    // 点击的菜单\r\n    menuSelect (data) {\r\n      this.crumbsData = [{ id: data.menuId, name: data.name, to: data.to }]\r\n      this.$router.push({ path: data.to, query: { ...data.params, ...this.pathParams } || {} })\r\n      this.pathParams = {}\r\n    },\r\n    newTab (data) {\r\n      var index = false\r\n      var crumbsData = this.crumbsData\r\n      crumbsData.forEach(item => {\r\n        if (item.id === data.menuId) {\r\n          index = true\r\n          item.to = data.to\r\n          item.name = data.name\r\n          item.params = data.params || {}\r\n        }\r\n      })\r\n      if (index) {\r\n        this.crumbsData = crumbsData\r\n      } else {\r\n        this.crumbsData.push({ id: data.menuId, name: data.name, to: data.to, params: data.params || {} })\r\n      }\r\n      this.$router.push({ path: data.to, query: data.params || {} })\r\n    },\r\n    matchingMenu (mune, data, params = {}) {\r\n      mune.forEach(item => {\r\n        if (item.to == data) { // eslint-disable-line \r\n          this.pathParams = params\r\n          this.menuId = item.menuId + ''\r\n          // this.crumbsData = [{ id: item.menuId, name: item.name, to: item.to, params: params }]\r\n          // this.$router.push({ path: item.to, query: params })\r\n        } else {\r\n          this.matchingMenu(item.children, data, params)\r\n        }\r\n      })\r\n    },\r\n    crumbsClcik (data, i) {\r\n      var arr = []\r\n      this.crumbsData.forEach((item, index) => {\r\n        if (index <= i) {\r\n          arr.push(item)\r\n        }\r\n      })\r\n      this.crumbsData = arr\r\n    },\r\n    tabDelJump () {\r\n      var arr = []\r\n      var data = {}\r\n      this.crumbsData.forEach((item, index) => {\r\n        if (index <= this.crumbsData.length - 2) {\r\n          arr.push(item)\r\n        }\r\n        if (index === this.crumbsData.length - 2) {\r\n          data = item\r\n        }\r\n      })\r\n      this.crumbsData = arr\r\n      this.$router.push({ path: data.to, query: data.params || {} })\r\n    },\r\n    tabNameDelete () {\r\n      var arr = []\r\n      var data = {}\r\n      this.crumbsData.forEach((item, index) => {\r\n        if (index <= this.crumbsData.length - 2) {\r\n          arr.push(item)\r\n        }\r\n        if (index === this.crumbsData.length - 2) {\r\n          data = item\r\n        }\r\n      })\r\n      this.crumbsData = arr\r\n      this.$router.push({ path: data.to, query: data.params || {} })\r\n    },\r\n    jumpMenu () {\r\n    },\r\n    system () {\r\n      sessionStorage.setItem('name', JSON.stringify('系统管理'))\r\n      sessionStorage.setItem('menuChild', JSON.stringify(this.systemShow))\r\n      this.name = '系统管理'\r\n      this.menuId = ''\r\n      this.menuData = this.systemShow\r\n      this.systemShow = []\r\n      this.menuSelected()\r\n      this.helpData()\r\n    },\r\n    returnClick () {\r\n      localStorage.removeItem('name')\r\n      sessionStorage.removeItem('menuChild')\r\n      this.$router.push({ name: 'home' })\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACbA,IAAI,GAAI;IACN,OAAO;MACLC,UAAU,EAAE,EADP;MAELC,QAAQ,EAAE,EAFL;MAGLC,MAAM,EAAE,EAHH;MAILC,QAAQ,EAAEC,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,WAAvB,CAAX,CAJL;MAKLC,QAAQ,EAAE,EALL;MAMLC,UAAU,EAAE,EANP;MAOLC,UAAU,EAAE,EAPP;MAQLC,QAAQ,EAAE;IARL,CAAP;EAUD,CAZY;;EAabC,KAAK,EAAE;IACLH,UAAU,CAAEI,GAAF,EAAO;MACf,IAAIC,GAAG,GAAG,EAAV;MACAD,GAAG,CAACE,OAAJ,CAAYC,IAAI,IAAI;QAClBF,GAAG,CAACG,IAAJ,CAASD,IAAI,CAACE,EAAL,CAAQC,SAAR,CAAkB,CAAlB,CAAT;MACD,CAFD;MAGA,KAAKR,QAAL,GAAgBG,GAAhB;IACD;;EAPI,CAbM;;EAsBbM,OAAO,GAAI;IACT,KAAKC,UAAL;IACA,KAAKC,QAAL;IACA,KAAKC,YAAL;EACD,CA1BY;;EA2BbC,OAAO,GAAI;IACT,IAAIC,MAAM,CAACC,OAAP,IAAkBD,MAAM,CAACC,OAAP,CAAeC,SAArC,EAAgD;MAC9C;MACAD,OAAO,CAACC,SAAR,CAAkB,IAAlB,EAAwB,IAAxB,EAA8BC,QAAQ,CAACC,GAAvC;MACAJ,MAAM,CAACK,gBAAP,CAAwB,UAAxB,EAAoC,KAAKC,MAAzC,EAAiD,KAAjD;IACD;EACF,CAjCY;;EAkCbC,SAAS,GAAI;IACXP,MAAM,CAACQ,mBAAP,CAA2B,UAA3B,EAAuC,KAAKF,MAA5C,EAAoD,KAApD;EACD,CApCY;;EAqCbG,OAAO,EAAE;IACPH,MAAM,GAAI;MACR,IAAI,KAAKtB,UAAL,CAAgB0B,MAAhB,KAA2B,CAA/B,EAAkC;QAChC,KAAKC,UAAL;MACD,CAFD,MAEO;QACL,KAAKC,WAAL;MACD;;MACDX,OAAO,CAACC,SAAR,CAAkB,IAAlB,EAAwB,IAAxB,EAA8BC,QAAQ,CAACC,GAAvC;IACD,CARM;;IASPN,YAAY,GAAI;MACd,IAAI,KAAKe,MAAL,CAAYC,KAAZ,CAAkBC,GAAtB,EAA2B;QACzB,KAAK/B,UAAL,GAAkBL,IAAI,CAACC,KAAL,CAAW,KAAKiC,MAAL,CAAYC,KAAZ,CAAkBC,GAA7B,CAAlB;MACD,CAFD,MAEO;QACL,KAAK/B,UAAL,GAAkB,KAAKgC,YAAL,CAAkB,KAAKtC,QAAvB,CAAlB;MACD;;MACD,KAAKD,MAAL,GAAc,KAAKO,UAAL,CAAgB,CAAhB,EAAmBiC,EAAjC,CANc,CAOd;IACD,CAjBM;;IAkBPD,YAAY,CAAE1C,IAAF,EAAQ;MAClB,IAAIe,GAAG,GAAG,EAAV;MACAf,IAAI,CAACgB,OAAL,CAAa,CAACC,IAAD,EAAO2B,KAAP,KAAiB;QAC5B,IAAIA,KAAK,KAAK,CAAd,EAAiB;UACf,IAAI3B,IAAI,CAAC4B,QAAL,CAAcT,MAAd,KAAyB,CAA7B,EAAgC;YAC9BrB,GAAG,CAACG,IAAJ,CAAS;cAAEyB,EAAE,EAAE1B,IAAI,CAACd,MAAX;cAAmB2C,IAAI,EAAE7B,IAAI,CAAC6B,IAA9B;cAAoC3B,EAAE,EAAEF,IAAI,CAACE,EAA7C;cAAiD4B,MAAM,EAAE;YAAzD,CAAT;UACD,CAFD,MAEO;YACLhC,GAAG,GAAGA,GAAG,CAACiC,MAAJ,CAAW,KAAKN,YAAL,CAAkBzB,IAAI,CAAC4B,QAAvB,CAAX,CAAN;UACD;QACF;MACF,CARD;MASA,OAAO9B,GAAP;IACD,CA9BM;;IA+BPO,UAAU,GAAI;MACZ,IAAIwB,IAAI,GAAGzC,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,MAAvB,CAAX,CAAX;MACA,IAAIsC,IAAI,KAAK,MAAb,EAAqB;MACrB,IAAIG,KAAK,GAAG5C,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,UAAU,KAAK0C,KAAL,EAAjC,CAAX,CAAZ;MACAD,KAAK,CAACjC,OAAN,CAAeC,IAAD,IAAU;QACtB,IAAIA,IAAI,CAAC6B,IAAL,KAAc,MAAlB,EAA0B;UACxB,KAAK7C,UAAL,GAAkBgB,IAAI,CAAC4B,QAAvB;QACD;MACF,CAJD;IAKD,CAxCM;;IAyCPtB,QAAQ,GAAI;MACV,IAAIuB,IAAI,GAAGzC,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,MAAvB,CAAX,CAAX;MACA,IAAIsC,IAAI,KAAK,MAAb,EAAqB;MACrB,IAAIG,KAAK,GAAG5C,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,UAAU,KAAK0C,KAAL,EAAjC,CAAX,CAAZ;MACAD,KAAK,CAACjC,OAAN,CAAeC,IAAD,IAAU;QACtB,IAAIA,IAAI,CAAC6B,IAAL,KAAc,MAAlB,EAA0B;UACxB,KAAK5C,QAAL,GAAgBe,IAAI,CAAC4B,QAArB;QACD;MACF,CAJD;IAKD,CAlDM;;IAmDPM,IAAI,GAAI;MACN5C,cAAc,CAAC6C,OAAf,CAAuB,MAAvB,EAA+B/C,IAAI,CAACgD,SAAL,CAAe,MAAf,CAA/B;MACA9C,cAAc,CAAC6C,OAAf,CAAuB,WAAvB,EAAoC/C,IAAI,CAACgD,SAAL,CAAe,KAAKnD,QAApB,CAApC;MACA,KAAK4C,IAAL,GAAY,MAAZ;MACA,KAAK3C,MAAL,GAAc,EAAd;MACA,KAAKC,QAAL,GAAgB,KAAKF,QAArB;MACA,KAAKA,QAAL,GAAgB,EAAhB;MACA,KAAKsB,YAAL;MACA,KAAKF,UAAL;IACD,CA5DM;;IA6DP;IACAgC,UAAU,CAAEtD,IAAF,EAAQ;MAChB,KAAKU,UAAL,GAAkB,CAAC;QAAEiC,EAAE,EAAE3C,IAAI,CAACG,MAAX;QAAmB2C,IAAI,EAAE9C,IAAI,CAAC8C,IAA9B;QAAoC3B,EAAE,EAAEnB,IAAI,CAACmB;MAA7C,CAAD,CAAlB;MACA,KAAKoC,OAAL,CAAarC,IAAb,CAAkB;QAAEsC,IAAI,EAAExD,IAAI,CAACmB,EAAb;QAAiBqB,KAAK,EAAE,EAAE,GAAGxC,IAAI,CAAC+C,MAAV;UAAkB,GAAG,KAAKpC;QAA1B,KAA0C;MAAlE,CAAlB;MACA,KAAKA,UAAL,GAAkB,EAAlB;IACD,CAlEM;;IAmEP8C,MAAM,CAAEzD,IAAF,EAAQ;MACZ,IAAI4C,KAAK,GAAG,KAAZ;MACA,IAAIlC,UAAU,GAAG,KAAKA,UAAtB;MACAA,UAAU,CAACM,OAAX,CAAmBC,IAAI,IAAI;QACzB,IAAIA,IAAI,CAAC0B,EAAL,KAAY3C,IAAI,CAACG,MAArB,EAA6B;UAC3ByC,KAAK,GAAG,IAAR;UACA3B,IAAI,CAACE,EAAL,GAAUnB,IAAI,CAACmB,EAAf;UACAF,IAAI,CAAC6B,IAAL,GAAY9C,IAAI,CAAC8C,IAAjB;UACA7B,IAAI,CAAC8B,MAAL,GAAc/C,IAAI,CAAC+C,MAAL,IAAe,EAA7B;QACD;MACF,CAPD;;MAQA,IAAIH,KAAJ,EAAW;QACT,KAAKlC,UAAL,GAAkBA,UAAlB;MACD,CAFD,MAEO;QACL,KAAKA,UAAL,CAAgBQ,IAAhB,CAAqB;UAAEyB,EAAE,EAAE3C,IAAI,CAACG,MAAX;UAAmB2C,IAAI,EAAE9C,IAAI,CAAC8C,IAA9B;UAAoC3B,EAAE,EAAEnB,IAAI,CAACmB,EAA7C;UAAiD4B,MAAM,EAAE/C,IAAI,CAAC+C,MAAL,IAAe;QAAxE,CAArB;MACD;;MACD,KAAKQ,OAAL,CAAarC,IAAb,CAAkB;QAAEsC,IAAI,EAAExD,IAAI,CAACmB,EAAb;QAAiBqB,KAAK,EAAExC,IAAI,CAAC+C,MAAL,IAAe;MAAvC,CAAlB;IACD,CApFM;;IAqFPW,YAAY,CAAEC,IAAF,EAAQ3D,IAAR,EAAc+C,MAAM,GAAG,EAAvB,EAA2B;MACrCY,IAAI,CAAC3C,OAAL,CAAaC,IAAI,IAAI;QACnB,IAAIA,IAAI,CAACE,EAAL,IAAWnB,IAAf,EAAqB;UAAE;UACrB,KAAKW,UAAL,GAAkBoC,MAAlB;UACA,KAAK5C,MAAL,GAAcc,IAAI,CAACd,MAAL,GAAc,EAA5B,CAFmB,CAGnB;UACA;QACD,CALD,MAKO;UACL,KAAKuD,YAAL,CAAkBzC,IAAI,CAAC4B,QAAvB,EAAiC7C,IAAjC,EAAuC+C,MAAvC;QACD;MACF,CATD;IAUD,CAhGM;;IAiGPa,WAAW,CAAE5D,IAAF,EAAQ6D,CAAR,EAAW;MACpB,IAAI9C,GAAG,GAAG,EAAV;MACA,KAAKL,UAAL,CAAgBM,OAAhB,CAAwB,CAACC,IAAD,EAAO2B,KAAP,KAAiB;QACvC,IAAIA,KAAK,IAAIiB,CAAb,EAAgB;UACd9C,GAAG,CAACG,IAAJ,CAASD,IAAT;QACD;MACF,CAJD;MAKA,KAAKP,UAAL,GAAkBK,GAAlB;IACD,CAzGM;;IA0GPsB,UAAU,GAAI;MACZ,IAAItB,GAAG,GAAG,EAAV;MACA,IAAIf,IAAI,GAAG,EAAX;MACA,KAAKU,UAAL,CAAgBM,OAAhB,CAAwB,CAACC,IAAD,EAAO2B,KAAP,KAAiB;QACvC,IAAIA,KAAK,IAAI,KAAKlC,UAAL,CAAgB0B,MAAhB,GAAyB,CAAtC,EAAyC;UACvCrB,GAAG,CAACG,IAAJ,CAASD,IAAT;QACD;;QACD,IAAI2B,KAAK,KAAK,KAAKlC,UAAL,CAAgB0B,MAAhB,GAAyB,CAAvC,EAA0C;UACxCpC,IAAI,GAAGiB,IAAP;QACD;MACF,CAPD;MAQA,KAAKP,UAAL,GAAkBK,GAAlB;MACA,KAAKwC,OAAL,CAAarC,IAAb,CAAkB;QAAEsC,IAAI,EAAExD,IAAI,CAACmB,EAAb;QAAiBqB,KAAK,EAAExC,IAAI,CAAC+C,MAAL,IAAe;MAAvC,CAAlB;IACD,CAvHM;;IAwHPe,aAAa,GAAI;MACf,IAAI/C,GAAG,GAAG,EAAV;MACA,IAAIf,IAAI,GAAG,EAAX;MACA,KAAKU,UAAL,CAAgBM,OAAhB,CAAwB,CAACC,IAAD,EAAO2B,KAAP,KAAiB;QACvC,IAAIA,KAAK,IAAI,KAAKlC,UAAL,CAAgB0B,MAAhB,GAAyB,CAAtC,EAAyC;UACvCrB,GAAG,CAACG,IAAJ,CAASD,IAAT;QACD;;QACD,IAAI2B,KAAK,KAAK,KAAKlC,UAAL,CAAgB0B,MAAhB,GAAyB,CAAvC,EAA0C;UACxCpC,IAAI,GAAGiB,IAAP;QACD;MACF,CAPD;MAQA,KAAKP,UAAL,GAAkBK,GAAlB;MACA,KAAKwC,OAAL,CAAarC,IAAb,CAAkB;QAAEsC,IAAI,EAAExD,IAAI,CAACmB,EAAb;QAAiBqB,KAAK,EAAExC,IAAI,CAAC+C,MAAL,IAAe;MAAvC,CAAlB;IACD,CArIM;;IAsIPgB,QAAQ,GAAI,CACX,CAvIM;;IAwIPC,MAAM,GAAI;MACRzD,cAAc,CAAC6C,OAAf,CAAuB,MAAvB,EAA+B/C,IAAI,CAACgD,SAAL,CAAe,MAAf,CAA/B;MACA9C,cAAc,CAAC6C,OAAf,CAAuB,WAAvB,EAAoC/C,IAAI,CAACgD,SAAL,CAAe,KAAKpD,UAApB,CAApC;MACA,KAAK6C,IAAL,GAAY,MAAZ;MACA,KAAK3C,MAAL,GAAc,EAAd;MACA,KAAKC,QAAL,GAAgB,KAAKH,UAArB;MACA,KAAKA,UAAL,GAAkB,EAAlB;MACA,KAAKuB,YAAL;MACA,KAAKD,QAAL;IACD,CAjJM;;IAkJPe,WAAW,GAAI;MACb2B,YAAY,CAACC,UAAb,CAAwB,MAAxB;MACA3D,cAAc,CAAC2D,UAAf,CAA0B,WAA1B;MACA,KAAKX,OAAL,CAAarC,IAAb,CAAkB;QAAE4B,IAAI,EAAE;MAAR,CAAlB;IACD;;EAtJM;AArCI,CAAf"}]}