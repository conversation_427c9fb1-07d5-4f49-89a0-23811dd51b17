{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\wang-editor\\wang-editor.vue?vue&type=style&index=0&id=4246d57f&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\wang-editor\\wang-editor.vue", "mtime": 1752541693505}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoud2FuZy1lZGl0b3Igew0KICB3aWR0aDogMTAwJTsNCg0KICAudy1lLXRleHQtY29udGFpbmVyIHsNCiAgICAudy1lLXRleHQgew0KICAgICAgaW1nIHsNCiAgICAgICAgd2lkdGg6IDgwJTsNCiAgICAgICAgaGVpZ2h0OiBhdXRvOw0KICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["wang-editor.vue"], "names": [], "mappings": ";AAqLA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "wang-editor.vue", "sourceRoot": "src/components/wang-editor", "sourcesContent": ["<template>\r\n  <div>\r\n    <div :id=\"editorId\"\r\n         class=\"wang-editor\"></div>\r\n    <p v-if=\"contentLength > $props.max\"\r\n       style=\"color: red;\">\r\n      已超出最大{{ $props.max }}字数限制！\r\n    </p>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 引入富文本编辑器\r\nimport WangEditor from 'wangeditor'\r\nimport axios from 'axios'\r\nconst { BtnMenu } = WangEditor\r\nexport default {\r\n  name: 'wang-editor',\r\n  props: {\r\n    value: {\r\n      required: true\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: () => '请输入正文'\r\n    },\r\n    max: {\r\n      type: Number,\r\n      default: () => 9999999\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      editor: '',\r\n      editorId: '',\r\n      contentLength: 0\r\n    }\r\n  },\r\n  watch: {\r\n    value (newval) {\r\n      if (this.editor) {\r\n        if (newval !== this.editor.txt.html()) {\r\n          this.editor.txt.html(newval)\r\n        }\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value', // 这个字段，是指父组件设置 v-model 时，将变量值传给子组件的 msg\r\n    event: 'input'// 这个字段，是指父组件监听 parent-event 事件\r\n  },\r\n  methods: {\r\n    // // 生成一个随机不重复id,可以通过时间和随机数生成\r\n    randomId () {\r\n      const baseId = 'wang_editor'\r\n      const now = new Date().getTime()\r\n      return `${baseId}_${now}`\r\n    },\r\n    // 初始化编辑器\r\n    initEditor () {\r\n      const _this = this\r\n      _this.editorId = _this.randomId()// 生成一个id\r\n      this.$nextTick(() => {\r\n        // 获取实例,wangEditor是被注册在window的\r\n        const editor = new WangEditor('#' + _this.editorId)\r\n        _this.editor = editor// 将实例保存待调用其他api\r\n        _this.setConfig()\r\n        editor.create()// 开始创建编辑器；\r\n        _this.editor.txt.html(this.value)\r\n        // 设置是否可编辑\r\n        if (this.disabled !== 'undefined') {\r\n          this.editor.$textElem.attr('contenteditable', !this.disabled)\r\n        }\r\n      })\r\n    },\r\n    // 创建富文本编辑器\r\n    setConfig () {\r\n      var _this = this\r\n      // 开始创建\r\n      const setting = {\r\n        uploadImgShowBase64: false, // 是否允许上传base64位图片\r\n        pasteFilterStyle: true, // 是否过滤粘贴的样式\r\n        zIndex: 100, // 设置层叠位置\r\n        // 菜单列表\r\n        menus: [\r\n          'head', // 标题\r\n          'bold', // 粗体\r\n          'fontSize', // 字号\r\n          'fontName', // 字体\r\n          'italic', // 斜体\r\n          'indent', // 缩进\r\n          'lineHeight', // 行高\r\n          'underline', // 下划线\r\n          'strikeThrough', // 删除线\r\n          'foreColor', // 文字颜色\r\n          'backColor', // 背景颜色\r\n          'link', // 插入链接\r\n          'list', // 列表\r\n          'justify', // 对齐方式\r\n          'quote', // 引用\r\n          'emoticon', // 表情\r\n          'image', // 插入图片\r\n          'table', // 表格\r\n          'video', // 插入视频\r\n          // 'code', // 插入代码\r\n          'undo', // 撤销\r\n          'redo', // 恢复\r\n          'qgs' // 恢复\r\n        ],\r\n        showLinkImg: true, // 是否显示“网络图片”tab\r\n        onchange: function (html) {\r\n          // console.log('html===>', html)\r\n          html = html.replace(/<strong>(.*?)<\\/strong>/g, '$1')\r\n          html = html.replace(/<b>(.*?)<\\/b>/g, '$1')\r\n          _this.$emit('input', html)\r\n          const text = html\r\n          _this.contentLength = text.length\r\n          if (_this.contentLength > _this.$props.max) {\r\n            _this.$emit('restrictions', `已超出最大${_this.$props.max}字数限制！`)\r\n          } else {\r\n            _this.$emit('restrictions', '')\r\n          }\r\n        },\r\n        onlineVideoCallback: v => {\r\n          if (v.endsWith('.mp4')) {\r\n            _this.editor.cmd.do(\r\n              'insertHTML', `<video src=\"${v}\" controls=\"controls\" style=\"max-width:100%\"></video>`\r\n            )\r\n          }\r\n        },\r\n        customUploadImg: (resultFiles, insertImgFn) => {\r\n          const formData = new FormData()\r\n          formData.append('upfile', resultFiles[0])\r\n          axios.post(`${_this.$api.general.baseURL()}/ueditor/exec?action=uploadimage`, formData).then(res => {\r\n            insertImgFn(res.data.url)\r\n          })\r\n        }\r\n      }\r\n      // 配置给编辑器\r\n      _this.editor.config = Object.assign(_this.editor.config, setting)\r\n      _this.editor.config.placeholder = _this.$props.placeholder\r\n    }\r\n  },\r\n  created () {\r\n    // 创建editor实例\r\n    class AlertMenu extends BtnMenu {\r\n      constructor(editor) {\r\n        // data-title属性表示当鼠标悬停在该按钮上时提示该按钮的功能简述\r\n        const $elem = WangEditor.$(\r\n          `<div class=\"w-e-menu\" data-title=\"清除格式\">\r\n               <svg style=\"width:14px;heigth:14px;\" viewBox=\"0 0 1024 1024\"><path d=\"M969.382408 288.738615l-319.401123-270.852152a67.074236 67.074236 0 0 0-96.459139 5.74922l-505.931379 574.922021a68.35184 68.35184 0 0 0-17.886463 47.910169 74.101061 74.101061 0 0 0 24.274486 47.910168l156.50655 132.232065h373.060512L975.131628 383.281347a67.074236 67.074236 0 0 0-5.74922-96.459139z m-440.134747 433.746725H264.144729l-90.071117-78.572676c-5.74922-5.74922-12.137243-12.137243-12.137243-17.886463a36.411728 36.411728 0 0 1 5.749221-24.274485l210.804741-240.828447 265.102932 228.691204z m-439.495945 180.781036h843.218964a60.047411 60.047411 0 1 1 0 120.733624H89.751716a60.047411 60.047411 0 1 1 0-120.733624z m0 0\"></path></svg>\r\n            </div>`\r\n        )\r\n        super($elem, editor)\r\n      }\r\n\r\n      clickHandler () {\r\n        var editor = this.editor\r\n        console.log('editor===>>', editor)\r\n        var str = editor.txt.html()\r\n        str = str.replace(/<xml>[\\s\\S]*?<\\/xml>/ig, '')\r\n        str = str.replace(/<style>[\\s\\S]*?<\\/style>/ig, '')\r\n        str = str.replace(/<\\/?[^>]*>/g, '')\r\n        str = str.replace(/[ | ]*\\n/g, '\\n')\r\n        str = str.replace(/&nbsp;/ig, '')\r\n        editor.txt.html(str)\r\n      }\r\n\r\n      tryChangeActive () { }\r\n    }\r\n    WangEditor.registerMenu('qgs', AlertMenu)\r\n    this.initEditor()\r\n  }\r\n}\r\n\r\n</script>\r\n<style lang=\"scss\">\r\n.wang-editor {\r\n  width: 100%;\r\n\r\n  .w-e-text-container {\r\n    .w-e-text {\r\n      img {\r\n        width: 80%;\r\n        height: auto;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}