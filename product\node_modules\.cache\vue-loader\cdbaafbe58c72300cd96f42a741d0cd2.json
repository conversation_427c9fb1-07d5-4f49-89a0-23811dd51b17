{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\UEditor\\UEditor.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\UEditor\\UEditor.vue", "mtime": 1752509077000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["UEditor.vue"], "names": [], "mappings": ";AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "UEditor.vue", "sourceRoot": "src/components/UEditor", "sourcesContent": ["<template>\r\n  <div class=\"hello\">\r\n    <vue-ueditor-wrap v-model=\"model\" @ready=\"ready\" :config=\"myConfig\"></vue-ueditor-wrap>\r\n    <div class=\"helloText\" v-if=\"maximumWords < length\">当前已输入{{ length }}个字符，您最多可以输入{{ maximumWords\r\n    }}个字符，当前已经超出{{ length - maximumWords }}个字符。\r\n    </div>\r\n    <div class=\"helloText\" v-if=\"maximumWords > length\">当前已输入{{ length }}个字符，您还可以输入{{ maximumWords - length\r\n    }}个字符。</div>\r\n    <div class=\"helloText\" v-if=\"!maximumWords\">当前已输入{{ length }}个字符</div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport VueUeditorWrap from './vue-ueditor-wrap.min.js'\r\nexport default {\r\n  name: 'UEditor',\r\n  components: {\r\n    VueUeditorWrap // eslint-disable-line\r\n  },\r\n  data () {\r\n    return {\r\n      length: 0,\r\n      myConfig: {\r\n        // 是否跟随内容撑开\r\n        autoHeightEnabled: false,\r\n        elementPathEnabled: false,\r\n        wordCount: false,\r\n        // 高度\r\n        initialFrameHeight: this.height,\r\n        // 宽度\r\n        initialFrameWidth: '100%',\r\n        // 图片上传的路径\r\n        serverUrl: `${this.$api.general.baseURL()}/ueditor/exec`,\r\n        // serverUrl: `http://*************/lzt/ueditor/exec`,\r\n        // 资源依赖的路径\r\n        UEDITOR_HOME_URL: './UEditor/',\r\n        // 鼠标右键的功能，但是一般不用编辑器带的功能，因为编辑器自带的不能右键复制，所以这个变量设置为空数组\r\n        contextMenu: [],\r\n        lineheight: [1, 1.25, 1.5, 1.75, 2, 2.25, 2.5, 2.75, 3, 3.25, 3.5, 3.75, 4],\r\n        autotypeset: {\r\n          mergeEmptyline: true, // 合并空行\r\n          removeClass: true, // 去掉冗余的class\r\n          removeEmptyline: false, // 去掉空行\r\n          textAlign: 'left', // 段落的排版方式，可以是 left,right,center,justify 去掉这个属性表示不执行排版\r\n          imageBlockLine: 'center', // 图片的浮动方式，独占一行剧中,左右浮动，默认: center,left,right,none 去掉这个属性表示不执行排版\r\n          pasteFilter: false, // 根据规则过滤没事粘贴进来的内容\r\n          clearFontSize: false, // 去掉所有的内嵌字号，使用编辑器默认的字号\r\n          clearFontFamily: false, // 去掉所有的内嵌字体，使用编辑器默认的字体\r\n          removeEmptyNode: false, // 去掉空节点\r\n          // 可以去掉的标签\r\n          removeTagNames: { 标签名字: 1 },\r\n          indent: true, // 行首缩进\r\n          indentValue: '2em', // 行首缩进的大小\r\n          bdc2sb: false,\r\n          tobdc: false\r\n        },\r\n        fontfamily: [\r\n          { label: '', name: 'songti', val: '宋体, SimSun' },\r\n          { label: '仿宋', name: 'fangsong', val: '仿宋, FangSong' },\r\n          { label: '仿宋_GB2312', name: 'fangsong', val: '仿宋_GB2312, 仿宋, FangSong' },\r\n          { label: '方正小标宋_GBK', name: '方正小标宋_GBK', val: '方正小标宋_GBK, 宋体, SimSun' },\r\n          { label: '方正仿宋_GBK', name: '方正仿宋_GBK', val: '方正仿宋_GBK, 仿宋, FangSong' },\r\n          { label: '方正楷体_GBK', name: '方正楷体_GBK', val: '方正楷体_GBK, 楷体, SimKai' },\r\n          { label: '方正黑体_GBK', name: '方正黑体_GBK', val: '方正黑体_GBK, 黑体, SimHei' },\r\n          { label: '', name: 'kaiti', val: '楷体, 楷体_GB2312, SimKai' },\r\n          { label: '', name: 'yahei', val: '微软雅黑, Microsoft YaHei' },\r\n          { label: '', name: 'heiti', val: '黑体, SimHei' },\r\n          { label: '', name: 'lishu', val: '隶书, SimLi' },\r\n          { label: '', name: 'andaleMono', val: 'andale mono' },\r\n          { label: '', name: 'arial', val: 'arial, helvetica,sans-serif' },\r\n          { label: '', name: 'arialBlack', val: 'arial black,avant garde' },\r\n          { label: '', name: 'comicSansMs', val: 'comic sans ms' },\r\n          { label: '', name: 'impact', val: 'impact,chicago' },\r\n          { label: '', name: 'timesNewRoman', val: 'times new roman' }\r\n        ],\r\n        toolbars: this.toolbars,\r\n        zIndex: 999\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    model: {\r\n      get () {\r\n        return this.value\r\n      },\r\n      set (value) {\r\n        this.length = value ? value.replace(/<.*?>/g, '').replace(/&nbsp;/ig, ' ').length : 0\r\n        this.$emit('input', value)\r\n      }\r\n    }\r\n  },\r\n  props: {\r\n    type: [String],\r\n    value: {\r\n      type: String\r\n    },\r\n    maximumWords: {\r\n      type: Number\r\n    },\r\n    height: {\r\n      type: Number,\r\n      default: 280\r\n    },\r\n    toolbars: {\r\n      type: Array,\r\n      default: () => [\r\n        [\r\n          'fullscreen',\r\n          'source', '|', 'undo', 'redo', '|',\r\n          'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',\r\n          'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',\r\n          'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',\r\n          'directionalityltr', 'directionalityrtl', 'indent', '|',\r\n          'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'touppercase', 'tolowercase', '|',\r\n          'link', 'unlink', 'anchor', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',\r\n          'simpleupload', 'insertimage', 'emotion', 'scrawl',\r\n          'insertvideo',\r\n          'music', 'attachment', 'map', 'gmap', 'insertframe', 'insertcode', 'webapp', 'pagebreak', 'template', 'background', '|',\r\n          'horizontal', 'date', 'time', 'spechars', 'snapscreen', 'wordimage', '|',\r\n          'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts', '|',\r\n          'print', 'preview', 'searchreplace', 'drafts', 'help'\r\n        ]\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    blur () {\r\n      this.$emit('blur')\r\n    },\r\n    ready (editor) {\r\n      editor.addListener('blur', this.blur)\r\n      if (this.type === 'ta') {\r\n        editor.ready(() => {\r\n          editor.execCommand('fontfamily', 'CESI仿宋-GB2312') // 字体\r\n          editor.execCommand('lineheight', 2) // 行间距\r\n          editor.execCommand('fontsize', '21px') // 字号\r\n        })\r\n      } else {\r\n        editor.ready(() => {\r\n          editor.execCommand('fontfamily', '宋体') // 字体\r\n          editor.execCommand('lineheight', 2) // 行间距\r\n        })\r\n      }\r\n      this.editor = editor\r\n    },\r\n    down () {\r\n      this.editor.ui.setFullScreen(false)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.hello {\r\n  overflow: hidden;\r\n\r\n  .edui-editor {\r\n    border-radius: 0 !important;\r\n  }\r\n\r\n  .edui-default {\r\n    // z-index: 10000 !important; /* 你可以根据需要设置不同的层级值 */\r\n  }\r\n\r\n  .helloText {\r\n    white-space: nowrap;\r\n    border-top: 0;\r\n    line-height: 24px;\r\n    font-size: 14px;\r\n    font-family: Arial, Helvetica, Tahoma, Verdana, Sans-Serif;\r\n    text-align: right;\r\n    color: #aaa;\r\n    border: 1px solid #ccc;\r\n    border-top: 0;\r\n    padding-right: 6px;\r\n  }\r\n\r\n  h1,\r\n  h2 {\r\n    font-weight: normal;\r\n  }\r\n\r\n  ul {\r\n    list-style-type: none;\r\n    padding: 0;\r\n  }\r\n\r\n  li {\r\n    display: inline-block;\r\n    margin: 0 10px;\r\n  }\r\n\r\n  a {\r\n    color: #42b983;\r\n  }\r\n}\r\n</style>\r\n"]}]}