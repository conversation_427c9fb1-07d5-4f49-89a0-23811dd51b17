{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\zy\\xm\\pc\\qdzx\\product\\src\\router\\index.js", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\router\\index.js", "mtime": 1756368983502}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "files", "require", "context", "pages", "keys", "for<PERSON>ach", "key", "concat", "default", "use", "routes", "path", "name", "component", "children", "createRouter", "scroll<PERSON>eh<PERSON>or", "history", "pushState", "document", "URL", "router", "resetRouter", "newRouter", "matcher"], "sources": ["D:/zy/xm/pc/qdzx/product/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport VueRouter from 'vue-router'\r\n// import generalList from './module'\r\nconst files = require.context('./module', false, /\\.js$/)\r\nvar pages = []\r\nfiles.keys().forEach(key => {\r\n  pages = pages.concat(files(key).default)\r\n})\r\nVue.use(VueRouter)\r\n\r\nconst routes = [\r\n  {\r\n    path: '/app',\r\n    name: 'app',\r\n    component: () => import(/* app分享二维码 */'@/views/main/appShare/app')\r\n  },\r\n  {\r\n    path: '/switchpage',\r\n    name: 'switchpage',\r\n    component: () => import(/* 入口切换页 */'@/views/main/home/<USER>')\r\n  },\r\n  {\r\n    path: '/general',\r\n    name: 'general',\r\n    component: () => import(/* layout 布局页面 */'@/views/general/general'),\r\n    children: pages\r\n  },\r\n  {\r\n    path: '/login-help',\r\n    name: 'login-help',\r\n    component: () => import('@/views/main/login-help/login-help')\r\n  },\r\n  {\r\n    path: '/homeBox',\r\n    name: 'homeBox',\r\n    component: () => import(/* 智慧云脑首页 */'@/views/smartBrainLargeScreen/home/<USER>')\r\n  },\r\n  {\r\n    path: '/committeeStatisticsBox',\r\n    name: 'committeeStatisticsBox',\r\n    component: () => import(/* 智慧云脑委员统计页面 */'@/views/smartBrainLargeScreen/committeeStatistics/committeeStatisticsBox')\r\n  },\r\n  {\r\n    path: '/performanceStatisticsBox',\r\n    name: 'performanceStatisticsBox',\r\n    component: () => import(/* 智慧云脑履职统计页面 */'@/views/smartBrainLargeScreen/performanceStatistics/performanceStatisticsBox')\r\n  },\r\n  {\r\n    path: '/proposalStatisticsBox',\r\n    name: 'proposalStatisticsBox',\r\n    component: () => import(/* 智慧云脑履职统计页面 */'@/views/smartBrainLargeScreen/proposalStatistics/proposalStatisticsBox')\r\n  },\r\n  {\r\n    path: '/networkDiscussBox',\r\n    name: 'networkDiscussBox',\r\n    component: () => import(/* 智慧云脑网络议政页面 */'@/views/smartBrainLargeScreen/networkDiscuss/networkDiscussBox')\r\n  }\r\n]\r\n\r\nconst createRouter = () => new VueRouter({\r\n  scrollBehavior: () => {\r\n    history.pushState(null, null, document.URL)\r\n  },\r\n  routes\r\n})\r\n\r\nconst router = createRouter()\r\nexport { routes }\r\nexport function resetRouter () {\r\n  const newRouter = createRouter()\r\n  router.matcher = newRouter.matcher // reset router\r\n}\r\n\r\nexport default router\r\n"], "mappings": "AAAA,OAAOA,GAAP,MAAgB,KAAhB;AACA,OAAOC,SAAP,MAAsB,YAAtB,C,CACA;;AACA,MAAMC,KAAK,GAAGC,OAAO,CAACC,OAAR,CAAgB,UAAhB,EAA4B,KAA5B,EAAmC,OAAnC,CAAd;;AACA,IAAIC,KAAK,GAAG,EAAZ;AACAH,KAAK,CAACI,IAAN,GAAaC,OAAb,CAAqBC,GAAG,IAAI;EAC1BH,KAAK,GAAGA,KAAK,CAACI,MAAN,CAAaP,KAAK,CAACM,GAAD,CAAL,CAAWE,OAAxB,CAAR;AACD,CAFD;AAGAV,GAAG,CAACW,GAAJ,CAAQV,SAAR;AAEA,MAAMW,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,MADR;EAEEC,IAAI,EAAE,KAFR;EAGEC,SAAS,EAAE,MAAM;EAAO;EAAc,2BAArB;AAHnB,CADa,EAMb;EACEF,IAAI,EAAE,aADR;EAEEC,IAAI,EAAE,YAFR;EAGEC,SAAS,EAAE,MAAM;EAAO;EAAW,8BAAlB;AAHnB,CANa,EAWb;EACEF,IAAI,EAAE,UADR;EAEEC,IAAI,EAAE,SAFR;EAGEC,SAAS,EAAE,MAAM;EAAO;EAAiB,yBAAxB,CAHnB;EAIEC,QAAQ,EAAEX;AAJZ,CAXa,EAiBb;EACEQ,IAAI,EAAE,aADR;EAEEC,IAAI,EAAE,YAFR;EAGEC,SAAS,EAAE,MAAM,OAAO,oCAAP;AAHnB,CAjBa,EAsBb;EACEF,IAAI,EAAE,UADR;EAEEC,IAAI,EAAE,SAFR;EAGEC,SAAS,EAAE,MAAM;EAAO;EAAY,4CAAnB;AAHnB,CAtBa,EA2Bb;EACEF,IAAI,EAAE,yBADR;EAEEC,IAAI,EAAE,wBAFR;EAGEC,SAAS,EAAE,MAAM;EAAO;EAAgB,0EAAvB;AAHnB,CA3Ba,EAgCb;EACEF,IAAI,EAAE,2BADR;EAEEC,IAAI,EAAE,0BAFR;EAGEC,SAAS,EAAE,MAAM;EAAO;EAAgB,8EAAvB;AAHnB,CAhCa,EAqCb;EACEF,IAAI,EAAE,wBADR;EAEEC,IAAI,EAAE,uBAFR;EAGEC,SAAS,EAAE,MAAM;EAAO;EAAgB,wEAAvB;AAHnB,CArCa,EA0Cb;EACEF,IAAI,EAAE,oBADR;EAEEC,IAAI,EAAE,mBAFR;EAGEC,SAAS,EAAE,MAAM;EAAO;EAAgB,gEAAvB;AAHnB,CA1Ca,CAAf;;AAiDA,MAAME,YAAY,GAAG,MAAM,IAAIhB,SAAJ,CAAc;EACvCiB,cAAc,EAAE,MAAM;IACpBC,OAAO,CAACC,SAAR,CAAkB,IAAlB,EAAwB,IAAxB,EAA8BC,QAAQ,CAACC,GAAvC;EACD,CAHsC;EAIvCV;AAJuC,CAAd,CAA3B;;AAOA,MAAMW,MAAM,GAAGN,YAAY,EAA3B;AACA,SAASL,MAAT;AACA,OAAO,SAASY,WAAT,GAAwB;EAC7B,MAAMC,SAAS,GAAGR,YAAY,EAA9B;EACAM,MAAM,CAACG,OAAP,GAAiBD,SAAS,CAACC,OAA3B,CAF6B,CAEM;AACpC;AAED,eAAeH,MAAf"}]}