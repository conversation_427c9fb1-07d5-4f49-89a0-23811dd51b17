{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\InnovationExcellence.vue?vue&type=style&index=0&id=3e499de5&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\InnovationExcellence.vue", "mtime": 1752541693826}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouSW5ub3ZhdGlvbkV4Y2VsbGVuY2Ugew0KICBoZWlnaHQ6IDEwMCU7DQogIHdpZHRoOiAxMDAlOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KDQogIC5xZC1saXN0LXdyYXAgew0KICAgIGhlaWdodDogY2FsYygxMDAlIC0gODNweCk7DQogIH0NCiAgLnRhYmxlRGF0YSB7DQogICAgLmVsLXNjcm9sbGJhcl9fd3JhcCAuZWwtc2Nyb2xsYmFyX192aWV3IHRoIHsNCiAgICAgIGJhY2tncm91bmQ6ICNmNWY3ZmI7DQogICAgfQ0KICAgIGhlaWdodDogY2FsYygxMDAlIC0gMTMycHgpOw0KICB9DQogIC5idXR0b24tYm94IHsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgIG1hcmdpbi10b3A6IDIwcHg7DQogIH0NCiAgLmJ1dHRvbi1ib3ggLmVsLWJ1dHRvbiB7DQogICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgfQ0KICAuZWwtYnV0dG9uLS1kYW5nZXIuaXMtcGxhaW46aG92ZXIsDQogIC5lbC1idXR0b24tLWRhbmdlci5pcy1wbGFpbjpmb2N1cyB7DQogICAgLy8gY29sb3I6ICNmNTZjNmM7DQogICAgYmFja2dyb3VuZDogI2Y1NmM2YzsNCiAgICBjb2xvcjogI2ZmZjsNCiAgfQ0KICAuZWwtYnV0dG9uLS1zdWNjZXNzLmlzLXBsYWluOmhvdmVyLA0KICAuZWwtYnV0dG9uLS1zdWNjZXNzLmlzLXBsYWluOmZvY3VzIHsNCiAgICBiYWNrZ3JvdW5kOiAjNjdjMjNhOw0KICAgIGNvbG9yOiAjZmZmOw0KICB9DQogIC5kZWxCdG4gew0KICAgIGNvbG9yOiAjZjU2YzZjOw0KICB9DQogIC5wYWdpbmdfYm94IHsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICB9DQogIC5zY29yZUVkaXQgew0KICAgIHdpZHRoOiA3MDBweDsNCiAgICBoZWlnaHQ6IDEwMCU7DQogICAgcGFkZGluZzogMjRweDsNCiAgICAuZm9ybS1idXR0b24gew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgIH0NCiAgfQ0KICAuc2VhcmNoLWJveCB7DQogICAgLy90aXRsZeaWh+Wtl+agt+W8jw0KICAgIC5zZWFyY2gtdGl0bGUgew0KICAgICAgd2lkdGg6IDEzM3B4Ow0KICAgICAgaGVpZ2h0OiAxNnB4Ow0KICAgICAgZm9udC1zaXplOiAkdGV4dFNpemUxNjsNCiAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZyBTQzsNCiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgY29sb3I6ICMzMzMzMzM7DQogICAgICBsaW5lLWhlaWdodDogMzZweDsNCiAgICAgIG1hcmdpbi1sZWZ0OiAzMnB4Ow0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["InnovationExcellence.vue"], "names": [], "mappings": ";AAsaA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "InnovationExcellence.vue", "sourceRoot": "src/views/AssessmentOrgan/InnovationExcellence", "sourcesContent": ["<template>\r\n  <!-- 创新创优目标 -->\r\n  <div class=\"InnovationExcellence\">\r\n    <search-box @search-click=\"search\"\r\n                @reset-click=\"reset\"\r\n                title=\"创新创优目标筛选\">\r\n      <!--关键字查找框  -->\r\n      <zy-widget label=\"关键字\">\r\n        <el-input placeholder=\"请输入关键词\"\r\n                  v-model=\"keyword\"\r\n                  clearable\r\n                  @keyup.enter.native=\"search\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\"></div>\r\n        </el-input>\r\n      </zy-widget>\r\n\r\n      <!-- 时间查询 -->\r\n      <zy-widget label=\"起止时间\">\r\n        <el-date-picker v-model=\"time\"\r\n                        type=\"daterange\"\r\n                        range-separator=\"至\"\r\n                        start-placeholder=\"开始时间\"\r\n                        value-format=\"timestamp\"\r\n                        end-placeholder=\"结束时间\">\r\n        </el-date-picker>\r\n      </zy-widget>\r\n\r\n      <!-- 部门查询(用到机构树)  -->\r\n      <zy-widget label=\"部门查询\"\r\n                 v-permissions=\"'auth:innovation:department'\">\r\n        <zy-select v-model=\"searchParams.officeId\"\r\n                   clearable\r\n                   @keyup.enter.native=\"search\"\r\n                   placeholder=\"请选择部门\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\">\r\n          </div>\r\n        </zy-select>\r\n      </zy-widget>\r\n\r\n      <!-- 审核状态查询 -->\r\n      <zy-widget label=\"审核状态查询\">\r\n        <el-select v-model=\"searchParams.auditStatusParams\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择审核状态\">\r\n          <el-option v-for=\"item in auditStatusData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n\r\n    </search-box>\r\n\r\n    <div class=\"qd-list-wrap\">\r\n      <!-- 新增按钮 -->\r\n      <div class=\"qd-btn-box\">\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-plus\"\r\n                   @click=\"newData\">新增\r\n        </el-button>\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   v-permissions=\"'auth:innovation:checkPass'\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n        <el-button type=\"danger\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   v-permissions=\"'auth:innovation:checkNotPass'\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n      </div>\r\n\r\n      <div class=\"tableData\">\r\n        <zy-table>\r\n          <el-table :data=\"tableData\"\r\n                    slot=\"zytable\"\r\n                    row-key=\"id\"\r\n                    ref=\"multipleTable\"\r\n                    @select=\"selected\"\r\n                    @select-all=\"selectedAll\"\r\n                    :tree-props=\"{children: 'children'}\">\r\n            <el-table-column type=\"selection\"\r\n                             fixed=\"left\"\r\n                             width=\"50\">\r\n            </el-table-column>\r\n            <el-table-column label=\"标题\"\r\n                             show-overflow-tooltip\r\n                             prop=\"title\"\r\n                             min-width=\"330px\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"modify(scope.row)\"\r\n                           size=\"small\"> {{scope.row.title}}</el-button>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"部门\"\r\n                             width=\"130px\"\r\n                             prop=\"officeName\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{scope.row.officeName }} </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"发布时间\"\r\n                             width=\"170\"\r\n                             prop=\"publishTime\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{$format(scope.row.publishTime).substr(0,16)}} </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"时限要求\"\r\n                             width=\"170\"\r\n                             prop=\"endTime\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{$format(scope.row.endTime).substr(0,16)}} </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"分值\"\r\n                             align=\"center\"\r\n                             width=\"70\"\r\n                             prop=\"score\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"审核状态\"\r\n                             width=\"120\"\r\n                             prop=\"auditStatus\">\r\n\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"重点工作\"\r\n                             align=\"center\"\r\n                             width=\"100\"\r\n                             prop=\"isMainwork\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"类型\"\r\n                             width=\"120\"\r\n                             show-overflow-tooltip\r\n                             prop=\"classify\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"完成情况\"\r\n                             width=\"100\"\r\n                             fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"finishStatus(scope.row)\"\r\n                           size=\"small\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 完成情况\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"操作\"\r\n                             width=\"120\"\r\n                             fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"handleClick(scope.row)\"\r\n                           size=\"small\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 编辑\r\n                </el-button>\r\n\r\n                <el-button type=\"text\"\r\n                           size=\"small\"\r\n                           @click=\"handleDelete(scope.row.id)\"\r\n                           :class=\"scope.row.auditStatus == '审核通过' ? '' : 'delBtn'\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 删除\r\n                </el-button>\r\n\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </zy-table>\r\n      </div>\r\n\r\n      <div class=\"paging_box\">\r\n        <el-pagination @size-change=\"handleSizeChange\"\r\n                       @current-change=\"handleCurrentChange\"\r\n                       :current-page.sync=\"pageNo\"\r\n                       :page-sizes=\"[10, 20, 30, 40]\"\r\n                       :page-size.sync=\"pageSize\"\r\n                       background\r\n                       layout=\"total, prev, pager, next, sizes, jumper\"\r\n                       :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <zy-pop-up v-model=\"show\"\r\n               :title=\"id?'编辑':'新增'\">\r\n      <InnovationNew :id=\"id\"\r\n                     :memberType=\"memberType\"\r\n                     @newCallback=\"newCallback\">\r\n      </InnovationNew>\r\n    </zy-pop-up>\r\n    <zy-pop-up v-model=\"showFinish\"\r\n               class=\"titleStyle\"\r\n               title=\"完成情况\">\r\n      <finishDetail :id=\"id\"\r\n                    :memberType=\"memberType\"\r\n                    @newCallback=\"newCallback\">\r\n      </finishDetail>\r\n    </zy-pop-up>\r\n    <zy-pop-up v-model=\"showTitleDetail\"\r\n               class=\"titleStyle\"\r\n               title=\"详情\"\r\n               :beforeClose=\"updateList\">\r\n      <titleDetail :id=\"id\"\r\n                   :memberType=\"memberType\"\r\n                   @newCallback=\"newCallback\">\r\n      </titleDetail>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport tableData from '@mixins/tableData'\r\nimport InnovationNew from './InnovationNew.vue'\r\nimport finishDetail from './finishDetail'\r\nimport titleDetail from './titleDetail'\r\n\r\nexport default {\r\n  name: 'InnovationExcellence',\r\n  components: {\r\n    InnovationNew,\r\n    finishDetail,\r\n    titleDetail\r\n\r\n  },\r\n  data () {\r\n    return {\r\n      officeData: [], // 机构树,\r\n      auditStatusData: [], // 审核状态\r\n      searchParams: {\r\n        officeId: '',\r\n        auditStatusParams: ''\r\n      },\r\n      time: [],\r\n      keyword: '',\r\n      publishStartTime: '',\r\n      // ***\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      total: 10,\r\n      currentRow: null,\r\n      show: false,\r\n      showFinish: false,\r\n      showTitleDetail: false,\r\n\r\n      ids: '',\r\n      id: '',\r\n      choose: [],\r\n      selectObj: [],\r\n      selectData: [],\r\n      tableData: []\r\n    }\r\n  },\r\n  props: ['memberType'],\r\n  mixins: [tableData],\r\n  mounted () {\r\n    this.getInnovationExcellenceList()\r\n    this.treeList()\r\n    this.dictionaryPubkvs()\r\n  },\r\n  methods: {\r\n    /**\r\n      *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluate_audit_status'\r\n      })\r\n      var { data } = res\r\n      this.auditStatusData = data.evaluate_audit_status\r\n    },\r\n\r\n    /**\r\n      *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    updateList () {\r\n      this.showTitleDetail = false\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    handleDelete (ids) {\r\n      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$api.AssessmentOrgan.reqDelInnovationExcellence({ ids }).then((res) => {\r\n          if (res.errcode === 200) {\r\n            this.getInnovationExcellenceList()// 删除后更新页面\r\n            this.$message.success('删除成功')\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('取消删除')\r\n        // this.getMonthlyWorkRecordlist()\r\n        return false\r\n      })\r\n    },\r\n    newCallback () {\r\n      this.getInnovationExcellenceList()\r\n      this.show = false\r\n      this.showFinish = false\r\n      this.showTitleDetail = false\r\n    },\r\n    // 请求后台数据 获取列表信息\r\n    async getInnovationExcellenceList () {\r\n      const res = await this.$api.AssessmentOrgan.reqInnovationExcellenceList({\r\n        pageNo: this.pageNo,\r\n        pageSize: this.pageSize,\r\n        keyword: this.keyword,\r\n        publishStartTime: this.time[0],\r\n        publishEndTime: this.time[1],\r\n        // // sedateId: this.sedateId\r\n        // sedateId: this.selectedYear\r\n        memberType: this.memberType,\r\n        officeId: this.searchParams.officeId, // 部门查询\r\n        auditStatus: this.searchParams.auditStatusParams // 审核状态\r\n      })\r\n      var { data, total } = res\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n      this.selectObj = []\r\n      this.selectData = []\r\n      this.$nextTick(function () {\r\n        this.memoryChecked()\r\n      })\r\n    },\r\n    // paging_box 里面用到的方法\r\n    handleSizeChange () {\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    search () {\r\n      this.pageNo = 1\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    reset () {\r\n      this.keyword = ''\r\n      this.publishStartTime = ''\r\n      this.time.splice(0, this.time.length)\r\n      this.searchParams.officeId = ''\r\n      this.searchParams.auditStatusParams = ''\r\n      // 重置后重新调用一次列表信息\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    // 新增\r\n    newData () {\r\n      this.id = ''\r\n      this.show = true\r\n    },\r\n    // 标题详情 modify\r\n    modify (row) {\r\n      this.id = row.id\r\n      this.showTitleDetail = true\r\n    },\r\n    // 完成情况\r\n    finishStatus (row) {\r\n      this.id = row.id\r\n      this.showFinish = true\r\n    },\r\n    // 编辑\r\n    handleClick (row) {\r\n      this.id = row.id\r\n      this.show = true\r\n    },\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckInnovation(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 审核 (创新创优)\r\n    async getCheckInnovation (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckInnovation({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getInnovationExcellenceList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.InnovationExcellence {\r\n  height: 100%;\r\n  width: 100%;\r\n  overflow: hidden;\r\n\r\n  .qd-list-wrap {\r\n    height: calc(100% - 83px);\r\n  }\r\n  .tableData {\r\n    .el-scrollbar__wrap .el-scrollbar__view th {\r\n      background: #f5f7fb;\r\n    }\r\n    height: calc(100% - 132px);\r\n  }\r\n  .button-box {\r\n    background: #fff;\r\n    margin-top: 20px;\r\n  }\r\n  .button-box .el-button {\r\n    background: #fff;\r\n  }\r\n  .el-button--danger.is-plain:hover,\r\n  .el-button--danger.is-plain:focus {\r\n    // color: #f56c6c;\r\n    background: #f56c6c;\r\n    color: #fff;\r\n  }\r\n  .el-button--success.is-plain:hover,\r\n  .el-button--success.is-plain:focus {\r\n    background: #67c23a;\r\n    color: #fff;\r\n  }\r\n  .delBtn {\r\n    color: #f56c6c;\r\n  }\r\n  .paging_box {\r\n    background: #fff;\r\n  }\r\n  .scoreEdit {\r\n    width: 700px;\r\n    height: 100%;\r\n    padding: 24px;\r\n    .form-button {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n  .search-box {\r\n    //title文字样式\r\n    .search-title {\r\n      width: 133px;\r\n      height: 16px;\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-left: 32px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}