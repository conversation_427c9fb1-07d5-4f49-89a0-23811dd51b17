{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\UEditor\\UEditor.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\UEditor\\UEditor.vue", "mtime": 1752509077000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAaA;AACA;EACAA,eADA;EAEAC;IACAC,cADA,CACA;;EADA,CAFA;;EAKAC;IACA;MACAC,SADA;MAEAC;QACA;QACAC,wBAFA;QAGAC,yBAHA;QAIAC,gBAJA;QAKA;QACAC,+BANA;QAOA;QACAC,yBARA;QASA;QACAC,wDAVA;QAWA;QACA;QACAC,8BAbA;QAcA;QACAC,eAfA;QAgBAC,2EAhBA;QAiBAC;UACAC,oBADA;UACA;UACAC,iBAFA;UAEA;UACAC,sBAHA;UAGA;UACAC,iBAJA;UAIA;UACAC,wBALA;UAKA;UACAC,kBANA;UAMA;UACAC,oBAPA;UAOA;UACAC,sBARA;UAQA;UACAC,sBATA;UASA;UACA;UACAC;YAAAC;UAAA,CAXA;UAYAC,YAZA;UAYA;UACAC,kBAbA;UAaA;UACAC,aAdA;UAeAC;QAfA,CAjBA;QAkCAC,aACA;UAAAC;UAAAhC;UAAAiC;QAAA,CADA,EAEA;UAAAD;UAAAhC;UAAAiC;QAAA,CAFA,EAGA;UAAAD;UAAAhC;UAAAiC;QAAA,CAHA,EAIA;UAAAD;UAAAhC;UAAAiC;QAAA,CAJA,EAKA;UAAAD;UAAAhC;UAAAiC;QAAA,CALA,EAMA;UAAAD;UAAAhC;UAAAiC;QAAA,CANA,EAOA;UAAAD;UAAAhC;UAAAiC;QAAA,CAPA,EAQA;UAAAD;UAAAhC;UAAAiC;QAAA,CARA,EASA;UAAAD;UAAAhC;UAAAiC;QAAA,CATA,EAUA;UAAAD;UAAAhC;UAAAiC;QAAA,CAVA,EAWA;UAAAD;UAAAhC;UAAAiC;QAAA,CAXA,EAYA;UAAAD;UAAAhC;UAAAiC;QAAA,CAZA,EAaA;UAAAD;UAAAhC;UAAAiC;QAAA,CAbA,EAcA;UAAAD;UAAAhC;UAAAiC;QAAA,CAdA,EAeA;UAAAD;UAAAhC;UAAAiC;QAAA,CAfA,EAgBA;UAAAD;UAAAhC;UAAAiC;QAAA,CAhBA,EAiBA;UAAAD;UAAAhC;UAAAiC;QAAA,CAjBA,CAlCA;QAqDAC,uBArDA;QAsDAC;MAtDA;IAFA;EA2DA,CAjEA;;EAkEAC;IACAC;MACAC;QACA;MACA,CAHA;;MAIAC;QACA;QACA;MACA;;IAPA;EADA,CAlEA;EA6EAC;IACAC,cADA;IAEAC;MACAD;IADA,CAFA;IAKAE;MACAF;IADA,CALA;IAQAG;MACAH,YADA;MAEAI;IAFA,CARA;IAYAX;MACAO,WADA;MAEAI,gBACA,CACA,YADA,EAEA,QAFA,EAEA,GAFA,EAEA,MAFA,EAEA,MAFA,EAEA,GAFA,EAGA,MAHA,EAGA,QAHA,EAGA,WAHA,EAGA,YAHA,EAGA,eAHA,EAGA,aAHA,EAGA,WAHA,EAGA,cAHA,EAGA,aAHA,EAGA,aAHA,EAGA,YAHA,EAGA,YAHA,EAGA,GAHA,EAGA,WAHA,EAGA,WAHA,EAGA,mBAHA,EAGA,qBAHA,EAGA,WAHA,EAGA,UAHA,EAGA,GAHA,EAIA,eAJA,EAIA,kBAJA,EAIA,YAJA,EAIA,GAJA,EAKA,aALA,EAKA,WALA,EAKA,YALA,EAKA,UALA,EAKA,GALA,EAMA,mBANA,EAMA,mBANA,EAMA,QANA,EAMA,GANA,EAOA,aAPA,EAOA,eAPA,EAOA,cAPA,EAOA,gBAPA,EAOA,GAPA,EAOA,aAPA,EAOA,aAPA,EAOA,GAPA,EAQA,MARA,EAQA,QARA,EAQA,QARA,EAQA,GARA,EAQA,WARA,EAQA,WARA,EAQA,YARA,EAQA,aARA,EAQA,GARA,EASA,cATA,EASA,aATA,EASA,SATA,EASA,QATA,EAUA,aAVA,EAWA,OAXA,EAWA,YAXA,EAWA,KAXA,EAWA,MAXA,EAWA,aAXA,EAWA,YAXA,EAWA,QAXA,EAWA,WAXA,EAWA,UAXA,EAWA,YAXA,EAWA,GAXA,EAYA,YAZA,EAYA,MAZA,EAYA,MAZA,EAYA,UAZA,EAYA,YAZA,EAYA,WAZA,EAYA,GAZA,EAaA,aAbA,EAaA,aAbA,EAaA,4BAbA,EAaA,WAbA,EAaA,WAbA,EAaA,WAbA,EAaA,WAbA,EAaA,YAbA,EAaA,YAbA,EAaA,WAbA,EAaA,cAbA,EAaA,aAbA,EAaA,aAbA,EAaA,QAbA,EAaA,GAbA,EAcA,OAdA,EAcA,SAdA,EAcA,eAdA,EAcA,QAdA,EAcA,MAdA,CADA;IAFA;EAZA,CA7EA;EA+GAC;IACAC;MACA;IACA,CAHA;;IAIAC;MACAC;;MACA;QACAA;UACAA,kDADA,CACA;;UACAA,oCAFA,CAEA;;UACAA,uCAHA,CAGA;QACA,CAJA;MAKA,CANA,MAMA;QACAA;UACAA,uCADA,CACA;;UACAA,oCAFA,CAEA;QACA,CAHA;MAIA;;MACA;IACA,CAnBA;;IAoBAC;MACA;IACA;;EAtBA;AA/GA", "names": ["name", "components", "VueUeditorWrap", "data", "length", "myConfig", "autoHeightEnabled", "elementPathEnabled", "wordCount", "initialFrameHeight", "initialFrame<PERSON><PERSON>th", "serverUrl", "UEDITOR_HOME_URL", "contextMenu", "lineheight", "autotypeset", "mergeEmptyline", "removeClass", "removeEmptyline", "textAlign", "imageBlockLine", "pasteFilter", "clearFontSize", "clearFontFamily", "removeEmptyNode", "removeTagNames", "标签名字", "indent", "indentValue", "bdc2sb", "tobdc", "fontfamily", "label", "val", "toolbars", "zIndex", "computed", "model", "get", "set", "props", "type", "value", "maximumWords", "height", "default", "methods", "blur", "ready", "editor", "down"], "sourceRoot": "src/components/UEditor", "sources": ["UEditor.vue"], "sourcesContent": ["<template>\r\n  <div class=\"hello\">\r\n    <vue-ueditor-wrap v-model=\"model\" @ready=\"ready\" :config=\"myConfig\"></vue-ueditor-wrap>\r\n    <div class=\"helloText\" v-if=\"maximumWords < length\">当前已输入{{ length }}个字符，您最多可以输入{{ maximumWords\r\n    }}个字符，当前已经超出{{ length - maximumWords }}个字符。\r\n    </div>\r\n    <div class=\"helloText\" v-if=\"maximumWords > length\">当前已输入{{ length }}个字符，您还可以输入{{ maximumWords - length\r\n    }}个字符。</div>\r\n    <div class=\"helloText\" v-if=\"!maximumWords\">当前已输入{{ length }}个字符</div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport VueUeditorWrap from './vue-ueditor-wrap.min.js'\r\nexport default {\r\n  name: 'UEditor',\r\n  components: {\r\n    VueUeditorWrap // eslint-disable-line\r\n  },\r\n  data () {\r\n    return {\r\n      length: 0,\r\n      myConfig: {\r\n        // 是否跟随内容撑开\r\n        autoHeightEnabled: false,\r\n        elementPathEnabled: false,\r\n        wordCount: false,\r\n        // 高度\r\n        initialFrameHeight: this.height,\r\n        // 宽度\r\n        initialFrameWidth: '100%',\r\n        // 图片上传的路径\r\n        serverUrl: `${this.$api.general.baseURL()}/ueditor/exec`,\r\n        // serverUrl: `http://*************/lzt/ueditor/exec`,\r\n        // 资源依赖的路径\r\n        UEDITOR_HOME_URL: './UEditor/',\r\n        // 鼠标右键的功能，但是一般不用编辑器带的功能，因为编辑器自带的不能右键复制，所以这个变量设置为空数组\r\n        contextMenu: [],\r\n        lineheight: [1, 1.25, 1.5, 1.75, 2, 2.25, 2.5, 2.75, 3, 3.25, 3.5, 3.75, 4],\r\n        autotypeset: {\r\n          mergeEmptyline: true, // 合并空行\r\n          removeClass: true, // 去掉冗余的class\r\n          removeEmptyline: false, // 去掉空行\r\n          textAlign: 'left', // 段落的排版方式，可以是 left,right,center,justify 去掉这个属性表示不执行排版\r\n          imageBlockLine: 'center', // 图片的浮动方式，独占一行剧中,左右浮动，默认: center,left,right,none 去掉这个属性表示不执行排版\r\n          pasteFilter: false, // 根据规则过滤没事粘贴进来的内容\r\n          clearFontSize: false, // 去掉所有的内嵌字号，使用编辑器默认的字号\r\n          clearFontFamily: false, // 去掉所有的内嵌字体，使用编辑器默认的字体\r\n          removeEmptyNode: false, // 去掉空节点\r\n          // 可以去掉的标签\r\n          removeTagNames: { 标签名字: 1 },\r\n          indent: true, // 行首缩进\r\n          indentValue: '2em', // 行首缩进的大小\r\n          bdc2sb: false,\r\n          tobdc: false\r\n        },\r\n        fontfamily: [\r\n          { label: '', name: 'songti', val: '宋体, SimSun' },\r\n          { label: '仿宋', name: 'fangsong', val: '仿宋, FangSong' },\r\n          { label: '仿宋_GB2312', name: 'fangsong', val: '仿宋_GB2312, 仿宋, FangSong' },\r\n          { label: '方正小标宋_GBK', name: '方正小标宋_GBK', val: '方正小标宋_GBK, 宋体, SimSun' },\r\n          { label: '方正仿宋_GBK', name: '方正仿宋_GBK', val: '方正仿宋_GBK, 仿宋, FangSong' },\r\n          { label: '方正楷体_GBK', name: '方正楷体_GBK', val: '方正楷体_GBK, 楷体, SimKai' },\r\n          { label: '方正黑体_GBK', name: '方正黑体_GBK', val: '方正黑体_GBK, 黑体, SimHei' },\r\n          { label: '', name: 'kaiti', val: '楷体, 楷体_GB2312, SimKai' },\r\n          { label: '', name: 'yahei', val: '微软雅黑, Microsoft YaHei' },\r\n          { label: '', name: 'heiti', val: '黑体, SimHei' },\r\n          { label: '', name: 'lishu', val: '隶书, SimLi' },\r\n          { label: '', name: 'andaleMono', val: 'andale mono' },\r\n          { label: '', name: 'arial', val: 'arial, helvetica,sans-serif' },\r\n          { label: '', name: 'arialBlack', val: 'arial black,avant garde' },\r\n          { label: '', name: 'comicSansMs', val: 'comic sans ms' },\r\n          { label: '', name: 'impact', val: 'impact,chicago' },\r\n          { label: '', name: 'timesNewRoman', val: 'times new roman' }\r\n        ],\r\n        toolbars: this.toolbars,\r\n        zIndex: 999\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    model: {\r\n      get () {\r\n        return this.value\r\n      },\r\n      set (value) {\r\n        this.length = value ? value.replace(/<.*?>/g, '').replace(/&nbsp;/ig, ' ').length : 0\r\n        this.$emit('input', value)\r\n      }\r\n    }\r\n  },\r\n  props: {\r\n    type: [String],\r\n    value: {\r\n      type: String\r\n    },\r\n    maximumWords: {\r\n      type: Number\r\n    },\r\n    height: {\r\n      type: Number,\r\n      default: 280\r\n    },\r\n    toolbars: {\r\n      type: Array,\r\n      default: () => [\r\n        [\r\n          'fullscreen',\r\n          'source', '|', 'undo', 'redo', '|',\r\n          'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',\r\n          'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',\r\n          'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',\r\n          'directionalityltr', 'directionalityrtl', 'indent', '|',\r\n          'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'touppercase', 'tolowercase', '|',\r\n          'link', 'unlink', 'anchor', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',\r\n          'simpleupload', 'insertimage', 'emotion', 'scrawl',\r\n          'insertvideo',\r\n          'music', 'attachment', 'map', 'gmap', 'insertframe', 'insertcode', 'webapp', 'pagebreak', 'template', 'background', '|',\r\n          'horizontal', 'date', 'time', 'spechars', 'snapscreen', 'wordimage', '|',\r\n          'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts', '|',\r\n          'print', 'preview', 'searchreplace', 'drafts', 'help'\r\n        ]\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    blur () {\r\n      this.$emit('blur')\r\n    },\r\n    ready (editor) {\r\n      editor.addListener('blur', this.blur)\r\n      if (this.type === 'ta') {\r\n        editor.ready(() => {\r\n          editor.execCommand('fontfamily', 'CESI仿宋-GB2312') // 字体\r\n          editor.execCommand('lineheight', 2) // 行间距\r\n          editor.execCommand('fontsize', '21px') // 字号\r\n        })\r\n      } else {\r\n        editor.ready(() => {\r\n          editor.execCommand('fontfamily', '宋体') // 字体\r\n          editor.execCommand('lineheight', 2) // 行间距\r\n        })\r\n      }\r\n      this.editor = editor\r\n    },\r\n    down () {\r\n      this.editor.ui.setFullScreen(false)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.hello {\r\n  overflow: hidden;\r\n\r\n  .edui-editor {\r\n    border-radius: 0 !important;\r\n  }\r\n\r\n  .edui-default {\r\n    // z-index: 10000 !important; /* 你可以根据需要设置不同的层级值 */\r\n  }\r\n\r\n  .helloText {\r\n    white-space: nowrap;\r\n    border-top: 0;\r\n    line-height: 24px;\r\n    font-size: 14px;\r\n    font-family: Arial, Helvetica, Tahoma, Verdana, Sans-Serif;\r\n    text-align: right;\r\n    color: #aaa;\r\n    border: 1px solid #ccc;\r\n    border-top: 0;\r\n    padding-right: 6px;\r\n  }\r\n\r\n  h1,\r\n  h2 {\r\n    font-weight: normal;\r\n  }\r\n\r\n  ul {\r\n    list-style-type: none;\r\n    padding: 0;\r\n  }\r\n\r\n  li {\r\n    display: inline-block;\r\n    margin: 0 10px;\r\n  }\r\n\r\n  a {\r\n    color: #42b983;\r\n  }\r\n}\r\n</style>\r\n"]}]}