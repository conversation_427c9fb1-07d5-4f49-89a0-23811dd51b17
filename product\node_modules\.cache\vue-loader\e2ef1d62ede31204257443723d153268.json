{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\sinceManagement-zx\\SinceReport\\SinceReportDetails.vue?vue&type=style&index=0&id=30d68ccc&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\sinceManagement-zx\\SinceReport\\SinceReportDetails.vue", "mtime": 1752541697058}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouU3VnZ2VzdEJpbGxEZXRhaWxzIHsNCiAgICBwYWRkaW5nLWJvdHRvbTogMjRweDsNCn0NCg=="}, {"version": 3, "sources": ["SinceReportDetails.vue"], "names": [], "mappings": ";AAkEA;AACA;AACA", "file": "SinceReportDetails.vue", "sourceRoot": "src/views/sinceManagement-zx/SinceReport", "sourcesContent": ["<template>\r\n  <div class=\"SuggestBillDetails details\">\r\n    <div class=\"details-title\">{{type?'我的年度履职报告':'委员年度履职报告'}}</div>\r\n    <div class=\"details-item-box\">\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">姓名</div>\r\n        <div class=\"details-item-value\">{{details.userName}}</div>\r\n      </div>\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">界别</div>\r\n        <div class=\"details-item-value\">{{details.deleId}}</div>\r\n      </div>\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">委员证号</div>\r\n        <div class=\"details-item-value\">{{details.memberNo}}</div>\r\n      </div>\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">年份</div>\r\n        <div class=\"details-item-value\">{{details.year}}</div>\r\n      </div>\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">附件</div>\r\n        <div class=\"details-item-value\">\r\n          <div\r\n            class=\"details-item-file\"\r\n            v-for=\"(item, index) in details.attachmentList\"\r\n            :key=\"index\"\r\n            @click=\"fileClick(item)\"\r\n          >{{item.fileName}}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"details-item-content-label\">报告内容</div>\r\n      <div\r\n        class=\"details-item-content\"\r\n        v-html=\"details.content\"\r\n      ></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data () {\r\n    return {\r\n      details: {}\r\n    }\r\n  },\r\n  props: ['id', 'type'],\r\n  mounted () {\r\n    if (this.id) {\r\n      this.dutyreportInfo()\r\n    }\r\n  },\r\n  methods: {\r\n    async dutyreportInfo () {\r\n      const res = await this.$api.sinceManagement.dutyreportInfo(this.id)\r\n      console.log(res)\r\n      var { data } = res\r\n      this.details = data\r\n    },\r\n    fileClick (data) {\r\n      this.$api.proposal.downloadFile({ id: data.id }, data.fileName)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestBillDetails {\r\n    padding-bottom: 24px;\r\n}\r\n</style>\r\n"]}]}