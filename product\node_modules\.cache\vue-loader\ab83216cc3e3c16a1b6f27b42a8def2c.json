{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tree\\zy-tree.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tree\\zy-tree.vue", "mtime": 1752541693607}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICd6eVRyZWUnLA0KICBkYXRhICgpIHsNCiAgICByZXR1cm4gew0KICAgICAgaWQ6IHRoaXMudmFsdWUNCiAgICB9DQogIH0sDQogIHByb3BzOiB7DQogICAgdmFsdWU6IFtTdHJpbmcsIE51bWJlciwgQXJyYXksIE9iamVjdF0sDQogICAgLy8g5pWw5o2uDQogICAgdHJlZTogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgLy8g5qCR57uT5p6E6YWN572uDQogICAgcHJvcHM6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBjaGlsZHJlbjogJ2NoaWxkcmVuJywNCiAgICAgICAgICBsYWJlbDogJ2xhYmVsJw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvLyDmmK/lkKbnpoHnlKgNCiAgICBkaXNhYmxlZDogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICAvLyDlrr3luqYNCiAgICB3aWR0aDogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJzI5NicNCiAgICB9LA0KICAgIG5vZGVLZXk6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICdpZCcNCiAgICB9LA0KICAgIGFueWtleTogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiBmdW5jdGlvbiAoKSB7DQogICAgICAgIHJldHVybiBbXQ0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgbW9kZWw6IHsNCiAgICBwcm9wOiAndmFsdWUnLA0KICAgIGV2ZW50OiAnaWQnDQogIH0sDQogIHdhdGNoOiB7DQogICAgdmFsdWUgKHZhbCkgew0KICAgICAgaWYgKHZhbCkgew0KICAgICAgICB0aGlzLmlkID0gdmFsDQogICAgICB9DQogICAgfSwNCiAgICBpZCAodmFsKSB7DQogICAgICB0aGlzLiRlbWl0KCdpZCcsIHZhbCkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBzZWxlY3RlZENsaWNrIChpdGVtKSB7DQogICAgICB0aGlzLiRlbWl0KCdvbi10cmVlLWNsaWNrJywgaXRlbSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["zy-tree.vue"], "names": [], "mappings": ";AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-tree.vue", "sourceRoot": "src/components/zy-tree", "sourcesContent": ["<template>\r\n  <div class=\"zy-tree\">\r\n    <el-scrollbar class=\"zy-tree-box\">\r\n      <zy-tree-components\r\n        :tree=\"tree\"\r\n        v-model=\"id\"\r\n        :anykey=\"anykey\"\r\n        :props=\"props\"\r\n        :node-key=\"nodeKey\"\r\n        @on-tree-click=\"selectedClick\"\r\n      ></zy-tree-components>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyTree',\r\n  data () {\r\n    return {\r\n      id: this.value\r\n    }\r\n  },\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    // 数据\r\n    tree: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 宽度\r\n    width: {\r\n      type: String,\r\n      default: '296'\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    anykey: {\r\n      type: Array,\r\n      default: function () {\r\n        return []\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.id = val\r\n      }\r\n    },\r\n    id (val) {\r\n      this.$emit('id', val)\r\n    }\r\n  },\r\n  methods: {\r\n    selectedClick (item) {\r\n      this.$emit('on-tree-click', item)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './zy-tree.scss';\r\n</style>\r\n"]}]}