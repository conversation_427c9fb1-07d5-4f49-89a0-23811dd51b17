{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-sliding\\zy-sliding.vue?vue&type=style&index=0&id=6ec30be2&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-sliding\\zy-sliding.vue", "mtime": 1752541693576}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3p5LXNsaWRpbmcuc2NzcyI7DQo="}, {"version": 3, "sources": ["zy-sliding.vue"], "names": [], "mappings": ";AAwNA", "file": "zy-sliding.vue", "sourceRoot": "src/components/zy-sliding", "sourcesContent": ["<template>\r\n  <div class=\"zy-sliding\"\r\n       ref=\"zy-sliding\">\r\n    <div class=\"zy-sliding-box\">\r\n      <div class=\"zy-sliding-item-box\">\r\n        <div :class=\"['zy-sliding-item',item.class?'zy-sliding-item-a':'']\"\r\n             v-for=\"(item, index) in slidingList\"\r\n             @click=\"slidingClick(item)\"\r\n             :key=\"index\">\r\n          {{item.name}}\r\n        </div>\r\n        <div class=\"zy-sliding-item-sliding\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zySliding',\r\n  data () {\r\n    return {\r\n      slidingId: this.value,\r\n      slidingList: []\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String\r\n    },\r\n    sliding: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    props: {\r\n      type: Object,\r\n      default: () => { }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  mounted () {\r\n    this.slidingData(this.deepCopy(this.sliding))\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.slidingList.forEach(item => {\r\n          if (item.id === this.value) {\r\n            this.slidingClick(item, true)\r\n          }\r\n        })\r\n      }\r\n    },\r\n    sliding (val) {\r\n      this.slidingData(this.deepCopy(this.sliding))\r\n    },\r\n    slidingList (val) {\r\n      if (val.length) {\r\n        this.slidingList.forEach(item => {\r\n          if (item.id === this.value) {\r\n            this.slidingClick(item)\r\n          }\r\n        })\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    slidingClick (data, type) {\r\n      if (this.value !== data.id) {\r\n        this.$emit('id', data.id)\r\n        this.returnData(data)\r\n      }\r\n      if (type) {\r\n        this.returnData(data)\r\n      }\r\n      this.slidingList.forEach(item => {\r\n        item.class = false\r\n        if (item.id === data.id) {\r\n          item.class = true\r\n        }\r\n      })\r\n      this.$nextTick(() => {\r\n        this.slidingBox()\r\n        this.slidingIocation()\r\n      })\r\n    },\r\n    returnData (data) {\r\n      var arr = []\r\n      this.sliding.forEach(item => {\r\n        if (this.props) {\r\n          if (data.id === item[this.props.id]) {\r\n            arr = item\r\n          }\r\n        } else {\r\n          if (data.id === item.id) {\r\n            arr = item\r\n          }\r\n        }\r\n      })\r\n      this.$emit('sliding-click', arr)\r\n    },\r\n    slidingIocation () {\r\n      const slidingItem = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-a')\r\n      const sliding = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-sliding')\r\n      sliding.style.width = `${slidingItem.offsetWidth}px`\r\n      sliding.style.transform = `translateX(${slidingItem.offsetLeft}px)`\r\n      sliding.style.transitionDuration = '.3s'\r\n    },\r\n    slidingBox () {\r\n      var sliding = this.$refs['zy-sliding'].querySelector('.zy-sliding-box')\r\n      var itemBox = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-box')\r\n      var item = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-a')\r\n      if (sliding.offsetWidth < itemBox.offsetWidth) {\r\n        itemBox.style.transform = 'translateX(0px)'\r\n        itemBox.style.transitionDuration = '.4s'\r\n        if (itemBox.offsetWidth === item.offsetLeft + item.offsetWidth) {\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - sliding.offsetWidth}px)`\r\n        } else if (sliding.offsetWidth / 2 > itemBox.offsetWidth - item.offsetLeft) {\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - sliding.offsetWidth}px)`\r\n        } else {\r\n          itemBox.style.transform = `translateX(-${item.offsetLeft - sliding.offsetWidth / 2}px)`\r\n        }\r\n      }\r\n    },\r\n    slidingData (data) {\r\n      this.initData(data)\r\n      this.slidingList = data\r\n    },\r\n    initData (items) {\r\n      items.forEach((item, index) => {\r\n        if (this.props) {\r\n          if ((typeof item.id) === 'undefined') { // eslint-disable-line\r\n            item.id = item[this.props.id]\r\n          }\r\n          if ((typeof item.name) === 'undefined') { // eslint-disable-line\r\n            item.name = item[this.props.name]\r\n          }\r\n        }\r\n        if ((typeof item.class) === 'undefined') { // eslint-disable-line\r\n          item.class = false\r\n        }\r\n        if (this.value === item.id) {\r\n          item.class = true\r\n          this.$emit('sliding-click', item)\r\n        }\r\n      })\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    },\r\n    makeData (data) {\r\n      const t = this.type(data)\r\n      let o\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (let i = 0; i < data.length; i++) {\r\n          o.push(this.makeData(data[i]))\r\n        }\r\n      } else if (t === 'object') {\r\n        for (const i in data) {\r\n          if (i != 'class') {// eslint-disable-line\r\n            o[i] = this.makeData(data[i])\r\n          }\r\n        }\r\n      }\r\n      return o\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-sliding.scss\";\r\n</style>\r\n"]}]}