{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\BusinessObjectives.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\BusinessObjectives.vue", "mtime": 1752541693782}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAkOA;AACA;AACA;AACA;AACA;EACAA,0BADA;EAEAC;IACAC,qBADA;IAEAC,YAFA;IAGAC;EAHA,CAFA;;EAOAC;IACA;MACAC,cADA;MACA;MACAC,mBAFA;MAEA;MACAC;QACAC,YADA;QAEAC;MAFA,CAHA;MAOAC,QAPA;MAQAC,WARA;MASAC,oBATA;MAUA;MACAC,SAXA;MAYAC,YAZA;MAaAC,SAbA;MAcAC,gBAdA;MAeAC,WAfA;MAgBAC,iBAhBA;MAiBAC,sBAjBA;MAkBAC,OAlBA;MAmBAC,MAnBA;MAoBAC,UApBA;MAqBAC,aArBA;MAsBAC,cAtBA;MAuBAC;IAvBA;EAyBA,CAjCA;;EAkCAC,qBAlCA;EAmCAC,mBAnCA;;EAoCAC;IACA;IACA;IACA;EACA,CAxCA;;EAyCAC;IACA;AACA;AACA;IACA;MACA;QACAC;MADA;MAGA;QAAA1B;MAAA;MACA;IACA,CAVA;;IAYA;AACA;AACA;IACA;MACA;MACA;QAAAA;MAAA;MACA;IACA,CAnBA;;IAoBA2B;MACA;MACA;IACA,CAvBA;;IAwBAC;MACA;QACAC,uBADA;QAEAC,sBAFA;QAGAC;MAHA,GAIAC,IAJA,CAIA;QACA;UAAAhB;QAAA;UACA;YACA,iCADA,CACA;;YACA;UACA;QACA,CALA;MAMA,CAXA,EAWAiB,KAXA,CAWA;QACA,2BADA,CAEA;;QACA;MACA,CAfA;IAgBA,CAzCA;;IA0CAC;MACA;MACA;MACA;MACA;IACA,CA/CA;;IAgDA;IACA;MACA;QACAzB,mBADA;QAEAC,uBAFA;QAGAH,qBAHA;QAIAC,8BAJA;QAKA2B,4BALA;QAMA;QACA;QACAC,2BARA;QASAhC,oCATA;QASA;QACAiC,gDAVA,CAUA;;MAVA;MAYA;QAAArC;QAAAW;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA,CAFA;IAGA,CAvEA;;IAwEA;IACA2B;MACA;IACA,CA3EA;;IA4EAC;MACA;IACA,CA9EA;;IA+EAC;MACA;MACA;IACA,CAlFA;;IAmFAC;MACA;MACA;MACA;MACA;MACA,yCALA,CAOA;;MACA;IACA,CA5FA;;IA6FA;IACAC;MACA;MACA;IACA,CAjGA;;IAkGA;IACAC;MACA;MACA;IACA,CAtGA;;IAuGA;IACAC;MACA;MACA;IACA,CA3GA;;IA4GA;IACAC;MACA;MACA;IACA,CAhHA;;IAiHAC;MACA;QACA;UACAjB,uBADA;UAEAC,sBAFA;UAGAC;QAHA,GAIAC,IAJA,CAIA;UACA;QACA,CANA,EAMAC,KANA,CAMA;UACA;YACAF,YADA;YAEAgB;UAFA;QAIA,CAXA;MAYA,CAbA,MAaA;QACA;UACAA,oBADA;UAEAhB;QAFA;MAIA;IACA,CArIA;;IAsIA;IACA;MACA;QAAAf;QAAAqB;MAAA;MACA;QAAAW;QAAAC;MAAA;;MACA;QACA;QACA;UACAF,eADA;UAEAhB;QAFA;MAIA;IACA;;EAjJA;AAzCA", "names": ["name", "components", "BusinessObjectivesNew", "finishDetail", "titleDetail", "data", "officeData", "auditStatusData", "searchParams", "officeId", "auditStatusParams", "time", "keyword", "publishStartTime", "pageNo", "pageSize", "total", "currentRow", "show", "showFinish", "showTitleDetail", "ids", "id", "choose", "selectObj", "selectData", "tableData", "props", "mixins", "mounted", "methods", "types", "updateList", "handleDelete", "confirmButtonText", "cancelButtonText", "type", "then", "catch", "newCallback", "publishEndTime", "memberType", "auditStatus", "handleSizeChange", "handleCurrentChange", "search", "reset", "newData", "modify", "finishStatus", "handleClick", "passClick", "message", "<PERSON><PERSON><PERSON>", "errmsg"], "sourceRoot": "src/views/AssessmentOrgan/BusinessObjectives", "sources": ["BusinessObjectives.vue"], "sourcesContent": ["<template>\r\n  <!-- 业务工作目标 -->\r\n  <div class=\"BusinessObjectives\">\r\n    <search-box @search-click=\"search\"\r\n                @reset-click=\"reset\"\r\n                title=\"业务工作目标筛选\">\r\n      <!--关键字查找框  -->\r\n      <zy-widget label=\"关键字\">\r\n        <el-input placeholder=\"请输入关键词\"\r\n                  v-model=\"keyword\"\r\n                  clearable\r\n                  @keyup.enter.native=\"search\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\"></div>\r\n        </el-input>\r\n      </zy-widget>\r\n\r\n      <!-- 时间查询 -->\r\n      <zy-widget label=\"起止时间\">\r\n        <el-date-picker v-model=\"time\"\r\n                        type=\"daterange\"\r\n                        range-separator=\"至\"\r\n                        start-placeholder=\"开始时间\"\r\n                        value-format=\"timestamp\"\r\n                        end-placeholder=\"结束时间\">\r\n        </el-date-picker>\r\n      </zy-widget>\r\n\r\n      <!-- 部门查询(用到机构树)  -->\r\n      <zy-widget label=\"部门查询\"\r\n                 v-permissions=\"'auth:business:department'\">\r\n        <zy-select v-model=\"searchParams.officeId\"\r\n                   clearable\r\n                   @keyup.enter.native=\"search\"\r\n                   placeholder=\"请选择部门\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\">\r\n          </div>\r\n        </zy-select>\r\n      </zy-widget>\r\n\r\n      <!-- 审核状态查询 -->\r\n      <zy-widget label=\"审核状态查询\">\r\n        <el-select v-model=\"searchParams.auditStatusParams\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择审核状态\">\r\n          <el-option v-for=\"item in auditStatusData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n\r\n    </search-box>\r\n\r\n    <div class=\"qd-list-wrap\">\r\n      <!-- 新增按钮 -->\r\n      <div class=\"qd-btn-box\">\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-plus\"\r\n                   @click=\"newData\">新增\r\n        </el-button>\r\n\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   v-permissions=\"'auth:business:checkPass'\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n        <el-button type=\"danger\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   v-permissions=\"'auth:business:checkNotPass'\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- ********** -->\r\n      <div class=\"tableData\">\r\n        <zy-table>\r\n          <el-table :data=\"tableData\"\r\n                    slot=\"zytable\"\r\n                    row-key=\"id\"\r\n                    ref=\"multipleTable\"\r\n                    @select=\"selected\"\r\n                    @select-all=\"selectedAll\"\r\n                    :tree-props=\"{children: 'children'}\">\r\n            <el-table-column type=\"selection\"\r\n                             fixed=\"left\"\r\n                             width=\"50\">\r\n            </el-table-column>\r\n            <el-table-column label=\"标题\"\r\n                             show-overflow-tooltip\r\n                             prop=\"title\"\r\n                             min-width=\"300px\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"modify(scope.row)\"\r\n                           size=\"small\"> {{scope.row.title}}</el-button>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"部门\"\r\n                             width=\"130px\"\r\n                             prop=\"officeName\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{scope.row.officeName }} </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"发布时间\"\r\n                             width=\"170\"\r\n                             prop=\"publishTime\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{$format(scope.row.publishTime).substr(0,16)}} </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"时限要求\"\r\n                             width=\"170\"\r\n                             prop=\"endTime\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{$format(scope.row.endTime).substr(0,16)}} </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"分值\"\r\n                             align=\"center\"\r\n                             width=\"80\"\r\n                             prop=\"score\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"审核状态\"\r\n                             width=\"120\"\r\n                             prop=\"auditStatus\">\r\n\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"重点工作\"\r\n                             align=\"center\"\r\n                             width=\"100\"\r\n                             prop=\"isMainwork\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"类型\"\r\n                             show-overflow-tooltip\r\n                             width=\"150\"\r\n                             prop=\"classify\">\r\n            </el-table-column>\r\n            <el-table-column label=\"完成情况\"\r\n                             width=\"100\"\r\n                             fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"finishStatus(scope.row)\"\r\n                           size=\"small\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 完成情况\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\"\r\n                             width=\"120\"\r\n                             fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n\r\n                <el-button type=\"text\"\r\n                           @click=\"handleClick(scope.row)\"\r\n                           size=\"small\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 编辑\r\n                </el-button>\r\n\r\n                <el-button type=\"text\"\r\n                           size=\"small\"\r\n                           @click=\"handleDelete(scope.row.id)\"\r\n                           :class=\"scope.row.auditStatus == '审核通过' ? '' : 'delBtn'\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 删除\r\n                </el-button>\r\n\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </zy-table>\r\n      </div>\r\n\r\n      <!-- ************ -->\r\n      <div class=\"paging_box\">\r\n        <el-pagination @size-change=\"handleSizeChange\"\r\n                       @current-change=\"handleCurrentChange\"\r\n                       :current-page.sync=\"pageNo\"\r\n                       :page-sizes=\"[10, 20, 30, 40]\"\r\n                       :page-size.sync=\"pageSize\"\r\n                       background\r\n                       layout=\"total, prev, pager, next, sizes, jumper\"\r\n                       :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <zy-pop-up v-model=\"show\"\r\n               :title=\"id?'编辑':'新增'\">\r\n      <BusinessObjectivesNew :id=\"id\"\r\n                             :memberType=\"memberType\"\r\n                             @newCallback=\"newCallback\">\r\n      </BusinessObjectivesNew>\r\n    </zy-pop-up>\r\n    <zy-pop-up v-model=\"showFinish\"\r\n               class=\"titleStyle\"\r\n               title=\"完成情况\">\r\n      <finishDetail :id=\"id\"\r\n                    :memberType=\"memberType\"\r\n                    @newCallback=\"newCallback\">\r\n      </finishDetail>\r\n    </zy-pop-up>\r\n    <zy-pop-up v-model=\"showTitleDetail\"\r\n               class=\"titleStyle\"\r\n               title=\"详情\"\r\n               :beforeClose=\"updateList\">\r\n      <titleDetail :id=\"id\"\r\n                   :memberType=\"memberType\"\r\n                   @newCallback=\"newCallback\">\r\n      </titleDetail>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport tableData from '@mixins/tableData'\r\nimport BusinessObjectivesNew from './BusinessObjectivesNew.vue'\r\nimport finishDetail from './finishDetail.vue'\r\nimport titleDetail from './titleDetail'\r\nexport default {\r\n  name: 'BusinessObjectives',\r\n  components: {\r\n    BusinessObjectivesNew,\r\n    finishDetail,\r\n    titleDetail\r\n  },\r\n  data () {\r\n    return {\r\n      officeData: [], // 机构树,\r\n      auditStatusData: [], // 审核状态\r\n      searchParams: {\r\n        officeId: '',\r\n        auditStatusParams: ''\r\n      },\r\n      time: [],\r\n      keyword: '',\r\n      publishStartTime: '',\r\n      // ***\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      total: 10,\r\n      currentRow: null,\r\n      show: false,\r\n      showFinish: false,\r\n      showTitleDetail: false,\r\n      ids: '',\r\n      id: '',\r\n      choose: [],\r\n      selectObj: [],\r\n      selectData: [],\r\n      tableData: []\r\n    }\r\n  },\r\n  props: ['memberType'],\r\n  mixins: [tableData],\r\n  mounted () {\r\n    this.getBusinessObjectivesList()\r\n    this.treeList()\r\n    this.dictionaryPubkvs()\r\n  },\r\n  methods: {\r\n    /**\r\n  *字典\r\n*/\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluate_audit_status'\r\n      })\r\n      var { data } = res\r\n      this.auditStatusData = data.evaluate_audit_status\r\n    },\r\n\r\n    /**\r\n      *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    updateList () {\r\n      this.showTitleDetail = false\r\n      this.getBusinessObjectivesList()\r\n    },\r\n    handleDelete (ids) {\r\n      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$api.AssessmentOrgan.reqDelBusinessObjective({ ids }).then((res) => {\r\n          if (res.errcode === 200) {\r\n            this.getBusinessObjectivesList()// 删除后更新页面\r\n            this.$message.success('删除成功')\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('取消删除')\r\n        // this.getMonthlyWorkRecordlist()\r\n        return false\r\n      })\r\n    },\r\n    newCallback () {\r\n      this.getBusinessObjectivesList()\r\n      this.show = false\r\n      this.showFinish = false\r\n      this.showTitleDetail = false\r\n    },\r\n    // 请求后台数据 获取列表信息\r\n    async getBusinessObjectivesList () {\r\n      const res = await this.$api.AssessmentOrgan.reqBusinessObjectivesList({\r\n        pageNo: this.pageNo,\r\n        pageSize: this.pageSize,\r\n        keyword: this.keyword,\r\n        publishStartTime: this.time[0],\r\n        publishEndTime: this.time[1],\r\n        // // sedateId: this.sedateId\r\n        // sedateId: this.selectedYear\r\n        memberType: this.memberType,\r\n        officeId: this.searchParams.officeId, // 部门查询\r\n        auditStatus: this.searchParams.auditStatusParams // 审核状态\r\n      })\r\n      var { data, total } = res\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n      this.selectObj = []\r\n      this.selectData = []\r\n      this.$nextTick(function () {\r\n        this.memoryChecked()\r\n      })\r\n    },\r\n    // paging_box 里面用到的方法\r\n    handleSizeChange () {\r\n      this.getBusinessObjectivesList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getBusinessObjectivesList()\r\n    },\r\n    search () {\r\n      this.pageNo = 1\r\n      this.getBusinessObjectivesList()\r\n    },\r\n    reset () {\r\n      this.keyword = ''\r\n      this.publishStartTime = ''\r\n      this.time.splice(0, this.time.length)\r\n      this.searchParams.officeId = ''\r\n      this.searchParams.auditStatusParams = ''\r\n\r\n      // 重置后重新调用一次列表信息\r\n      this.getBusinessObjectivesList()\r\n    },\r\n    // 新增\r\n    newData () {\r\n      this.id = ''\r\n      this.show = true\r\n    },\r\n    // 标题详情 modify\r\n    modify (row) {\r\n      this.id = row.id\r\n      this.showTitleDetail = true\r\n    },\r\n    // 完成情况\r\n    finishStatus (row) {\r\n      this.id = row.id\r\n      this.showFinish = true\r\n    },\r\n    // 编辑\r\n    handleClick (row) {\r\n      this.id = row.id\r\n      this.show = true\r\n    },\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckWork(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 审核 (工作目标)\r\n    async getCheckWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckWork({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getBusinessObjectivesList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.BusinessObjectives {\r\n  height: 100%;\r\n  width: 100%;\r\n  overflow: hidden;\r\n\r\n  .qd-list-wrap {\r\n    height: calc(100% - 83px);\r\n  }\r\n\r\n  .zy-pop-up-body-box{\r\n    // overflow: hidden;\r\n  }\r\n\r\n  .titleStyle {\r\n    width: 115px;\r\n    height: 16px;\r\n    font-size: $textSize16;\r\n    font-family: PingFang SC;\r\n    font-weight: bold;\r\n    color: #333333;\r\n    line-height: 36px;\r\n    margin-left: 32px;\r\n  }\r\n\r\n  .tableData {\r\n    .el-scrollbar__wrap .el-scrollbar__view th {\r\n      background: #f5f7fb;\r\n    }\r\n    height: calc(100% - 132px);\r\n  }\r\n  .button-box {\r\n    background: #fff;\r\n    margin-top: 20px;\r\n  }\r\n  .button-box .el-button {\r\n    background: #fff;\r\n  }\r\n  .el-button--danger.is-plain:hover,\r\n  .el-button--danger.is-plain:focus {\r\n    // color: #f56c6c;\r\n    background: #f56c6c;\r\n    color: #fff;\r\n  }\r\n  .el-button--success.is-plain:hover,\r\n  .el-button--success.is-plain:focus {\r\n    background: #67c23a;\r\n    color: #fff;\r\n  }\r\n  .delBtn {\r\n    color: #f56c6c;\r\n  }\r\n  .paging_box {\r\n    background: #fff;\r\n  }\r\n  .scoreEdit {\r\n    width: 700px;\r\n    height: 100%;\r\n    padding: 24px;\r\n    .form-button {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n  .search-box {\r\n    //title文字样式\r\n    .search-title {\r\n      width: 133px;\r\n      height: 16px;\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-left: 32px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}