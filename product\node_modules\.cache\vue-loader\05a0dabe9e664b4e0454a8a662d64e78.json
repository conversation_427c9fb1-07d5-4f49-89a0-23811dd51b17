{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarChart.vue?vue&type=style&index=0&id=2fd92365&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarChart.vue", "mtime": 1756343512133}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5kaXNjdXNzaW9uLWNoYXJ0IHsKICB3aWR0aDogMTAwJTsKICBoZWlnaHQ6IDEwMCU7Cn0K"}, {"version": 3, "sources": ["BarChart.vue"], "names": [], "mappings": ";AAwOA;AACA;AACA;AACA", "file": "BarChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"discussion-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'DiscussionChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  watch: {\n    chartData: {\n      handler () {\n        this.renderChart()\n      },\n      deep: true\n    }\n  },\n  methods: {\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      this.renderChart()\n      window.addEventListener('resize', this.resizeChart)\n    },\n    getOption () {\n      const xAxisData = this.chartData.map(item => item.name)\n      const seriesData = this.chartData.map(item => item.value)\n      return {\n        legend: {\n          show: this.id === 'committee_proposal' ? true : null,\n          data: ['提交件数'],\n          top: '2%',\n          left: 'center',\n          textStyle: {\n            color: '#fff',\n            fontSize: 12\n          },\n          itemWidth: 12,\n          itemHeight: 8\n        },\n        grid: {\n          left: this.id === 'committee_proposal' ? '0%' : '3%',\n          right: this.id === 'committee_proposal' ? '0%' : '3%',\n          bottom: this.id === 'committee_proposal' ? '20%' : '8%',\n          top: this.id === 'committee_proposal' ? '10%' : '10%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData,\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.3)'\n            }\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#A2B0B8',\n            fontSize: this.id === 'committee_proposal' ? 12 : 14,\n            interval: 0,\n            rotate: 0,\n            margin: 8,\n            formatter: this.id === 'committee_proposal' ? this.formatAxisLabel : null\n          }\n        },\n        yAxis: {\n          type: 'value',\n          splitNumber: 4,\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#A2B0B8',\n            fontSize: 14\n          },\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              type: 'dashed'\n            }\n          }\n        },\n        series: this.id === 'committee_proposal' ? [\n          {\n            type: 'bar',\n            name: '提交件数',\n            data: seriesData,\n            barWidth: 25,\n            showBackground: true, // 显示背景\n            backgroundStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              borderRadius: [2, 2, 0, 0]\n            },\n            itemStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: 'rgba(31, 198, 255, 1)' },\n                  { offset: 1, color: 'rgba(31, 198, 255, 0)' }\n                ]\n              },\n              borderRadius: [2, 2, 0, 0]\n            }\n          }\n        ] : [\n          {\n            type: 'bar',\n            data: seriesData,\n            barWidth: 20,\n            itemStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: 'rgba(31, 198, 255, 1)' },\n                  { offset: 1, color: 'rgba(31, 198, 255, 0)' }\n                ]\n              },\n              borderRadius: [2, 2, 0, 0]\n            },\n            emphasis: {\n              itemStyle: {\n                color: {\n                  type: 'linear',\n                  x: 0,\n                  y: 0,\n                  x2: 0,\n                  y2: 1,\n                  colorStops: [\n                    { offset: 0, color: '#00E4FF' },\n                    { offset: 0.5, color: '#0090FF' },\n                    { offset: 1, color: '#005090' }\n                  ]\n                }\n              }\n            },\n            label: {\n              show: false,\n              position: 'top',\n              color: '#00D4FF',\n              fontSize: 12,\n              fontWeight: 'bold'\n            }\n          }\n        ],\n        tooltip: {\n          trigger: 'axis',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00D4FF',\n          borderWidth: 1,\n          textStyle: {\n            color: '#FFFFFF'\n          },\n          formatter: function (params) {\n            const data = params[0]\n            return `${data.name}<br/>人数: ${data.value}人`\n          }\n        }\n      }\n    },\n    renderChart () {\n      if (!this.chart || !this.chartData.length) return\n      this.chart.setOption(this.getOption(), true)\n    },\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    },\n    formatAxisLabel (value) {\n      // 为committee_proposal处理文本换行\n      if (this.id === 'committee_proposal') {\n        // 根据文本长度进行换行处理\n        if (value.length > 4) {\n          // 如果包含\"委\"字，在\"委\"字后换行\n          if (value.includes('委') && value.indexOf('委') < value.length - 1) {\n            return value.replace('委', '委\\n')\n          }\n          // 否则在中间位置换行\n          const mid = Math.ceil(value.length / 2)\n          return value.substring(0, mid) + '\\n' + value.substring(mid)\n        }\n      }\n      return value\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.discussion-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}