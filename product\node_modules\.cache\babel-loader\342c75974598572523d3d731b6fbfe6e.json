{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\finishDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\finishDetail.vue", "mtime": 1752541693830}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAsIA;AACA;AAEA;AACA;AAEA;EACAA,oBADA;EAEAC,mBAFA;EAGAC;IACAC,eADA;IAEAC;EAFA,CAHA;;EAOAC;IACA;MACAC,iBADA;MAEAC,uBAFA;MAIAC,aAJA;MAKAC,cALA;MAMAC,aANA;MAQAC,gBARA;MASAC,SATA;MAUAC,YAVA;MAWAC,OAXA;MAWA;MACAC,cAZA;MAaAC;IAbA;EAgBA,CAxBA;;EAyBAC,2BAzBA;EA0BAC,kBA1BA;;EA4BAC;IACA;MACA;IACA;EACA,CAhCA;;EAiCAC;IACAC;MACA;MACA;IACA,CAJA;;IAKAC;MACA;QACA;UACAC,uBADA;UAEAC,sBAFA;UAGAC;QAHA,GAIAC,IAJA,CAIA;UACA;QACA,CANA,EAMAC,KANA,CAMA;UACA;YACAF,YADA;YAEAG;UAFA;QAIA,CAXA;MAYA,CAbA,MAaA;QACA;UACAA,oBADA;UAEAH;QAFA;MAIA;IACA,CAzBA;;IA0BA;IACA;MACA;QAAAI;QAAAC;MAAA;MACA;QAAAC;QAAAC;MAAA;;MACA;QACA;QACA;UACAJ,eADA;UAEAH;QAFA;MAIA;IACA,CArCA;;IAsCAQ;MACA,wBADA,CACA;;MACA;MACA,2BAHA,CAGA;IACA,CA1CA;;IA2CA;IACA;MACA;QACAtB,qBADA;QACA;QACAC,wBAFA;QAGAC,uBAHA;QAIA;QACAqB,2BALA;QAMAJ;MANA;MAQA;QAAAzB;QAAAW;MAAA;MACA;MACA;MACA;IACA,CAzDA;;IA0DA;IACAmB;MACA;MACA,aAFA,CAEA;IACA,CA9DA;;IA+DA;IACAC;MACA;MACA;IACA,CAnEA;;IAoEA;IACAC;MACA;MACA;IACA,CAxEA;;IAyEA;IACAC;MAAA;MACA;QACAf,uBADA;QAEAC,sBAFA;QAGAC;MAHA,GAIAC,IAJA,CAIA;QACA;UAAAG;QAAA;UACA;YACA,2BADA,CACA;;YACA;UACA;QACA,CALA;MAMA,CAXA,EAWAF,KAXA,CAWA;QACA,2BADA,CAEA;;QACA;MACA,CAfA;IAgBA,CA3FA;;IA4FAY;MACA;QACA;MACA;;MACA;IACA,CAjGA;;IAkGAC;MACA;;MACA;QACAC;UACA;YACAA;UACA;QACA,CAJA;QAKA;MACA,CAPA,MAOA;QACA;QACAA;QACA;MACA;IACA,CAhHA;;IAiHAC;MACA;MACA;;MACA;QACAC;UACA;UACA;QACA,CAHA;MAIA;IACA,CA1HA;;IA2HA;IACAC;MACA;IACA,CA9HA;;IA+HAC;MACA;IACA;;EAjIA;AAjCA", "names": ["name", "mixins", "components", "newFinishDetail", "FinishDetailPop", "data", "showFinish", "showFinishDetail", "tableData", "selectData", "selectObj", "evaluationId", "pageNo", "pageSize", "uid", "currentPage", "total", "props", "inject", "mounted", "methods", "updateList", "passClick", "confirmButtonText", "cancelButtonText", "type", "then", "catch", "message", "ids", "auditStatus", "<PERSON><PERSON><PERSON>", "errmsg", "newCallback", "memberType", "finishStatus", "handleClick", "modify", "handleDelete", "handleBatchDelete", "select", "arr", "selectAll", "selection", "handleSizeChange", "handleCurrentChange"], "sourceRoot": "src/views/AssessmentOrgan/InnovationExcellence", "sources": ["finishDetail.vue"], "sourcesContent": ["<template>\r\n  <!-- 点击完成情况 -->\r\n  <div class=\"finishDetail\">\r\n    <div class=\"buttonColumn\">\r\n      <!-- 新增/删除 按钮 -->\r\n      <div class=\"qd-btn-box\">\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-plus\"\r\n                   @click=\"finishStatus\">新增\r\n        </el-button>\r\n        <el-button type=\"danger\"\r\n                   icon=\"el-icon-delete\"\r\n                   plain\r\n                   @click=\"handleBatchDelete\">删除\r\n        </el-button>\r\n\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   v-permissions=\"'auth:innovation:checkPass'\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n        <el-button type=\"danger\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   v-permissions=\"'auth:innovation:checkNotPass'\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"listView\">\r\n\r\n      <el-table :data=\"tableData\"\r\n                row-key=\"id\"\r\n                ref=\"multipleTable\"\r\n                @select=\"selected\"\r\n                @select-all=\"selectedAll\"\r\n                :header-cell-style=\"{background:'#eef1f6',color:'#606266'}\"\r\n                tooltip-effect=\"dark\"\r\n                class=\"tableStyle\">\r\n        <el-table-column type=\"selection\"\r\n                         width=\"55\">\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"标题\"\r\n                         show-overflow-tooltip\r\n                         prop=\"title\"\r\n                         width=\"450\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\"\r\n                       @click=\"modify(scope.row)\"\r\n                       size=\"small\"> {{scope.row.title}}\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"审核状态\"\r\n                         width=\"110\"\r\n                         prop=\"auditStatus\">\r\n\r\n        </el-table-column>\r\n\r\n        <el-table-column prop=\"overTime\"\r\n                         label=\"完成时间\"\r\n                         width=\"220\">\r\n          <template slot-scope=\"scope\">\r\n            <div> {{$format(scope.row.overTime).substr(0,16)}} </div>\r\n          </template>\r\n        </el-table-column>\r\n        <!-- <el-table-column prop=\"auditStatus\"\r\n                         label=\"审核状态\"\r\n                         show-overflow-tooltip>\r\n          <template slot-scope=\"scope\"> -->\r\n        <!-- <div v-if=\"scope.row.auditStatus == '1' \"> 待审核 </div>\r\n                <div v-if=\"scope.row.auditStatus == '0' \"> 审核通过</div> -->\r\n        <!-- <div>{{scope.row.auditStatus}}</div>\r\n          </template>\r\n        </el-table-column> -->\r\n\r\n        <el-table-column label=\"操作\"\r\n                         width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\"\r\n                       @click=\"handleClick(scope.row)\"\r\n                       size=\"small\"> 编辑</el-button>\r\n\r\n            <el-button type=\"text\"\r\n                       @click=\"handleDelete(scope.row.id)\"\r\n                       class=\"delBtn\"\r\n                       size=\"small\"> 删除</el-button>\r\n\r\n          </template>\r\n        </el-table-column>\r\n\r\n      </el-table>\r\n\r\n    </div>\r\n\r\n    <div class=\"paging_box\">\r\n      <el-pagination @size-change=\"handleSizeChange\"\r\n                     @current-change=\"handleCurrentChange\"\r\n                     :current-page.sync=\"currentPage\"\r\n                     :page-sizes=\"[10, 20, 30, 40]\"\r\n                     :page-size.sync=\"pageSize\"\r\n                     background\r\n                     layout=\"total, prev, pager, next, sizes, jumper\"\r\n                     :total=\"total\">\r\n      </el-pagination>\r\n    </div>\r\n\r\n    <zy-pop-up v-model=\"showFinish\"\r\n               class=\"titleStyle\"\r\n               title=\"完成情况\">\r\n      <newFinishDetail :id=\"id\"\r\n                       :uid=\"uid\"\r\n                       @newCallback=\"newCallback\">\r\n      </newFinishDetail>\r\n    </zy-pop-up>\r\n\r\n    <zy-pop-up v-model=\"showFinishDetail\"\r\n               class=\"titleStyle\"\r\n               title=\"完成情况详情\"\r\n               :beforeClose=\"updateList\">\r\n\r\n      <FinishDetailPop :id=\"id\"\r\n                       :uid=\"uid\"\r\n                       @newCallback=\"newCallback\">\r\n      </FinishDetailPop>\r\n\r\n    </zy-pop-up>\r\n\r\n  </div>\r\n</template>\r\n<script>\r\n// import newFinishDetail from '../newFinishDetail.vue'\r\nimport newFinishDetail from '../BusinessObjectives/newFinishDetail'\r\n\r\nimport FinishDetailPop from './FinishDetailPop.vue'\r\nimport tableData from '@mixins/tableData'\r\n\r\nexport default {\r\n  name: 'finishDetail',\r\n  mixins: [tableData],\r\n  components: {\r\n    newFinishDetail,\r\n    FinishDetailPop\r\n  },\r\n  data () {\r\n    return {\r\n      showFinish: false,\r\n      showFinishDetail: false,\r\n\r\n      tableData: [],\r\n      selectData: [],\r\n      selectObj: [],\r\n\r\n      evaluationId: '',\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      uid: '', // 完成情况列表的id\r\n      currentPage: 1,\r\n      total: 10\r\n\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  inject: ['newTab'],\r\n\r\n  mounted () {\r\n    if (this.id) {\r\n      this.getfinishDetailList()\r\n    }\r\n  },\r\n  methods: {\r\n    updateList () {\r\n      this.showFinishDetail = false\r\n      this.getfinishDetailList()\r\n    },\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckWork(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 审核\r\n    async getCheckWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckFinishDetail({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getfinishDetailList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n    newCallback () {\r\n      this.showFinish = false // 关闭弹窗\r\n      this.showFinishDetail = false\r\n      this.getfinishDetailList() // 重新调用(更新)一次列表\r\n    },\r\n    // 请求后台数据 获取列表信息\r\n    async getfinishDetailList () {\r\n      const res = await this.$api.AssessmentOrgan.reqfinishDetailList({\r\n        evaluationId: this.id, // TODO:工作目标或创新创优id(需检查是否传错)\r\n        pageNo: this.currentPage,\r\n        pageSize: this.pageSize,\r\n        // *******************\r\n        memberType: this.memberType,\r\n        auditStatus: this.auditStatus\r\n      })\r\n      var { data, total } = res\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n    },\r\n    // 新增 完成情况\r\n    finishStatus () {\r\n      this.showFinish = true\r\n      this.uid = 0 // 将newFinishDetail组件的属性:uid设置为0 (false) 达到新增页面效果\r\n    },\r\n    // 编辑\r\n    handleClick (row) {\r\n      this.uid = row.id\r\n      this.showFinish = true\r\n    },\r\n    // 详情\r\n    modify (row) {\r\n      this.uid = row.id\r\n      this.showFinishDetail = true\r\n    },\r\n    // 删除按钮方法  批量删除\r\n    handleDelete (ids) { // TODO: handleDelete 和 handleBatchDelete 1.组合使用为新增/删除栏的删除功能 2.单独使用handleDelete则为操作项的删除功能\r\n      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$api.AssessmentOrgan.reqDelFinishDetail({ ids }).then((res) => {\r\n          if (res.errcode === 200) {\r\n            this.getfinishDetailList()// 删除后更新页面\r\n            this.$message.success('删除成功')\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('取消删除')\r\n        // this.getfinishDetailList()\r\n        return false\r\n      })\r\n    },\r\n    handleBatchDelete () {\r\n      if (this.selectData.length === 0) {\r\n        return this.$message.warning('请选择想要删除的项')\r\n      }\r\n      this.handleDelete(this.selectData.join(','))\r\n    },\r\n    select (selection, row) {\r\n      var arr = this.selectData\r\n      if (this.selectObj[row.id]) {\r\n        arr.forEach((item, index) => {\r\n          if (item === row.id) {\r\n            arr.splice(index, 1)\r\n          }\r\n        })\r\n        delete this.selectObj[row.id]\r\n      } else {\r\n        this.selectObj[row.id] = row.id\r\n        arr.push(row.id)\r\n        this.selectData = arr\r\n      }\r\n    },\r\n    selectAll (selection) {\r\n      this.selectData = []\r\n      this.selectObj = []\r\n      if (selection.length) {\r\n        selection.forEach((item, index) => {\r\n          this.selectObj[item.id] = item.id\r\n          this.selectData.push(item.id)\r\n        })\r\n      }\r\n    },\r\n    // 底部页签\r\n    handleSizeChange () {\r\n      this.getfinishDetailList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getfinishDetailList()\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.finishDetail {\r\n  width: 1100px;\r\n  height: 100%;\r\n  padding: 1px 40px;\r\n\r\n  .qd-btn-box {\r\n    padding-bottom: 5px;\r\n  }\r\n\r\n  .listView {\r\n    height: 100%;\r\n\r\n    .tableStyle {\r\n      width: 100%;\r\n      height: 500px;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n    }\r\n  }\r\n\r\n  .tableZy {\r\n    height: 500px;\r\n  }\r\n}\r\n</style>\r\n"]}]}