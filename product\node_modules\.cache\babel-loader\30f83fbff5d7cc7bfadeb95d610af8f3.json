{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-upload\\zy-upload.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-upload\\zy-upload.vue", "mtime": 1752541693624}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAyCA;EACAA,gBADA;EAEAC;IACAC,cADA;IAEAC;MACAC,YADA;MAEAC;IAFA,CAFA;IAMAC;MACAF,aADA;MAEAC;IAFA,CANA;IAUAE;MACAH,YADA;MAEAC;IAFA;EAVA,CAFA;EAiBAG;IACAC,aADA;IACA;IACAC,aAFA,CAEA;;EAFA,CAjBA;EAqBAC;IACAT;MACA;QACA;QACA;MACA,CAHA,MAGA;QACA;MACA;IACA,CARA;;IASAU;MACA;IACA;;EAXA,CArBA;;EAkCAC;IACA;MACAC,oBADA;MAEAC,kBAFA;MAGAH,oBAHA;MAIAI;IAJA;EAMA,CAzCA;;EA0CAC;IACA;IACAC;MACA;MACA;IACA,CALA;;IAMA;IACAC;MACA;MACA;;MACA;QACA;UACAC;QACA;MACA;;MACA;IACA,CAhBA;;IAiBA;IACAC;MACA;MACA;MACA;;MACA;QACA;QACA;MACA;;MACA;QACA;QACA;MACA;;MACA;IACA,CA/BA;;IAgCA;IACA;MACA;MACA;MACAC;MACAA;MACAA;MACA;QACA;UAAAC;UAAAV;QAAA;;QACA;UACA;YACAb,sBADA;YAEAwB,sBAFA;YAGApB,sBAHA;YAIAqB,qBAJA;YAKAC,cALA;YAMAC;UANA;UAQA;QACA;MACA,CAbA;IAcA,CArDA;;IAsDAC;MACA;QACA;MACA;IACA;;EA1DA;AA1CA", "names": ["name", "props", "value", "limit", "type", "default", "multiple", "module", "model", "prop", "event", "watch", "filelist", "data", "dialogVisible", "dialogImageUrl", "showBtn", "methods", "handlePictureCardPreview", "handleRemove", "arr", "beforeAvatarUpload", "formData", "<PERSON><PERSON><PERSON>", "size", "url", "id", "uid", "handleChange"], "sourceRoot": "src/components/zy-upload", "sources": ["zy-upload.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-upload\">\r\n    <el-upload action=\"#\"\r\n               :class=\"{ uploadSty:showBtn,disUploadSty:!showBtn}\"\r\n               :multiple=\"multiple\"\r\n               :limit=\"limit\"\r\n               list-type=\"picture-card\"\r\n               accept=\".jpg,.jpeg,.png,.PNG,.JPG\"\r\n               :on-change=\"handleChange\"\r\n               :http-request=\"customUpload\"\r\n               :before-upload=\"beforeAvatarUpload\"\r\n               :file-list=\"filelist\">\r\n      <i slot=\"default\"\r\n         class=\"el-icon-plus\"></i>\r\n      <div slot=\"file\"\r\n           slot-scope=\"{file}\">\r\n        <img class=\"el-upload-list__item-thumbnail\"\r\n             :src=\"file.url\"\r\n             alt=\"\">\r\n        <span class=\"el-upload-list__item-actions\">\r\n          <span class=\"el-upload-list__item-preview\"\r\n                @click=\"handlePictureCardPreview(file)\">\r\n            <i class=\"el-icon-zoom-in\"></i>\r\n          </span>\r\n          <span class=\"el-upload-list__item-delete\"\r\n                @click=\"handleRemove(file)\">\r\n            <i class=\"el-icon-delete\"></i>\r\n          </span>\r\n        </span>\r\n      </div>\r\n    </el-upload>\r\n    <el-dialog :visible.sync=\"dialogVisible\"\r\n               append-to-body>\r\n      <img width=\"100%\"\r\n           :src=\"dialogImageUrl\"\r\n           alt=\"\">\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'zyUpload',\r\n  props: {\r\n    value: [Array],\r\n    limit: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    multiple: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    module: {\r\n      type: String,\r\n      default: 'minisuggestion'\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value', // 这个字段，是指父组件设置 v-model 时，将变量值传给子组件的 msg\r\n    event: 'file'// 这个字段，是指父组件监听 parent-event 事件\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.filelist = val\r\n        this.showBtn = this.filelist.length < this.limit\r\n      } else {\r\n        this.filelist = []\r\n      }\r\n    },\r\n    filelist (val) {\r\n      this.$emit('file', val)\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      dialogVisible: false,\r\n      dialogImageUrl: '',\r\n      filelist: this.value,\r\n      showBtn: true\r\n    }\r\n  },\r\n  methods: {\r\n    // 图片预览\r\n    handlePictureCardPreview (file) {\r\n      this.dialogImageUrl = file.url\r\n      this.dialogVisible = true\r\n    },\r\n    // 移除图片\r\n    handleRemove (file) {\r\n      // 存在浅拷贝 所以不需要再赋值\r\n      const arr = this.filelist\r\n      for (let i = 0; i < arr.length; i++) {\r\n        if (arr[i].uid === file.uid) {\r\n          arr.splice(i, 1)\r\n        }\r\n      }\r\n      this.showBtn = this.filelist.length < this.limit\r\n    },\r\n    // 校验文件类型和文件大小\r\n    beforeAvatarUpload (file) {\r\n      // const isJPG = file.type === 'image/jpeg'\r\n      const isLt2M = file.size / 1024 / 1024 < 10\r\n      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n      if (testmsg !== 'png' && testmsg !== 'jpg' && testmsg !== 'PNG' && testmsg !== 'jpeg' && testmsg !== 'JPG') {\r\n        this.$message.error('图片文件格式暂时不支持!')\r\n        return false\r\n      }\r\n      if (!isLt2M) {\r\n        this.$message.error('上传图片大小不能超过 10MB!')\r\n        return false\r\n      }\r\n      return isLt2M && testmsg\r\n    },\r\n    // 上传逻辑\r\n    async customUpload (file) {\r\n      const siteId = JSON.parse(sessionStorage.getItem('user' + this.$logo())).areaId\r\n      const formData = new FormData()\r\n      formData.append('attachment', file.file)\r\n      formData.append('module', this.module)\r\n      formData.append('siteId', siteId)\r\n      this.$api.microAdvice.uploadFile(formData).then(res => {\r\n        const { errcode, data } = res\r\n        if (errcode === 200) {\r\n          const fileData = {\r\n            name: data[0].fileName,\r\n            size: data[0].fileSize,\r\n            type: data[0].fileType,\r\n            url: data[0].filePath,\r\n            id: data[0].id,\r\n            uid: data[0].uid\r\n          }\r\n          this.filelist.push(fileData)\r\n        }\r\n      })\r\n    },\r\n    handleChange (file, filelist) {\r\n      if (file.status !== 'ready') {\r\n        this.showBtn = filelist.length < this.limit\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"./zy-upload.scss\";\r\n</style>\r\n"]}]}