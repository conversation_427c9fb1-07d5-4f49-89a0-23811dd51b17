{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-cascader-checkbox\\zy-cascader-checkbox.vue?vue&type=style&index=0&id=624c11cf&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-cascader-checkbox\\zy-cascader-checkbox.vue", "mtime": 1660102037660}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3p5LWNhc2NhZGVyLWNoZWNrYm94LnNjc3MiOw0K"}, {"version": 3, "sources": ["zy-cascader-checkbox.vue"], "names": [], "mappings": ";AA0LA", "file": "zy-cascader-checkbox.vue", "sourceRoot": "src/components/zy-cascader-checkbox", "sourcesContent": ["<template>\r\n  <div class=\"zy-cascader-checkbox\"\r\n       :style=\"{ width: width + 'px' }\">\r\n    <el-popover popper-class=\"zy-cascader-checkbox-popover\"\r\n                :trigger=\"trigger\"\r\n                :disabled=\"disabled\"\r\n                :width=\"width\"\r\n                v-model=\"options_show\">\r\n      <div slot=\"reference\"\r\n           :class=\"['zy-cascader-checkbox-input',disabled?'zy-cascader-checkbox-input-disabled':'']\">\r\n        <div :class=\"['zy-cascader-checkbox-input-icon',options_show?'zy-cascader-checkbox-input-icon-a':'']\">\r\n          <i class=\"el-icon-arrow-down\"></i>\r\n        </div>\r\n        <div v-if=\"!selecteds.length\"\r\n             class=\"zy-cascader-checkbox-input-text\">{{placeholder}}</div>\r\n        <el-tag closable\r\n                size=\"medium\"\r\n                v-for=\"item in selecteds\"\r\n                :disable-transitions=\"false\"\r\n                :title=\"item[selfProps.label]\"\r\n                :key=\"item[nodeKey]\"\r\n                @close=\"tabClose(item[nodeKey])\">{{ item[selfProps.label] }}</el-tag>\r\n      </div>\r\n\r\n      <el-scrollbar class=\"zy-cascader-checkbox-box-box\">\r\n        <div>\r\n          <el-input placeholder=\"请输入关键字查询\"\r\n                    v-model=\"keyword\"\r\n                    clearable>\r\n          </el-input>\r\n        </div>\r\n        <el-scrollbar class=\"zy-cascader-checkbox-box\">\r\n          <el-tree show-checkbox\r\n                   :data=\"selfData\"\r\n                   ref=\"tree-select\"\r\n                   highlight-current\r\n                   :props=\"selfProps\"\r\n                   :node-key=\"nodeKey\"\r\n                   @check=\"handleCheckChange\"\r\n                   :filter-node-method=\"filterNode\"\r\n                   class=\"zy-cascader-checkbox-tree\"\r\n                   :default-checked-keys=\"checked_keys\"\r\n                   :default-expand-all=\"defaultExpandAll\"\r\n                   :default-expanded-keys=\"defaultExpandedKeys\"></el-tree>\r\n        </el-scrollbar>\r\n      </el-scrollbar>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyCascaderCheckbox',\r\n  data () {\r\n    return {\r\n      keyword: '',\r\n      selecteds: [],\r\n      options_show: false,\r\n      checked_keys: []\r\n    }\r\n  },\r\n  props: {\r\n    // 数据\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {}\r\n      }\r\n    },\r\n    // node-key\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    // 选中数据\r\n    value: [String, Number, Array, Object],\r\n    // 是否只可选叶子节点\r\n    leaf: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 宽度\r\n    width: String,\r\n    // 触发方式 click/focus/hover/manual\r\n    trigger: {\r\n      type: String,\r\n      default: 'click'\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '请选择'\r\n    },\r\n    // 是否展开全部\r\n    defaultExpandAll: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 默认展开的节点的 key 的数组\r\n    defaultExpandedKeys: {\r\n      type: Array,\r\n      default: () => {\r\n        return []\r\n      }\r\n    },\r\n    max: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'change'\r\n  },\r\n  created () {\r\n    this.chaeckDefaultValue()\r\n  },\r\n  watch: {\r\n    keyword (val) {\r\n      this.$refs['tree-select'].filter(val)\r\n    },\r\n    value (val) {\r\n      this.chaeckDefaultValue()\r\n    },\r\n    data (val) {\r\n      this.chaeckDefaultValue()\r\n    }\r\n  },\r\n  computed: {\r\n    selfData () {\r\n      return this.data\r\n    },\r\n    selfProps () {\r\n      return {\r\n        label: 'label',\r\n        children: 'children',\r\n        disabled: data => {\r\n          return data.disabled\r\n        },\r\n        ...this.props\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    filterNode (value, data) {\r\n      if (!value) return true\r\n      return data[this.selfProps.label].indexOf(value) !== -1\r\n    },\r\n    handleCheckChange () {\r\n      const nodes = this.$refs['tree-select'].getCheckedNodes(this.leaf)\r\n      this.selecteds = nodes\r\n      this.$emit('change', nodes)\r\n    },\r\n    tabClose (Id) {\r\n      if (this.disabled) return\r\n      this.$refs['tree-select'].setChecked(Id, false, true)\r\n      this.selecteds = this.$refs['tree-select'].getCheckedNodes()\r\n      this.$emit('change', this.selecteds)\r\n    },\r\n    chaeckDefaultValue () {\r\n      const val = this.value\r\n      if (!val || (Array.isArray(val) && val.length === 0)) {\r\n        this.selecteds = []\r\n        this.checked_keys = []\r\n        this.$nextTick(() => {\r\n          this.$refs['tree-select'].setCheckedKeys([])\r\n        })\r\n        return\r\n      }\r\n      this.checked_keys = typeof val[0] === 'object' ? val.map(i => i[this.nodeKey]) : val\r\n      this.$nextTick(() => {\r\n        this.selecteds = this.$refs['tree-select'].getCheckedNodes(this.leaf)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-cascader-checkbox.scss\";\r\n</style>\r\n"]}]}