{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\screening-box\\screening-box.vue?vue&type=style&index=0&id=1fb499b9&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\screening-box\\screening-box.vue", "mtime": 1752541693479}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3NjcmVlbmluZy1ib3guc2NzcyI7DQo="}, {"version": 3, "sources": ["screening-box.vue"], "names": [], "mappings": ";AAoFA", "file": "screening-box.vue", "sourceRoot": "src/components/screening-box", "sourcesContent": ["<template>\r\n  <div class=\"screening-box\" ref=\"screening\">\r\n    <slot></slot>\r\n    <div :class=\"['screening-button',button?'screening-button-a':'']\">\r\n      <el-button type=\"primary\" @click=\"search\" v-if=\"searchButton\">查询</el-button>\r\n      <el-button @click=\"reset\" v-if=\"resetButton\">重置</el-button>\r\n      <el-button type=\"text\" v-if=\"more\" @click=\"moreClick\"><i :class=\"['el-icon-arrow-down',moreShow?'':'el-icon-arrow-down-a']\"></i>{{moreShow?'更多':'收起'}}</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'screeningBox',\r\n  data () {\r\n    return {\r\n      button: false,\r\n      more: false,\r\n      moreShow: true\r\n    }\r\n  },\r\n  props: {\r\n    searchButton: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    resetButton: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  created () {\r\n  },\r\n  mounted () {\r\n    this.$nextTick(() => {\r\n      this.resolution()\r\n    })\r\n  },\r\n  methods: {\r\n    search () {\r\n      this.$emit('search-click')\r\n    },\r\n    reset () {\r\n      this.$emit('reset-click')\r\n    },\r\n    moreClick () {\r\n      var screening = this.$refs.screening\r\n      if (this.moreShow) {\r\n        screening.style.height = 'auto'\r\n      } else {\r\n        screening.style.height = ''\r\n      }\r\n      this.$emit('more-click', screening.offsetHeight, this.moreShow)\r\n      this.moreShow = !this.moreShow\r\n    },\r\n    resolution () {\r\n      if (this.$refs.screening) {\r\n        var screening = this.$refs.screening\r\n        var Width = 246\r\n        var length = 0\r\n        for (let index = 0; index < screening.childNodes.length - 1; index++) {\r\n          if (screening.childNodes[index].offsetWidth === undefined) {\r\n          } else {\r\n            Width += screening.childNodes[index].offsetWidth + 24\r\n          }\r\n          if (screening.offsetWidth < Width && length === 0) {\r\n            length = index\r\n          }\r\n        }\r\n        if (screening.offsetWidth < Width) {\r\n          this.more = true\r\n          var arr = []\r\n          for (let index = length; index < screening.childNodes.length - 1; index++) {\r\n            arr.push(screening.childNodes[index])\r\n          }\r\n          for (let index = 0; index < arr.length; index++) {\r\n            screening.appendChild(arr[index])\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./screening-box.scss\";\r\n</style>\r\n"]}]}