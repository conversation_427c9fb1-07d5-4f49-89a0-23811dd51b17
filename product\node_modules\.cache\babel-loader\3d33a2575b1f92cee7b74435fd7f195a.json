{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\general\\office.js", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\general\\office.js", "mtime": 1752541695850}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["webOfficeTpl", "data", "methods", "initWebOfficeObject", "url", "arrUrl", "split", "start", "indexOf", "<PERSON><PERSON>l", "substring", "serveUrl", "webOfficeObj", "ServerUrl", "UserName", "FileName", "FileType", "ShowWindow", "EditType", "ShowMenu", "ShowToolBar", "ShowMenuBar", "NewShowToolBar", "WebOpenOut", "console", "log", "e"], "sources": ["D:/zy/xm/pc/qdzx/product/src/views/general/office.js"], "sourcesContent": ["\r\nimport webOfficeTpl from '../../../public/static/js/iWebOffice2015.js'\r\nexport default {\r\n  data () {\r\n    return {\r\n      webOfficeTpl: webOfficeTpl\r\n    }\r\n  },\r\n  methods: {\r\n    initWebOfficeObject (data) {\r\n      try {\r\n        const url = data\r\n        const arrUrl = url.split('//')\r\n        const start = arrUrl[1].indexOf('/')\r\n        let openurl = arrUrl[1].substring(start)// stop省略，截取从start开始到结尾的所有字符\r\n        if (openurl.indexOf('?') !== -1) {\r\n          openurl = openurl.split('?')[0]\r\n        }\r\n        const serveUrl = arrUrl[0] + '//' + arrUrl[1].substring(0, start)\r\n        // this.webOfficeObj.ServerUrl = 'http://test.dc.cszysoft.com:21408'\r\n        this.webOfficeObj.ServerUrl = serveUrl\r\n\r\n        this.webOfficeObj.UserName = 'XXX'\r\n        this.webOfficeObj.FileName = 'Mytemplate.doc'\r\n        this.webOfficeObj.FileType = '.doc' // FileType:文档类型  .doc  .xls\r\n        this.webOfficeObj.ShowWindow = false // 显示/隐藏进度条\r\n        this.webOfficeObj.EditType = '1' // 设置加载文档类型 0 锁定文档，1无痕迹模式，2带痕迹模式\r\n        this.webOfficeObj.ShowMenu = 1\r\n        this.webOfficeObj.ShowToolBar = 0\r\n        // this.webOfficeObj.SetCaption(this.webOfficeObj.UserName + '正在编辑文档') // 设置控件标题栏标题文本信息\r\n        // 参数顺序依次为：控件标题栏颜色、自定义菜单开始颜色、自定义工具栏按钮开始颜色、自定义工具栏按钮结束颜色、\r\n        // 自定义工具栏按钮边框颜色、自定义工具栏开始颜色、控件标题栏文本颜色（默认值为：0x000000）\r\n        // if (!this.webOfficeObj.WebSetSkin(0xdbdbdb, 0xeaeaea, 0xeaeaea, 0xdbdbdb, 0xdbdbdb, 0xdbdbdb, 0x000000)) {\r\n        //   // alert(this.webOfficeObj.Status)\r\n        // }\r\n        this.webOfficeObj.ShowMenuBar(this.webOfficeObj.ShowMenu) // 控制菜单栏是否可以显示\r\n        this.webOfficeObj.NewShowToolBar(this.webOfficeObj.ShowToolBar) // 控制Office工具栏和自定义工具栏\r\n        // this.webOfficeObj.AppendMenu('1', '打开本地文件(&L)')\r\n        // this.webOfficeObj.AppendMenu('2', '保存本地文件(&S)')\r\n        // this.webOfficeObj.AppendMenu('3', '-')\r\n        // this.webOfficeObj.AppendMenu('4', '打印预览(&C)')\r\n        // this.webOfficeObj.AppendMenu('5', '退出打印预览(&E)')\r\n        // var script = document.createElement('script')\r\n        // script.type = 'text/javascript'\r\n        // script.for = 'WebOffice2015'\r\n        // script.event = 'onload(ID, Caption, bCancel)'\r\n        // document.getElementsByTagName('head')[0].appendChild(script)\r\n        // this.webOfficeObj.AddCustomMenu()\r\n        // this.webOfficeObj.HookEnabled()\r\n        this.webOfficeObj.WebOpenOut(openurl)\r\n        console.log('try')\r\n      } catch (e) {\r\n        console.log(e)\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AACA,OAAOA,YAAP,MAAyB,6CAAzB;AACA,eAAe;EACbC,IAAI,GAAI;IACN,OAAO;MACLD,YAAY,EAAEA;IADT,CAAP;EAGD,CALY;;EAMbE,OAAO,EAAE;IACPC,mBAAmB,CAAEF,IAAF,EAAQ;MACzB,IAAI;QACF,MAAMG,GAAG,GAAGH,IAAZ;QACA,MAAMI,MAAM,GAAGD,GAAG,CAACE,KAAJ,CAAU,IAAV,CAAf;QACA,MAAMC,KAAK,GAAGF,MAAM,CAAC,CAAD,CAAN,CAAUG,OAAV,CAAkB,GAAlB,CAAd;QACA,IAAIC,OAAO,GAAGJ,MAAM,CAAC,CAAD,CAAN,CAAUK,SAAV,CAAoBH,KAApB,CAAd,CAJE,CAIsC;;QACxC,IAAIE,OAAO,CAACD,OAAR,CAAgB,GAAhB,MAAyB,CAAC,CAA9B,EAAiC;UAC/BC,OAAO,GAAGA,OAAO,CAACH,KAAR,CAAc,GAAd,EAAmB,CAAnB,CAAV;QACD;;QACD,MAAMK,QAAQ,GAAGN,MAAM,CAAC,CAAD,CAAN,GAAY,IAAZ,GAAmBA,MAAM,CAAC,CAAD,CAAN,CAAUK,SAAV,CAAoB,CAApB,EAAuBH,KAAvB,CAApC,CARE,CASF;;QACA,KAAKK,YAAL,CAAkBC,SAAlB,GAA8BF,QAA9B;QAEA,KAAKC,YAAL,CAAkBE,QAAlB,GAA6B,KAA7B;QACA,KAAKF,YAAL,CAAkBG,QAAlB,GAA6B,gBAA7B;QACA,KAAKH,YAAL,CAAkBI,QAAlB,GAA6B,MAA7B,CAdE,CAckC;;QACpC,KAAKJ,YAAL,CAAkBK,UAAlB,GAA+B,KAA/B,CAfE,CAemC;;QACrC,KAAKL,YAAL,CAAkBM,QAAlB,GAA6B,GAA7B,CAhBE,CAgB+B;;QACjC,KAAKN,YAAL,CAAkBO,QAAlB,GAA6B,CAA7B;QACA,KAAKP,YAAL,CAAkBQ,WAAlB,GAAgC,CAAhC,CAlBE,CAmBF;QACA;QACA;QACA;QACA;QACA;;QACA,KAAKR,YAAL,CAAkBS,WAAlB,CAA8B,KAAKT,YAAL,CAAkBO,QAAhD,EAzBE,CAyBwD;;QAC1D,KAAKP,YAAL,CAAkBU,cAAlB,CAAiC,KAAKV,YAAL,CAAkBQ,WAAnD,EA1BE,CA0B8D;QAChE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QACA,KAAKR,YAAL,CAAkBW,UAAlB,CAA6Bd,OAA7B;QACAe,OAAO,CAACC,GAAR,CAAY,KAAZ;MACD,CAzCD,CAyCE,OAAOC,CAAP,EAAU;QACVF,OAAO,CAACC,GAAR,CAAYC,CAAZ;MACD;IACF;;EA9CM;AANI,CAAf"}]}