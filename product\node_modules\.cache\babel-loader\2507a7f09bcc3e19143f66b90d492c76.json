{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-sliding-box\\zy-sliding-box.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-sliding-box\\zy-sliding-box.vue", "mtime": 1660102037673}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAkBA;AACA;AACA;EACAA,oBADA;;EAEAC;IACA;MACAC,WADA;MAEAC,SAFA;MAGAC;IAHA;EAKA,CARA;;EASAC;IACAC;MACAC,YADA;MAEAC;IAFA;EADA,CATA;;EAeAC;IACA;MACA;MACAC;QACAC;UACAA;QACA,CAFA;MAGA,CAJA;MAKAD;QACAC;UACAA;QACA,CAFA;MAGA,CAJA;IAKA,CAZA;EAaA,CA7BA;;EA8BAC;IACAC;MACA;MACA;;MACA;QACA;QACA;MACA,CAHA,MAGA;QACA;MACA;IACA,CAVA;;IAWAC;MACA;MACA;;MACA;QACAX;MACA;;MACAY;MACAA;MACA;IACA,CApBA;;IAqBAC;MACA;MACA;;MACA;QACAb;MACA;;MACAY;MACAA;MACA;IACA;;EA9BA,CA9BA;;EA8DAE;IACAP;IACAA;EACA;;AAjEA", "names": ["name", "data", "show", "offset", "biggest", "props", "shift", "type", "default", "mounted", "erd", "that", "methods", "biggestClick", "slidingLeft", "itemBox", "slidingRight", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "src/components/zy-sliding-box", "sources": ["zy-sliding-box.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-sliding-box\"\r\n       ref=\"zySlidingBox\">\r\n    <div class=\"zy-sliding-left\"\r\n         v-if=\"show&&offset>0||offset!=0\"\r\n         @click.stop=\"slidingLeft\"><i class=\"el-icon-d-arrow-left\"></i></div>\r\n    <div class=\"zy-sliding-right\"\r\n         v-if=\"show&&offset<biggest\"\r\n         @click.stop=\"slidingRight\"><i class=\"el-icon-d-arrow-right\"></i></div>\r\n    <div class=\"zy-sliding-box-s\">\r\n      <div class=\"zy-sliding-item-list\"\r\n           ref=\"zySlidingItemList\">\r\n        <slot></slot>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nexport default {\r\n  name: 'zySlidingBox',\r\n  data () {\r\n    return {\r\n      show: false,\r\n      offset: 0,\r\n      biggest: 0\r\n    }\r\n  },\r\n  props: {\r\n    shift: {\r\n      type: Number,\r\n      default: 168\r\n    }\r\n  },\r\n  mounted () {\r\n    this.$nextTick(() => {\r\n      const that = this\r\n      erd.listenTo(this.$refs.zySlidingBox, (element) => {\r\n        that.$nextTick(() => {\r\n          that.biggestClick()\r\n        })\r\n      })\r\n      erd.listenTo(this.$refs.zySlidingItemList, (element) => {\r\n        that.$nextTick(() => {\r\n          that.biggestClick()\r\n        })\r\n      })\r\n    })\r\n  },\r\n  methods: {\r\n    biggestClick () {\r\n      var tabBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-box-s')\r\n      var itemBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-item-list')\r\n      if (itemBox.offsetWidth > tabBox.offsetWidth) {\r\n        this.show = true\r\n        this.biggest = itemBox.offsetWidth - tabBox.offsetWidth\r\n      } else {\r\n        this.show = false\r\n      }\r\n    },\r\n    slidingLeft () {\r\n      var itemBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-item-list')\r\n      var offset = this.offset - this.shift\r\n      if (this.offset - this.shift <= 0) {\r\n        offset = 0\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      itemBox.style.transitionDuration = '.4s'\r\n      this.offset = offset\r\n    },\r\n    slidingRight () {\r\n      var itemBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-item-list')\r\n      var offset = this.offset + this.shift\r\n      if (this.biggest < this.offset + this.shift) {\r\n        offset = this.biggest\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      itemBox.style.transitionDuration = '.4s'\r\n      this.offset = offset\r\n    }\r\n  },\r\n  beforeDestroy () {\r\n    erd.uninstall(this.$refs.zySlidingBox)\r\n    erd.uninstall(this.$refs.zySlidingItemList)\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-sliding-box.scss\";\r\n</style>\r\n"]}]}