# 提案整体情况功能实现说明

## 功能概述

按照设计图要求，在 Vue 页面的`proposal_overall_situation_content`中实现了提案整体情况的展示功能。

## 实现的功能模块

### 1. 左侧数据卡片区域

- **提案总件数**: 1500 件，蓝色主题
- **立案总件数**: 600 件，蓝色主题
- **答复总件数**: 600 件，黄色主题

每个卡片都有：

- 渐变背景效果
- 发光边框
- 数字高亮显示
- 阴影效果

### 2. 右侧图表区域

#### 上方圆形进度图

- **立案率**: 69%，蓝色主题的圆形进度图，显示在背景图中央
- **答复率**: 69%，黄色主题的圆形进度图，显示在背景图中央
- 使用了 `icon_case_filing.png` 和 `icon_reply_rate.png` 作为背景图
- 圆形进度图通过绝对定位居中显示在背景图上

#### 下方答复类型统计

- **左侧饼图**: 显示面复和函复的比例分布
  - 面复：360 件
  - 函复：240 件
- **右侧进度条**: 显示具体数量和占比
  - 面复：360 件，占答复总件数 60%
  - 函复：240 件，占答复总件数 40%

## 新增的组件

### 1. CircularProgress.vue

圆形进度图组件，用于显示立案率和答复率

- 支持自定义颜色
- 支持动画效果
- 中心显示百分比和标签

### 2. ProgressBar.vue

进度条组件，用于显示答复类型的详细数据

- 支持多个进度条
- 显示数量、百分比信息
- 支持自定义颜色

## 样式特点

- 采用科技感的蓝色和黄色主题
- 渐变背景和发光效果
- 毛玻璃效果（backdrop-filter）
- 响应式布局
- 与大屏整体风格保持一致

## 关键实现细节

### 圆形进度图居中显示

```css
.chart-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .circular-progress {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    z-index: 10;
  }
}
```

### 背景图片设置

```css
&.approval-rate {
  background: url('../../../assets/largeScreen/icon_case_filing.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

&.reply-rate {
  background: url('../../../assets/largeScreen/icon_reply_rate.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}
```

## 文件结构

```
product/src/views/smartBrainLargeScreen/
├── proposalStatistics/
│   └── proposalStatisticsBox.vue (主页面)
└── components/
    ├── CircularProgress.vue (圆形进度图)
    ├── ProgressBar.vue (进度条)
    └── PieChart.vue (饼图，已修改支持答复类型)
```

## 数据结构

```javascript
// 提案整体数据
proposalOverallData: {
  totalProposals: 1500,    // 提案总件数
  approvedProposals: 600,  // 立案总件数
  repliedProposals: 600,   // 答复总件数
  approvalRate: 69,        // 立案率
  replyRate: 69           // 答复率
}

// 答复类型数据
replyTypeChartData: [     // 饼图数据
  { name: '面复', value: 360 },
  { name: '函复', value: 240 }
]

replyTypeProgressData: [  // 进度条数据
  { label: '面复', value: 360, percent: 60, color: '#00d4ff' },
  { label: '函复', value: 240, percent: 40, color: '#ffd700' }
]
```

## 使用说明

1. 数据可以通过修改`proposalOverallData`、`replyTypeChartData`和`replyTypeProgressData`来更新
2. 颜色主题可以通过修改 CSS 变量来调整
3. 组件支持响应式，会根据容器大小自动调整

## 效果预览

实现的效果完全按照设计图要求：

- 左右分布的布局结构
- 左侧三个数据卡片，带背景图片效果
- 右侧上方两个圆形进度图
- 右侧下方饼图和进度条的组合展示
