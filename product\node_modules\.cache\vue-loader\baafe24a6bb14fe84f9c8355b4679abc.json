{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\DoubleQuote\\DoubleQuote.vue?vue&type=style&index=0&id=27ab58c6&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\DoubleQuote\\DoubleQuote.vue", "mtime": 1752541693818}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouRG91YmxlUXVvdGUgew0KICBoZWlnaHQ6IDEwMCU7DQogIHdpZHRoOiAxMDAlOw0KICAudGFibGVEYXRhIHsNCiAgICAuZWwtc2Nyb2xsYmFyX193cmFwIC5lbC1zY3JvbGxiYXJfX3ZpZXcgdGggew0KICAgICAgYmFja2dyb3VuZDogI2Y1ZjdmYjsNCiAgICB9DQogICAgaGVpZ2h0OiBjYWxjKDEwMCUgLSAxNTBweCk7DQogIH0NCiAgLmJ1dHRvbi1ib3ggew0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgbWFyZ2luLXRvcDogMjBweDsNCiAgfQ0KICAuYnV0dG9uLWJveCAuZWwtYnV0dG9uIHsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICB9DQogIC5lbC1idXR0b24tLWRhbmdlci5pcy1wbGFpbjpob3ZlciwNCiAgLmVsLWJ1dHRvbi0tZGFuZ2VyLmlzLXBsYWluOmZvY3VzIHsNCiAgICAvLyBjb2xvcjogI2Y1NmM2YzsNCiAgICBiYWNrZ3JvdW5kOiAjZjU2YzZjOw0KICAgIGNvbG9yOiAjZmZmOw0KICB9DQogIC5lbC1idXR0b24tLXN1Y2Nlc3MuaXMtcGxhaW46aG92ZXIsDQogIC5lbC1idXR0b24tLXN1Y2Nlc3MuaXMtcGxhaW46Zm9jdXMgew0KICAgIGJhY2tncm91bmQ6ICM2N2MyM2E7DQogICAgY29sb3I6ICNmZmY7DQogIH0NCiAgLmRlbEJ0biB7DQogICAgY29sb3I6ICNmNTZjNmM7DQogIH0NCiAgLnBhZ2luZ19ib3ggew0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogIH0NCiAgLnNjb3JlRWRpdCB7DQogICAgd2lkdGg6IDcwMHB4Ow0KICAgIGhlaWdodDogMTAwJTsNCiAgICBwYWRkaW5nOiAyNHB4Ow0KICAgIC5mb3JtLWJ1dHRvbiB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgfQ0KICB9DQogIC5zZWFyY2gtYm94IHsNCiAgICAvL+aciOW3peS9nOWunumZhXRpdGxl5paH5a2X5qC35byPDQogICAgLnNlYXJjaC10aXRsZSB7DQogICAgICB3aWR0aDogMTMzcHg7DQogICAgICBoZWlnaHQ6IDE2cHg7DQogICAgICBmb250LXNpemU6ICR0ZXh0U2l6ZTE2Ow0KICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nIFNDOw0KICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgIGxpbmUtaGVpZ2h0OiAzNnB4Ow0KICAgICAgbWFyZ2luLWxlZnQ6IDMycHg7DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["DoubleQuote.vue"], "names": [], "mappings": ";AAybA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "DoubleQuote.vue", "sourceRoot": "src/views/AssessmentOrgan/DoubleQuote", "sourcesContent": ["<template>\r\n  <!-- 双招双引 DoubleQuote -->\r\n  <div class=\"DoubleQuote\">\r\n    <search-box @search-click=\"search\"\r\n                @reset-click=\"reset\"\r\n                title=\"双招双引筛选\">\r\n      <!--关键字查找框  -->\r\n      <zy-widget label=\"关键字\">\r\n        <el-input placeholder=\"请输入关键词\"\r\n                  v-model=\"form.keyword\"\r\n                  clearable\r\n                  @keyup.enter.native=\"search\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\">\r\n          </div>\r\n        </el-input>\r\n      </zy-widget>\r\n\r\n      <!-- 用到机构树  -->\r\n      <zy-widget label=\"部门查询\"\r\n                 v-permissions=\"'auth:double:department'\">\r\n        <zy-select v-model=\"form.officeId\"\r\n                   clearable\r\n                   @keyup.enter.native=\"search\"\r\n                   placeholder=\"请选择部门\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\">\r\n          </div>\r\n        </zy-select>\r\n      </zy-widget>\r\n\r\n      <!-- 时间查询 -->\r\n      <zy-widget label=\"时间查询\">\r\n        <!--  下拉列表中 选中年份功能  -->\r\n        <el-select v-model=\"selectedYear\"\r\n                   placeholder=\"请选择年份\"\r\n                   @keyup.enter.native=\"search\">\r\n          <el-option v-for=\"item in timeArr\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n\r\n      <!-- 审核状态查询 -->\r\n      <zy-widget label=\"审核状态查询\">\r\n        <el-select v-model=\"searchParams.auditStatusParams\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择审核状态\">\r\n          <el-option v-for=\"item in auditStatusData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n\r\n    </search-box>\r\n\r\n    <div class=\"qd-list-wrap\">\r\n      <!-- 新增/删除 按钮 -->\r\n      <div class=\"qd-btn-box\">\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-plus\"\r\n                   @click=\"handleAdd\">新增\r\n        </el-button>\r\n\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   v-permissions=\"'auth:double:checkPass'\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n        <el-button type=\"danger\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   v-permissions=\"'auth:double:checkNotPass'\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n        <!-- <el-button type=\"danger\"\r\n                   icon=\"el-icon-delete\"\r\n                   @click=\"handleBatchDelete\">删除\r\n        </el-button> -->\r\n      </div>\r\n      <div class=\"tableData\">\r\n        <zy-table>\r\n          <el-table :data=\"tableData\"\r\n                    slot=\"zytable\"\r\n                    row-key=\"menuId\"\r\n                    ref=\"multipleTable\"\r\n                    @select=\"selected\"\r\n                    @select-all=\"selectedAll\"\r\n                    :tree-props=\"{children: 'children'}\">\r\n            <el-table-column type=\"selection\"\r\n                             width=\"55\">\r\n            </el-table-column>\r\n            <el-table-column label=\"标题\"\r\n                             show-overflow-tooltip\r\n                             prop=\"title\"\r\n                             width=\"400px\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"modify(scope.row)\"\r\n                           size=\"small\"> {{scope.row.title}}\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"部门\"\r\n                             width=\"180px\"\r\n                             prop=\"officeName\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"发布时间\"\r\n                             width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{$format(scope.row.publishTime).substr(0,16)}} </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"审核状态\"\r\n                             width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <!-- <div v-if=\"scope.row.auditStatus == '1' \"> 待审核 </div>\r\n                <div v-if=\"scope.row.auditStatus == '0' \"> 审核通过</div> -->\r\n                <div>{{scope.row.auditStatus}}</div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"类型\"\r\n                             min-width=\"180\"\r\n                             show-overflow-tooltip\r\n                             prop=\"doubleType\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"操作\"\r\n                             width=\"150\"\r\n                             fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"editClick(scope.row)\"\r\n                           size=\"small\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 编辑\r\n                </el-button>\r\n\r\n                <el-button type=\"text\"\r\n                           size=\"small\"\r\n                           @click=\"handleDelete(scope.row.id,scope.row.auditStatus)\"\r\n                           :class=\"scope.row.auditStatus == '审核通过' ? '' : 'delBtn'\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 删除\r\n                </el-button>\r\n\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </zy-table>\r\n      </div>\r\n\r\n      <div class=\"paging_box\">\r\n        <el-pagination @size-change=\"handleSizeChange\"\r\n                       @current-change=\"handleCurrentChange\"\r\n                       :current-page.sync=\"currentPage\"\r\n                       :page-sizes=\"[10, 20, 30, 40]\"\r\n                       :page-size.sync=\"pageSize\"\r\n                       background\r\n                       layout=\"total, prev, pager, next, sizes, jumper\"\r\n                       :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport tableData from '@mixins/tableData'\r\n\r\nimport { mapGetters } from 'vuex'\r\nexport default {\r\n  name: 'DoubleQuote',\r\n  mixins: [tableData],\r\n  components: {\r\n  },\r\n  data () {\r\n    return {\r\n      officeData: [], // 机构树,\r\n      auditStatusData: [], // 审核状态\r\n      searchParams: {\r\n\r\n        officeId: '',\r\n        auditStatusParams: ''\r\n\r\n      },\r\n      time: [],\r\n      tableData: [],\r\n      officeName: '',\r\n      selectedYear: '',\r\n      timeArr: [],\r\n\r\n      publishStartTime: '',\r\n      form: {\r\n        id: '',\r\n        keyword: '',\r\n        officeId: ''\r\n      },\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      total: 10,\r\n      show: false,\r\n      showExport: false,\r\n      rowId: '',\r\n      choose: [],\r\n      selectObj: [],\r\n      selectData: [],\r\n\r\n      ClasstypeData: [],\r\n      reporttype: [],\r\n      approve: [],\r\n      years: '',\r\n      useInfo: JSON.parse(sessionStorage.getItem('userzx')).otherInfo.userOtherInfo,\r\n      hanlShow: false,\r\n      processStatus: null\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  activated () {\r\n    this.getDoubleQuoteList()\r\n  },\r\n  created () {\r\n    this.getDoubleQuoteList()\r\n    // this.socialcategorytree()\r\n  },\r\n  watch: {\r\n  },\r\n  computed: {\r\n    ...mapGetters(['conversion', 'permissions'])\r\n  },\r\n  mounted () {\r\n    this.treeList()\r\n    this.dictionaryPubkvs()\r\n    this.initTime()\r\n    this.getDoubleQuoteList()\r\n\r\n    const mune = JSON.parse(sessionStorage.getItem('menuChild'))\r\n    const path = this.$route.path\r\n    this.getPa(mune, path)\r\n  },\r\n  inject: ['newTab'],\r\n  methods: {\r\n    getPa (data, path) {\r\n      data.forEach(item => {\r\n        if (item.children) {\r\n          this.getPa(item.children, path)\r\n        }\r\n        if (item.to === path) {\r\n          this.permissionsArr = item.permissions\r\n        }\r\n      })\r\n    },\r\n\r\n    /**\r\n    *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluate_audit_status'\r\n      })\r\n      var { data } = res\r\n      this.auditStatusData = data.evaluate_audit_status\r\n    },\r\n    /**\r\n    *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    // 请求后台数据 初始化时间 在时间查询下拉框内\r\n    async initTime () {\r\n      const res = await this.$api.AssessmentOrgan.getMonthlyWorkTime()\r\n      var { data } = res\r\n      this.timeArr = data\r\n    },\r\n    search () { // 搜索\r\n      this.currentPage = 1\r\n      this.getDoubleQuoteList()\r\n    },\r\n    reset () { // 重置\r\n      this.form.officeName = ''\r\n      this.form.keyword = ''\r\n      this.selectedYear = ''\r\n      this.form.officeId = ''\r\n      this.searchParams.auditStatusParams = ''\r\n      this.time.splice(0, this.time.length)\r\n      this.getDoubleQuoteList()\r\n    },\r\n    // 新增按钮方法\r\n    handleAdd () {\r\n      const mid = new Date().getTime().toString()\r\n      this.newTab({\r\n        name: '新建双招双引',\r\n        menuId: mid,\r\n        to: '/DoubleQuoteAddOrEdit',\r\n        params: { mid, titleId: 1 }\r\n      })\r\n    },\r\n    // 删除按钮方法  批量删除\r\n    handleDelete (ids) { // TODO: handleDelete 和 handleBatchDelete 1.组合使用为新增/删除栏的删除功能 2.单独使用handleDelete则为操作项的删除功能\r\n      if (ids.auditStatus !== '审核通过') {\r\n        this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.$api.AssessmentOrgan.reqDelDoubleQuote({ ids }).then((res) => {\r\n            if (res.errcode === 200) {\r\n              this.getDoubleQuoteList()// 删除后更新页面\r\n              this.$message.success('删除成功')\r\n            }\r\n          })\r\n        }).catch(() => {\r\n          this.$message.info('取消删除')\r\n          // this.getDoubleQuoteList()\r\n          return false\r\n        })\r\n      } else {\r\n        this.$message.info('不能删除审核通过项')\r\n        return false\r\n      }\r\n    },\r\n\r\n    handleBatchDelete () {\r\n      if (this.choose.length === 0) {\r\n        return this.$message.warning('请选择想要删除的项')\r\n      }\r\n      this.handleDelete(this.choose.join(','))\r\n    },\r\n\r\n    async getDoubleQuoteList () {\r\n      const res = await this.$api.AssessmentOrgan.reqDoubleQuoteList({\r\n        keyword: this.form.keyword,\r\n        pageNo: this.currentPage,\r\n        pageSize: this.pageSize,\r\n        sedateId: this.selectedYear,\r\n        publishStartTime: this.time[0],\r\n        publishEndTime: this.time[1],\r\n        officeId: this.form.officeId, // 部门查询\r\n        auditStatus: this.searchParams.auditStatusParams // 审核状态\r\n      })\r\n      var { data, total } = res\r\n      const arr = []\r\n      data.forEach(item => {\r\n        if (item.submiterType) {\r\n          if (item.submiterType.indexOf('-') != -1) { // eslint-disable-line\r\n            item.submiterType = item.submiterType.split('-')[1]\r\n            arr.push(item)\r\n          } else {\r\n            arr.push(item)\r\n          }\r\n        } else {\r\n          arr.push(item)\r\n        }\r\n      })\r\n\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n      this.selectObj = []\r\n      this.selectData = []\r\n      this.$nextTick(function () {\r\n        this.memoryChecked()\r\n      })\r\n    },\r\n\r\n    modify (row) {\r\n      this.newTab({\r\n        name: '详情',\r\n        menuId: '1',\r\n        to: '/DoubleDetails',\r\n        params: {\r\n          rowId: row.id,\r\n          approve: this.permissionsArr.includes('auth:double:checkPass'),\r\n          noApprove: this.permissionsArr.includes('auth:double:checkNotPass')\r\n        }\r\n      })\r\n    },\r\n    editClick (row) {\r\n      this.newTab({ name: '编辑双招双引', menuId: '1', to: '/DoubleQuoteAddOrEdit', params: { id: row.id } })\r\n    },\r\n\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckDouble(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 审核 (双招双引)\r\n    async getCheckDouble (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckDouble({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getDoubleQuoteList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n\r\n    handleSizeChange () {\r\n      this.getDoubleQuoteList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getDoubleQuoteList()\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.DoubleQuote {\r\n  height: 100%;\r\n  width: 100%;\r\n  .tableData {\r\n    .el-scrollbar__wrap .el-scrollbar__view th {\r\n      background: #f5f7fb;\r\n    }\r\n    height: calc(100% - 150px);\r\n  }\r\n  .button-box {\r\n    background: #fff;\r\n    margin-top: 20px;\r\n  }\r\n  .button-box .el-button {\r\n    background: #fff;\r\n  }\r\n  .el-button--danger.is-plain:hover,\r\n  .el-button--danger.is-plain:focus {\r\n    // color: #f56c6c;\r\n    background: #f56c6c;\r\n    color: #fff;\r\n  }\r\n  .el-button--success.is-plain:hover,\r\n  .el-button--success.is-plain:focus {\r\n    background: #67c23a;\r\n    color: #fff;\r\n  }\r\n  .delBtn {\r\n    color: #f56c6c;\r\n  }\r\n  .paging_box {\r\n    background: #fff;\r\n  }\r\n  .scoreEdit {\r\n    width: 700px;\r\n    height: 100%;\r\n    padding: 24px;\r\n    .form-button {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n  .search-box {\r\n    //月工作实际title文字样式\r\n    .search-title {\r\n      width: 133px;\r\n      height: 16px;\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-left: 32px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}