{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-sliding-box\\zy-sliding-box.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-sliding-box\\zy-sliding-box.vue", "mtime": 1660102037673}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["zy-sliding-box.vue"], "names": [], "mappings": ";AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zy-sliding-box.vue", "sourceRoot": "src/components/zy-sliding-box", "sourcesContent": ["<template>\r\n  <div class=\"zy-sliding-box\"\r\n       ref=\"zySlidingBox\">\r\n    <div class=\"zy-sliding-left\"\r\n         v-if=\"show&&offset>0||offset!=0\"\r\n         @click.stop=\"slidingLeft\"><i class=\"el-icon-d-arrow-left\"></i></div>\r\n    <div class=\"zy-sliding-right\"\r\n         v-if=\"show&&offset<biggest\"\r\n         @click.stop=\"slidingRight\"><i class=\"el-icon-d-arrow-right\"></i></div>\r\n    <div class=\"zy-sliding-box-s\">\r\n      <div class=\"zy-sliding-item-list\"\r\n           ref=\"zySlidingItemList\">\r\n        <slot></slot>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nexport default {\r\n  name: 'zySlidingBox',\r\n  data () {\r\n    return {\r\n      show: false,\r\n      offset: 0,\r\n      biggest: 0\r\n    }\r\n  },\r\n  props: {\r\n    shift: {\r\n      type: Number,\r\n      default: 168\r\n    }\r\n  },\r\n  mounted () {\r\n    this.$nextTick(() => {\r\n      const that = this\r\n      erd.listenTo(this.$refs.zySlidingBox, (element) => {\r\n        that.$nextTick(() => {\r\n          that.biggestClick()\r\n        })\r\n      })\r\n      erd.listenTo(this.$refs.zySlidingItemList, (element) => {\r\n        that.$nextTick(() => {\r\n          that.biggestClick()\r\n        })\r\n      })\r\n    })\r\n  },\r\n  methods: {\r\n    biggestClick () {\r\n      var tabBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-box-s')\r\n      var itemBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-item-list')\r\n      if (itemBox.offsetWidth > tabBox.offsetWidth) {\r\n        this.show = true\r\n        this.biggest = itemBox.offsetWidth - tabBox.offsetWidth\r\n      } else {\r\n        this.show = false\r\n      }\r\n    },\r\n    slidingLeft () {\r\n      var itemBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-item-list')\r\n      var offset = this.offset - this.shift\r\n      if (this.offset - this.shift <= 0) {\r\n        offset = 0\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      itemBox.style.transitionDuration = '.4s'\r\n      this.offset = offset\r\n    },\r\n    slidingRight () {\r\n      var itemBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-item-list')\r\n      var offset = this.offset + this.shift\r\n      if (this.biggest < this.offset + this.shift) {\r\n        offset = this.biggest\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      itemBox.style.transitionDuration = '.4s'\r\n      this.offset = offset\r\n    }\r\n  },\r\n  beforeDestroy () {\r\n    erd.uninstall(this.$refs.zySlidingBox)\r\n    erd.uninstall(this.$refs.zySlidingItemList)\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-sliding-box.scss\";\r\n</style>\r\n"]}]}