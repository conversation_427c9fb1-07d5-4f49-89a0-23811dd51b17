{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecord\\detailsContents.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\MonthlyWorkRecord\\detailsContents.vue", "mtime": 1752541693846}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["detailsContents.vue"], "names": [], "mappings": ";AA6DA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "detailsContents.vue", "sourceRoot": "src/views/AssessmentOrgan/MonthlyWorkRecord", "sourcesContent": ["<template>\r\n  <div class=\"detailComment scrollBar \">\r\n    <div class=\"officeDetial-title\"> {{form.title}} </div>\r\n    <div class=\"officeDetial-org\">\r\n      <div class=\"org-item\"> 所属个人: <span> {{form.publishUserName}}</span> </div>\r\n      <div class=\"org-item\"> 时间： <span>{{ $format(form.publishTime).substr(0,16)}}</span> </div>\r\n      <div class=\"org-item\"> 部门： <span> {{form.officeName}}</span> </div>\r\n      <div>\r\n\r\n        <el-button type=\"primary\"\r\n                   size=\"small\"\r\n                   v-if=\"this.approve==='true'\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n\r\n        <el-button type=\"danger\"\r\n                   size=\"small\"\r\n                   v-if=\"this.noApprove==='true'\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n      </div>\r\n\r\n    </div>\r\n    <!-- 内容 -->\r\n    <div class=\"contBox\">\r\n      <div class=\"content\"\r\n           v-if=\"form.content\"\r\n           v-html=\"form.content\">\r\n      </div>\r\n    </div>\r\n\r\n    <!-- *****附件***** -->\r\n    <div class=\"fileBox\"\r\n         v-if=\"form.attachment\">\r\n      <div class=\"file_title\"\r\n           v-if=\"form.attachment.length!==0\"> 附件 </div>\r\n\r\n      <div class=\"fileListt\">\r\n        <div class=\"file_item\"\r\n             v-for=\"(item,index) in form.attachment \"\r\n             :key=\"index\">\r\n\r\n          <div class=\"file_name\"> {{item.oldName}} </div>\r\n\r\n          <div class=\"file_load\">\r\n            <div class=\"load_text\"><a :href=\"item.fullPath\"\r\n                 target=\"_blank\">预览</a>\r\n            </div>\r\n            <div class=\"shu\"></div>\r\n            <div class=\"load_text\"\r\n                 @click=\"download(item)\">下载</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { mapActions, mapState } from 'vuex'\r\n\r\nexport default {\r\n  name: 'detailsContents',\r\n  components: {},\r\n\r\n  data () {\r\n    return {\r\n      ids: [],\r\n      TyposShow: false,\r\n      // SimilarityShow: false,\r\n      form: {},\r\n      fileList: [],\r\n      manuscriptData: {},\r\n      all: this.$route.query.all || false,\r\n      rowId: this.$route.query.rowId,\r\n\r\n      approve: this.$route.query.approve,\r\n      noApprove: this.$route.query.noApprove,\r\n\r\n      manuscriptFlag: false,\r\n      isexcellent: this.$route.query.isexcellent || false,\r\n      helper: this.$route.query.helper || false,\r\n      similar: '0%',\r\n      suspend: 0,\r\n      wrong: 0,\r\n      show: true,\r\n      clickTime: ''\r\n\r\n    }\r\n  },\r\n  inject: ['openoffice', 'openPdf'],\r\n  created () {\r\n    this.getWorkDetails()\r\n  },\r\n  mounted () {\r\n  },\r\n  methods: {\r\n\r\n    passClick (auditStatus) {\r\n      this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.getCheckMonthlyWork(this.$route.query.rowId, auditStatus)\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消操作'\r\n        })\r\n      })\r\n    },\r\n    // 审核 (加分项)\r\n    async getCheckMonthlyWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckMonthlyWork({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n\r\n    ...mapActions(['getWorkDetails']),\r\n    showHelper () {\r\n      if (new Date().getTime() - this.clickTime > 1000) {\r\n        this.clickTime = new Date().getTime()\r\n        this.show = !this.show\r\n      }\r\n    },\r\n\r\n    async getcorrector () {\r\n      const res = await this.$api.publicOpinionNew.corrector({\r\n        title: '',\r\n        content: this.form.content,\r\n        sessions: '',\r\n        times: ''\r\n      })\r\n      var data = JSON.parse(res.data)\r\n\r\n      this.suspend = data.detail.length\r\n      this.wrong = data.suspend_detail.length\r\n    },\r\n    // 预览(另一种方法替代)\r\n    // priew (data) {\r\n    //   const arr = ['doc', 'docx', 'xlsx']\r\n    //   if (arr.includes(data.extension)) {\r\n    //     this.openoffice(data.openUrl)\r\n    //   }\r\n    // },\r\n\r\n    async getWorkDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqWorkDetails(this.$route.query.rowId)\r\n      var { data } = res\r\n      this.form = data\r\n      if (this.form.content) {\r\n        this.form.content = this.form.content.replace(/&amp;nbsp;/g, ' ') // 消除空格字符\r\n      }\r\n    },\r\n    // 通用的附件下载方法download 只需要改对应的附件id和名字\r\n    download (data) {\r\n      this.$api.proposal.downloadFile({ id: this.form.attachment.id }, this.form.attachment.oldName)\r\n    },\r\n    callback (data) {\r\n      this.TyposShow = false\r\n      console.log(data)\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    // 测试: 通过映射函数 获取title信息\r\n    ...mapState(['title'])\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" >\r\n.detailComment {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding-top: 33px;\r\n  background: #fff;\r\n  .officeDetial-title {\r\n    font-size: 26px;\r\n    font-size: 24px;\r\n    font-family: PingFang SC;\r\n    font-weight: 800;\r\n    color: #333333;\r\n    line-height: 36px;\r\n    text-align: center;\r\n    margin: 29px;\r\n  }\r\n  .officeDetial-org {\r\n    font-size: $textSize14;\r\n    font-family: PingFang SC;\r\n    font-weight: 400;\r\n    line-height: 36px;\r\n    display: flex;\r\n    justify-content: space-around;\r\n    // padding-left: 40px;\r\n    .org-item {\r\n      color: #999999;\r\n      // margin-right: 140px;\r\n      // min-width: 300px;\r\n      span {\r\n        margin-left: 38px;\r\n      }\r\n    }\r\n    // .org-item + .org-item {\r\n    //     margin-left: 140px;\r\n    // }\r\n  }\r\n\r\n  .contBox {\r\n    margin-top: 20px;\r\n    border-top: 2px solid #ebebeb;\r\n    display: flex;\r\n\r\n    .content {\r\n      flex: 1;\r\n      padding: 30px 40px;\r\n      line-height: 30px;\r\n      min-height: 500px;\r\n    }\r\n    .content + .content {\r\n      border-left: 1px solid #ebebeb;\r\n    }\r\n  }\r\n\r\n  .similarityImg {\r\n    padding-left: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    img {\r\n      width: 80px;\r\n      cursor: pointer;\r\n    }\r\n    .analysisReslut {\r\n      height: 60px;\r\n      line-height: 60px;\r\n      padding: 0 44px;\r\n      // background: #f5f5fb;\r\n      background-image: url(\"../../../assets/qdimg/round.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      border-radius: 20px;\r\n      span {\r\n        font-size: 26px;\r\n        font-family: PangMenZhengDao;\r\n        font-weight: 700;\r\n        color: #007bff;\r\n        line-height: 36px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n  .manuscript {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-right: 40px;\r\n    padding-bottom: 20px;\r\n    .yuangoa {\r\n      flex: 1;\r\n      span {\r\n        font-size: 26px;\r\n        font-family: PingFang SC;\r\n        font-weight: 500;\r\n        color: #999999;\r\n        line-height: 36px;\r\n        margin-left: 40px;\r\n      }\r\n      span + span {\r\n        margin-left: 50%;\r\n      }\r\n    }\r\n  }\r\n  .fileBox {\r\n    width: 100%;\r\n    background: #ffffff;\r\n    border-radius: 10px;\r\n    padding: 30px;\r\n    .file_title {\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-bottom: 23px;\r\n    }\r\n    .fileListt {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      justify-content: space-between;\r\n      .file_item {\r\n        width: 48%;\r\n        background: #f5f5fb;\r\n        flex-shrink: 0;\r\n        height: 60px;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0 18px;\r\n        margin-bottom: 10px;\r\n        .file_type {\r\n          width: 32px;\r\n          height: 32px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .file_name {\r\n          margin-left: 12px;\r\n          flex: 1;\r\n          cursor: pointer;\r\n        }\r\n        .file_load {\r\n          display: flex;\r\n          align-items: center;\r\n          .load_text {\r\n            font-size: $textSize16;\r\n            font-family: PingFang SC;\r\n            font-weight: 500;\r\n            color: #007bff;\r\n            line-height: 36px;\r\n            cursor: pointer;\r\n          }\r\n          .shu {\r\n            width: 2px;\r\n            height: 22px;\r\n            background: #4f96fe;\r\n            margin: 0 12px;\r\n          }\r\n          .del {\r\n            width: 24px;\r\n            height: 24px;\r\n            margin-left: 23px;\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .hanldtype {\r\n    display: flex;\r\n    padding: 20px 30px;\r\n    justify-content: space-between;\r\n    > div {\r\n      font-weight: 700;\r\n    }\r\n  }\r\n  .handinfo {\r\n    width: 100%;\r\n    .hanldClounm .hanldCont .el-checkbox {\r\n      width: 25%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}