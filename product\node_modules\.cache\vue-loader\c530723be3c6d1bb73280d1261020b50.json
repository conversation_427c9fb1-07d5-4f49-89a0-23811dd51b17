{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-sliding-box\\zy-sliding-box.vue?vue&type=style&index=0&id=35c6f262&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-sliding-box\\zy-sliding-box.vue", "mtime": 1660102037673}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3p5LXNsaWRpbmctYm94LnNjc3MiOw0K"}, {"version": 3, "sources": ["zy-sliding-box.vue"], "names": [], "mappings": ";AAyFA", "file": "zy-sliding-box.vue", "sourceRoot": "src/components/zy-sliding-box", "sourcesContent": ["<template>\r\n  <div class=\"zy-sliding-box\"\r\n       ref=\"zySlidingBox\">\r\n    <div class=\"zy-sliding-left\"\r\n         v-if=\"show&&offset>0||offset!=0\"\r\n         @click.stop=\"slidingLeft\"><i class=\"el-icon-d-arrow-left\"></i></div>\r\n    <div class=\"zy-sliding-right\"\r\n         v-if=\"show&&offset<biggest\"\r\n         @click.stop=\"slidingRight\"><i class=\"el-icon-d-arrow-right\"></i></div>\r\n    <div class=\"zy-sliding-box-s\">\r\n      <div class=\"zy-sliding-item-list\"\r\n           ref=\"zySlidingItemList\">\r\n        <slot></slot>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nexport default {\r\n  name: 'zySlidingBox',\r\n  data () {\r\n    return {\r\n      show: false,\r\n      offset: 0,\r\n      biggest: 0\r\n    }\r\n  },\r\n  props: {\r\n    shift: {\r\n      type: Number,\r\n      default: 168\r\n    }\r\n  },\r\n  mounted () {\r\n    this.$nextTick(() => {\r\n      const that = this\r\n      erd.listenTo(this.$refs.zySlidingBox, (element) => {\r\n        that.$nextTick(() => {\r\n          that.biggestClick()\r\n        })\r\n      })\r\n      erd.listenTo(this.$refs.zySlidingItemList, (element) => {\r\n        that.$nextTick(() => {\r\n          that.biggestClick()\r\n        })\r\n      })\r\n    })\r\n  },\r\n  methods: {\r\n    biggestClick () {\r\n      var tabBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-box-s')\r\n      var itemBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-item-list')\r\n      if (itemBox.offsetWidth > tabBox.offsetWidth) {\r\n        this.show = true\r\n        this.biggest = itemBox.offsetWidth - tabBox.offsetWidth\r\n      } else {\r\n        this.show = false\r\n      }\r\n    },\r\n    slidingLeft () {\r\n      var itemBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-item-list')\r\n      var offset = this.offset - this.shift\r\n      if (this.offset - this.shift <= 0) {\r\n        offset = 0\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      itemBox.style.transitionDuration = '.4s'\r\n      this.offset = offset\r\n    },\r\n    slidingRight () {\r\n      var itemBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-item-list')\r\n      var offset = this.offset + this.shift\r\n      if (this.biggest < this.offset + this.shift) {\r\n        offset = this.biggest\r\n      }\r\n      itemBox.style.transform = `translateX(-${offset}px)`\r\n      itemBox.style.transitionDuration = '.4s'\r\n      this.offset = offset\r\n    }\r\n  },\r\n  beforeDestroy () {\r\n    erd.uninstall(this.$refs.zySlidingBox)\r\n    erd.uninstall(this.$refs.zySlidingItemList)\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-sliding-box.scss\";\r\n</style>\r\n"]}]}