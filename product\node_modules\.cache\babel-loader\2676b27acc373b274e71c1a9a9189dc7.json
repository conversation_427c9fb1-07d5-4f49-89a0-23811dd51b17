{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\finishDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\finishDetail.vue", "mtime": 1752541693790}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA+IA;AACA;AACA;AAEA;EACAA,oBADA;EAEAC,mBAFA;EAGAC;IACAC,eADA;IAEAC;EAFA,CAHA;;EAOAC;IACA;MACAC,iBADA;MAEAC,uBAFA;MAGAC,aAHA;MAIAC,gBAJA;MAKAC,SALA;MAMAC,YANA;MAOAC,OAPA;MAOA;MACAC,cARA;MASAC;IATA;EAYA,CApBA;;EAqBAC,2BArBA;EAsBAC,kBAtBA;;EAwBAC;IACA;MACA;IACA;EACA,CA5BA;;EA6BAC;IACAC;MACA;MACA;IACA,CAJA;;IAKAC;MACA;QACA;UACAC,uBADA;UAEAC,sBAFA;UAGAC;QAHA,GAIAC,IAJA,CAIA;UACA;QACA,CANA,EAMAC,KANA,CAMA;UACA;YACAF,YADA;YAEAG;UAFA;QAIA,CAXA;MAYA,CAbA,MAaA;QACA;UACAA,oBADA;UAEAH;QAFA;MAIA;IACA,CAzBA;;IA0BA;IACA;MACA;QAAAI;QAAAC;MAAA;MACA;QAAAC;QAAAC;MAAA;;MACA;QACA;QACA;UACAJ,eADA;UAEAH;QAFA;MAIA;IACA,CArCA;;IAsCAQ;MACA,wBADA,CACA;;MACA;MACA,2BAHA,CAGA;IACA,CA1CA;;IA2CA;IACA;MACA;QACAtB,qBADA;QACA;QACAC,wBAFA;QAGAC,uBAHA;QAIA;QACAqB,2BALA;QAMAJ;MANA;MAQA;QAAAvB;QAAAS;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA,CAFA,EAfA,CAkBA;MACA;IACA,CAhEA;;IAiEA;IACAmB;MACA;MACA,aAFA,CAEA;IACA,CArEA;;IAsEA;IACAC;MACA;MACA;IACA,CA1EA;;IA2EA;IACAC;MACA;MACA;IACA,CA/EA;;IAgFA;IACAC;MAAA;MACA;QACAf,uBADA;QAEAC,sBAFA;QAGAC;MAHA,GAIAC,IAJA,CAIA;QACA;UAAAG;QAAA;UACA;YACA,2BADA,CACA;;YACA;UACA;QACA,CALA;MAMA,CAXA,EAWAF,KAXA,CAWA;QACA,2BADA,CAEA;;QACA;MACA,CAfA;IAgBA,CAlGA;;IAmGAY;MACA;QACA;MACA;;MACA;IACA,CAxGA;;IAyGA;IACAC;MACA;IACA,CA5GA;;IA6GAC;MACA;IACA;;EA/GA;AA7BA", "names": ["name", "mixins", "components", "newFinishDetail", "FinishDetailPop", "data", "showFinish", "showFinishDetail", "tableData", "evaluationId", "pageNo", "pageSize", "uid", "currentPage", "total", "props", "inject", "mounted", "methods", "updateList", "passClick", "confirmButtonText", "cancelButtonText", "type", "then", "catch", "message", "ids", "auditStatus", "<PERSON><PERSON><PERSON>", "errmsg", "newCallback", "memberType", "finishStatus", "handleClick", "modify", "handleDelete", "handleBatchDelete", "handleSizeChange", "handleCurrentChange"], "sourceRoot": "src/views/AssessmentOrgan/BusinessObjectives", "sources": ["finishDetail.vue"], "sourcesContent": ["<template>\r\n  <!-- 点击完成情况 -->\r\n  <div class=\"finishDetail\">\r\n    <div class=\"buttonColumn\">\r\n      <!-- 新增/删除 按钮 -->\r\n      <div class=\"qd-btn-box\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"finishStatus\"\r\n          >新增\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          plain\r\n          @click=\"handleBatchDelete\"\r\n          >删除\r\n        </el-button>\r\n\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-circle-check\"\r\n          v-permissions=\"\r\n            'auth:business:checkPass'\r\n              ? 'auth:business:checkPass'\r\n              : 'auth:innovation:checkPass'\r\n          \"\r\n          @click=\"passClick(2)\"\r\n          >审核通过\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-remove-outline\"\r\n          v-permissions=\"\r\n            'auth:business:checkNotPass'\r\n              ? 'auth:business:checkNotPass'\r\n              : 'auth:innovation:checkNotPass'\r\n          \"\r\n          @click=\"passClick(3)\"\r\n          >审核不通过\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"listView\">\r\n      <zy-table>\r\n        <el-table\r\n          slot=\"zytable\"\r\n          :data=\"tableData\"\r\n          row-key=\"id\"\r\n          ref=\"multipleTable\"\r\n          @select=\"selected\"\r\n          @select-all=\"selectedAll\"\r\n          :header-cell-style=\"{ background: '#eef1f6', color: '#606266' }\"\r\n          tooltip-effect=\"dark\"\r\n          class=\"tableStyle\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\"> </el-table-column>\r\n\r\n          <el-table-column\r\n            label=\"标题\"\r\n            show-overflow-tooltip\r\n            prop=\"title\"\r\n            width=\"450\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" @click=\"modify(scope.row)\" size=\"small\">\r\n                {{ scope.row.title }}\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"审核状态\" width=\"110\" prop=\"auditStatus\">\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"overTime\" label=\"完成时间\" width=\"220\">\r\n            <template slot-scope=\"scope\">\r\n              <div>{{ $format(scope.row.overTime).substr(0, 16) }}</div>\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"auditStatus\"\r\n                         label=\"审核状态\"\r\n                         show-overflow-tooltip>\r\n          <template slot-scope=\"scope\"> -->\r\n          <!-- <div v-if=\"scope.row.auditStatus == '1' \"> 待审核 </div>\r\n                <div v-if=\"scope.row.auditStatus == '0' \"> 审核通过</div> -->\r\n          <!-- <div>{{scope.row.auditStatus}}</div>\r\n          </template>\r\n        </el-table-column> -->\r\n\r\n          <el-table-column label=\"操作\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                type=\"text\"\r\n                @click=\"handleClick(scope.row)\"\r\n                size=\"small\"\r\n              >\r\n                编辑</el-button\r\n              >\r\n\r\n              <el-button\r\n                type=\"text\"\r\n                @click=\"handleDelete(scope.row.id)\"\r\n                class=\"delBtn\"\r\n                size=\"small\"\r\n              >\r\n                删除</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </zy-table>\r\n    </div>\r\n\r\n    <div class=\"paging_box\">\r\n      <el-pagination\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page.sync=\"currentPage\"\r\n        :page-sizes=\"[10, 20, 30, 40]\"\r\n        :page-size.sync=\"pageSize\"\r\n        background\r\n        layout=\"total, prev, pager, next, sizes, jumper\"\r\n        :total=\"total\"\r\n      >\r\n      </el-pagination>\r\n    </div>\r\n\r\n    <zy-pop-up v-model=\"showFinish\" class=\"titleStyle\" title=\"完成情况\">\r\n      <newFinishDetail :id=\"id\" :uid=\"uid\" @newCallback=\"newCallback\">\r\n      </newFinishDetail>\r\n    </zy-pop-up>\r\n\r\n    <zy-pop-up\r\n      v-model=\"showFinishDetail\"\r\n      class=\"titleStyle\"\r\n      title=\"完成情况详情\"\r\n      :beforeClose=\"updateList\"\r\n    >\r\n      <FinishDetailPop :id=\"id\" :uid=\"uid\" @newCallback=\"newCallback\">\r\n      </FinishDetailPop>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n<script>\r\nimport newFinishDetail from './newFinishDetail.vue'\r\nimport FinishDetailPop from './FinishDetailPop.vue'\r\nimport tableData from '@mixins/tableData'\r\n\r\nexport default {\r\n  name: 'finishDetail',\r\n  mixins: [tableData],\r\n  components: {\r\n    newFinishDetail,\r\n    FinishDetailPop\r\n  },\r\n  data () {\r\n    return {\r\n      showFinish: false,\r\n      showFinishDetail: false,\r\n      tableData: [],\r\n      evaluationId: '',\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      uid: '', // 完成情况列表的id\r\n      currentPage: 1,\r\n      total: 10\r\n\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  inject: ['newTab'],\r\n\r\n  mounted () {\r\n    if (this.id) {\r\n      this.getfinishDetailList()\r\n    }\r\n  },\r\n  methods: {\r\n    updateList () {\r\n      this.showFinishDetail = false\r\n      this.getfinishDetailList()\r\n    },\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckWork(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 审核\r\n    async getCheckWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckFinishDetail({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getfinishDetailList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n    newCallback () {\r\n      this.showFinish = false // 关闭弹窗\r\n      this.showFinishDetail = false\r\n      this.getfinishDetailList() // 重新调用(更新)一次列表\r\n    },\r\n    // 请求后台数据 获取列表信息\r\n    async getfinishDetailList () {\r\n      const res = await this.$api.AssessmentOrgan.reqfinishDetailList({\r\n        evaluationId: this.id, // TODO:工作目标或创新创优id(需检查是否传错)\r\n        pageNo: this.currentPage,\r\n        pageSize: this.pageSize,\r\n        // *******************\r\n        memberType: this.memberType,\r\n        auditStatus: this.auditStatus\r\n      })\r\n      var { data, total } = res\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n      this.selectObj = []\r\n      this.selectData = []\r\n      this.$nextTick(function () {\r\n        this.memoryChecked()\r\n      })\r\n      // this.choose = []\r\n      // this.selectObjData = []\r\n    },\r\n    // 新增 完成情况\r\n    finishStatus () {\r\n      this.showFinish = true\r\n      this.uid = 0 // 将newFinishDetail组件的属性:uid设置为0 (false) 达到新增页面效果\r\n    },\r\n    // 编辑\r\n    handleClick (row) {\r\n      this.uid = row.id\r\n      this.showFinish = true\r\n    },\r\n    // 详情\r\n    modify (row) {\r\n      this.uid = row.id\r\n      this.showFinishDetail = true\r\n    },\r\n    // 删除按钮方法  批量删除\r\n    handleDelete (ids) { // TODO: handleDelete 和 handleBatchDelete 1.组合使用为新增/删除栏的删除功能 2.单独使用handleDelete则为操作项的删除功能\r\n      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$api.AssessmentOrgan.reqDelFinishDetail({ ids }).then((res) => {\r\n          if (res.errcode === 200) {\r\n            this.getfinishDetailList()// 删除后更新页面\r\n            this.$message.success('删除成功')\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('取消删除')\r\n        // this.getfinishDetailList()\r\n        return false\r\n      })\r\n    },\r\n    handleBatchDelete () {\r\n      if (this.choose.length === 0) {\r\n        return this.$message.warning('请选择想要删除的项')\r\n      }\r\n      this.handleDelete(this.choose.join(','))\r\n    },\r\n    // 底部页签\r\n    handleSizeChange () {\r\n      this.getfinishDetailList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getfinishDetailList()\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.finishDetail {\r\n  width: 1100px;\r\n  height: 600px;\r\n  padding: 1px 40px;\r\n  overflow: hidden;\r\n  .qd-btn-box {\r\n    padding-bottom: 5px;\r\n  }\r\n\r\n  .listView {\r\n    height: calc(100% - 132px);\r\n    .tableStyle {\r\n      width: 100%;\r\n      height: 500px;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n    }\r\n  }\r\n\r\n  .tableZy {\r\n    height: 500px;\r\n  }\r\n}\r\n</style>\r\n"]}]}