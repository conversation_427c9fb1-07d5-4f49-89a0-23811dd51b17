{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-table\\zy-table.vue?vue&type=style&index=0&id=060bac62&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-table\\zy-table.vue", "mtime": 1752541693589}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3p5LXRhYmxlLnNjc3MiOw0K"}, {"version": 3, "sources": ["zy-table.vue"], "names": [], "mappings": ";AAuHA", "file": "zy-table.vue", "sourceRoot": "src/components/zy-table", "sourcesContent": ["\r\n<template>\r\n  <div class=\"zy-table\"\r\n       ref=\"zy-table\">\r\n    <el-scrollbar class=\"my-scroll-bar\"\r\n                  :style=\"{width:width+'px',height:height+'px',}\">\r\n      <slot name=\"zytable\"></slot>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nexport default {\r\n  name: 'zyTable',\r\n  data () {\r\n    return {\r\n      width: 0,\r\n      height: 0,\r\n      top: 0,\r\n      left: 0\r\n    }\r\n  },\r\n  // created () {\r\n  //   this.scrollshow()\r\n  // },\r\n  activated () {\r\n    this.scrollshow()\r\n    this.handleScroll()\r\n  },\r\n  mounted () {\r\n    this.$nextTick(() => {\r\n      if (this.$refs['zy-table']) {\r\n        this.$refs['zy-table'].querySelector('.el-scrollbar__wrap').addEventListener('scroll', this.handleScroll)\r\n      }\r\n      this.handleScroll()\r\n      this.scrollshow()\r\n      this.sliding()\r\n      if (this.$refs['zy-table']) {\r\n        const that = this\r\n        erd.listenTo(this.$refs['zy-table'], (element) => {\r\n          that.$nextTick(() => {\r\n            that.width = element.offsetWidth\r\n            that.height = element.offsetHeight\r\n            this.scrollshow()\r\n            this.handleScroll()\r\n          })\r\n        })\r\n      }\r\n    })\r\n    // this.loading()\r\n  },\r\n  methods: {\r\n    loading () {\r\n      this.$nextTick(() => {\r\n        var box = this.$refs['zy-table']\r\n        var loading = box.querySelector('.el-loading-mask')\r\n        if (loading) {\r\n          loading.style.height = `${box.clientHeight}px`\r\n        }\r\n      })\r\n    },\r\n    handleScroll (event) {\r\n      if (this.$refs['zy-table']) {\r\n        var box = this.$refs['zy-table'].querySelector('.el-table__header-wrapper')\r\n        this.top = this.$refs['zy-table'].getBoundingClientRect().top\r\n        this.left = this.$refs['zy-table'].getBoundingClientRect().left\r\n        var boxTop = this.$refs['zy-table'].querySelector('.el-table').getBoundingClientRect().top\r\n        var boxss = this.top - 1 - boxTop\r\n        box.style.top = boxss + 'px'\r\n        var fixed = this.$refs['zy-table'].querySelector('.el-table__fixed')\r\n        if (fixed) {\r\n          const fixedHeader = fixed.querySelector('.el-table__fixed-header-wrapper')\r\n          const boxTop = this.$refs['zy-table'].querySelector('.el-table').getBoundingClientRect().left\r\n          const distance = this.left - 1 - boxTop\r\n          fixed.style.left = distance + 'px'\r\n          fixedHeader.style.top = boxss + 'px'\r\n        }\r\n        var fixedright = this.$refs['zy-table'].querySelector('.el-table__fixed-right')\r\n        if (fixedright) {\r\n          const fixedHeader = fixedright.querySelector('.el-table__fixed-header-wrapper')\r\n          const aa = this.$refs['zy-table'].offsetWidth\r\n          const boxTop = this.$refs['zy-table'].querySelector('.el-table').getBoundingClientRect().left\r\n          const distance = this.left - 1 - boxTop\r\n          fixedright.style.left = (distance + aa - fixedright.offsetWidth) + 'px'\r\n          fixedHeader.style.top = boxss + 'px'\r\n        }\r\n      }\r\n    },\r\n    sliding () {\r\n      if (this.$refs['zy-table']) {\r\n        var fixedright = this.$refs['zy-table'].querySelector('.el-table__fixed-right')\r\n        if (fixedright) {\r\n          const aa = this.$refs['zy-table'].offsetWidth\r\n          fixedright.style.left = (aa - fixedright.offsetWidth) + 'px'\r\n        }\r\n      }\r\n    },\r\n    scrollshow () {\r\n      this.$nextTick(() => {\r\n        if (this.$refs['zy-table']) {\r\n          var arrayWidth = this.$refs['zy-table'].clientWidth\r\n          var arrWidth = this.$refs['zy-table'].querySelector('.el-table__body-wrapper').querySelector('tbody').clientWidth\r\n          var horizontal = this.$refs['zy-table'].querySelector('.is-horizontal')\r\n          if (arrayWidth < arrWidth) {\r\n            horizontal.style.backgroundColor = '#EEF1F4'\r\n          } else {\r\n            horizontal.style.backgroundColor = 'transparent'\r\n          }\r\n        }\r\n      })\r\n    }\r\n  },\r\n  beforeDestroy () {\r\n    erd.uninstall(this.$refs['zy-table'])\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-table.scss\";\r\n</style>\r\n"]}]}