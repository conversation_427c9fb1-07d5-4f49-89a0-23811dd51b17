{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-select\\zy-select.vue?vue&type=style&index=0&id=2f9e8b36&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-select\\zy-select.vue", "mtime": 1752541693557}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICIuL3p5LXRyZWUtc2VsZWN0LnNjc3MiOw0K"}, {"version": 3, "sources": ["zy-select.vue"], "names": [], "mappings": ";AAoOA", "file": "zy-select.vue", "sourceRoot": "src/components/zy-select", "sourcesContent": ["<template>\r\n  <div class=\"zy-tree-select\"\r\n       ref=\"zyTreeSelect\">\r\n    <el-popover :width=\"w\"\r\n                placement=\"bottom\"\r\n                trigger=\"manual\"\r\n                v-model=\"visible\"\r\n                popper-class=\"zy-tree-select-popover\">\r\n      <template slot=\"reference\">\r\n        <el-input :placeholder=\"inputvalue\"\r\n                  :readonly=\"!filterable\"\r\n                  @mouseover.native=\"mouseover\"\r\n                  @mouseleave.native=\"mouseleave\"\r\n                  :disabled=\"disabled\"\r\n                  v-model=\"input\"\r\n                  @focus=\"focus\"\r\n                  @blur=\"blur\"\r\n                  ref=\"input\">\r\n          <template slot=\"suffix\">\r\n            <i v-if=\"show\"\r\n               :class=\"['zy-tree-select-icon','el-icon-arrow-down',visible?'el-icon-arrow-down-a':'']\"></i>\r\n            <i v-if=\"!show\"\r\n               @click=\"empty\"\r\n               class=\"el-icon-circle-close\"></i>\r\n          </template>\r\n        </el-input>\r\n      </template>\r\n      <el-scrollbar class=\"zy-tree-select-body\"\r\n                    :style=\"{height: h}\">\r\n        <div class=\"select-body\"\r\n             ref=\"selectBody\">\r\n          <el-tree ref=\"tree\"\r\n                   :data=\"data\"\r\n                   :props=\"props\"\r\n                   highlight-current\r\n                   :node-key=\"nodeKey\"\r\n                   @node-expand=\"switchClick\"\r\n                   @node-collapse=\"switchClick\"\r\n                   @node-click=\"handleNodeClick\"\r\n                   :filter-node-method=\"filterNode\"\r\n                   :expand-on-click-node=\"false\"></el-tree>\r\n        </div>\r\n      </el-scrollbar>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nconst delay = (function () {\r\n  let timer = 0\r\n  return function (callback, ms) {\r\n    clearTimeout(timer)\r\n    timer = setTimeout(callback, ms)\r\n  }\r\n})()\r\nexport default {\r\n  name: 'zySelect',\r\n  data () {\r\n    return {\r\n      h: 'auto',\r\n      w: 0,\r\n      visible: false,\r\n      i: 0,\r\n      show: true,\r\n      input: '',\r\n      inputvalue: ''\r\n    }\r\n  },\r\n  props: {\r\n    value: [String, Number, Array, Object],\r\n    data: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    // 是否禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 是否开启关键字搜索\r\n    filterable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '请选择内容'\r\n    },\r\n    // 是否可以清空\r\n    clearable: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  watch: {\r\n    visible (val) {\r\n      if (val) {\r\n        this.w = this.$refs.zyTreeSelect.offsetWidth\r\n        this.$nextTick(() => {\r\n          const that = this\r\n          erd.listenTo(this.$refs.selectBody, (element) => {\r\n            that.$nextTick(() => {\r\n              if (element.offsetHeight > 260) {\r\n                that.h = '260px'\r\n              } else {\r\n                that.h = element.offsetHeight + 'px'\r\n                // that.h = 'auto'\r\n              }\r\n            })\r\n          })\r\n        })\r\n      } else {\r\n        erd.uninstall(this.$refs.selectBody)\r\n      }\r\n    },\r\n    value (val) {\r\n      if (val) {\r\n        this.selectedMethods(this.data)\r\n      } else {\r\n        this.input = ''\r\n        this.inputvalue = this.placeholder\r\n        this.$nextTick(function () {\r\n          this.$refs.tree.setCurrentKey()\r\n        })\r\n      }\r\n    },\r\n    data () {\r\n      this.selectedMethods(this.data)\r\n    },\r\n    input (val) {\r\n      this.$refs.tree.filter(val)\r\n    }\r\n  },\r\n  mounted () {\r\n    this.selectedMethods(this.data)\r\n    this.inputvalue = this.placeholder\r\n  },\r\n  methods: {\r\n    focus () {\r\n      delay(() => {\r\n        this.visible = true\r\n        if (this.filterable && this.value && this.i === 0) {\r\n          this.inputvalue = this.input\r\n          this.input = ''\r\n        }\r\n      }, 200)\r\n    },\r\n    blur () {\r\n      delay(() => {\r\n        this.visible = false\r\n        if (this.filterable && this.i !== 1) {\r\n          if (this.value && this.inputvalue !== this.placeholder) {\r\n            this.input = this.inputvalue\r\n          }\r\n          this.inputvalue = this.placeholder\r\n        }\r\n        this.i = 0\r\n      }, 200)\r\n    },\r\n    switchClick () {\r\n      delay(() => {\r\n        this.visible = true\r\n      }, 200)\r\n      if (this.filterable) {\r\n        this.i = 2\r\n      }\r\n      this.$refs.input.focus()\r\n    },\r\n    filterNode (value, data) {\r\n      if (!this.filterable) return true\r\n      if (!value) return true\r\n      return data[this.props.label].indexOf(value) !== -1\r\n    },\r\n    handleNodeClick (data) {\r\n      if (this.filterable) {\r\n        this.i = 1\r\n      }\r\n      this.$emit('input', data[this.nodeKey])\r\n    },\r\n    // 首次进来默认选中\r\n    selectedMethods (data) {\r\n      data.forEach(item => {\r\n        if (item[this.nodeKey] === this.value) {\r\n          this.input = item[this.props.label]\r\n          this.$emit('select', item)\r\n          this.$nextTick(function () {\r\n            this.$refs.tree.setCurrentKey(item[this.nodeKey])\r\n          })\r\n        }\r\n        if (item.children && item.children.length > 0) {\r\n          this.selectedMethods(item.children)\r\n        }\r\n      })\r\n    },\r\n    empty () {\r\n      this.i = 1\r\n      this.$emit('input', '')\r\n    },\r\n    mouseover () {\r\n      if (this.value && this.clearable) {\r\n        this.show = false\r\n      }\r\n    },\r\n    mouseleave () {\r\n      console.log(1)\r\n      this.show = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"./zy-tree-select.scss\";\r\n</style>\r\n"]}]}