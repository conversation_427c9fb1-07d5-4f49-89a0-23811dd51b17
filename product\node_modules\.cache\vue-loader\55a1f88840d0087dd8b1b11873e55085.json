{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\FinishDetailPop.vue?vue&type=style&index=0&id=e107e346&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\FinishDetailPop.vue", "mtime": 1752541693823}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouRmluaXNoRGV0YWlsUG9wIHsNCiAgd2lkdGg6IDkwMHB4Ow0KICBoZWlnaHQ6IDEwMCU7DQogIHBhZGRpbmc6IDAgMjRweDsNCiAgcGFkZGluZy1ib3R0b206IDI0cHg7DQoNCiAgLmNoZWNrQ2xhc3Mgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCiAgICBwYWRkaW5nLXJpZ2h0OiA0NXB4Ow0KICB9DQoNCiAgLmRldGFpbHMtaXRlbS1jb250ZW50IHsNCiAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7DQogICAgd2lkdGg6IDEwMCU7DQogICAgcGFkZGluZzogMjRweDsNCg0KICAgIHAgew0KICAgICAgbGluZS1oZWlnaHQ6IDM2cHg7DQogICAgfQ0KICB9DQoNCiAgLmRldGFpbHMtaXRlbS1pbWcgew0KICAgIGltZyB7DQogICAgICB3aWR0aDogY2FsYygxMDAlIC0gMjRweCk7DQogICAgfQ0KICB9DQoNCiAgLmRldGFpbHMtaXRlbS1maWxlcyB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBwYWRkaW5nLXJpZ2h0OiAyMHB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["FinishDetailPop.vue"], "names": [], "mappings": ";AA+LA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "FinishDetailPop.vue", "sourceRoot": "src/views/AssessmentOrgan/InnovationExcellence", "sourcesContent": ["<template>\r\n  <div class=\"FinishDetailPop details\">\r\n\r\n    <div class=\"checkClass\">\r\n\r\n      <el-button type=\"primary\"\r\n                 icon=\"el-icon-circle-check\"\r\n                 v-permissions=\"'auth:innovation:checkPass'\"\r\n                 @click=\"passClick(2)\">审核通过\r\n      </el-button>\r\n\r\n      <el-button type=\"danger\"\r\n                 v-permissions=\"'auth:innovation:checkNotPass'\"\r\n                 icon=\"el-icon-remove-outline\"\r\n                 @click=\"passClick(3)\">审核不通过\r\n      </el-button>\r\n    </div>\r\n\r\n    <div class=\"details-title\">完成情况详情</div>\r\n    <div class=\"details-item-box\">\r\n      <div class=\"details-item-title\">\r\n        <div class=\"details-item-label\">标题</div>\r\n        <div class=\"details-item-value\">{{details.title}}</div>\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">完成时间</div>\r\n          <div class=\"details-item-value\">{{$format(details.overTime).substr(0,16)}}</div>\r\n        </div>\r\n        <!-- <div class=\"details-item\">\r\n          <div class=\"details-item-label\">发布部门</div>\r\n          <div class=\"details-item-value\">{{details.orgName}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">是否置顶</div>\r\n          <div class=\"details-item-value\">{{details.isTop?'是':'否'}}</div>\r\n        </div> -->\r\n      </div>\r\n      <div class=\"details-item-column\">\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">审核状态</div>\r\n          <div class=\"details-item-value\">{{auditStatusName}}</div>\r\n        </div>\r\n        <!-- <div class=\"details-item\">\r\n          <div class=\"details-item-label\">类型</div>\r\n          <div class=\"details-item-value\">{{details.type}}</div>\r\n        </div>\r\n        <div class=\"details-item\">\r\n          <div class=\"details-item-label\">是否App显示</div>\r\n          <div class=\"details-item-value\">{{details.isAppShow?'是':'否'}}</div>\r\n        </div> -->\r\n      </div>\r\n      <!-- 以下附件内容未修改 -->\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">附件</div>\r\n\r\n        <div class=\"details-item-value\"\r\n             v-if=\"details.attachmentInfo\">\r\n          <div class=\"details-item-files\"\r\n               v-for=\"(item,index) in details.attachmentInfo \"\r\n               :key=\"index\">\r\n            <p>{{item.oldName}}</p>\r\n            <div>\r\n\r\n              <!-- 预览修改 -->\r\n              <el-button size=\"medium\"\r\n                         type=\"text\">\r\n                <a :href=\"item.fullPath\"\r\n                   target=\"_blank\">预览</a>\r\n              </el-button>\r\n\r\n              <el-button size=\"medium\"\r\n                         type=\"text\"\r\n                         @click=\"fileClick(item)\"> 下载\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-content\"\r\n             v-html=\"details.content\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  props: ['id', 'uid'],\r\n  data () {\r\n    return {\r\n      form: {\r\n        title: '',\r\n        overTime: '',\r\n        officeName: '',\r\n        content: '',\r\n        // evaluationId: this.$route.query.mid,\r\n        // id: this.$route.query.id,\r\n        // ******\r\n        attachmentId: ''\r\n\r\n      },\r\n      details: {\r\n        auditStatus: ''\r\n      }\r\n    }\r\n  },\r\n  inject: ['openoffice', 'openPdf'],\r\n  mounted () {\r\n    this.form.overTime = this.$format()\r\n    this.getFinishDetail()\r\n  },\r\n  computed: {\r\n    auditStatusName () {\r\n      if (this.details.auditStatus === '1') {\r\n        return '待审核'\r\n      } else if (this.details.auditStatus === '2') {\r\n        return '审核通过'\r\n      } else {\r\n        return '审核不通过'\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    passClick (auditStatus) {\r\n      this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.getCheckWork(this.uid, auditStatus)\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消操作'\r\n        })\r\n      })\r\n    },\r\n    // 审核 (加分项)\r\n    async getCheckWork (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckFinishDetail({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n        this.getFinishDetail()\r\n      }\r\n    },\r\n    // 获取 完成情况详情\r\n    // async getFinishDetail () {\r\n    //   const res = await this.$api.AssessmentOrgan.reqfinishDetail(this.uid)\r\n    //   const { id, evaluationId, title, overTime, auditStatus, content, attchmentId } = res.data\r\n    //   // this.file = attchmentId //报错原因:传入的附件attachment为对象 不能直接赋值给file数组\r\n    //   for (const i in attchmentId) { // 方法:将对象转为数组(因为传入的附件数据为对象)\r\n    //     this.file.push(attchmentId[i])\r\n    //   }\r\n\r\n    //   this.form = { title, overTime, evaluationId, id, content, auditStatus }\r\n    //   // console.log('@@@查看是否能收到审核状态auditStatus', this.form)\r\n    // },\r\n\r\n    // async noticeinfo () {\r\n    //   const res = await this.$api.notificationAnnouncement.noticeinfo(this.rowId)\r\n    //   var { data } = res\r\n    //   this.details = data\r\n    // },\r\n    async getFinishDetail () {\r\n      const res = await this.$api.AssessmentOrgan.reqfinishDetail(this.uid)\r\n      var { data } = res\r\n      this.details = data\r\n    },\r\n    fileClick (data) {\r\n      this.$api.proposal.downloadFile({ id: data.id }, data.oldName) // 附件文件名显示\r\n    },\r\n    priew (data) {\r\n      if (data.fileType === 'pdf') {\r\n        this.openPdf(data.filePath)\r\n        return\r\n      }\r\n      const arr = ['doc', 'docx', 'xlsx']\r\n      if (arr.includes(data.fileType)) {\r\n        this.openoffice(data.filePath)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.FinishDetailPop {\r\n  width: 900px;\r\n  height: 100%;\r\n  padding: 0 24px;\r\n  padding-bottom: 24px;\r\n\r\n  .checkClass {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding-right: 45px;\r\n  }\r\n\r\n  .details-item-content {\r\n    word-break: break-all;\r\n    width: 100%;\r\n    padding: 24px;\r\n\r\n    p {\r\n      line-height: 36px;\r\n    }\r\n  }\r\n\r\n  .details-item-img {\r\n    img {\r\n      width: calc(100% - 24px);\r\n    }\r\n  }\r\n\r\n  .details-item-files {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-right: 20px;\r\n  }\r\n}\r\n</style>\r\n"]}]}