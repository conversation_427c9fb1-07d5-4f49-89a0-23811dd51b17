{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tree-components\\zy-tree-components.vue?vue&type=style&index=0&id=002e01af&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-tree-components\\zy-tree-components.vue", "mtime": 1752541693604}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICcuL3p5LXRyZWUtY29tcG9uZW50cy5zY3NzJzsNCg=="}, {"version": 3, "sources": ["zy-tree-components.vue"], "names": [], "mappings": ";AAiTA", "file": "zy-tree-components.vue", "sourceRoot": "src/components/zy-tree-components", "sourcesContent": ["<template>\r\n  <div class=\"zy-tree-components\">\r\n    <div v-for=\"treeItem in filterTreeData(treeData)\" :key=\"treeItem.id\">\r\n      <div\r\n        @click=\"selected(treeItem)\"\r\n        v-if=\"!treeItem[props.children].length\"\r\n        :style=\"{ paddingLeft: padding(hierarchy) + 'px' }\"\r\n        :class=\"[\r\n          'zy-tree-components-item',\r\n          treeItem.selected ? 'zy-tree-components-item-selected' : ''\r\n        ]\"\r\n      >\r\n        <div class=\"zy-tree-components-item-icon\"></div>\r\n        <div class=\"zy-tree-components-item-text\">\r\n          {{ treeItem[props.label] }}\r\n        </div>\r\n      </div>\r\n      <div v-else>\r\n        <div\r\n          @click=\"subTree(treeItem)\"\r\n          :style=\"{ paddingLeft: padding(hierarchy) + 'px' }\"\r\n          :class=\"[\r\n            'zy-tree-components-item',\r\n            treeItem.active ? 'zy-tree-components-item-active' : '',\r\n            treeItem.selected ? 'zy-tree-components-item-selected' : ''\r\n          ]\"\r\n        >\r\n          <div\r\n            class=\"zy-tree-components-item-icon\"\r\n            @click.stop=\"subTreeicon(treeItem)\"\r\n          >\r\n            <i class=\"el-icon-caret-right\"></i>\r\n          </div>\r\n          <div class=\"zy-tree-components-item-text\">\r\n            {{ treeItem[props.label] }}\r\n          </div>\r\n        </div>\r\n        <el-collapse-transition>\r\n          <zy-tree-components\r\n            v-if=\"treeItem.active\"\r\n            :anykey=\"anykey\"\r\n            :child=\"child\"\r\n            :props=\"props\"\r\n            v-model=\"treeId\"\r\n            :nodeKey=\"nodeKey\"\r\n            :hierarchy=\"hierarchy + 1\"\r\n            :tree=\"treeItem[props.children]\"\r\n          ></zy-tree-components>\r\n        </el-collapse-transition>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zyTreeComponents',\r\n  data () {\r\n    return {\r\n      treeId: this.value,\r\n      treeData: []\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    keyword: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    tree: {\r\n      type: Array,\r\n      default: function () {\r\n        return []\r\n      }\r\n    },\r\n    hierarchy: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    nodeKey: {\r\n      type: String,\r\n      default: 'id'\r\n    },\r\n    // 树结构配置\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          children: 'children',\r\n          label: 'label'\r\n        }\r\n      }\r\n    },\r\n    determine: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    child: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    anykey: {\r\n      type: Array,\r\n      default: function () {\r\n        return []\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      this.treeId = val\r\n      this.selectedMethods()\r\n    },\r\n    treeId (val) {\r\n      this.$emit('id', val)\r\n    },\r\n    tree (val) {\r\n      this.treeDataCope(this.deepCopy(this.tree))\r\n    },\r\n    anykey (val) {\r\n      this.treeDataCope(this.deepCopy(this.tree))\r\n    }\r\n  },\r\n  created () {\r\n    this.treeDataCope(this.deepCopy(this.tree), true)\r\n  },\r\n  methods: {\r\n    padding (index) {\r\n      var hierarchy = 18 * index\r\n      return hierarchy\r\n    },\r\n    treeDataCope (data) {\r\n      data.forEach(item => {\r\n        item.selected = false\r\n        if (this.treeId === item[this.nodeKey]) {\r\n          item.selected = true\r\n        }\r\n        if (item[this.props.children].length) {\r\n          if ((typeof item.active) === 'undefined') { // eslint-disable-line\r\n            item.active = false\r\n          }\r\n          this.anykey.forEach(items => {\r\n            if (item[this.nodeKey] === items) {\r\n              item.active = true\r\n            }\r\n          })\r\n        }\r\n      })\r\n      this.treeData = data\r\n      this.treehierarchy(this.treeData, true)\r\n    },\r\n    treehierarchy (data, type) {\r\n      data.forEach(item => {\r\n        if (item[this.nodeKey] === this.treeId) {\r\n          const result = this.makeData(item)\r\n          if (!type) {\r\n            this.$emit('on-tree-click', result)\r\n          }\r\n        }\r\n        if (item[this.props.children].length) {\r\n          this.treehierarchy(item[this.props.children], type)\r\n        }\r\n      })\r\n    },\r\n    selected (data) {\r\n      this.treeId = data[this.nodeKey]\r\n    },\r\n    selectedMethods () {\r\n      if (this.hierarchy === 0) {\r\n        this.treehierarchy(this.treeData)\r\n      }\r\n      const treeData = this.treeData\r\n      treeData.forEach(item => {\r\n        item.selected = false\r\n        if (this.treeId === item[this.nodeKey]) {\r\n          item.selected = true\r\n        }\r\n      })\r\n      this.treeData = treeData\r\n    },\r\n    subTree (data) {\r\n      if (!this.child) {\r\n        this.treeId = data[this.nodeKey]\r\n      }\r\n      const treeData = this.treeData\r\n      treeData.forEach(item => {\r\n        if (item[this.nodeKey] === data[this.nodeKey]) {\r\n          item.active = !item.active\r\n        }\r\n      })\r\n      this.treeData = treeData\r\n    },\r\n    subTreeicon (data) {\r\n      const treeData = this.treeData\r\n      treeData.forEach(item => {\r\n        if (item[this.nodeKey] === data[this.nodeKey]) {\r\n          item.active = !item.active\r\n        }\r\n      })\r\n      this.treeData = treeData\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    },\r\n    makeData (data) {\r\n      const t = this.type(data)\r\n      let o\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (let i = 0; i < data.length; i++) {\r\n          o.push(this.makeData(data[i]))\r\n        }\r\n      } else if (t === 'object') {\r\n        for (const i in data) {\r\n          if (i != 'active' && i != 'selected') {// eslint-disable-line\r\n            o[i] = this.makeData(data[i])\r\n          }\r\n        }\r\n      }\r\n      return o\r\n    },\r\n    filterTreeData (data) {\r\n      if (this.determine) {\r\n        return data\r\n      }\r\n      if (this.keyword === '') {\r\n        return data\r\n      }\r\n      return this.filterTree(data, this.shopfilterNode) || []\r\n    },\r\n    filterTree (nodes, predicate) {\r\n      if (this.hierarchy !== 0) {\r\n        return nodes\r\n      }\r\n      if (!nodes || !nodes.length) return void 0 // eslint-disable-line\r\n      const children = []\r\n      for (let node of nodes) {\r\n        node = Object.assign({}, node)\r\n        const sub = this.filterTree(node[this.props.children], predicate)\r\n        if ((sub && sub.length) || predicate(node)) {\r\n          sub && (node[this.props.children] = sub)\r\n          if (this.keyword) {\r\n            node.active = true\r\n          }\r\n          children.push(node)\r\n        }\r\n      }\r\n      return children.length ? children : void 0 // eslint-disable-line\r\n    },\r\n    shopfilterNode (data) {\r\n      return data[this.props.label].includes(this.keyword)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './zy-tree-components.scss';\r\n</style>\r\n"]}]}