{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\InnovationExcellence.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\InnovationExcellence\\InnovationExcellence.vue", "mtime": 1752541693826}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgdGFibGVEYXRhIGZyb20gJ0BtaXhpbnMvdGFibGVEYXRhJw0KaW1wb3J0IElubm92YXRpb25OZXcgZnJvbSAnLi9Jbm5vdmF0aW9uTmV3LnZ1ZScNCmltcG9ydCBmaW5pc2hEZXRhaWwgZnJvbSAnLi9maW5pc2hEZXRhaWwnDQppbXBvcnQgdGl0bGVEZXRhaWwgZnJvbSAnLi90aXRsZURldGFpbCcNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnSW5ub3ZhdGlvbkV4Y2VsbGVuY2UnLA0KICBjb21wb25lbnRzOiB7DQogICAgSW5ub3ZhdGlvbk5ldywNCiAgICBmaW5pc2hEZXRhaWwsDQogICAgdGl0bGVEZXRhaWwNCg0KICB9LA0KICBkYXRhICgpIHsNCiAgICByZXR1cm4gew0KICAgICAgb2ZmaWNlRGF0YTogW10sIC8vIOacuuaehOagkSwNCiAgICAgIGF1ZGl0U3RhdHVzRGF0YTogW10sIC8vIOWuoeaguOeKtuaAgQ0KICAgICAgc2VhcmNoUGFyYW1zOiB7DQogICAgICAgIG9mZmljZUlkOiAnJywNCiAgICAgICAgYXVkaXRTdGF0dXNQYXJhbXM6ICcnDQogICAgICB9LA0KICAgICAgdGltZTogW10sDQogICAgICBrZXl3b3JkOiAnJywNCiAgICAgIHB1Ymxpc2hTdGFydFRpbWU6ICcnLA0KICAgICAgLy8gKioqDQogICAgICBwYWdlTm86IDEsDQogICAgICBwYWdlU2l6ZTogMTAsDQogICAgICB0b3RhbDogMTAsDQogICAgICBjdXJyZW50Um93OiBudWxsLA0KICAgICAgc2hvdzogZmFsc2UsDQogICAgICBzaG93RmluaXNoOiBmYWxzZSwNCiAgICAgIHNob3dUaXRsZURldGFpbDogZmFsc2UsDQoNCiAgICAgIGlkczogJycsDQogICAgICBpZDogJycsDQogICAgICBjaG9vc2U6IFtdLA0KICAgICAgc2VsZWN0T2JqOiBbXSwNCiAgICAgIHNlbGVjdERhdGE6IFtdLA0KICAgICAgdGFibGVEYXRhOiBbXQ0KICAgIH0NCiAgfSwNCiAgcHJvcHM6IFsnbWVtYmVyVHlwZSddLA0KICBtaXhpbnM6IFt0YWJsZURhdGFdLA0KICBtb3VudGVkICgpIHsNCiAgICB0aGlzLmdldElubm92YXRpb25FeGNlbGxlbmNlTGlzdCgpDQogICAgdGhpcy50cmVlTGlzdCgpDQogICAgdGhpcy5kaWN0aW9uYXJ5UHVia3ZzKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKg0KICAgICAgKuWtl+WFuA0KICAgICovDQogICAgYXN5bmMgZGljdGlvbmFyeVB1Ymt2cyAoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCB0aGlzLiRhcGkuc3lzdGVtU2V0dGluZ3MuZGljdGlvbmFyeVB1Ymt2cyh7DQogICAgICAgIHR5cGVzOiAnZXZhbHVhdGVfYXVkaXRfc3RhdHVzJw0KICAgICAgfSkNCiAgICAgIHZhciB7IGRhdGEgfSA9IHJlcw0KICAgICAgdGhpcy5hdWRpdFN0YXR1c0RhdGEgPSBkYXRhLmV2YWx1YXRlX2F1ZGl0X3N0YXR1cw0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgICrmnLrmnoTmoJENCiAgICAqLw0KICAgIGFzeW5jIHRyZWVMaXN0ICgpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHRoaXMuJGFwaS5zeXN0ZW1TZXR0aW5ncy50cmVlTGlzdCh7fSkNCiAgICAgIHZhciB7IGRhdGEgfSA9IHJlcw0KICAgICAgdGhpcy5vZmZpY2VEYXRhID0gZGF0YQ0KICAgIH0sDQogICAgdXBkYXRlTGlzdCAoKSB7DQogICAgICB0aGlzLnNob3dUaXRsZURldGFpbCA9IGZhbHNlDQogICAgICB0aGlzLmdldElubm92YXRpb25FeGNlbGxlbmNlTGlzdCgpDQogICAgfSwNCiAgICBoYW5kbGVEZWxldGUgKGlkcykgew0KICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5Yig6Zmk6YCJ5Lit55qE6aG5LCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kYXBpLkFzc2Vzc21lbnRPcmdhbi5yZXFEZWxJbm5vdmF0aW9uRXhjZWxsZW5jZSh7IGlkcyB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLmVycmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgdGhpcy5nZXRJbm5vdmF0aW9uRXhjZWxsZW5jZUxpc3QoKS8vIOWIoOmZpOWQjuabtOaWsOmhtemdog0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflj5bmtojliKDpmaQnKQ0KICAgICAgICAvLyB0aGlzLmdldE1vbnRobHlXb3JrUmVjb3JkbGlzdCgpDQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIG5ld0NhbGxiYWNrICgpIHsNCiAgICAgIHRoaXMuZ2V0SW5ub3ZhdGlvbkV4Y2VsbGVuY2VMaXN0KCkNCiAgICAgIHRoaXMuc2hvdyA9IGZhbHNlDQogICAgICB0aGlzLnNob3dGaW5pc2ggPSBmYWxzZQ0KICAgICAgdGhpcy5zaG93VGl0bGVEZXRhaWwgPSBmYWxzZQ0KICAgIH0sDQogICAgLy8g6K+35rGC5ZCO5Y+w5pWw5o2uIOiOt+WPluWIl+ihqOS/oeaBrw0KICAgIGFzeW5jIGdldElubm92YXRpb25FeGNlbGxlbmNlTGlzdCAoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCB0aGlzLiRhcGkuQXNzZXNzbWVudE9yZ2FuLnJlcUlubm92YXRpb25FeGNlbGxlbmNlTGlzdCh7DQogICAgICAgIHBhZ2VObzogdGhpcy5wYWdlTm8sDQogICAgICAgIHBhZ2VTaXplOiB0aGlzLnBhZ2VTaXplLA0KICAgICAgICBrZXl3b3JkOiB0aGlzLmtleXdvcmQsDQogICAgICAgIHB1Ymxpc2hTdGFydFRpbWU6IHRoaXMudGltZVswXSwNCiAgICAgICAgcHVibGlzaEVuZFRpbWU6IHRoaXMudGltZVsxXSwNCiAgICAgICAgLy8gLy8gc2VkYXRlSWQ6IHRoaXMuc2VkYXRlSWQNCiAgICAgICAgLy8gc2VkYXRlSWQ6IHRoaXMuc2VsZWN0ZWRZZWFyDQogICAgICAgIG1lbWJlclR5cGU6IHRoaXMubWVtYmVyVHlwZSwNCiAgICAgICAgb2ZmaWNlSWQ6IHRoaXMuc2VhcmNoUGFyYW1zLm9mZmljZUlkLCAvLyDpg6jpl6jmn6Xor6INCiAgICAgICAgYXVkaXRTdGF0dXM6IHRoaXMuc2VhcmNoUGFyYW1zLmF1ZGl0U3RhdHVzUGFyYW1zIC8vIOWuoeaguOeKtuaAgQ0KICAgICAgfSkNCiAgICAgIHZhciB7IGRhdGEsIHRvdGFsIH0gPSByZXMNCiAgICAgIHRoaXMudGFibGVEYXRhID0gZGF0YQ0KICAgICAgdGhpcy50b3RhbCA9IHRvdGFsDQogICAgICB0aGlzLmNob29zZSA9IFtdDQogICAgICB0aGlzLnNlbGVjdE9iaiA9IFtdDQogICAgICB0aGlzLnNlbGVjdERhdGEgPSBbXQ0KICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgew0KICAgICAgICB0aGlzLm1lbW9yeUNoZWNrZWQoKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIHBhZ2luZ19ib3gg6YeM6Z2i55So5Yiw55qE5pa55rOVDQogICAgaGFuZGxlU2l6ZUNoYW5nZSAoKSB7DQogICAgICB0aGlzLmdldElubm92YXRpb25FeGNlbGxlbmNlTGlzdCgpDQogICAgfSwNCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlICgpIHsNCiAgICAgIHRoaXMuZ2V0SW5ub3ZhdGlvbkV4Y2VsbGVuY2VMaXN0KCkNCiAgICB9LA0KICAgIHNlYXJjaCAoKSB7DQogICAgICB0aGlzLnBhZ2VObyA9IDENCiAgICAgIHRoaXMuZ2V0SW5ub3ZhdGlvbkV4Y2VsbGVuY2VMaXN0KCkNCiAgICB9LA0KICAgIHJlc2V0ICgpIHsNCiAgICAgIHRoaXMua2V5d29yZCA9ICcnDQogICAgICB0aGlzLnB1Ymxpc2hTdGFydFRpbWUgPSAnJw0KICAgICAgdGhpcy50aW1lLnNwbGljZSgwLCB0aGlzLnRpbWUubGVuZ3RoKQ0KICAgICAgdGhpcy5zZWFyY2hQYXJhbXMub2ZmaWNlSWQgPSAnJw0KICAgICAgdGhpcy5zZWFyY2hQYXJhbXMuYXVkaXRTdGF0dXNQYXJhbXMgPSAnJw0KICAgICAgLy8g6YeN572u5ZCO6YeN5paw6LCD55So5LiA5qyh5YiX6KGo5L+h5oGvDQogICAgICB0aGlzLmdldElubm92YXRpb25FeGNlbGxlbmNlTGlzdCgpDQogICAgfSwNCiAgICAvLyDmlrDlop4NCiAgICBuZXdEYXRhICgpIHsNCiAgICAgIHRoaXMuaWQgPSAnJw0KICAgICAgdGhpcy5zaG93ID0gdHJ1ZQ0KICAgIH0sDQogICAgLy8g5qCH6aKY6K+m5oOFIG1vZGlmeQ0KICAgIG1vZGlmeSAocm93KSB7DQogICAgICB0aGlzLmlkID0gcm93LmlkDQogICAgICB0aGlzLnNob3dUaXRsZURldGFpbCA9IHRydWUNCiAgICB9LA0KICAgIC8vIOWujOaIkOaDheWGtQ0KICAgIGZpbmlzaFN0YXR1cyAocm93KSB7DQogICAgICB0aGlzLmlkID0gcm93LmlkDQogICAgICB0aGlzLnNob3dGaW5pc2ggPSB0cnVlDQogICAgfSwNCiAgICAvLyDnvJbovpENCiAgICBoYW5kbGVDbGljayAocm93KSB7DQogICAgICB0aGlzLmlkID0gcm93LmlkDQogICAgICB0aGlzLnNob3cgPSB0cnVlDQogICAgfSwNCiAgICBwYXNzQ2xpY2sgKGF1ZGl0U3RhdHVzKSB7DQogICAgICBpZiAodGhpcy5jaG9vc2UubGVuZ3RoKSB7DQogICAgICAgIHRoaXMuJGNvbmZpcm0oYOatpOaTjeS9nOWwhumAieaLqeeahOWuoeaguOeKtuaAgeaUueS4uiR7YXVkaXRTdGF0dXMgPT09IDIgPyAn5a6h5qC46YCa6L+HJyA6ICflrqHmoLjkuI3pgJrov4cnfSwg5piv5ZCm57un57utP2AsICfmj5DnpLonLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldENoZWNrSW5ub3ZhdGlvbih0aGlzLmNob29zZS5qb2luKCcsJyksIGF1ZGl0U3RhdHVzKQ0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5pON5L2cJw0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn6K+36Iez5bCR6YCJ5oup5LiA5p2h5pWw5o2uJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWuoeaguCAo5Yib5paw5Yib5LyYKQ0KICAgIGFzeW5jIGdldENoZWNrSW5ub3ZhdGlvbiAoaWQsIGF1ZGl0U3RhdHVzKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCB0aGlzLiRhcGkuQXNzZXNzbWVudE9yZ2FuLnJlcUNoZWNrSW5ub3ZhdGlvbih7IGlkczogaWQsIGF1ZGl0U3RhdHVzOiBhdWRpdFN0YXR1cyB9KQ0KICAgICAgdmFyIHsgZXJyY29kZSwgZXJybXNnIH0gPSByZXMNCiAgICAgIGlmIChlcnJjb2RlID09PSAyMDApIHsNCiAgICAgICAgdGhpcy5nZXRJbm5vdmF0aW9uRXhjZWxsZW5jZUxpc3QoKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiBlcnJtc2csDQogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgIH0pDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["InnovationExcellence.vue"], "names": [], "mappings": ";AAgOA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "InnovationExcellence.vue", "sourceRoot": "src/views/AssessmentOrgan/InnovationExcellence", "sourcesContent": ["<template>\r\n  <!-- 创新创优目标 -->\r\n  <div class=\"InnovationExcellence\">\r\n    <search-box @search-click=\"search\"\r\n                @reset-click=\"reset\"\r\n                title=\"创新创优目标筛选\">\r\n      <!--关键字查找框  -->\r\n      <zy-widget label=\"关键字\">\r\n        <el-input placeholder=\"请输入关键词\"\r\n                  v-model=\"keyword\"\r\n                  clearable\r\n                  @keyup.enter.native=\"search\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\"></div>\r\n        </el-input>\r\n      </zy-widget>\r\n\r\n      <!-- 时间查询 -->\r\n      <zy-widget label=\"起止时间\">\r\n        <el-date-picker v-model=\"time\"\r\n                        type=\"daterange\"\r\n                        range-separator=\"至\"\r\n                        start-placeholder=\"开始时间\"\r\n                        value-format=\"timestamp\"\r\n                        end-placeholder=\"结束时间\">\r\n        </el-date-picker>\r\n      </zy-widget>\r\n\r\n      <!-- 部门查询(用到机构树)  -->\r\n      <zy-widget label=\"部门查询\"\r\n                 v-permissions=\"'auth:innovation:department'\">\r\n        <zy-select v-model=\"searchParams.officeId\"\r\n                   clearable\r\n                   @keyup.enter.native=\"search\"\r\n                   placeholder=\"请选择部门\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\">\r\n          <div slot=\"prefix\"\r\n               class=\"input-search\">\r\n          </div>\r\n        </zy-select>\r\n      </zy-widget>\r\n\r\n      <!-- 审核状态查询 -->\r\n      <zy-widget label=\"审核状态查询\">\r\n        <el-select v-model=\"searchParams.auditStatusParams\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择审核状态\">\r\n          <el-option v-for=\"item in auditStatusData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </zy-widget>\r\n\r\n    </search-box>\r\n\r\n    <div class=\"qd-list-wrap\">\r\n      <!-- 新增按钮 -->\r\n      <div class=\"qd-btn-box\">\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-plus\"\r\n                   @click=\"newData\">新增\r\n        </el-button>\r\n        <el-button type=\"primary\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   v-permissions=\"'auth:innovation:checkPass'\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n        <el-button type=\"danger\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   v-permissions=\"'auth:innovation:checkNotPass'\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n      </div>\r\n\r\n      <div class=\"tableData\">\r\n        <zy-table>\r\n          <el-table :data=\"tableData\"\r\n                    slot=\"zytable\"\r\n                    row-key=\"id\"\r\n                    ref=\"multipleTable\"\r\n                    @select=\"selected\"\r\n                    @select-all=\"selectedAll\"\r\n                    :tree-props=\"{children: 'children'}\">\r\n            <el-table-column type=\"selection\"\r\n                             fixed=\"left\"\r\n                             width=\"50\">\r\n            </el-table-column>\r\n            <el-table-column label=\"标题\"\r\n                             show-overflow-tooltip\r\n                             prop=\"title\"\r\n                             min-width=\"330px\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"modify(scope.row)\"\r\n                           size=\"small\"> {{scope.row.title}}</el-button>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"部门\"\r\n                             width=\"130px\"\r\n                             prop=\"officeName\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{scope.row.officeName }} </div>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"发布时间\"\r\n                             width=\"170\"\r\n                             prop=\"publishTime\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{$format(scope.row.publishTime).substr(0,16)}} </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"时限要求\"\r\n                             width=\"170\"\r\n                             prop=\"endTime\">\r\n              <template slot-scope=\"scope\">\r\n                <div> {{$format(scope.row.endTime).substr(0,16)}} </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"分值\"\r\n                             align=\"center\"\r\n                             width=\"70\"\r\n                             prop=\"score\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"审核状态\"\r\n                             width=\"120\"\r\n                             prop=\"auditStatus\">\r\n\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"重点工作\"\r\n                             align=\"center\"\r\n                             width=\"100\"\r\n                             prop=\"isMainwork\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"类型\"\r\n                             width=\"120\"\r\n                             show-overflow-tooltip\r\n                             prop=\"classify\">\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"完成情况\"\r\n                             width=\"100\"\r\n                             fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"finishStatus(scope.row)\"\r\n                           size=\"small\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 完成情况\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column label=\"操作\"\r\n                             width=\"120\"\r\n                             fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\"\r\n                           @click=\"handleClick(scope.row)\"\r\n                           size=\"small\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 编辑\r\n                </el-button>\r\n\r\n                <el-button type=\"text\"\r\n                           size=\"small\"\r\n                           @click=\"handleDelete(scope.row.id)\"\r\n                           :class=\"scope.row.auditStatus == '审核通过' ? '' : 'delBtn'\"\r\n                           :disabled=\"scope.row.auditStatus == '审核通过'\"> 删除\r\n                </el-button>\r\n\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </zy-table>\r\n      </div>\r\n\r\n      <div class=\"paging_box\">\r\n        <el-pagination @size-change=\"handleSizeChange\"\r\n                       @current-change=\"handleCurrentChange\"\r\n                       :current-page.sync=\"pageNo\"\r\n                       :page-sizes=\"[10, 20, 30, 40]\"\r\n                       :page-size.sync=\"pageSize\"\r\n                       background\r\n                       layout=\"total, prev, pager, next, sizes, jumper\"\r\n                       :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <zy-pop-up v-model=\"show\"\r\n               :title=\"id?'编辑':'新增'\">\r\n      <InnovationNew :id=\"id\"\r\n                     :memberType=\"memberType\"\r\n                     @newCallback=\"newCallback\">\r\n      </InnovationNew>\r\n    </zy-pop-up>\r\n    <zy-pop-up v-model=\"showFinish\"\r\n               class=\"titleStyle\"\r\n               title=\"完成情况\">\r\n      <finishDetail :id=\"id\"\r\n                    :memberType=\"memberType\"\r\n                    @newCallback=\"newCallback\">\r\n      </finishDetail>\r\n    </zy-pop-up>\r\n    <zy-pop-up v-model=\"showTitleDetail\"\r\n               class=\"titleStyle\"\r\n               title=\"详情\"\r\n               :beforeClose=\"updateList\">\r\n      <titleDetail :id=\"id\"\r\n                   :memberType=\"memberType\"\r\n                   @newCallback=\"newCallback\">\r\n      </titleDetail>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport tableData from '@mixins/tableData'\r\nimport InnovationNew from './InnovationNew.vue'\r\nimport finishDetail from './finishDetail'\r\nimport titleDetail from './titleDetail'\r\n\r\nexport default {\r\n  name: 'InnovationExcellence',\r\n  components: {\r\n    InnovationNew,\r\n    finishDetail,\r\n    titleDetail\r\n\r\n  },\r\n  data () {\r\n    return {\r\n      officeData: [], // 机构树,\r\n      auditStatusData: [], // 审核状态\r\n      searchParams: {\r\n        officeId: '',\r\n        auditStatusParams: ''\r\n      },\r\n      time: [],\r\n      keyword: '',\r\n      publishStartTime: '',\r\n      // ***\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      total: 10,\r\n      currentRow: null,\r\n      show: false,\r\n      showFinish: false,\r\n      showTitleDetail: false,\r\n\r\n      ids: '',\r\n      id: '',\r\n      choose: [],\r\n      selectObj: [],\r\n      selectData: [],\r\n      tableData: []\r\n    }\r\n  },\r\n  props: ['memberType'],\r\n  mixins: [tableData],\r\n  mounted () {\r\n    this.getInnovationExcellenceList()\r\n    this.treeList()\r\n    this.dictionaryPubkvs()\r\n  },\r\n  methods: {\r\n    /**\r\n      *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluate_audit_status'\r\n      })\r\n      var { data } = res\r\n      this.auditStatusData = data.evaluate_audit_status\r\n    },\r\n\r\n    /**\r\n      *机构树\r\n    */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    updateList () {\r\n      this.showTitleDetail = false\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    handleDelete (ids) {\r\n      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$api.AssessmentOrgan.reqDelInnovationExcellence({ ids }).then((res) => {\r\n          if (res.errcode === 200) {\r\n            this.getInnovationExcellenceList()// 删除后更新页面\r\n            this.$message.success('删除成功')\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('取消删除')\r\n        // this.getMonthlyWorkRecordlist()\r\n        return false\r\n      })\r\n    },\r\n    newCallback () {\r\n      this.getInnovationExcellenceList()\r\n      this.show = false\r\n      this.showFinish = false\r\n      this.showTitleDetail = false\r\n    },\r\n    // 请求后台数据 获取列表信息\r\n    async getInnovationExcellenceList () {\r\n      const res = await this.$api.AssessmentOrgan.reqInnovationExcellenceList({\r\n        pageNo: this.pageNo,\r\n        pageSize: this.pageSize,\r\n        keyword: this.keyword,\r\n        publishStartTime: this.time[0],\r\n        publishEndTime: this.time[1],\r\n        // // sedateId: this.sedateId\r\n        // sedateId: this.selectedYear\r\n        memberType: this.memberType,\r\n        officeId: this.searchParams.officeId, // 部门查询\r\n        auditStatus: this.searchParams.auditStatusParams // 审核状态\r\n      })\r\n      var { data, total } = res\r\n      this.tableData = data\r\n      this.total = total\r\n      this.choose = []\r\n      this.selectObj = []\r\n      this.selectData = []\r\n      this.$nextTick(function () {\r\n        this.memoryChecked()\r\n      })\r\n    },\r\n    // paging_box 里面用到的方法\r\n    handleSizeChange () {\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    handleCurrentChange () {\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    search () {\r\n      this.pageNo = 1\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    reset () {\r\n      this.keyword = ''\r\n      this.publishStartTime = ''\r\n      this.time.splice(0, this.time.length)\r\n      this.searchParams.officeId = ''\r\n      this.searchParams.auditStatusParams = ''\r\n      // 重置后重新调用一次列表信息\r\n      this.getInnovationExcellenceList()\r\n    },\r\n    // 新增\r\n    newData () {\r\n      this.id = ''\r\n      this.show = true\r\n    },\r\n    // 标题详情 modify\r\n    modify (row) {\r\n      this.id = row.id\r\n      this.showTitleDetail = true\r\n    },\r\n    // 完成情况\r\n    finishStatus (row) {\r\n      this.id = row.id\r\n      this.showFinish = true\r\n    },\r\n    // 编辑\r\n    handleClick (row) {\r\n      this.id = row.id\r\n      this.show = true\r\n    },\r\n    passClick (auditStatus) {\r\n      if (this.choose.length) {\r\n        this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.getCheckInnovation(this.choose.join(','), auditStatus)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          })\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: '请至少选择一条数据',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 审核 (创新创优)\r\n    async getCheckInnovation (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckInnovation({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.getInnovationExcellenceList()\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.InnovationExcellence {\r\n  height: 100%;\r\n  width: 100%;\r\n  overflow: hidden;\r\n\r\n  .qd-list-wrap {\r\n    height: calc(100% - 83px);\r\n  }\r\n  .tableData {\r\n    .el-scrollbar__wrap .el-scrollbar__view th {\r\n      background: #f5f7fb;\r\n    }\r\n    height: calc(100% - 132px);\r\n  }\r\n  .button-box {\r\n    background: #fff;\r\n    margin-top: 20px;\r\n  }\r\n  .button-box .el-button {\r\n    background: #fff;\r\n  }\r\n  .el-button--danger.is-plain:hover,\r\n  .el-button--danger.is-plain:focus {\r\n    // color: #f56c6c;\r\n    background: #f56c6c;\r\n    color: #fff;\r\n  }\r\n  .el-button--success.is-plain:hover,\r\n  .el-button--success.is-plain:focus {\r\n    background: #67c23a;\r\n    color: #fff;\r\n  }\r\n  .delBtn {\r\n    color: #f56c6c;\r\n  }\r\n  .paging_box {\r\n    background: #fff;\r\n  }\r\n  .scoreEdit {\r\n    width: 700px;\r\n    height: 100%;\r\n    padding: 24px;\r\n    .form-button {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n  .search-box {\r\n    //title文字样式\r\n    .search-title {\r\n      width: 133px;\r\n      height: 16px;\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-left: 32px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}