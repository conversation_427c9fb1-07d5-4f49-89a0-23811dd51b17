{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\BusinessObjectivesNew.vue?vue&type=style&index=0&id=38956e4b&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\BusinessObjectivesNew.vue", "mtime": 1752541693786}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouQnVzaW5lc3NPYmplY3RpdmVzTmV3IHsNCiAgd2lkdGg6IDY5MnB4Ow0KICBoZWlnaHQ6IDEwMCU7DQogIHBhZGRpbmc6IDI0cHggNDBweDsNCiAgLy8gb3ZlcmZsb3cteTogc2Nyb2xsOw0KICBvdmVyZmxvdy14OiBoaWRkZW47DQogIG92ZXJmbG93LXk6IGF1dG87DQoNCiAgLmVsLWZvcm0taXRlbV9fbGFiZWwgew0KICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7DQogICAgZmxvYXQ6IGxlZnQ7DQogICAgZm9udC1zaXplOiAxM3B4Ow0KICAgIGNvbG9yOiAjNjA2MjY2Ow0KICAgIGxpbmUtaGVpZ2h0OiA0MHB4Ow0KICAgIHBhZGRpbmc6IDAgMTJweCAwIDA7DQogICAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["BusinessObjectivesNew.vue"], "names": [], "mappings": ";AAmPA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BusinessObjectivesNew.vue", "sourceRoot": "src/views/AssessmentOrgan/BusinessObjectives", "sourcesContent": ["<template>\r\n  <div class=\"BusinessObjectivesNew\">\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"demo-form\">\r\n      <el-form-item label=\"标题\"\r\n                    prop=\"title\">\r\n        <el-input v-model=\"form.title\"\r\n                  clearable>\r\n\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"发布时间\"\r\n                    prop=\"publishTime\">\r\n\r\n        <el-date-picker type=\"date\"\r\n                        placeholder=\"选择日期\"\r\n                        value-format=\"timestamp\"\r\n                        v-model=\"form.publishTime\"\r\n                        style=\"width: 100%;\">\r\n        </el-date-picker>\r\n\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"部门\"\r\n                    prop=\"officeId\">\r\n        <zy-select v-model=\"form.officeId\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\"\r\n                   @select=\"select\"\r\n                   placeholder=\"请选择部门\">\r\n        </zy-select>\r\n        <!-- <button @click=\"demo\">11</button> -->\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"时效选择\"\r\n                    prop=\"endTime\">\r\n        <el-form-item prop=\"endTime\">\r\n          <el-date-picker type=\"date\"\r\n                          placeholder=\"选择日期\"\r\n                          value-format=\"timestamp\"\r\n                          v-model=\"form.endTime\"\r\n                          style=\"width: 100%;\"></el-date-picker>\r\n        </el-form-item>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"分值\"\r\n                    class=\"form-title\">\r\n        <el-input-number v-model=\"form.score\"\r\n                         controls-position=\"right\"\r\n                         @change=\"handleChange\"\r\n                         :min=\"0\"\r\n                         :max=\"100\"\r\n                         class=\"form-content\">\r\n        </el-input-number>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"是否重点工作\"\r\n                    prop=\"isMainwork\">\r\n        <el-radio-group v-model=\"form.isMainwork\">\r\n          <el-radio label=\"1\">是</el-radio>\r\n          <el-radio label=\"0\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"类型\"\r\n                    prop=\"classify\">\r\n        <el-select v-model=\"form.classify\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择类型\">\r\n          <el-option v-for=\"item in classifyData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'BusinessObjectivesNew',\r\n  data () {\r\n    return {\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())), // 获取当前登录人信息\r\n      officeData: [],\r\n      classifyData: [],\r\n      form: {\r\n        id: '',\r\n        title: '',\r\n        officeId: '',\r\n        officeName: '',\r\n        endTime: '',\r\n        score: '',\r\n        classify: '',\r\n        isMainwork: '',\r\n        publishTime: '',\r\n        auditStatus: ''\r\n\r\n      },\r\n      rules: {\r\n\r\n        title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n        officeId: [\r\n          { required: true, message: '请选择部门', trigger: 'blur' }\r\n        ],\r\n        publishTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ],\r\n        endTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ],\r\n        isMainwork: [\r\n          { required: true, message: '请选择', trigger: 'blur' }\r\n        ]\r\n      },\r\n      circlesStatus: []\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  mounted () {\r\n    this.treeList()\r\n    this.form.publishTime = new Date().getTime()\r\n    // this.form.endTime = this.$utils.tmp(false) //获取当前时间\r\n\r\n    this.form.officeName = this.user.officeName\r\n    this.form.officeId = this.user.officeId\r\n    // this.userData = [{ mobile: this.user.mobile, name: this.user.userName, position: this.user.position, userId: this.user.id, userName: this.user.userName }]\r\n\r\n    this.dictionaryPubkvs()\r\n    if (this.id) {\r\n      this.getBusinessObjectiveDetails()\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n    *机构树\r\n   */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    select (row) {\r\n      this.form.officeName = row ? row.label : ''\r\n    },\r\n    /**\r\n     *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_functional_work'\r\n      })\r\n      var { data } = res\r\n      this.classifyData = data.evaluation_functional_work\r\n    },\r\n    // // 选择用户的回调\r\n    // userCallback (data, type) {\r\n    //   if (type) {\r\n    //     this.userData = data\r\n    //     this.form.publishUserName = data[0].name\r\n    //     this.form.officeName = data[0].officeName\r\n    //   }\r\n    //   this.userShow = !this.userShow\r\n    // },\r\n\r\n    // 获取目标详情 (作用:编辑界面内容填充)\r\n    async getBusinessObjectiveDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqBusinessObjectiveDetails(this.id)\r\n      var { data } = res\r\n      this.form.id = data.id\r\n      this.form.title = data.title\r\n      this.form.officeName = data.officeName\r\n      this.form.officeId = data.officeId\r\n      this.form.publishTime = data.publishTime\r\n      this.form.endTime = data.endTime\r\n      this.form.score = data.score\r\n      this.form.auditStatus = data.auditStatus\r\n      this.form.isMainwork = data.isMainwork\r\n      this.form.classify = data.classify\r\n    },\r\n\r\n    handleChange (value) {\r\n      console.log(value)\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var url = '/functional/work/add?'\r\n          if (this.id) {\r\n            url = '/functional/work/edit?'\r\n          }\r\n          this.$api.AssessmentOrgan.reqAddBusinessObjectives(url, {\r\n            id: this.id,\r\n            title: this.form.title,\r\n            officeId: this.form.officeId,\r\n            officeName: this.form.officeName,\r\n            endTime: this.form.endTime,\r\n            score: this.form.score,\r\n            classify: this.form.classify,\r\n            isMainwork: this.form.isMainwork,\r\n            publishTime: this.form.publishTime,\r\n            auditStatus: this.form.auditStatus\r\n\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('newCallback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('newCallback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.BusinessObjectivesNew {\r\n  width: 692px;\r\n  height: 100%;\r\n  padding: 24px 40px;\r\n  // overflow-y: scroll;\r\n  overflow-x: hidden;\r\n  overflow-y: auto;\r\n\r\n  .el-form-item__label {\r\n    text-align: right;\r\n    vertical-align: middle;\r\n    float: left;\r\n    font-size: 13px;\r\n    color: #606266;\r\n    line-height: 40px;\r\n    padding: 0 12px 0 0;\r\n    box-sizing: border-box;\r\n  }\r\n}\r\n</style>\r\n"]}]}