{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\sinceManagement-zx\\SinceReport\\SinceReportDetails.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\sinceManagement-zx\\SinceReport\\SinceReportDetails.vue", "mtime": 1752541697058}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEgKCkgew0KICAgIHJldHVybiB7DQogICAgICBkZXRhaWxzOiB7fQ0KICAgIH0NCiAgfSwNCiAgcHJvcHM6IFsnaWQnLCAndHlwZSddLA0KICBtb3VudGVkICgpIHsNCiAgICBpZiAodGhpcy5pZCkgew0KICAgICAgdGhpcy5kdXR5cmVwb3J0SW5mbygpDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgYXN5bmMgZHV0eXJlcG9ydEluZm8gKCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgdGhpcy4kYXBpLnNpbmNlTWFuYWdlbWVudC5kdXR5cmVwb3J0SW5mbyh0aGlzLmlkKQ0KICAgICAgY29uc29sZS5sb2cocmVzKQ0KICAgICAgdmFyIHsgZGF0YSB9ID0gcmVzDQogICAgICB0aGlzLmRldGFpbHMgPSBkYXRhDQogICAgfSwNCiAgICBmaWxlQ2xpY2sgKGRhdGEpIHsNCiAgICAgIHRoaXMuJGFwaS5wcm9wb3NhbC5kb3dubG9hZEZpbGUoeyBpZDogZGF0YS5pZCB9LCBkYXRhLmZpbGVOYW1lKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["SinceReportDetails.vue"], "names": [], "mappings": ";AAwCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SinceReportDetails.vue", "sourceRoot": "src/views/sinceManagement-zx/SinceReport", "sourcesContent": ["<template>\r\n  <div class=\"SuggestBillDetails details\">\r\n    <div class=\"details-title\">{{type?'我的年度履职报告':'委员年度履职报告'}}</div>\r\n    <div class=\"details-item-box\">\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">姓名</div>\r\n        <div class=\"details-item-value\">{{details.userName}}</div>\r\n      </div>\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">界别</div>\r\n        <div class=\"details-item-value\">{{details.deleId}}</div>\r\n      </div>\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">委员证号</div>\r\n        <div class=\"details-item-value\">{{details.memberNo}}</div>\r\n      </div>\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">年份</div>\r\n        <div class=\"details-item-value\">{{details.year}}</div>\r\n      </div>\r\n      <div class=\"details-item\">\r\n        <div class=\"details-item-label\">附件</div>\r\n        <div class=\"details-item-value\">\r\n          <div\r\n            class=\"details-item-file\"\r\n            v-for=\"(item, index) in details.attachmentList\"\r\n            :key=\"index\"\r\n            @click=\"fileClick(item)\"\r\n          >{{item.fileName}}</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"details-item-content-label\">报告内容</div>\r\n      <div\r\n        class=\"details-item-content\"\r\n        v-html=\"details.content\"\r\n      ></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data () {\r\n    return {\r\n      details: {}\r\n    }\r\n  },\r\n  props: ['id', 'type'],\r\n  mounted () {\r\n    if (this.id) {\r\n      this.dutyreportInfo()\r\n    }\r\n  },\r\n  methods: {\r\n    async dutyreportInfo () {\r\n      const res = await this.$api.sinceManagement.dutyreportInfo(this.id)\r\n      console.log(res)\r\n      var { data } = res\r\n      this.details = data\r\n    },\r\n    fileClick (data) {\r\n      this.$api.proposal.downloadFile({ id: data.id }, data.fileName)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SuggestBillDetails {\r\n    padding-bottom: 24px;\r\n}\r\n</style>\r\n"]}]}