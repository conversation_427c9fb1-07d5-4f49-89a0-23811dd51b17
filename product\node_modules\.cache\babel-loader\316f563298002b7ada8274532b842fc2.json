{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-sliding\\zy-sliding.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\zy-sliding\\zy-sliding.vue", "mtime": 1752541693576}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICd6eVNsaWRpbmcnLAoKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc2xpZGluZ0lkOiB0aGlzLnZhbHVlLAogICAgICBzbGlkaW5nTGlzdDogW10KICAgIH07CiAgfSwKCiAgcHJvcHM6IHsKICAgIHZhbHVlOiB7CiAgICAgIHR5cGU6IFN0cmluZwogICAgfSwKICAgIHNsaWRpbmc6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdCiAgICB9LAogICAgcHJvcHM6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiAoKSA9PiB7fQogICAgfQogIH0sCiAgbW9kZWw6IHsKICAgIHByb3A6ICd2YWx1ZScsCiAgICBldmVudDogJ2lkJwogIH0sCgogIG1vdW50ZWQoKSB7CiAgICB0aGlzLnNsaWRpbmdEYXRhKHRoaXMuZGVlcENvcHkodGhpcy5zbGlkaW5nKSk7CiAgfSwKCiAgd2F0Y2g6IHsKICAgIHZhbHVlKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgdGhpcy5zbGlkaW5nTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0uaWQgPT09IHRoaXMudmFsdWUpIHsKICAgICAgICAgICAgdGhpcy5zbGlkaW5nQ2xpY2soaXRlbSwgdHJ1ZSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCgogICAgc2xpZGluZyh2YWwpIHsKICAgICAgdGhpcy5zbGlkaW5nRGF0YSh0aGlzLmRlZXBDb3B5KHRoaXMuc2xpZGluZykpOwogICAgfSwKCiAgICBzbGlkaW5nTGlzdCh2YWwpIHsKICAgICAgaWYgKHZhbC5sZW5ndGgpIHsKICAgICAgICB0aGlzLnNsaWRpbmdMaXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS5pZCA9PT0gdGhpcy52YWx1ZSkgewogICAgICAgICAgICB0aGlzLnNsaWRpbmdDbGljayhpdGVtKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgfQoKICB9LAogIG1ldGhvZHM6IHsKICAgIHNsaWRpbmdDbGljayhkYXRhLCB0eXBlKSB7CiAgICAgIGlmICh0aGlzLnZhbHVlICE9PSBkYXRhLmlkKSB7CiAgICAgICAgdGhpcy4kZW1pdCgnaWQnLCBkYXRhLmlkKTsKICAgICAgICB0aGlzLnJldHVybkRhdGEoZGF0YSk7CiAgICAgIH0KCiAgICAgIGlmICh0eXBlKSB7CiAgICAgICAgdGhpcy5yZXR1cm5EYXRhKGRhdGEpOwogICAgICB9CgogICAgICB0aGlzLnNsaWRpbmdMaXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgaXRlbS5jbGFzcyA9IGZhbHNlOwoKICAgICAgICBpZiAoaXRlbS5pZCA9PT0gZGF0YS5pZCkgewogICAgICAgICAgaXRlbS5jbGFzcyA9IHRydWU7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuc2xpZGluZ0JveCgpOwogICAgICAgIHRoaXMuc2xpZGluZ0lvY2F0aW9uKCk7CiAgICAgIH0pOwogICAgfSwKCiAgICByZXR1cm5EYXRhKGRhdGEpIHsKICAgICAgdmFyIGFyciA9IFtdOwogICAgICB0aGlzLnNsaWRpbmcuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpZiAodGhpcy5wcm9wcykgewogICAgICAgICAgaWYgKGRhdGEuaWQgPT09IGl0ZW1bdGhpcy5wcm9wcy5pZF0pIHsKICAgICAgICAgICAgYXJyID0gaXRlbTsKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgaWYgKGRhdGEuaWQgPT09IGl0ZW0uaWQpIHsKICAgICAgICAgICAgYXJyID0gaXRlbTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgICB0aGlzLiRlbWl0KCdzbGlkaW5nLWNsaWNrJywgYXJyKTsKICAgIH0sCgogICAgc2xpZGluZ0lvY2F0aW9uKCkgewogICAgICBjb25zdCBzbGlkaW5nSXRlbSA9IHRoaXMuJHJlZnNbJ3p5LXNsaWRpbmcnXS5xdWVyeVNlbGVjdG9yKCcuenktc2xpZGluZy1pdGVtLWEnKTsKICAgICAgY29uc3Qgc2xpZGluZyA9IHRoaXMuJHJlZnNbJ3p5LXNsaWRpbmcnXS5xdWVyeVNlbGVjdG9yKCcuenktc2xpZGluZy1pdGVtLXNsaWRpbmcnKTsKICAgICAgc2xpZGluZy5zdHlsZS53aWR0aCA9IGAke3NsaWRpbmdJdGVtLm9mZnNldFdpZHRofXB4YDsKICAgICAgc2xpZGluZy5zdHlsZS50cmFuc2Zvcm0gPSBgdHJhbnNsYXRlWCgke3NsaWRpbmdJdGVtLm9mZnNldExlZnR9cHgpYDsKICAgICAgc2xpZGluZy5zdHlsZS50cmFuc2l0aW9uRHVyYXRpb24gPSAnLjNzJzsKICAgIH0sCgogICAgc2xpZGluZ0JveCgpIHsKICAgICAgdmFyIHNsaWRpbmcgPSB0aGlzLiRyZWZzWyd6eS1zbGlkaW5nJ10ucXVlcnlTZWxlY3RvcignLnp5LXNsaWRpbmctYm94Jyk7CiAgICAgIHZhciBpdGVtQm94ID0gdGhpcy4kcmVmc1snenktc2xpZGluZyddLnF1ZXJ5U2VsZWN0b3IoJy56eS1zbGlkaW5nLWl0ZW0tYm94Jyk7CiAgICAgIHZhciBpdGVtID0gdGhpcy4kcmVmc1snenktc2xpZGluZyddLnF1ZXJ5U2VsZWN0b3IoJy56eS1zbGlkaW5nLWl0ZW0tYScpOwoKICAgICAgaWYgKHNsaWRpbmcub2Zmc2V0V2lkdGggPCBpdGVtQm94Lm9mZnNldFdpZHRoKSB7CiAgICAgICAgaXRlbUJveC5zdHlsZS50cmFuc2Zvcm0gPSAndHJhbnNsYXRlWCgwcHgpJzsKICAgICAgICBpdGVtQm94LnN0eWxlLnRyYW5zaXRpb25EdXJhdGlvbiA9ICcuNHMnOwoKICAgICAgICBpZiAoaXRlbUJveC5vZmZzZXRXaWR0aCA9PT0gaXRlbS5vZmZzZXRMZWZ0ICsgaXRlbS5vZmZzZXRXaWR0aCkgewogICAgICAgICAgaXRlbUJveC5zdHlsZS50cmFuc2Zvcm0gPSBgdHJhbnNsYXRlWCgtJHtpdGVtQm94Lm9mZnNldFdpZHRoIC0gc2xpZGluZy5vZmZzZXRXaWR0aH1weClgOwogICAgICAgIH0gZWxzZSBpZiAoc2xpZGluZy5vZmZzZXRXaWR0aCAvIDIgPiBpdGVtQm94Lm9mZnNldFdpZHRoIC0gaXRlbS5vZmZzZXRMZWZ0KSB7CiAgICAgICAgICBpdGVtQm94LnN0eWxlLnRyYW5zZm9ybSA9IGB0cmFuc2xhdGVYKC0ke2l0ZW1Cb3gub2Zmc2V0V2lkdGggLSBzbGlkaW5nLm9mZnNldFdpZHRofXB4KWA7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGl0ZW1Cb3guc3R5bGUudHJhbnNmb3JtID0gYHRyYW5zbGF0ZVgoLSR7aXRlbS5vZmZzZXRMZWZ0IC0gc2xpZGluZy5vZmZzZXRXaWR0aCAvIDJ9cHgpYDsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCgogICAgc2xpZGluZ0RhdGEoZGF0YSkgewogICAgICB0aGlzLmluaXREYXRhKGRhdGEpOwogICAgICB0aGlzLnNsaWRpbmdMaXN0ID0gZGF0YTsKICAgIH0sCgogICAgaW5pdERhdGEoaXRlbXMpIHsKICAgICAgaXRlbXMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsKICAgICAgICBpZiAodGhpcy5wcm9wcykgewogICAgICAgICAgaWYgKHR5cGVvZiBpdGVtLmlkID09PSAndW5kZWZpbmVkJykgewogICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1saW5lCiAgICAgICAgICAgIGl0ZW0uaWQgPSBpdGVtW3RoaXMucHJvcHMuaWRdOwogICAgICAgICAgfQoKICAgICAgICAgIGlmICh0eXBlb2YgaXRlbS5uYW1lID09PSAndW5kZWZpbmVkJykgewogICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1saW5lCiAgICAgICAgICAgIGl0ZW0ubmFtZSA9IGl0ZW1bdGhpcy5wcm9wcy5uYW1lXTsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIGlmICh0eXBlb2YgaXRlbS5jbGFzcyA9PT0gJ3VuZGVmaW5lZCcpIHsKICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLWxpbmUKICAgICAgICAgIGl0ZW0uY2xhc3MgPSBmYWxzZTsKICAgICAgICB9CgogICAgICAgIGlmICh0aGlzLnZhbHVlID09PSBpdGVtLmlkKSB7CiAgICAgICAgICBpdGVtLmNsYXNzID0gdHJ1ZTsKICAgICAgICAgIHRoaXMuJGVtaXQoJ3NsaWRpbmctY2xpY2snLCBpdGVtKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKCiAgICBkZWVwQ29weShkYXRhKSB7CiAgICAgIHZhciB0ID0gdGhpcy50eXBlKGRhdGEpOwogICAgICB2YXIgbzsKICAgICAgdmFyIGk7CiAgICAgIHZhciBuaTsKCiAgICAgIGlmICh0ID09PSAnYXJyYXknKSB7CiAgICAgICAgbyA9IFtdOwogICAgICB9IGVsc2UgaWYgKHQgPT09ICdvYmplY3QnKSB7CiAgICAgICAgbyA9IHt9OwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiBkYXRhOwogICAgICB9CgogICAgICBpZiAodCA9PT0gJ2FycmF5JykgewogICAgICAgIGZvciAoaSA9IDAsIG5pID0gZGF0YS5sZW5ndGg7IGkgPCBuaTsgaSsrKSB7CiAgICAgICAgICBvLnB1c2godGhpcy5kZWVwQ29weShkYXRhW2ldKSk7CiAgICAgICAgfQoKICAgICAgICByZXR1cm4gbzsKICAgICAgfSBlbHNlIGlmICh0ID09PSAnb2JqZWN0JykgewogICAgICAgIGZvciAoaSBpbiBkYXRhKSB7CiAgICAgICAgICBvW2ldID0gdGhpcy5kZWVwQ29weShkYXRhW2ldKTsKICAgICAgICB9CgogICAgICAgIHJldHVybiBvOwogICAgICB9CiAgICB9LAoKICAgIHR5cGUob2JqKSB7CiAgICAgIHZhciB0b1N0cmluZyA9IE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmc7CiAgICAgIHZhciBtYXAgPSB7CiAgICAgICAgJ1tvYmplY3QgQm9vbGVhbl0nOiAnYm9vbGVhbicsCiAgICAgICAgJ1tvYmplY3QgTnVtYmVyXSc6ICdudW1iZXInLAogICAgICAgICdbb2JqZWN0IFN0cmluZ10nOiAnc3RyaW5nJywKICAgICAgICAnW29iamVjdCBGdW5jdGlvbl0nOiAnZnVuY3Rpb24nLAogICAgICAgICdbb2JqZWN0IEFycmF5XSc6ICdhcnJheScsCiAgICAgICAgJ1tvYmplY3QgRGF0ZV0nOiAnZGF0ZScsCiAgICAgICAgJ1tvYmplY3QgUmVnRXhwXSc6ICdyZWdFeHAnLAogICAgICAgICdbb2JqZWN0IFVuZGVmaW5lZF0nOiAndW5kZWZpbmVkJywKICAgICAgICAnW29iamVjdCBOdWxsXSc6ICdudWxsJywKICAgICAgICAnW29iamVjdCBPYmplY3RdJzogJ29iamVjdCcKICAgICAgfTsKICAgICAgcmV0dXJuIG1hcFt0b1N0cmluZy5jYWxsKG9iaildOwogICAgfSwKCiAgICBtYWtlRGF0YShkYXRhKSB7CiAgICAgIGNvbnN0IHQgPSB0aGlzLnR5cGUoZGF0YSk7CiAgICAgIGxldCBvOwoKICAgICAgaWYgKHQgPT09ICdhcnJheScpIHsKICAgICAgICBvID0gW107CiAgICAgIH0gZWxzZSBpZiAodCA9PT0gJ29iamVjdCcpIHsKICAgICAgICBvID0ge307CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuIGRhdGE7CiAgICAgIH0KCiAgICAgIGlmICh0ID09PSAnYXJyYXknKSB7CiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICBvLnB1c2godGhpcy5tYWtlRGF0YShkYXRhW2ldKSk7CiAgICAgICAgfQogICAgICB9IGVsc2UgaWYgKHQgPT09ICdvYmplY3QnKSB7CiAgICAgICAgZm9yIChjb25zdCBpIGluIGRhdGEpIHsKICAgICAgICAgIGlmIChpICE9ICdjbGFzcycpIHsKICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbGluZQogICAgICAgICAgICBvW2ldID0gdGhpcy5tYWtlRGF0YShkYXRhW2ldKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIHJldHVybiBvOwogICAgfQoKICB9Cn07"}, {"version": 3, "mappings": "AAiBA;EACAA,iBADA;;EAEAC;IACA;MACAC,qBADA;MAEAC;IAFA;EAIA,CAPA;;EAQAC;IACAC;MACAC;IADA,CADA;IAIAC;MACAD,WADA;MAEAE;IAFA,CAJA;IAQAJ;MACAE,YADA;MAEAE;IAFA;EARA,CARA;EAqBAC;IACAC,aADA;IAEAC;EAFA,CArBA;;EAyBAC;IACA;EACA,CA3BA;;EA4BAC;IACAR;MACA;QACA;UACA;YACA;UACA;QACA,CAJA;MAKA;IACA,CATA;;IAUAE;MACA;IACA,CAZA;;IAaAJ;MACA;QACA;UACA;YACA;UACA;QACA,CAJA;MAKA;IACA;;EArBA,CA5BA;EAmDAW;IACAC;MACA;QACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACAC;;QACA;UACAA;QACA;MACA,CALA;MAMA;QACA;QACA;MACA,CAHA;IAIA,CAnBA;;IAoBAC;MACA;MACA;QACA;UACA;YACAC;UACA;QACA,CAJA,MAIA;UACA;YACAA;UACA;QACA;MACA,CAVA;MAWA;IACA,CAlCA;;IAmCAC;MACA;MACA;MACAZ;MACAA;MACAA;IACA,CAzCA;;IA0CAa;MACA;MACA;MACA;;MACA;QACAC;QACAA;;QACA;UACAA;QACA,CAFA,MAEA;UACAA;QACA,CAFA,MAEA;UACAA;QACA;MACA;IACA,CAzDA;;IA0DAC;MACA;MACA;IACA,CA7DA;;IA8DAC;MACAC;QACA;UACA;YAAA;YACAR;UACA;;UACA;YAAA;YACAA;UACA;QACA;;QACA;UAAA;UACAA;QACA;;QACA;UACAA;UACA;QACA;MACA,CAhBA;IAiBA,CAhFA;;IAiFAS;MACA;MACA;MACA;MACA;;MACA;QACAC;MACA,CAFA,MAEA;QACAA;MACA,CAFA,MAEA;QACA;MACA;;MACA;QACA;UACAA;QACA;;QACA;MACA,CALA,MAKA;QACA;UACAA;QACA;;QACA;MACA;IACA,CAxGA;;IAyGApB;MACA;MACA;QACA,6BADA;QAEA,2BAFA;QAGA,2BAHA;QAIA,+BAJA;QAKA,yBALA;QAMA,uBANA;QAOA,2BAPA;QAQA,iCARA;QASA,uBATA;QAUA;MAVA;MAYA;IACA,CAxHA;;IAyHAqB;MACA;MACA;;MACA;QACAD;MACA,CAFA,MAEA;QACAA;MACA,CAFA,MAEA;QACA;MACA;;MACA;QACA;UACAA;QACA;MACA,CAJA,MAIA;QACA;UACA;YAAA;YACAA;UACA;QACA;MACA;;MACA;IACA;;EA/IA;AAnDA", "names": ["name", "data", "slidingId", "slidingList", "props", "value", "type", "sliding", "default", "model", "prop", "event", "mounted", "watch", "methods", "slidingClick", "item", "returnData", "arr", "slidingIocation", "slidingBox", "itemBox", "slidingData", "initData", "items", "deepCopy", "o", "makeData"], "sourceRoot": "src/components/zy-sliding", "sources": ["zy-sliding.vue"], "sourcesContent": ["<template>\r\n  <div class=\"zy-sliding\"\r\n       ref=\"zy-sliding\">\r\n    <div class=\"zy-sliding-box\">\r\n      <div class=\"zy-sliding-item-box\">\r\n        <div :class=\"['zy-sliding-item',item.class?'zy-sliding-item-a':'']\"\r\n             v-for=\"(item, index) in slidingList\"\r\n             @click=\"slidingClick(item)\"\r\n             :key=\"index\">\r\n          {{item.name}}\r\n        </div>\r\n        <div class=\"zy-sliding-item-sliding\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'zySliding',\r\n  data () {\r\n    return {\r\n      slidingId: this.value,\r\n      slidingList: []\r\n    }\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String\r\n    },\r\n    sliding: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    props: {\r\n      type: Object,\r\n      default: () => { }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value',\r\n    event: 'id'\r\n  },\r\n  mounted () {\r\n    this.slidingData(this.deepCopy(this.sliding))\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.slidingList.forEach(item => {\r\n          if (item.id === this.value) {\r\n            this.slidingClick(item, true)\r\n          }\r\n        })\r\n      }\r\n    },\r\n    sliding (val) {\r\n      this.slidingData(this.deepCopy(this.sliding))\r\n    },\r\n    slidingList (val) {\r\n      if (val.length) {\r\n        this.slidingList.forEach(item => {\r\n          if (item.id === this.value) {\r\n            this.slidingClick(item)\r\n          }\r\n        })\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    slidingClick (data, type) {\r\n      if (this.value !== data.id) {\r\n        this.$emit('id', data.id)\r\n        this.returnData(data)\r\n      }\r\n      if (type) {\r\n        this.returnData(data)\r\n      }\r\n      this.slidingList.forEach(item => {\r\n        item.class = false\r\n        if (item.id === data.id) {\r\n          item.class = true\r\n        }\r\n      })\r\n      this.$nextTick(() => {\r\n        this.slidingBox()\r\n        this.slidingIocation()\r\n      })\r\n    },\r\n    returnData (data) {\r\n      var arr = []\r\n      this.sliding.forEach(item => {\r\n        if (this.props) {\r\n          if (data.id === item[this.props.id]) {\r\n            arr = item\r\n          }\r\n        } else {\r\n          if (data.id === item.id) {\r\n            arr = item\r\n          }\r\n        }\r\n      })\r\n      this.$emit('sliding-click', arr)\r\n    },\r\n    slidingIocation () {\r\n      const slidingItem = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-a')\r\n      const sliding = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-sliding')\r\n      sliding.style.width = `${slidingItem.offsetWidth}px`\r\n      sliding.style.transform = `translateX(${slidingItem.offsetLeft}px)`\r\n      sliding.style.transitionDuration = '.3s'\r\n    },\r\n    slidingBox () {\r\n      var sliding = this.$refs['zy-sliding'].querySelector('.zy-sliding-box')\r\n      var itemBox = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-box')\r\n      var item = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-a')\r\n      if (sliding.offsetWidth < itemBox.offsetWidth) {\r\n        itemBox.style.transform = 'translateX(0px)'\r\n        itemBox.style.transitionDuration = '.4s'\r\n        if (itemBox.offsetWidth === item.offsetLeft + item.offsetWidth) {\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - sliding.offsetWidth}px)`\r\n        } else if (sliding.offsetWidth / 2 > itemBox.offsetWidth - item.offsetLeft) {\r\n          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - sliding.offsetWidth}px)`\r\n        } else {\r\n          itemBox.style.transform = `translateX(-${item.offsetLeft - sliding.offsetWidth / 2}px)`\r\n        }\r\n      }\r\n    },\r\n    slidingData (data) {\r\n      this.initData(data)\r\n      this.slidingList = data\r\n    },\r\n    initData (items) {\r\n      items.forEach((item, index) => {\r\n        if (this.props) {\r\n          if ((typeof item.id) === 'undefined') { // eslint-disable-line\r\n            item.id = item[this.props.id]\r\n          }\r\n          if ((typeof item.name) === 'undefined') { // eslint-disable-line\r\n            item.name = item[this.props.name]\r\n          }\r\n        }\r\n        if ((typeof item.class) === 'undefined') { // eslint-disable-line\r\n          item.class = false\r\n        }\r\n        if (this.value === item.id) {\r\n          item.class = true\r\n          this.$emit('sliding-click', item)\r\n        }\r\n      })\r\n    },\r\n    deepCopy (data) {\r\n      var t = this.type(data)\r\n      var o\r\n      var i\r\n      var ni\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (i = 0, ni = data.length; i < ni; i++) {\r\n          o.push(this.deepCopy(data[i]))\r\n        }\r\n        return o\r\n      } else if (t === 'object') {\r\n        for (i in data) {\r\n          o[i] = this.deepCopy(data[i])\r\n        }\r\n        return o\r\n      }\r\n    },\r\n    type (obj) {\r\n      var toString = Object.prototype.toString\r\n      var map = {\r\n        '[object Boolean]': 'boolean',\r\n        '[object Number]': 'number',\r\n        '[object String]': 'string',\r\n        '[object Function]': 'function',\r\n        '[object Array]': 'array',\r\n        '[object Date]': 'date',\r\n        '[object RegExp]': 'regExp',\r\n        '[object Undefined]': 'undefined',\r\n        '[object Null]': 'null',\r\n        '[object Object]': 'object'\r\n      }\r\n      return map[toString.call(obj)]\r\n    },\r\n    makeData (data) {\r\n      const t = this.type(data)\r\n      let o\r\n      if (t === 'array') {\r\n        o = []\r\n      } else if (t === 'object') {\r\n        o = {}\r\n      } else {\r\n        return data\r\n      }\r\n      if (t === 'array') {\r\n        for (let i = 0; i < data.length; i++) {\r\n          o.push(this.makeData(data[i]))\r\n        }\r\n      } else if (t === 'object') {\r\n        for (const i in data) {\r\n          if (i != 'class') {// eslint-disable-line\r\n            o[i] = this.makeData(data[i])\r\n          }\r\n        }\r\n      }\r\n      return o\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./zy-sliding.scss\";\r\n</style>\r\n"]}]}