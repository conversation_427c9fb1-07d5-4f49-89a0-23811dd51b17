<template>
  <div class="big-screen" ref="bigScreen">
    <div class="screen-header">
      <div class="header-left">
        <span class="date-time">{{ currentTime }}</span>
        <span class="weather">晴 24℃ 东南风</span>
      </div>
      <div class="header-center">
        <img src="../../../assets/largeScreen/top_header_txt.png" alt="" style="height: 50px;">
      </div>
      <div class="header-right">
        <div class="header-buttons">
          <div class="header-btn current-module-btn">
            <span>网络议政</span>
          </div>
          <div class="header-btn home-btn" @click="goHome">
            <span>返回首页</span>
          </div>
        </div>
      </div>
    </div>
    <div class="screen-content">
      <div class="left-panel">
        <!-- 网络议政整体情况 -->
        <div class="discussion_overall_situation">
          <div class="header_box">
            <span class="header_text_left">网络议政整体情况</span>
          </div>
          <div class="discussion_overall_situation_content">
            
          </div>
        </div>
        <!-- 热词分析 -->
        <div class="hot_word_analysis">
          <div class="header_box">
            <span class="header_text_left">热词分析</span>
          </div>
          <div class="hot_word_analysis_content">
            <WordCloud chart-id="hotWordChart" :words="hotWordsData" @word-click="onWordClick" />
          </div>
        </div>
        <!-- 发布单位征集次数统计 -->
        <div class="publish_unit_collections">
          <div class="header_box">
            <span class="header_text_left">发布单位征集次数统计</span>
          </div>
          <div class="publish_unit_collections_content">
            <BarChart id="publish_unit_collections" :chart-data="publishUnitCollectionsData" :legendShow="true"
              legendName="征集次数" />
          </div>
        </div>
      </div>
      <div class="right-panel">
        <!-- 最热话题 -->
        <div class="hottest_topic">
          <div class="header_box">
            <span class="header_text_left">最热话题</span>
          </div>
          <div class="hottest_topic_list">
            <div v-for="(item, index) in hottestTopicData" :key="item.id" class="hottest_topic_item"
              :class="{ 'with-bg-image': index % 2 === 0, 'with-bg-color': index % 2 === 1 }">
              <div class="hottest_topic_content">
                <img src="../../../assets/largeScreen/icon_hottest_topic.png" alt="" class="hottest_topic_icon">
                <div class="hottest_topic_title">{{ item.title }}</div>
                <div class="hottest_topic_unit">{{ item.unit }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useIndex } from '../screen.js'
import WordCloud from '../components/WordCloud.vue'
import BarChart from '../components/BarChart.vue'
export default {
  name: 'BigScreen',
  components: {
    WordCloud,
    BarChart
  },
  data () {
    return {
      currentTime: '',
      hotWordsData: [
        { name: '经济建设', value: 10 },
        { name: '人才培养', value: 9 },
        { name: 'AI技术', value: 6 },
        { name: '改革创新', value: 7 },
        { name: '教育', value: 5 },
        { name: '车辆交通', value: 6 },
        { name: '旅游', value: 5 },
        { name: '公共安全', value: 7 },
        { name: '智能化', value: 6 },
        { name: '电梯故障', value: 4 },
        { name: '社会保障', value: 6 },
        { name: '环境保护', value: 5 },
        { name: '医疗卫生', value: 7 },
        { name: '文化建设', value: 4 },
        { name: '科技创新', value: 8 }
      ],
      hottestTopicData: [
        {
          id: 1,
          title: '市政协社会和法制工作办公室围绕"居家适老化改造"开展专题调研',
          unit: '市政协'
        },
        {
          id: 2,
          title: '"与民同行 共创共赢"新格局下民营企业转型发展座谈会召开',
          unit: '市政协'
        },
        {
          id: 3,
          title: '"惠民生·基层行"义诊活动温暖人心',
          unit: '市政协'
        },
        {
          id: 4,
          title: '市科技局面复市政协科技界别提案',
          unit: '市政协'
        },
        {
          id: 5,
          title: '市政协召开"推进数字化转型"专题协商会',
          unit: '市政协'
        },
        {
          id: 6,
          title: '政协委员深入基层开展"三服务"活动',
          unit: '市政协'
        },
        {
          id: 1,
          title: '市政协社会和法制工作办公室围绕"居家适老化改造"开展专题调研',
          unit: '市政协'
        },
        {
          id: 2,
          title: '"与民同行 共创共赢"新格局下民营企业转型发展座谈会召开',
          unit: '市政协'
        },
        {
          id: 3,
          title: '"惠民生·基层行"义诊活动温暖人心',
          unit: '市政协'
        },
        {
          id: 4,
          title: '市科技局面复市政协科技界别提案',
          unit: '市政协'
        },
        {
          id: 5,
          title: '市政协召开"推进数字化转型"专题协商会',
          unit: '市政协'
        },
        {
          id: 6,
          title: '政协委员深入基层开展"三服务"活动',
          unit: '市政协'
        },
        {
          id: 1,
          title: '市政协社会和法制工作办公室围绕"居家适老化改造"开展专题调研',
          unit: '市政协'
        },
        {
          id: 2,
          title: '"与民同行 共创共赢"新格局下民营企业转型发展座谈会召开',
          unit: '市政协'
        },
        {
          id: 3,
          title: '"惠民生·基层行"义诊活动温暖人心',
          unit: '市政协'
        },
        {
          id: 4,
          title: '市科技局面复市政协科技界别提案',
          unit: '市政协'
        },
        {
          id: 5,
          title: '市政协召开"推进数字化转型"专题协商会',
          unit: '市政协'
        },
        {
          id: 6,
          title: '政协委员深入基层开展"三服务"活动',
          unit: '市政协'
        },
        {
          id: 1,
          title: '市政协社会和法制工作办公室围绕"居家适老化改造"开展专题调研',
          unit: '市政协'
        },
        {
          id: 2,
          title: '"与民同行 共创共赢"新格局下民营企业转型发展座谈会召开',
          unit: '市政协'
        },
        {
          id: 3,
          title: '"惠民生·基层行"义诊活动温暖人心',
          unit: '市政协'
        },
        {
          id: 4,
          title: '市科技局面复市政协科技界别提案',
          unit: '市政协'
        },
        {
          id: 5,
          title: '市政协召开"推进数字化转型"专题协商会',
          unit: '市政协'
        },
        {
          id: 6,
          title: '政协委员深入基层开展"三服务"活动',
          unit: '市政协'
        }
      ],
      // 各专委会提案数数据
      publishUnitCollectionsData: [
        { name: '提案委', value: 43 },
        { name: '经济委', value: 67 },
        { name: '农业农村委', value: 84 },
        { name: '人口资源环境委', value: 52 },
        { name: '教科卫体委', value: 36 },
        { name: '社会和法制委', value: 66 },
        { name: '民族宗教委', value: 26 },
        { name: '港澳台侨外事委', value: 60 },
        { name: '文化文史和学习委', value: 46 }
      ]
    }
  },
  computed: {
  },
  mounted () {
    this.initScreen()
    this.updateTime()
    this.timeInterval = setInterval(this.updateTime, 1000)
  },
  beforeDestroy () {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    initScreen () {
      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)
      calcRate()
      windowDraw()
    },
    updateTime () {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    // 返回首页
    goHome () {
      this.$router.push({ path: '/homeBox' })
    },
    // 词云点击事件
    onWordClick (word) {
      console.log('词汇点击:', word)
    }
  }
}
</script>

<style lang="scss" scoped>
.big-screen {
  width: 1920px;
  height: 1080px;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transform-origin: left top;
  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;
  background-size: cover;
  background-position: center;

  .screen-header {
    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;
    background-size: 100% 100%;
    background-position: center;
    height: 65px;
    display: flex;
    align-items: center;
    padding: 0 40px;

    .header-left {
      display: flex;
      gap: 20px;
      font-size: 14px;
      color: #8cc8ff;
      flex: 1;
    }

    .header-center {
      width: 60%;
      text-align: center;
    }

    .header-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .header-buttons {
        display: flex;
        gap: 15px;

        .header-btn {
          height: 42px;
          line-height: 42px;
          padding: 0 16px;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover {
            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);
            border-color: rgba(0, 181, 254, 1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);

            &::before {
              left: 100%;
            }
          }

          &.current-module-btn {
            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
            font-weight: bold;
            font-size: 16px;
            color: #FFFFFF;
          }

          &.home-btn {
            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
            font-weight: 400;
            font-size: 16px;
            color: #1FC6FF;
          }

          &.area-select-btn {
            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);
            border: 1px solid rgba(0, 181, 254, 0.5);
            border-radius: 6px;
            font-weight: 500;
            font-size: 16px;
            color: #1FC6FF;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-width: 120px;

            .dropdown-icon {
              margin-left: 8px;
              font-size: 12px;
              transition: transform 0.3s ease;
              color: #1FC6FF;

              &.active {
                transform: rotate(180deg);
              }
            }

            &:hover {
              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);
              border-color: rgba(0, 181, 254, 0.8);
            }
          }
        }
      }
    }
  }

  .screen-content {
    height: calc(100% - 65px);
    display: flex;
    padding: 20px;
    gap: 20px;

    .header_box {
      position: absolute;
      top: 15px;
      left: 24px;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header_text_left {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        cursor: pointer;
      }

      .header_text_right {
        font-size: 15px;
        color: #FFD600;
      }

      .header_text_center {
        font-size: 15px;
        color: #FFFFFF;
        display: flex;
        align-items: center;

        span {
          font-weight: 500;
          font-size: 24px;
          color: #02FBFB;
          margin: 0 10px 0 6px;
        }
      }
    }

    .left-panel {
      flex: 1;
      display: grid;
      grid-template-columns: 2fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 20px;
      height: 100%;
    }

    .right-panel {
      width: 630px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    // 左侧面板样式
    .left-panel {

      // 网络议政整体情况
      .discussion_overall_situation {
        background: url('../../../assets/largeScreen/discussion_overall_situation_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 1; // 第一列
        grid-row: 1; // 第一行
        height: 400px;

        .discussion_overall_situation_content {
          display: flex;
          justify-content: space-around;
          align-items: center;
          height: 100%;
          margin-top: 30px;
        }
      }

      // 热词分析
      .hot_word_analysis {
        background: url('../../../assets/largeScreen/hot_word_analysis_bg2.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 2; // 第二列
        grid-row: 1; // 第一行
        height: 400px;

        .hot_word_analysis_content {
          margin-top: 50px;
          height: calc(100% - 50px);
        }
      }

      // 发布单位征集次数统计
      .publish_unit_collections {
        background: url('../../../assets/largeScreen/publish_unit_collections_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        grid-column: 1 / -1; // 跨越三列（讨论组人员统计在第三行）
        grid-row: 2; // 明确指定在第三行
        height: 550px;

        .publish_unit_collections_content {
          margin-top: 50px;
          height: calc(100% - 50px);
        }
      }
    }

    .right-panel {

      // 最热话题
      .hottest_topic {
        background: url('../../../assets/largeScreen/hottest_topic_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 100%;

        .hottest_topic_list {
          margin-top: 70px;
          margin-left: 14px;
          margin-right: 14px;
          height: calc(100% - 70px);
          overflow-y: auto;

          &::-webkit-scrollbar {
            width: 4px;
          }

          &::-webkit-scrollbar-track {
            background: rgba(0, 30, 60, 0.3);
            border-radius: 2px;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(0, 212, 255, 0.4);
            border-radius: 2px;

            &:hover {
              background: rgba(0, 212, 255, 0.6);
            }
          }

          .hottest_topic_item {
            margin-bottom: 12px;
            overflow: hidden;
            position: relative;

            &:last-child {
              margin-bottom: 0;
            }

            // 奇数项 - 背景图片样式
            &.with-bg-image {
              background: url('../../../assets/largeScreen/table_bg.png') no-repeat;
              background-size: 100% 100%;
              background-position: center;
            }

            // 偶数项 - 背景颜色样式
            &.with-bg-color {
              background: rgba(6, 79, 219, 0.05);
            }

            .hottest_topic_content {
              padding: 12px 15px;
              position: relative;
              z-index: 2;
              display: flex;
              justify-content: space-between;
              align-items: center;

              .hottest_topic_icon {
                width: 15px;
                height: 18px;
                margin-right: 10px;
              }

              .hottest_topic_title {
                flex: 1;
                color: #fff;
                font-size: 16px;
                margin-right: 16px;
                // 文本溢出处理
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .hottest_topic_unit {
                flex-shrink: 0;
                font-size: 16px;
                color: #FFFFFF;
              }
            }
          }
        }
      }
    }
  }
}
</style>
