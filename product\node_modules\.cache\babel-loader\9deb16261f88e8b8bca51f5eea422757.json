{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\BusinessObjectivesNew.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\BusinessObjectivesNew.vue", "mtime": 1752541693786}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA2FA;EACAA,6BADA;;EAEAC;IACA;MACAC,+DADA;MACA;MACAC,cAFA;MAGAC,gBAHA;MAIAC;QACAC,MADA;QAEAC,SAFA;QAGAC,YAHA;QAIAC,cAJA;QAKAC,WALA;QAMAC,SANA;QAOAC,YAPA;QAQAC,cARA;QASAC,eATA;QAUAC;MAVA,CAJA;MAiBAC;QAEAT,QACA;UAAAU;UAAAC;UAAAC;QAAA,CADA,CAFA;QAKAX,WACA;UAAAS;UAAAC;UAAAC;QAAA,CADA,CALA;QAQAL,cACA;UAAAG;UAAAC;UAAAC;QAAA,CADA,CARA;QAWAT,UACA;UAAAO;UAAAC;UAAAC;QAAA,CADA,CAXA;QAcAN,aACA;UAAAI;UAAAC;UAAAC;QAAA,CADA;MAdA,CAjBA;MAmCAC;IAnCA;EAqCA,CAxCA;;EAyCAC,2BAzCA;;EA0CAC;IACA;IACA,6CAFA,CAGA;;IAEA;IACA,wCANA,CAOA;;IAEA;;IACA;MACA;IACA;EACA,CAvDA;;EAwDAC;IACA;AACA;AACA;IACA;MACA;MACA;QAAAtB;MAAA;MACA;IACA,CARA;;IASAuB;MACA;IACA,CAXA;;IAYA;AACA;AACA;IACA;MACA;QACAC;MADA;MAGA;QAAAxB;MAAA;MACA;IACA,CArBA;;IAsBA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;MACA;MACA;QAAAA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CA9CA;;IAgDAyB;MACAC;IACA,CAlDA;;IAmDAC;MACA;QACA;UACA;;UACA;YACAC;UACA;;UACA;YACAvB,WADA;YAEAC,sBAFA;YAGAC,4BAHA;YAIAC,gCAJA;YAKAC,0BALA;YAMAC,sBANA;YAOAC,4BAPA;YAQAC,gCARA;YASAC,kCATA;YAUAC;UAVA,GAYAe,IAZA,CAYAC;YACA;cAAAC;cAAAC;YAAA;;YACA;cACA;gBACAf,eADA;gBAEAgB;cAFA;cAIA;YACA;UACA,CArBA;QAsBA,CA3BA,MA2BA;UACA;YACAhB,iBADA;YAEAgB;UAFA;UAIA;QACA;MACA,CAnCA;IAoCA,CAxFA;;IAyFAC;MACA;IACA;;EA3FA;AAxDA", "names": ["name", "data", "user", "officeData", "classifyData", "form", "id", "title", "officeId", "officeName", "endTime", "score", "classify", "isMainwork", "publishTime", "auditStatus", "rules", "required", "message", "trigger", "circlesStatus", "props", "mounted", "methods", "select", "types", "handleChange", "console", "submitForm", "url", "then", "res", "<PERSON><PERSON><PERSON>", "errmsg", "type", "resetForm"], "sourceRoot": "src/views/AssessmentOrgan/BusinessObjectives", "sources": ["BusinessObjectivesNew.vue"], "sourcesContent": ["<template>\r\n  <div class=\"BusinessObjectivesNew\">\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"demo-form\">\r\n      <el-form-item label=\"标题\"\r\n                    prop=\"title\">\r\n        <el-input v-model=\"form.title\"\r\n                  clearable>\r\n\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"发布时间\"\r\n                    prop=\"publishTime\">\r\n\r\n        <el-date-picker type=\"date\"\r\n                        placeholder=\"选择日期\"\r\n                        value-format=\"timestamp\"\r\n                        v-model=\"form.publishTime\"\r\n                        style=\"width: 100%;\">\r\n        </el-date-picker>\r\n\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"部门\"\r\n                    prop=\"officeId\">\r\n        <zy-select v-model=\"form.officeId\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\"\r\n                   @select=\"select\"\r\n                   placeholder=\"请选择部门\">\r\n        </zy-select>\r\n        <!-- <button @click=\"demo\">11</button> -->\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"时效选择\"\r\n                    prop=\"endTime\">\r\n        <el-form-item prop=\"endTime\">\r\n          <el-date-picker type=\"date\"\r\n                          placeholder=\"选择日期\"\r\n                          value-format=\"timestamp\"\r\n                          v-model=\"form.endTime\"\r\n                          style=\"width: 100%;\"></el-date-picker>\r\n        </el-form-item>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"分值\"\r\n                    class=\"form-title\">\r\n        <el-input-number v-model=\"form.score\"\r\n                         controls-position=\"right\"\r\n                         @change=\"handleChange\"\r\n                         :min=\"0\"\r\n                         :max=\"100\"\r\n                         class=\"form-content\">\r\n        </el-input-number>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"是否重点工作\"\r\n                    prop=\"isMainwork\">\r\n        <el-radio-group v-model=\"form.isMainwork\">\r\n          <el-radio label=\"1\">是</el-radio>\r\n          <el-radio label=\"0\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"类型\"\r\n                    prop=\"classify\">\r\n        <el-select v-model=\"form.classify\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择类型\">\r\n          <el-option v-for=\"item in classifyData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'BusinessObjectivesNew',\r\n  data () {\r\n    return {\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())), // 获取当前登录人信息\r\n      officeData: [],\r\n      classifyData: [],\r\n      form: {\r\n        id: '',\r\n        title: '',\r\n        officeId: '',\r\n        officeName: '',\r\n        endTime: '',\r\n        score: '',\r\n        classify: '',\r\n        isMainwork: '',\r\n        publishTime: '',\r\n        auditStatus: ''\r\n\r\n      },\r\n      rules: {\r\n\r\n        title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n        officeId: [\r\n          { required: true, message: '请选择部门', trigger: 'blur' }\r\n        ],\r\n        publishTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ],\r\n        endTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ],\r\n        isMainwork: [\r\n          { required: true, message: '请选择', trigger: 'blur' }\r\n        ]\r\n      },\r\n      circlesStatus: []\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  mounted () {\r\n    this.treeList()\r\n    this.form.publishTime = new Date().getTime()\r\n    // this.form.endTime = this.$utils.tmp(false) //获取当前时间\r\n\r\n    this.form.officeName = this.user.officeName\r\n    this.form.officeId = this.user.officeId\r\n    // this.userData = [{ mobile: this.user.mobile, name: this.user.userName, position: this.user.position, userId: this.user.id, userName: this.user.userName }]\r\n\r\n    this.dictionaryPubkvs()\r\n    if (this.id) {\r\n      this.getBusinessObjectiveDetails()\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n    *机构树\r\n   */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    select (row) {\r\n      this.form.officeName = row ? row.label : ''\r\n    },\r\n    /**\r\n     *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_functional_work'\r\n      })\r\n      var { data } = res\r\n      this.classifyData = data.evaluation_functional_work\r\n    },\r\n    // // 选择用户的回调\r\n    // userCallback (data, type) {\r\n    //   if (type) {\r\n    //     this.userData = data\r\n    //     this.form.publishUserName = data[0].name\r\n    //     this.form.officeName = data[0].officeName\r\n    //   }\r\n    //   this.userShow = !this.userShow\r\n    // },\r\n\r\n    // 获取目标详情 (作用:编辑界面内容填充)\r\n    async getBusinessObjectiveDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqBusinessObjectiveDetails(this.id)\r\n      var { data } = res\r\n      this.form.id = data.id\r\n      this.form.title = data.title\r\n      this.form.officeName = data.officeName\r\n      this.form.officeId = data.officeId\r\n      this.form.publishTime = data.publishTime\r\n      this.form.endTime = data.endTime\r\n      this.form.score = data.score\r\n      this.form.auditStatus = data.auditStatus\r\n      this.form.isMainwork = data.isMainwork\r\n      this.form.classify = data.classify\r\n    },\r\n\r\n    handleChange (value) {\r\n      console.log(value)\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var url = '/functional/work/add?'\r\n          if (this.id) {\r\n            url = '/functional/work/edit?'\r\n          }\r\n          this.$api.AssessmentOrgan.reqAddBusinessObjectives(url, {\r\n            id: this.id,\r\n            title: this.form.title,\r\n            officeId: this.form.officeId,\r\n            officeName: this.form.officeName,\r\n            endTime: this.form.endTime,\r\n            score: this.form.score,\r\n            classify: this.form.classify,\r\n            isMainwork: this.form.isMainwork,\r\n            publishTime: this.form.publishTime,\r\n            auditStatus: this.form.auditStatus\r\n\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('newCallback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('newCallback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.BusinessObjectivesNew {\r\n  width: 692px;\r\n  height: 100%;\r\n  padding: 24px 40px;\r\n  // overflow-y: scroll;\r\n  overflow-x: hidden;\r\n  overflow-y: auto;\r\n\r\n  .el-form-item__label {\r\n    text-align: right;\r\n    vertical-align: middle;\r\n    float: left;\r\n    font-size: 13px;\r\n    color: #606266;\r\n    line-height: 40px;\r\n    padding: 0 12px 0 0;\r\n    box-sizing: border-box;\r\n  }\r\n}\r\n</style>\r\n"]}]}