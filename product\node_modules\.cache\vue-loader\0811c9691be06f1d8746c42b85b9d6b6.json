{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\BusinessObjectivesNew.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\BusinessObjectives\\BusinessObjectivesNew.vue", "mtime": 1752541693786}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BusinessObjectivesNew.vue"], "names": [], "mappings": ";AA2FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BusinessObjectivesNew.vue", "sourceRoot": "src/views/AssessmentOrgan/BusinessObjectives", "sourcesContent": ["<template>\r\n  <div class=\"BusinessObjectivesNew\">\r\n    <el-form :model=\"form\"\r\n             :rules=\"rules\"\r\n             ref=\"form\"\r\n             label-width=\"100px\"\r\n             class=\"demo-form\">\r\n      <el-form-item label=\"标题\"\r\n                    prop=\"title\">\r\n        <el-input v-model=\"form.title\"\r\n                  clearable>\r\n\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"发布时间\"\r\n                    prop=\"publishTime\">\r\n\r\n        <el-date-picker type=\"date\"\r\n                        placeholder=\"选择日期\"\r\n                        value-format=\"timestamp\"\r\n                        v-model=\"form.publishTime\"\r\n                        style=\"width: 100%;\">\r\n        </el-date-picker>\r\n\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"部门\"\r\n                    prop=\"officeId\">\r\n        <zy-select v-model=\"form.officeId\"\r\n                   node-key=\"id\"\r\n                   :data=\"officeData\"\r\n                   style=\"width: 100%;\"\r\n                   @select=\"select\"\r\n                   placeholder=\"请选择部门\">\r\n        </zy-select>\r\n        <!-- <button @click=\"demo\">11</button> -->\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"时效选择\"\r\n                    prop=\"endTime\">\r\n        <el-form-item prop=\"endTime\">\r\n          <el-date-picker type=\"date\"\r\n                          placeholder=\"选择日期\"\r\n                          value-format=\"timestamp\"\r\n                          v-model=\"form.endTime\"\r\n                          style=\"width: 100%;\"></el-date-picker>\r\n        </el-form-item>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"分值\"\r\n                    class=\"form-title\">\r\n        <el-input-number v-model=\"form.score\"\r\n                         controls-position=\"right\"\r\n                         @change=\"handleChange\"\r\n                         :min=\"0\"\r\n                         :max=\"100\"\r\n                         class=\"form-content\">\r\n        </el-input-number>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"是否重点工作\"\r\n                    prop=\"isMainwork\">\r\n        <el-radio-group v-model=\"form.isMainwork\">\r\n          <el-radio label=\"1\">是</el-radio>\r\n          <el-radio label=\"0\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"类型\"\r\n                    prop=\"classify\">\r\n        <el-select v-model=\"form.classify\"\r\n                   filterable\r\n                   clearable\r\n                   placeholder=\"请选择类型\">\r\n          <el-option v-for=\"item in classifyData\"\r\n                     :key=\"item.id\"\r\n                     :label=\"item.value\"\r\n                     :value=\"item.id\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'BusinessObjectivesNew',\r\n  data () {\r\n    return {\r\n      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())), // 获取当前登录人信息\r\n      officeData: [],\r\n      classifyData: [],\r\n      form: {\r\n        id: '',\r\n        title: '',\r\n        officeId: '',\r\n        officeName: '',\r\n        endTime: '',\r\n        score: '',\r\n        classify: '',\r\n        isMainwork: '',\r\n        publishTime: '',\r\n        auditStatus: ''\r\n\r\n      },\r\n      rules: {\r\n\r\n        title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ],\r\n        officeId: [\r\n          { required: true, message: '请选择部门', trigger: 'blur' }\r\n        ],\r\n        publishTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ],\r\n        endTime: [\r\n          { required: true, message: '请选择时间', trigger: 'blur' }\r\n        ],\r\n        isMainwork: [\r\n          { required: true, message: '请选择', trigger: 'blur' }\r\n        ]\r\n      },\r\n      circlesStatus: []\r\n    }\r\n  },\r\n  props: ['id', 'memberType'],\r\n  mounted () {\r\n    this.treeList()\r\n    this.form.publishTime = new Date().getTime()\r\n    // this.form.endTime = this.$utils.tmp(false) //获取当前时间\r\n\r\n    this.form.officeName = this.user.officeName\r\n    this.form.officeId = this.user.officeId\r\n    // this.userData = [{ mobile: this.user.mobile, name: this.user.userName, position: this.user.position, userId: this.user.id, userName: this.user.userName }]\r\n\r\n    this.dictionaryPubkvs()\r\n    if (this.id) {\r\n      this.getBusinessObjectiveDetails()\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n    *机构树\r\n   */\r\n    async treeList () {\r\n      const res = await this.$api.systemSettings.treeList({})\r\n      var { data } = res\r\n      this.officeData = data\r\n    },\r\n    select (row) {\r\n      this.form.officeName = row ? row.label : ''\r\n    },\r\n    /**\r\n     *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_functional_work'\r\n      })\r\n      var { data } = res\r\n      this.classifyData = data.evaluation_functional_work\r\n    },\r\n    // // 选择用户的回调\r\n    // userCallback (data, type) {\r\n    //   if (type) {\r\n    //     this.userData = data\r\n    //     this.form.publishUserName = data[0].name\r\n    //     this.form.officeName = data[0].officeName\r\n    //   }\r\n    //   this.userShow = !this.userShow\r\n    // },\r\n\r\n    // 获取目标详情 (作用:编辑界面内容填充)\r\n    async getBusinessObjectiveDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqBusinessObjectiveDetails(this.id)\r\n      var { data } = res\r\n      this.form.id = data.id\r\n      this.form.title = data.title\r\n      this.form.officeName = data.officeName\r\n      this.form.officeId = data.officeId\r\n      this.form.publishTime = data.publishTime\r\n      this.form.endTime = data.endTime\r\n      this.form.score = data.score\r\n      this.form.auditStatus = data.auditStatus\r\n      this.form.isMainwork = data.isMainwork\r\n      this.form.classify = data.classify\r\n    },\r\n\r\n    handleChange (value) {\r\n      console.log(value)\r\n    },\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var url = '/functional/work/add?'\r\n          if (this.id) {\r\n            url = '/functional/work/edit?'\r\n          }\r\n          this.$api.AssessmentOrgan.reqAddBusinessObjectives(url, {\r\n            id: this.id,\r\n            title: this.form.title,\r\n            officeId: this.form.officeId,\r\n            officeName: this.form.officeName,\r\n            endTime: this.form.endTime,\r\n            score: this.form.score,\r\n            classify: this.form.classify,\r\n            isMainwork: this.form.isMainwork,\r\n            publishTime: this.form.publishTime,\r\n            auditStatus: this.form.auditStatus\r\n\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('newCallback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('newCallback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.BusinessObjectivesNew {\r\n  width: 692px;\r\n  height: 100%;\r\n  padding: 24px 40px;\r\n  // overflow-y: scroll;\r\n  overflow-x: hidden;\r\n  overflow-y: auto;\r\n\r\n  .el-form-item__label {\r\n    text-align: right;\r\n    vertical-align: middle;\r\n    float: left;\r\n    font-size: 13px;\r\n    color: #606266;\r\n    line-height: 40px;\r\n    padding: 0 12px 0 0;\r\n    box-sizing: border-box;\r\n  }\r\n}\r\n</style>\r\n"]}]}