{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\qd-upload-img\\qd-upload-img.vue?vue&type=style&index=0&id=8020ea1a&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\components\\qd-upload-img\\qd-upload-img.vue", "mtime": 1752541693469}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouYXZhdGFyLXVwbG9hZGVyIHsNCiAgLmVsLXVwbG9hZCB7DQogICAgYm9yZGVyOiAxcHggZGFzaGVkICNkOWQ5ZDk7DQogICAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgfQ0KICAuZWwtdXBsb2FkOmhvdmVyIHsNCiAgICBib3JkZXItY29sb3I6ICM0MDllZmY7DQogIH0NCiAgLmF2YXRhci11cGxvYWRlci1pY29uIHsNCiAgICBmb250LXNpemU6IDI4cHg7DQogICAgY29sb3I6ICM4YzkzOWQ7DQogICAgd2lkdGg6IDE0OHB4Ow0KICAgIGhlaWdodDogMTQ4cHg7DQogICAgbGluZS1oZWlnaHQ6IDE0OHB4Ow0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgfQ0KICAuYXZhdGFyIHsNCiAgICB3aWR0aDogMTQ4cHg7DQogICAgaGVpZ2h0OiAxNDhweDsNCiAgICBkaXNwbGF5OiBibG9jazsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["qd-upload-img.vue"], "names": [], "mappings": ";AAiGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "qd-upload-img.vue", "sourceRoot": "src/components/qd-upload-img", "sourcesContent": ["<template>\r\n  <el-upload\r\n    class=\"avatar-uploader\"\r\n    action=\"/#\"\r\n    accept=\".jpg,.jpeg,.png,.PNG,.JPG\"\r\n    :show-file-list=\"false\"\r\n    :http-request=\"customUpload\"\r\n    :before-upload=\"beforeAvatarUpload\"\r\n  >\r\n    <img v-if=\"photoList.length\" :src=\"photoList[0].filePath\" class=\"avatar\" />\r\n    <img v-else-if=\"files.length > 0\" :src=\"files[0].filePath\" class=\"avatar\" />\r\n    <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\r\n    <div slot=\"tip\" class=\"el-upload__tip\">{{ tip }}</div>\r\n  </el-upload>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'qd-upload-img',\r\n  props: {\r\n    value: Array,\r\n    module: {\r\n      type: String,\r\n      default: 'splashImg'\r\n    },\r\n    tip: {\r\n      type: String,\r\n      default: '只能上传jpg/png等图片格式的文件，且不超过10mb'\r\n    },\r\n    size: {\r\n      type: Array,\r\n      default: () => { return [] }\r\n    },\r\n    photoList: {\r\n      type: Array,\r\n      default: () => {\r\n        return []\r\n      }\r\n    }\r\n  },\r\n  model: {\r\n    prop: 'value', // 这个字段，是指父组件设置 v-model 时，将变量值传给子组件的 msg\r\n    event: 'file'// 这个字段，是指父组件监听 parent-event 事件\r\n  },\r\n  data () {\r\n    return {\r\n      files: []\r\n    }\r\n  },\r\n  watch: {\r\n    value (val) {\r\n      if (val) {\r\n        this.files = val\r\n      } else {\r\n        this.files = []\r\n      }\r\n    },\r\n    files (val) {\r\n      this.$emit('file', val)\r\n    }\r\n  },\r\n  methods: {\r\n    // 校验文件类型和文件大小\r\n    beforeAvatarUpload (file) {\r\n      // const isJPG = file.type === 'image/jpeg'\r\n      const isLt2M = file.size / 1024 / 1024 < 10\r\n      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n      if (testmsg !== 'png' && testmsg !== 'jpg' && testmsg !== 'PNG' && testmsg !== 'jpeg' && testmsg !== 'JPG' && testmsg !== 'gif') {\r\n        this.$message.error('图片文件格式暂时不支持!')\r\n        return false\r\n      }\r\n      if (!isLt2M) {\r\n        this.$message.error('上传图片大小不能超过 10MB!')\r\n        return false\r\n      }\r\n      return isLt2M && testmsg\r\n    },\r\n    // 上传逻辑\r\n    async customUpload (file) {\r\n      const siteId = JSON.parse(sessionStorage.getItem('user' + this.$logo())).areaId\r\n      const formData = new FormData()\r\n      formData.append('attachment', file.file)\r\n      formData.append('module', this.module)\r\n      formData.append('siteId', siteId)\r\n      this.$api.microAdvice.uploadFile(formData).then(res => {\r\n        const { errcode, data } = res\r\n        if (errcode === 200) {\r\n          this.files = data\r\n        }\r\n      })\r\n      this.$emit('initPhoto')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.avatar-uploader {\r\n  .el-upload {\r\n    border: 1px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n  }\r\n  .el-upload:hover {\r\n    border-color: #409eff;\r\n  }\r\n  .avatar-uploader-icon {\r\n    font-size: 28px;\r\n    color: #8c939d;\r\n    width: 148px;\r\n    height: 148px;\r\n    line-height: 148px;\r\n    text-align: center;\r\n  }\r\n  .avatar {\r\n    width: 148px;\r\n    height: 148px;\r\n    display: block;\r\n  }\r\n}\r\n</style>\r\n"]}]}