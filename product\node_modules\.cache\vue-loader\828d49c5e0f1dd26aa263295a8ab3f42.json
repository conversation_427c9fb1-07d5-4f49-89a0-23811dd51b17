{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\general-custom-topic\\custom-topic-details\\custom-topic-details.vue?vue&type=style&index=0&id=694d92bf&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\general-custom-topic\\custom-topic-details\\custom-topic-details.vue", "mtime": 1752541697693}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpAaW1wb3J0ICcuL2N1c3RvbS10b3BpYy1kZXRhaWxzLnNjc3MnOw0K"}, {"version": 3, "sources": ["custom-topic-details.vue"], "names": [], "mappings": ";AA8DA", "file": "custom-topic-details.vue", "sourceRoot": "src/views/wisdomWarehouse/general-custom-topic/custom-topic-details", "sourcesContent": ["<template>\r\n  <div class=\"custom-project-details\">\r\n    <div class=\"custom-project-details-title\">{{details.name}}</div>\r\n    <div class=\"custom-project-details-xx\">\r\n      <div class=\"custom-project-details-tiem\">发布时间：{{details.pubDate}}</div>\r\n    </div>\r\n    <div class=\"custom-project-details-content\"\r\n         v-html=\"details.content\"></div>\r\n    <!-- *****附件***** -->\r\n    <div class=\"fileBox\"\r\n         v-if=\"details.attachmentList !==undefined && details.attachmentList.length > 0 \">\r\n      <div class=\"file_title\"> 资讯附件 </div>\r\n      <div class=\"fileListt\"\r\n           v-for=\"(item, index) in details.attachmentList\"\r\n           :key=\"index\">\r\n        <div class=\"file_item\">\r\n\r\n          <div class=\"file_name\"> {{item.fileName}} </div>\r\n\r\n          <div class=\"file_load\">\r\n            <div class=\"load_text\"><a :href=\"item.filePath\"\r\n                 target=\"_blank\">预览</a>\r\n            </div>\r\n            <div class=\"shu\"></div>\r\n            <div class=\"load_text\"\r\n                 @click=\"download(item)\">下载</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'committeeDataCustomTopic-ww',\r\n  data () {\r\n    return {\r\n      details: {}\r\n    }\r\n  },\r\n  props: ['id', 'name'],\r\n  mounted () {\r\n    if (this.id) {\r\n      this.customTopicListInfo()\r\n    }\r\n  },\r\n  methods: {\r\n    async customTopicListInfo () {\r\n      const res = await this.$api.wisdomWarehouse.assemblyInfo(this.id)\r\n      var { data } = res\r\n      this.details = data\r\n      // if (data.externalLinks) {\r\n      //   window.open(data.externalLinks, '_blank')\r\n      // }\r\n    },\r\n    download (data) {\r\n      this.$api.proposal.downloadFile({ id: data.id }, data.fileName)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import './custom-topic-details.scss';\r\n</style>\r\n"]}]}