{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\DoubleQuote\\DoubleDetails.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\AssessmentOrgan\\DoubleQuote\\DoubleDetails.vue", "mtime": 1752541693814}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAoEA;AACA;AACA;AACA;AACA;AACA;AAEA;EACAA,qBADA;EAEA;EACAC,cAHA;;EAKAC;IACA;MACAC,kCADA;MAEAC,sCAFA;MAGAC,kBAHA;MAIAC,OAJA;MAKAC,gBALA;MAMA;MACAC,QAPA;MAQAC,YARA;MASAC,kBATA;MAUAC,mCAVA;MAWAC,8BAXA;MAYAC,qBAZA;MAaAC,mDAbA;MAcAC,yCAdA;MAeAC,aAfA;MAgBAC,UAhBA;MAiBAC,QAjBA;MAkBAC,UAlBA;MAmBAC;IAnBA;EAsBA,CA5BA;;EA6BAC,iCA7BA;;EA8BAC;IACA;EACA,CAhCA;;EAiCAC;IACAC;MACA;QACAC,uBADA;QAEAC,sBAFA;QAGAC;MAHA,GAIAC,IAJA,CAIA;QACA;MACA,CANA,EAMAC,KANA,CAMA;QACA;UACAF,YADA;UAEAG;QAFA;MAIA,CAXA;IAYA,CAdA;;IAeA;IACA;MACA;QAAAxB;QAAAyB;MAAA;MACA;QAAAC;QAAAC;MAAA;;MACA;QACA;UACAH,eADA;UAEAH;QAFA;MAIA;IACA,CAzBA;;IA2BA;AACA;AACA;IACA;MACA;QACAO;MADA;MAGA;QAAAhC;MAAA;MACA;IACA,CApCA;;IAsCA;IACA,wCAvCA;;IAwCA;IACAiC;MACA;QACA;QACA;MACA;IACA,CA9CA;;IAgDA;MACA;QACAC,SADA;QAEAC,0BAFA;QAGAC,YAHA;QAIAC;MAJA;MAMA;MAEA;MACA;IACA,CA3DA;;IA4DA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;MACA;MACA;QAAArC;MAAA;MAEA;IACA,CA1EA;;IA2EA;IACAsC;MACA;QAAAC;MAAA;IACA,CA9EA;;IA+EAC;MACA;MACAC;IACA;;EAlFA,CAjCA;;EAqHAC;IACA;EACA,CAvHA;;EAwHAC,YACA;IACA,sBAFA;;IAGAC;MACA;MACA;QACA;UACAC;QACA;MACA,CAJA;MAKA;IACA;;EAXA;AAxHA", "names": ["name", "components", "data", "approve", "noApprove", "doubleTypeData", "ids", "TyposShow", "form", "fileList", "manuscriptData", "all", "rowId", "manuscriptFlag", "isexcellent", "helper", "similar", "suspend", "wrong", "show", "clickTime", "inject", "created", "methods", "passClick", "confirmButtonText", "cancelButtonText", "type", "then", "catch", "message", "auditStatus", "<PERSON><PERSON><PERSON>", "errmsg", "types", "showHelper", "title", "content", "sessions", "times", "download", "id", "callback", "console", "mounted", "computed", "doubleClass", "double1"], "sourceRoot": "src/views/AssessmentOrgan/DoubleQuote", "sources": ["DoubleDetails.vue"], "sourcesContent": ["<template>\r\n  <!-- 双招双引详情 -->\r\n  <div class=\"detailComment scrollBar \">\r\n    <div class=\"officeDetial-title\"> {{form.title}} </div>\r\n\r\n    <div class=\"relevantInformation\">\r\n\r\n      <div class=\"officeDetial-org\">\r\n        <div class=\"org-item\"> 类型 : <span> {{doubleClass}}</span> </div>\r\n        <div class=\"org-item\"> 所属个人: <span> {{form.publishUserName}}</span> </div>\r\n      </div>\r\n      <div class=\"officeDetial-org\">\r\n        <div class=\"org-item\"> 部门： <span> {{form.officeName}}</span> </div>\r\n        <div class=\"org-item\"> 时间： <span>{{ $format(form.publishTime)  }}</span></div>\r\n\r\n      </div>\r\n      <div>\r\n        <el-button type=\"primary\"\r\n                   v-if=\"this.approve==='true'\"\r\n                   icon=\"el-icon-circle-check\"\r\n                   @click=\"passClick(2)\">审核通过\r\n        </el-button>\r\n\r\n        <el-button type=\"danger\"\r\n                   v-if=\"this.noApprove==='true'\"\r\n                   icon=\"el-icon-remove-outline\"\r\n                   @click=\"passClick(3)\">审核不通过\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n    <!-- 内容 -->\r\n    <div class=\"contBox\">\r\n      <!-- <div v-if=\"manuscriptFlag\"\r\n           class=\"content\"\r\n           v-html=\"manuscriptData.content\"> </div> -->\r\n      <div class=\"content\"\r\n           v-if=\"form.content\"\r\n           v-html=\"form.content\"></div>\r\n    </div>\r\n\r\n    <!-- *****附件***** -->\r\n    <div class=\"fileBox\"\r\n         v-if=\"form.attachmentInfo\">\r\n      <div class=\"file_title\"\r\n           v-if=\"form.attachmentInfo.length!==0\"> 资讯附件 </div>\r\n      <div class=\"fileListt\">\r\n\r\n        <div class=\"file_item\"\r\n             v-for=\"(item,index) in form.attachmentInfo\"\r\n             :key=\"index\">\r\n\r\n          <div class=\"file_name\"> {{item.oldName}} </div>\r\n\r\n          <div class=\"file_load\">\r\n            <div class=\"load_text\"><a :href=\"item.fullPath\"\r\n                 target=\"_blank\">预览</a>\r\n            </div>\r\n            <div class=\"shu\"></div>\r\n            <div class=\"load_text\"\r\n                 @click=\"download(item)\">下载</div>\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\n// 套用模板:src\\views\\socialpublic-qd\\detailComment\\detailComment.vue\r\n// 修改了import引入的三个文件的相对路径\r\n// import handinfo from '../../socialpublic-qd/socialpulicManage/Allsocialpublic/handinfo/handinfo'\r\n// import similarity from '../../socialpublic-qd/info/submitSocialpublic/similarity/similarity'\r\n// import Typos from '../../socialpublic-qd/info/submitSocialpublic/Typos/Typos'\r\nimport { mapActions, mapState } from 'vuex'\r\n\r\nexport default {\r\n  name: 'DoubleDetails',\r\n  // components: { handinfo, similarity, Typos },\r\n  components: {},\r\n\r\n  data () {\r\n    return {\r\n      approve: this.$route.query.approve,\r\n      noApprove: this.$route.query.noApprove,\r\n      doubleTypeData: [],\r\n      ids: [],\r\n      TyposShow: false,\r\n      // SimilarityShow: false,\r\n      form: {},\r\n      fileList: [],\r\n      manuscriptData: {},\r\n      all: this.$route.query.all || false,\r\n      rowId: this.$route.query.rowId,\r\n      manuscriptFlag: false,\r\n      isexcellent: this.$route.query.isexcellent || false,\r\n      helper: this.$route.query.helper || false,\r\n      similar: '0%',\r\n      suspend: 0,\r\n      wrong: 0,\r\n      show: true,\r\n      clickTime: ''\r\n\r\n    }\r\n  },\r\n  inject: ['openoffice', 'openPdf'],\r\n  created () {\r\n    this.getDoubleQuoteDetails()\r\n  },\r\n  methods: {\r\n    passClick (auditStatus) {\r\n      this.$confirm(`此操作将选择的审核状态改为${auditStatus === 2 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.getCheckDouble(this.$route.query.rowId, auditStatus)\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消操作'\r\n        })\r\n      })\r\n    },\r\n    // 审核 (加分项)\r\n    async getCheckDouble (id, auditStatus) {\r\n      const res = await this.$api.AssessmentOrgan.reqCheckDouble({ ids: id, auditStatus: auditStatus })\r\n      var { errcode, errmsg } = res\r\n      if (errcode === 200) {\r\n        this.$message({\r\n          message: errmsg,\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n\r\n    /**\r\n        *字典\r\n    */\r\n    async dictionaryPubkvs () {\r\n      const res = await this.$api.systemSettings.dictionaryPubkvs({\r\n        types: 'evaluation_same_double'\r\n      })\r\n      var { data } = res\r\n      this.doubleTypeData = data.evaluation_same_double\r\n    },\r\n\r\n    // **************\r\n    ...mapActions(['getDoubleQuoteDetails']),\r\n    // ******************\r\n    showHelper () {\r\n      if (new Date().getTime() - this.clickTime > 1000) {\r\n        this.clickTime = new Date().getTime()\r\n        this.show = !this.show\r\n      }\r\n    },\r\n\r\n    async getcorrector () {\r\n      const res = await this.$api.publicOpinionNew.corrector({\r\n        title: '',\r\n        content: this.form.content,\r\n        sessions: '',\r\n        times: ''\r\n      })\r\n      var data = JSON.parse(res.data)\r\n\r\n      this.suspend = data.detail.length\r\n      this.wrong = data.suspend_detail.length\r\n    },\r\n    // 预览(另一种方法替代)\r\n    // priew (data) {\r\n    //   const arr = ['doc', 'docx', 'xlsx']\r\n    //   if (arr.includes(data.extension)) {\r\n    //     this.openoffice(data.openUrl)\r\n    //   }\r\n    // },\r\n\r\n    // 后台获取详情数据(部分还需修改)\r\n    async getDoubleQuoteDetails () {\r\n      const res = await this.$api.AssessmentOrgan.reqDoubleQuoteDetails(this.$route.query.rowId)\r\n      var { data } = res\r\n\r\n      this.form = data\r\n    },\r\n    // 通用的附件下载方法download 只需要改对应的附件id和名字\r\n    download (item) {\r\n      this.$api.proposal.downloadFile({ id: item.id }, item.oldName)\r\n    },\r\n    callback (data) {\r\n      this.TyposShow = false\r\n      console.log(data)\r\n    }\r\n  },\r\n  mounted () {\r\n    this.dictionaryPubkvs()\r\n  },\r\n  computed: {\r\n    // 测试: 通过映射函数 获取title信息\r\n    ...mapState(['title']),\r\n    doubleClass () {\r\n      var double1 = ''\r\n      this.doubleTypeData.forEach(item => {\r\n        if (item.id === this.form.doubleType) {\r\n          double1 = item.value\r\n        }\r\n      })\r\n      return double1\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" >\r\n.detailComment {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding-top: 33px;\r\n  background: #fff;\r\n  .officeDetial-title {\r\n    font-size: 26px;\r\n    font-size: 24px;\r\n    font-family: PingFang SC;\r\n    font-weight: 800;\r\n    color: #333333;\r\n    line-height: 36px;\r\n    text-align: center;\r\n    margin: 29px;\r\n  }\r\n  .relevantInformation {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    .officeDetial-org {\r\n      font-size: $textSize14;\r\n      font-family: PingFang SC;\r\n      font-weight: 400;\r\n      line-height: 24px;\r\n      // display: flex;\r\n      // justify-content: space-around;\r\n      // padding-left: 40px;\r\n      .org-item {\r\n        color: #999999;\r\n        // margin-right: 140px;\r\n        // min-width: 300px;\r\n        span {\r\n          margin-left: 38px;\r\n        }\r\n      }\r\n      // .org-item + .org-item {\r\n      //     margin-left: 140px;\r\n      // }\r\n    }\r\n  }\r\n\r\n  .contBox {\r\n    margin-top: 20px;\r\n    border-top: 2px solid #ebebeb;\r\n    display: flex;\r\n\r\n    .content {\r\n      flex: 1;\r\n      padding: 30px 40px;\r\n      line-height: 30px;\r\n      min-height: 500px;\r\n    }\r\n    .content + .content {\r\n      border-left: 1px solid #ebebeb;\r\n    }\r\n  }\r\n\r\n  .similarityImg {\r\n    padding-left: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    img {\r\n      width: 80px;\r\n      cursor: pointer;\r\n    }\r\n    .analysisReslut {\r\n      height: 60px;\r\n      line-height: 60px;\r\n      padding: 0 44px;\r\n      // background: #f5f5fb;\r\n      background-image: url(\"../../../assets/qdimg/round.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      border-radius: 20px;\r\n      span {\r\n        font-size: 26px;\r\n        font-family: PangMenZhengDao;\r\n        font-weight: 700;\r\n        color: #007bff;\r\n        line-height: 36px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n  .manuscript {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-right: 40px;\r\n    padding-bottom: 20px;\r\n    .yuangoa {\r\n      flex: 1;\r\n      span {\r\n        font-size: 26px;\r\n        font-family: PingFang SC;\r\n        font-weight: 500;\r\n        color: #999999;\r\n        line-height: 36px;\r\n        margin-left: 40px;\r\n      }\r\n      span + span {\r\n        margin-left: 50%;\r\n      }\r\n    }\r\n  }\r\n  .fileBox {\r\n    width: 100%;\r\n    background: #ffffff;\r\n    border-radius: 10px;\r\n    padding: 30px;\r\n    .file_title {\r\n      font-size: $textSize16;\r\n      font-family: PingFang SC;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 36px;\r\n      margin-bottom: 23px;\r\n    }\r\n    .fileListt {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      justify-content: space-between;\r\n      .file_item {\r\n        width: 48%;\r\n        background: #f5f5fb;\r\n        flex-shrink: 0;\r\n        height: 60px;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0 18px;\r\n        margin-bottom: 10px;\r\n        .file_type {\r\n          width: 32px;\r\n          height: 32px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .file_name {\r\n          margin-left: 12px;\r\n          flex: 1;\r\n          cursor: pointer;\r\n        }\r\n        .file_load {\r\n          display: flex;\r\n          align-items: center;\r\n          .load_text {\r\n            font-size: $textSize16;\r\n            font-family: PingFang SC;\r\n            font-weight: 500;\r\n            color: #007bff;\r\n            line-height: 36px;\r\n            cursor: pointer;\r\n          }\r\n          .shu {\r\n            width: 2px;\r\n            height: 22px;\r\n            background: #4f96fe;\r\n            margin: 0 12px;\r\n          }\r\n          .del {\r\n            width: 24px;\r\n            height: 24px;\r\n            margin-left: 23px;\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .hanldtype {\r\n    display: flex;\r\n    padding: 20px 30px;\r\n    justify-content: space-between;\r\n    > div {\r\n      font-weight: 700;\r\n    }\r\n  }\r\n  .handinfo {\r\n    width: 100%;\r\n    .hanldClounm .hanldCont .el-checkbox {\r\n      width: 25%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}