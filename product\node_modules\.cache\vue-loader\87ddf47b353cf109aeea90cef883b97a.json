{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\SinceSituation\\SinceSituation.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\wisdomWarehouse\\SinceSituation\\SinceSituation.vue", "mtime": 1752541697671}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SinceSituation.vue"], "names": [], "mappings": ";AA+CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SinceSituation.vue", "sourceRoot": "src/views/wisdomWarehouse/SinceSituation", "sourcesContent": ["<template>\r\n  <div class=\"SinceSituation\">\r\n    <search-box @search-click=\"search\"\r\n                @reset-click=\"reset\"\r\n                title=\"履职档案筛选\">\r\n      <zy-widget label=\"关键词\">\r\n        <el-input placeholder=\"请输入关键词\"\r\n                  v-model=\"keyword\"\r\n                  clearable\r\n                  @keyup.enter.native=\"search\">\r\n        </el-input>\r\n      </zy-widget>\r\n      <zy-widget label=\"年份\">\r\n        <el-date-picker v-model=\"year\"\r\n                        type=\"year\"\r\n                        value-format=\"yyyy\"\r\n                        placeholder=\"选择年份\">\r\n        </el-date-picker>\r\n      </zy-widget>\r\n    </search-box>\r\n\r\n    <div class=\"tableData\">\r\n      <mytable @userDetail=\"userDetail\"\r\n               :tableData=\"tableData\"\r\n               :data=\"ListHead\"></mytable>\r\n    </div>\r\n\r\n    <div class=\"paging_box\">\r\n      <el-pagination @size-change=\"howManyArticle\"\r\n                     @current-change=\"whatPage\"\r\n                     :current-page.sync=\"page\"\r\n                     :page-sizes=\"[10, 20, 50, 80, 100, 200, 500]\"\r\n                     :page-size.sync=\"pageSize\"\r\n                     background\r\n                     layout=\"total, sizes, prev, pager, next, jumper\"\r\n                     :total=\"total\">\r\n      </el-pagination>\r\n    </div>\r\n\r\n    <zy-pop-up v-model=\"detailsShow\"\r\n               title=\"履职统计详情\">\r\n      <SinceDetails :year=\"year\"\r\n                    :rowId=\"rowId\"></SinceDetails>\r\n    </zy-pop-up>\r\n  </div>\r\n</template>\r\n<script>\r\nimport tableData from '@/mixins/tableData'\r\nimport SinceDetails from './SinceDetail'\r\nimport Mytable from './mytable.vue'\r\nimport sinceData from './sinceData'\r\nexport default {\r\n  // 履职统计\r\n  name: 'sinceSituation-ww',\r\n  data () {\r\n    return {\r\n      keyword: '',\r\n      year: '',\r\n      delegation: '',\r\n      representerTeam: [],\r\n      tableData: [],\r\n      total: 0,\r\n      page: 1,\r\n      pageSize: 10,\r\n      rowId: '',\r\n      detailsShow: false\r\n\r\n    }\r\n  },\r\n  mixins: [tableData, sinceData],\r\n  components: {\r\n    SinceDetails,\r\n    Mytable\r\n  },\r\n  mounted () {\r\n    this.year = this.$format('', 'YYYY')\r\n    this.findDutys()\r\n    // this.getListHeader()\r\n  },\r\n  methods: {\r\n    search () {\r\n      this.findDutys()\r\n    },\r\n    reset () {\r\n      this.year = this.$format('', 'YYYY')\r\n      this.delegation = ''\r\n      this.keyword = ''\r\n      this.findDutys()\r\n    },\r\n    // 列表数据请求\r\n    async findDutys () {\r\n      const res = await this.$api.wisdomWarehouse.getWisemanUserDuty({\r\n        pageNo: this.page,\r\n        pageSize: this.pageSize,\r\n        keyword: this.keyword,\r\n        year: this.year\r\n      })\r\n      var { data, total } = res\r\n      console.log(res)\r\n      this.tableData = data || []\r\n      this.total = total\r\n      this.$nextTick(function () {\r\n        this.memoryChecked()\r\n      })\r\n    },\r\n    async getListHeader () {\r\n      const res = await this.$api.sinceManagement.getListHeader({\r\n        pageNo: this.page,\r\n        pageSize: this.pageSize,\r\n        year: this.year | this.$format('', 'YYYY')\r\n      })\r\n      this.ListHead = res.data\r\n    },\r\n    userDetail (data) {\r\n      this.rowId = data\r\n      this.detailsShow = true\r\n    },\r\n    howManyArticle (val) {\r\n      console.log(val)\r\n      this.findDutys()\r\n    },\r\n    whatPage (val) {\r\n      this.findDutys()\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.SinceSituation {\r\n  height: 100%;\r\n  width: 100%;\r\n  .tableData {\r\n    height: calc(100% - 116px);\r\n  }\r\n\r\n  .zy-table .el-scrollbar__wrap .el-scrollbar__view .el-table--border th {\r\n    border-color: #ccc;\r\n    background: #f5f7fb;\r\n  }\r\n  .zy-table .el-scrollbar__wrap .el-scrollbar__view .el-table--border td {\r\n    border-color: #ccc;\r\n  }\r\n}\r\n</style>\r\n"]}]}